from utils.database import db

class Vertex(db.Model):
    """
    Vertex model - represents a node in the path graph
    """
    __tablename__ = 'vertexes'
    __table_args__ = {'extend_existing': True}

    vertex_id = db.Column(db.Integer, primary_key=True)
    label = db.Column(db.String(255), nullable=True)
    x = db.Column(db.Integer, nullable=False)
    y = db.Column(db.Integer, nullable=False)
    type = db.Column(db.Integer, nullable=False)

    def to_dict(self):
        """Convert vertex to dictionary"""
        return {
            'vertex_id': self.vertex_id,
            'label': self.label,
            'x': self.x,
            'y': self.y,
            'type': self.type
        }

class Edge(db.Model):
    """
    Edge model - represents a connection between two vertices
    """
    __tablename__ = 'edges'
    __table_args__ = {'extend_existing': True}

    edge_id = db.Column(db.Integer, primary_key=True)
    src_id = db.Column(db.<PERSON><PERSON>, db.<PERSON>('vertexes.vertex_id'), nullable=True)
    dest_id = db.Column(db.Integer, db.ForeignKey('vertexes.vertex_id'), nullable=True)
    weight = db.Column(db.Integer, nullable=True)
    crowding = db.Column(db.Float, default=1.0, nullable=True)
    is_rideable = db.Column(db.Boolean, default=True, nullable=True)

    # Define relationships
    source = db.relationship('Vertex', foreign_keys=[src_id], backref='outgoing_edges')
    destination = db.relationship('Vertex', foreign_keys=[dest_id], backref='incoming_edges')

    def get_time_weight(self):
        """Calculate time-based weight considering crowding"""
        return int(self.weight / self.crowding)

    def get_rideable_weight(self):
        """Calculate weight for rideable transportation"""
        if not self.is_rideable:
            return self.get_time_weight()
        else:
            return int(self.get_time_weight() / 3)

    def get_smart_travel_weight(self):
        """Calculate weight for smart travel strategy (intelligent cycling + walking)"""
        base_weight = self.get_time_weight()
        if self.is_rideable:
            # Prefer rideable segments but not as aggressively as pure cycling strategy
            return int(base_weight / 2)
        else:
            # Use normal time weight for non-rideable segments
            return base_weight

    def get_cycling_distance_weight(self):
        """Calculate weight for cycling shortest distance strategy"""
        if self.is_rideable:
            return self.weight  # Use distance weight for rideable segments
        else:
            return self.weight * 10  # Heavily penalize non-rideable segments

    def get_cycling_time_weight(self):
        """Calculate weight for cycling shortest time strategy (considering crowding)"""
        crowding = self.crowding if self.crowding and self.crowding > 0 else 1.0
        if self.is_rideable:
            # Rideable segments: cycling time weight considering crowding
            # Cycling speed affected by crowding: speed = 12 * crowding
            ride_time_weight = self.weight / (12 * crowding)
            return int(ride_time_weight * 100)  # Scale up to maintain precision
        else:
            # Non-rideable segments: walking time weight with penalty
            walk_time_weight = self.weight / (4 * crowding)
            return int(walk_time_weight * 100 * 2)  # Double penalty for walking

    def get_weight_by_strategy(self, strategy):
        """
        Get weight based on strategy:
        0 - shortest distance
        1 - shortest time (considering crowding)
        2 - rideable transportation
        3 - smart travel (intelligent cycling + walking)
        4 - cycling shortest distance
        5 - cycling shortest time (considering crowding)
        """
        if strategy == 0:
            return self.weight
        elif strategy == 1:
            return self.get_time_weight()
        elif strategy == 2:
            return self.get_rideable_weight()
        elif strategy == 3:
            return self.get_smart_travel_weight()
        elif strategy == 4:
            return self.get_cycling_distance_weight()
        elif strategy == 5:
            return self.get_cycling_time_weight()
        else:
            return self.weight  # Default to distance

    def to_dict(self):
        """Convert edge to dictionary"""
        return {
            'edge_id': self.edge_id,
            'src_id': self.src_id,
            'dest_id': self.dest_id,
            'weight': self.weight,
            'crowding': self.crowding,
            'is_rideable': self.is_rideable
        }

class Vertex2(db.Model):
    """
    Vertex2 model - represents a node in the Chaoyang Park graph
    """
    __tablename__ = 'vertexes2'
    __table_args__ = {'extend_existing': True}

    vertex_id = db.Column(db.Integer, primary_key=True)
    label = db.Column(db.String(255), nullable=True)
    x = db.Column(db.Integer, nullable=False)
    y = db.Column(db.Integer, nullable=False)
    type = db.Column(db.String(255), nullable=True)

    def to_dict(self):
        """Convert vertex to dictionary"""
        return {
            'vertex_id': self.vertex_id,
            'label': self.label,
            'x': self.x,
            'y': self.y,
            'type': self.type
        }

class Edge2(db.Model):
    """
    Edge2 model - represents a connection between two vertices in Chaoyang Park
    """
    __tablename__ = 'edges2'
    __table_args__ = {'extend_existing': True}

    id = db.Column(db.Integer, primary_key=True)
    src_id = db.Column(db.Integer, db.ForeignKey('vertexes2.vertex_id'), nullable=False)
    des_id = db.Column(db.Integer, db.ForeignKey('vertexes2.vertex_id'), nullable=False)
    weight = db.Column(db.Integer, nullable=False)
    crowding = db.Column(db.Float, default=1.0, nullable=True)
    is_car = db.Column(db.Integer, default=0, nullable=True)

    # Define relationships
    source = db.relationship('Vertex2', foreign_keys=[src_id], backref='outgoing_edges2')
    destination = db.relationship('Vertex2', foreign_keys=[des_id], backref='incoming_edges2')

    def to_dict(self):
        """Convert edge to dictionary"""
        return {
            'id': self.id,
            'src_id': self.src_id,
            'des_id': self.des_id,
            'weight': self.weight,
            'crowding': self.crowding,
            'is_car': self.is_car
        }
