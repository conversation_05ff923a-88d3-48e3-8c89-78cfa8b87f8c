<template>
  <div class="shopping-cart-container">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>

    <template v-else>
      <div v-if="cartItems.length > 0" class="cart-items">
        <div class="cart-header">
          <h3>我的购物车 ({{ cartItems.length }})</h3>
          <el-button type="danger" size="small" @click="clearCart">清空购物车</el-button>
        </div>

        <div class="cart-item" v-for="item in cartItems" :key="item.id">
          <div class="item-image">
            <img :src="item.dish_image ? `http://localhost:5000${item.dish_image}` : defaultFoodImage" :alt="item.dish_name" />
          </div>
          <div class="item-info">
            <div class="item-name-price">
              <h4>{{ item.dish_name }}</h4>
              <div class="item-price">¥{{ item.dish_price }}</div>
            </div>
            <div class="item-restaurant">{{ item.restaurant_name }}</div>
            <div class="item-actions">
              <div class="quantity-control">
                <el-button
                  type="primary"
                  size="small"
                  circle
                  :icon="Minus"
                  @click="decreaseQuantity(item)"
                  :disabled="item.quantity <= 1"
                />
                <span class="quantity">{{ item.quantity }}</span>
                <el-button
                  type="primary"
                  size="small"
                  circle
                  :icon="Plus"
                  @click="increaseQuantity(item)"
                />
              </div>
              <el-button
                type="danger"
                size="small"
                :icon="Delete"
                circle
                @click="removeItem(item.id)"
              />
            </div>
          </div>
        </div>

        <div class="cart-summary">
          <div class="total-price">
            <span>总计:</span>
            <span class="price">¥{{ totalPrice.toFixed(2) }}</span>
          </div>
          <el-button type="success" @click="checkout">下单</el-button>
        </div>
      </div>

      <el-empty v-else description="购物车为空" />
    </template>

    <!-- 下单成功对话框 -->
    <el-dialog
      v-model="checkoutSuccessVisible"
      title="下单成功"
      width="30%"
      center
    >
      <div class="checkout-success">
        <el-icon class="success-icon" :size="64" color="#67C23A"><CircleCheckFilled /></el-icon>
        <p>下单成功！</p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="checkoutSuccessVisible = false">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Minus, Delete, CircleCheckFilled } from '@element-plus/icons-vue';
import { getUserCart, updateCartItem, removeFromCart, clearCart as clearCartApi, checkout as checkoutApi } from '@/api/cart';
import defaultFoodImage from '@/assets/forbidden_city.jpg';

const props = defineProps({
  userId: {
    type: [String, Number],
    required: true
  }
});

const loading = ref(false);
const cartItems = ref([]);
const checkoutSuccessVisible = ref(false);

// 计算总价
const totalPrice = computed(() => {
  return cartItems.value.reduce((total, item) => {
    return total + (item.dish_price * item.quantity);
  }, 0);
});

// 获取购物车数据
const fetchCartData = async () => {
  loading.value = true;
  try {
    const response = await getUserCart(props.userId);
    if (response && response.code === 0 && response.data) {
      cartItems.value = response.data.items || [];
    } else {
      ElMessage.error('获取购物车数据失败');
    }
  } catch (error) {
    console.error('获取购物车数据出错:', error);
    ElMessage.error('获取购物车数据出错');
  } finally {
    loading.value = false;
  }
};

// 增加数量
const increaseQuantity = async (item) => {
  try {
    const response = await updateCartItem(item.id, item.quantity + 1);
    if (response && response.code === 0) {
      item.quantity += 1;
      ElMessage.success('数量已更新');
    }
  } catch (error) {
    console.error('更新数量出错:', error);
    ElMessage.error('更新数量失败');
  }
};

// 减少数量
const decreaseQuantity = async (item) => {
  if (item.quantity <= 1) return;

  try {
    const response = await updateCartItem(item.id, item.quantity - 1);
    if (response && response.code === 0) {
      item.quantity -= 1;
      ElMessage.success('数量已更新');
    }
  } catch (error) {
    console.error('更新数量出错:', error);
    ElMessage.error('更新数量失败');
  }
};

// 移除商品
const removeItem = async (itemId) => {
  try {
    await ElMessageBox.confirm('确定要从购物车中移除该商品吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    const response = await removeFromCart(itemId);
    if (response && response.code === 0) {
      cartItems.value = cartItems.value.filter(item => item.id !== itemId);
      ElMessage.success('商品已从购物车中移除');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('移除商品出错:', error);
      ElMessage.error('移除商品失败');
    }
  }
};

// 清空购物车
const clearCart = async () => {
  try {
    await ElMessageBox.confirm('确定要清空购物车吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    const response = await clearCartApi(props.userId);
    if (response && response.code === 0) {
      cartItems.value = [];
      ElMessage.success('购物车已清空');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空购物车出错:', error);
      ElMessage.error('清空购物车失败');
    }
  }
};

// 下单
const checkout = async () => {
  try {
    const response = await checkoutApi({ user_id: props.userId });
    if (response && response.code === 0) {
      checkoutSuccessVisible.value = true;
      cartItems.value = [];
    } else {
      ElMessage.error(response?.message || '下单失败');
    }
  } catch (error) {
    console.error('下单出错:', error);
    ElMessage.error('下单失败，请稍后再试');
  }
};

onMounted(() => {
  fetchCartData();
});
</script>

<style scoped>
.shopping-cart-container {
  padding: 20px;
}

.loading-container {
  padding: 20px;
}

.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.cart-header h3 {
  margin: 0;
  font-size: 1.5rem;
  color: #333;
}

.cart-items {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.cart-item {
  display: flex;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.cart-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.item-image {
  width: 120px;
  height: 120px;
  overflow: hidden;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-info {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.item-name-price {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 5px;
}

.item-name-price h4 {
  margin: 0;
  font-size: 1.1rem;
  color: #333;
}

.item-price {
  font-weight: bold;
  color: #e74c3c;
  font-size: 1.1rem;
}

.item-restaurant {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 10px;
}

.item-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.quantity-control {
  display: flex;
  align-items: center;
  gap: 10px;
}

.quantity {
  font-size: 1rem;
  font-weight: bold;
  min-width: 30px;
  text-align: center;
}

.cart-summary {
  margin-top: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.total-price {
  font-size: 1.2rem;
}

.price {
  font-weight: bold;
  color: #e74c3c;
  margin-left: 10px;
}

.checkout-success {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.checkout-success p {
  font-size: 1.2rem;
  margin: 0;
}
</style>
