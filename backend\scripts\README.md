# 后端脚本工具集

本文档包含后端脚本工具的使用说明，包括数据库迁移工具和地点图片更新工具。

## 前提条件

1. 确保您已经安装了Python 3.10或更高版本
2. 确保您已经安装了MySQL数据库
3. 确保Java版本的数据库已经创建并包含数据
4. 确保您已经安装了所有必要的Python依赖项（可以通过运行`pip install -r requirements.txt`来安装）

## 配置

在运行迁移脚本之前，您需要配置数据库连接信息。您可以通过创建一个`.env`文件或设置环境变量来实现。

### 创建`.env`文件

在`backend`目录下创建一个`.env`文件，包含以下内容：

```
# Java数据库配置
JAVA_DB_HOST=localhost
JAVA_DB_USER=root
JAVA_DB_PASSWORD=123456
JAVA_DB_NAME=study_tour_system

# Flask数据库配置
FLASK_DB_HOST=localhost
FLASK_DB_USER=root
FLASK_DB_PASSWORD=123456
FLASK_DB_NAME=travel_system
```

请根据您的实际情况修改这些值。

## 运行迁移脚本

1. 确保您的虚拟环境已经激活：
   ```
   # Windows
   venv\Scripts\activate

   # macOS/Linux
   source venv/bin/activate
   ```

2. 运行迁移脚本：
   ```
   cd backend
   python scripts/db_migration.py
   ```

3. 脚本将执行以下操作：
   - 创建Flask数据库（如果不存在）
   - 创建所有必要的表
   - 从Java数据库迁移locations数据
   - 从Java数据库迁移vertices数据
   - 从Java数据库迁移edges数据
   - 从Java数据库迁移users数据

## 注意事项

1. 迁移脚本会清空目标数据库中的现有数据。如果您已经在Flask数据库中有重要数据，请先备份。

2. 脚本会自动为Java数据库中的用户生成电子邮件地址和密码哈希。用户的电子邮件地址将是`<EMAIL>`，密码将保持不变。

3. 如果您遇到任何问题，请检查错误消息并确保您的数据库配置正确。

4. 如果您需要只迁移特定的表，可以修改`main`函数中的调用顺序或注释掉不需要的迁移函数。

## 手动迁移

如果自动迁移脚本不能满足您的需求，您也可以手动迁移数据：

1. 从Java数据库导出数据：
   ```
   mysqldump -u root -p study_tour_system > java_data.sql
   ```

2. 修改SQL文件以适应Flask数据库的表结构

3. 将修改后的SQL导入Flask数据库：
   ```
   mysql -u root -p travel_system < modified_data.sql
   ```

## 故障排除

如果您遇到以下问题，请尝试以下解决方案：

1. **连接错误**：确保MySQL服务正在运行，并且您提供的用户名和密码正确。

2. **表不存在**：确保您已经运行了Flask应用程序至少一次，以创建所有必要的表。

3. **数据类型错误**：检查Java和Flask数据库之间的数据类型差异，并在必要时修改迁移脚本。

# 地点图片更新工具

这个工具用于将`uploads/locations`文件夹中的景点图片替换为真实的景点图片。

## 功能特点

1. **多种图片来源**：
   - Unsplash API - 提供高质量的免费图片
   - Pexels API - 提供高质量的免费图片
   - DeepSeek API - 使用AI生成图片
   - 备用方案 - 如果所有API都失败，创建简单的带有地点名称的图片

2. **智能搜索**：
   - 根据地点名称和关键词构建搜索查询
   - 根据地点类型（教育类或景点类）优化搜索

3. **错误处理**：
   - 验证下载的图片是否有效
   - 提供备用方案，确保每个地点都有图片

## 使用方法

### 1. 配置API密钥

在`.env`文件中配置以下API密钥：

```
UNSPLASH_API_KEY=your_unsplash_api_key
PEXELS_API_KEY=your_pexels_api_key
DEEPSEEK_API_KEY=your_deepseek_api_key
```

您可以从以下网站获取API密钥：
- Unsplash API: https://unsplash.com/developers
- Pexels API: https://www.pexels.com/api/
- DeepSeek API: https://platform.deepseek.com/

### 2. 测试API

运行测试脚本，检查API是否正常工作：

```bash
cd backend
python scripts/test_image_api.py
```

### 3. 更新地点图片

运行更新脚本，为所有地点获取图片：

```bash
cd backend
python scripts/update_location_images.py
```

## 工作原理

1. 从SQL文件中提取所有地点信息（ID、名称、类型、关键词）
2. 对于每个地点：
   - 首先尝试使用Unsplash API搜索图片
   - 如果失败，尝试使用Pexels API搜索图片
   - 如果仍然失败，尝试使用DeepSeek API生成图片
   - 如果所有API都失败，创建一个简单的备用图片
3. 将图片保存为`{location_id}_{name}.jpg`格式
4. 所有图片都保存在`backend/uploads/locations`目录中

## 注意事项

1. **API限制**：
   - Unsplash API: 每小时50次请求
   - Pexels API: 每小时200次请求
   - DeepSeek API: 根据您的订阅计划而定

2. **图片质量**：
   - Unsplash和Pexels提供的是真实照片，质量通常较高
   - DeepSeek生成的是AI图片，质量可能不稳定
   - 备用图片是简单的带有地点名称的图片

3. **版权问题**：
   - Unsplash和Pexels的图片可以免费使用，但建议查看其使用条款
   - DeepSeek生成的图片版权归您所有
   - 如果您的项目是商业用途，请确保遵守相关的使用条款

## 故障排除

1. **API密钥问题**：
   - 确保您的API密钥正确无误
   - 使用`test_image_api.py`脚本测试API是否正常工作

2. **图片下载失败**：
   - 检查网络连接
   - 检查API限制是否已达到
   - 尝试使用备用API

3. **图片质量问题**：
   - 如果图片质量不满意，可以尝试修改搜索查询
   - 对于DeepSeek API，可以尝试修改提示词以获得更好的结果
