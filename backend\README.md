# 个性化旅游系统后端

这是个性化旅游系统的Flask后端，支持路径规划、文章分享、地点推荐等功能。

## 功能特点

### 路径规划
- 单目的地路径规划
- 多目的地路径规划（使用Held-Karp和模拟退火算法）
- 多种规划策略（最短距离、最短时间、可骑行优先）
- 拥挤度感知路径规划

### 地点管理
- 地点浏览和搜索
- 个性化地点推荐
- 地点浏览计数

### 文章系统
- 文章创建、编辑和删除
- 文章内容压缩（使用Huffman编码）
- 文章评分和推荐
- 文章内容搜索（使用Boyer-Moore算法）

### 用户系统
- 用户注册和登录
- 个人中心

## 技术栈

- Flask：Web框架
- SQLAlchemy：ORM
- PyMySQL：MySQL连接器
- Flask-Migrate：数据库迁移
- Flask-CORS：跨域资源共享

## 安装和运行


1. 配置环境
   - 安装 Python 3.10 及以上版本。
   - 使用命令 python -m venv venv 创建虚拟环境。
   - 激活虚拟环境：
     - Windows：venv\Scripts\activate
     - macOS/Linux：source venv/bin/activate

2. 安装依赖
   - 执行命令 pip install -r requirements.txt 安装项目依赖。(需要在虚拟环境下执行)

3. 配置数据库
   
   - 推荐方法：
     在虚拟环境下运行命令
     python .\scripts\recreate_database.py
     但是该方法需要修改配置文件，将 DATABASE_URL 修改为你的数据库连接字符串，并修改密码。


   - 方法一：使用SQL文件直接导入
     ```
     # 创建数据库
     mysql -u root -p -e "CREATE DATABASE travel_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

     # 导入SQL文件
     mysql -u root -p travel_system < backend/database/study_tour_system.sql
     ```

   - 方法二：使用Flask迁移工具
     ```
     # 创建数据库
     mysql -u root -p -e "CREATE DATABASE travel_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

     # 初始化迁移
     flask db init

     # 创建迁移脚本
     flask db migrate -m "Initial migration"

     # 应用迁移
     flask db upgrade
     ```

4. 创建`.env`文件，添加以下内容：
   ```
   DATABASE_URL=mysql+pymysql://username:password@localhost/travel_system
   SECRET_KEY=your-secret-key
   ```

   注意：将username和password替换为你的MySQL用户名和密码。

5. 创建上传目录（如果不存在）：
   ```
   mkdir -p backend/uploads/locations
   ```

6. 运行应用
   ```
   flask run
   ```

## 常见问题解决

### 解决数据库迁移问题

如果遇到数据库迁移问题，可以尝试以下步骤：

1. 首先确认模型中已经添加了新的字段
2. 将数据库迁移状态标记为最新版本：
   ```
   flask db stamp head
   ```
3. 生成新的迁移脚本：
   ```
   flask db migrate -m "Add new fields"
   ```
4. 应用迁移：
   ```
   flask db upgrade
   ```

### 解决"No module named 'config'"错误

如果在运行`flask db init`或其他Flask命令时遇到以下错误：

```
Error: While importing 'backend.wsgi', an ImportError was raised:

Traceback (most recent call last):
  File "...\flask\cli.py", line 218, in locate_app
    __import__(module_name)
  File "...\backend\__init__.py", line 1, in <module>
    from .app import create_app
  File "...\backend\app.py", line 3, in <module>
    from config import Config
ModuleNotFoundError: No module named 'config'
```

请尝试以下解决方案：

1. 确保您在正确的目录中运行命令：
   ```
   cd backend
   flask db init
   ```

2. 如果仍然有问题，请设置FLASK_APP环境变量：
   ```
   # Windows
   set FLASK_APP=app.py

   # Linux/macOS
   export FLASK_APP=app.py
   ```

3. 如果导入路径仍有问题，可以尝试修改app.py中的导入语句：
   ```python
   # 将
   from config import Config

   # 修改为
   from .config import Config  # 使用相对导入
   # 或
   import os
   import sys
   sys.path.append(os.path.dirname(os.path.abspath(__file__)))
   from config import Config
   ```

