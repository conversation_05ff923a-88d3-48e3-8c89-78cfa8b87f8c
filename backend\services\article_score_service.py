"""
Article score service
Aligned with Java version's ArticleScoreServiceImpl
"""
from typing import Dict, Any, List
from models.article import Article, ArticleScore
from utils.database import db

class ArticleScoreService:
    """Article score service class"""

    def __init__(self):
        """Initialize the service"""
        pass

    def get_average_score(self, article_id: int) -> float:
        """
        Get average score for an article

        Args:
            article_id: Article ID

        Returns:
            Average score
        """
        try:
            # Get all scores for this article
            scores = ArticleScore.query.filter_by(article_id=article_id).all()

            if not scores:
                return 0.0

            # Calculate average
            total = sum(score.score for score in scores)
            return total / len(scores)
        except Exception as e:
            print(f"Error getting average score: {e}")
            return 0.0

    def get_all_scores(self, article_id: int) -> List[Dict[str, Any]]:
        """
        Get all scores for an article

        Args:
            article_id: Article ID

        Returns:
            List of scores with user information
        """
        try:
            # Check if article exists
            article = Article.query.get(article_id)
            if not article:
                print(f"Article with ID {article_id} not found")
                return None

            # Get all scores for this article
            scores = ArticleScore.query.filter_by(article_id=article_id).all()

            if not scores:
                print(f"No scores found for article with ID {article_id}")
                return []

            # Format scores with user information
            result = []
            for score in scores:
                # Get user information
                from models.user import User
                user = User.query.get(score.user_id)
                if user:
                    # 构建基本结果
                    score_data = {
                        'user_id': user.user_id,
                        'username': user.username,
                        'score': score.score
                    }

                    # 如果存在created_at字段，添加到结果中
                    if hasattr(score, 'created_at') and score.created_at:
                        score_data['created_at'] = score.created_at.isoformat()
                    else:
                        score_data['created_at'] = None

                    result.append(score_data)

            return result
        except Exception as e:
            print(f"Error getting all scores: {e}")
            import traceback
            traceback.print_exc()
            return None

    def set_score(self, user_id: int, article_id: int, score: float) -> bool:
        """
        Set score for an article

        Args:
            user_id: User ID
            article_id: Article ID
            score: Score value

        Returns:
            True if successful, False otherwise
        """
        try:
            # Check if article exists
            article = Article.query.get(article_id)
            if not article:
                return False

            # Check if user has already scored this article
            existing_score = ArticleScore.query.filter_by(
                user_id=user_id, article_id=article_id).first()

            if existing_score:
                # Update existing score
                existing_score.score = score
            else:
                # Create new score
                new_score = ArticleScore(
                    user_id=user_id,
                    article_id=article_id,
                    score=score
                )
                db.session.add(new_score)

            # Update article evaluation
            article.evaluation = self.get_average_score(article_id)

            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error setting score: {e}")
            return False
