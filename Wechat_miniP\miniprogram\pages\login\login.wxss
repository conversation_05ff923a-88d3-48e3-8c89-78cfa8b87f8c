/* login.wxss */
.page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 32rpx;
  box-sizing: border-box;
}

.container {
  width: 100%;
  max-width: 600rpx;
}

/* 头部 */
.header {
  text-align: center;
  margin-bottom: 80rpx;
}

.logo {
  font-size: 120rpx;
  margin-bottom: 24rpx;
  animation: bounce 2s infinite;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 300;
}

/* 登录表单 */
.login-form {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 48rpx 40rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.2);
  margin-bottom: 40rpx;
}

.form-item {
  margin-bottom: 40rpx;
}

.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.label-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.label-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.form-input {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #667eea;
  background: white;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.2);
}

/* 登录操作 */
.login-actions {
  margin-top: 48rpx;
}

.login-btn {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  border-radius: 48rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

.login-btn[disabled] {
  opacity: 0.6;
  transform: none !important;
}

.action-links {
  display: flex;
  justify-content: space-between;
  margin-top: 32rpx;
}

.link-text {
  font-size: 26rpx;
  color: #667eea;
  font-weight: 500;
  transition: color 0.3s ease;
}

.link-text:active {
  color: #5a67d8;
}

/* 快速登录 */
.quick-login {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 32rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.quick-title {
  text-align: center;
  font-size: 28rpx;
  color: white;
  margin-bottom: 24rpx;
  font-weight: 500;
}

.quick-buttons {
  display: flex;
  gap: 20rpx;
}

.quick-btn {
  flex: 1;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  color: white;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  transition: all 0.3s ease;
}

.quick-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}

.quick-icon {
  font-size: 28rpx;
}

.quick-text {
  font-weight: 500;
}

/* 动画 */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10rpx);
  }
  60% {
    transform: translateY(-5rpx);
  }
}
