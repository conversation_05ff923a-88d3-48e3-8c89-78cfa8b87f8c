# 鸿雁智游个性化旅游系统 - 运行指南

## 系统启动顺序

为确保系统正常运行，请按以下顺序启动各个组件：
1. 数据库服务 (MySQL)
2. 后端服务 (Flask)
3. 代理服务器 (Node.js)
4. Web前端 (Vue.js)
5. 微信小程序 (可选)

## 详细启动步骤

### 1. 启动数据库服务

#### Windows系统
```bash
# 启动MySQL服务
net start mysql

# 或通过服务管理器启动
services.msc -> 找到MySQL -> 启动
```

#### macOS系统
```bash
# 使用Homebrew安装的MySQL
brew services start mysql

# 或使用系统偏好设置中的MySQL面板
```

#### Linux系统
```bash
# Ubuntu/Debian
sudo systemctl start mysql

# CentOS/RHEL
sudo systemctl start mysqld
```

#### 验证数据库连接
```bash
# 进入后端目录
cd backend

# 激活虚拟环境
source venv/bin/activate  # macOS/Linux
# 或
venv\Scripts\activate     # Windows

# 测试数据库连接
python test_db_connection.py
```

### 2. 启动后端服务

#### 2.1 激活Python虚拟环境
```bash
cd backend

# Windows
venv\Scripts\activate

# macOS/Linux
source venv/bin/activate
```

#### 2.2 启动Flask应用
```bash
# 方式1: 直接运行
python app.py

# 方式2: 使用Flask命令
export FLASK_APP=app.py  # Linux/macOS
set FLASK_APP=app.py     # Windows
flask run --host=0.0.0.0 --port=5000

# 方式3: 使用gunicorn (生产环境推荐)
gunicorn -w 4 -b 0.0.0.0:5000 wsgi:app
```

#### 2.3 验证后端服务
打开浏览器访问：
- `http://localhost:5000/api/health` - 健康检查
- `http://localhost:5000/api/locations` - 获取景点列表

### 3. 启动代理服务器

#### 3.1 配置IP地址
编辑 `proxy-server.js`，确保IP地址正确：
```javascript
target: 'http://你的实际IP:5000'
```

#### 3.2 启动代理服务
```bash
# 在项目根目录
npm start

# 或使用开发模式
npm run dev

# 或直接运行
node proxy-server.js
```

#### 3.3 验证代理服务
访问：
- `http://localhost:3000/health` - 代理服务健康检查
- `http://localhost:3000/api/locations` - 通过代理访问后端API

### 4. 启动Web前端

#### 4.1 进入前端目录
```bash
cd frontend_logged/travel_system_logged
```

#### 4.2 安装依赖（首次运行）
```bash
npm install
```

#### 4.3 启动开发服务器
```bash
# 开发模式
npm run serve

# 构建生产版本
npm run build
```

#### 4.4 访问Web应用
- 开发环境：`http://localhost:8080`
- 生产环境：部署到Web服务器

### 5. 启动微信小程序

#### 5.1 打开微信开发者工具
1. 启动微信开发者工具
2. 选择"导入项目"
3. 选择 `Wechat_miniP` 目录
4. 填写项目信息

#### 5.2 配置小程序
1. 确保 `project.config.json` 中的AppID正确
2. 在小程序管理后台配置服务器域名
3. 添加代理服务器地址到合法域名列表

#### 5.3 编译和预览
1. 点击"编译"按钮
2. 使用"预览"功能在手机上测试
3. 或使用模拟器进行调试

## 系统功能验证

### 1. 用户注册登录
1. 访问Web前端或小程序
2. 点击"注册"创建新用户
3. 使用注册的账号登录系统

### 2. 景点推荐功能
1. 登录后进入首页
2. 查看推荐景点列表
3. 测试按热度、评分排序
4. 验证个性化推荐算法

### 3. 路径规划功能
1. 选择起点和终点
2. 测试最短距离策略
3. 测试最短时间策略
4. 验证多点路径规划

### 4. 场所查询功能
1. 搜索特定景点
2. 查看附近设施
3. 测试距离排序功能

### 5. 旅游日记功能
1. 创建新的旅游日记
2. 上传图片和视频
3. 测试AIGC动画生成
4. 查看和评分其他用户日记

## 性能监控

### 1. 后端性能监控
```bash
# 查看Flask应用日志
tail -f backend/logs/app.log

# 监控系统资源
top
htop
```

### 2. 数据库性能监控
```sql
-- 查看当前连接数
SHOW STATUS LIKE 'Threads_connected';

-- 查看慢查询
SHOW VARIABLES LIKE 'slow_query_log';

-- 查看数据库大小
SELECT 
    table_schema AS 'Database',
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'study_tour_system';
```

### 3. 前端性能监控
- 使用浏览器开发者工具
- 监控网络请求响应时间
- 检查控制台错误信息

## 常见问题排查

### 1. 后端启动失败
```bash
# 检查端口占用
netstat -ano | findstr :5000

# 检查Python环境
python --version
pip list

# 查看详细错误信息
python app.py --debug
```

### 2. 数据库连接失败
```bash
# 测试数据库连接
python backend/test_db_connection.py

# 检查MySQL服务状态
systemctl status mysql  # Linux
net start mysql         # Windows
```

### 3. 前端无法访问后端
1. 检查后端服务是否正常运行
2. 验证API地址配置是否正确
3. 检查CORS配置
4. 查看浏览器控制台错误

### 4. 小程序无法连接服务器
1. 检查代理服务器是否运行
2. 验证小程序合法域名配置
3. 确认网络连接正常
4. 查看小程序调试控制台

### 5. AIGC功能异常
1. 检查API密钥配置
2. 验证账户余额充足
3. 查看API调用日志
4. 确认网络连接稳定

## 日志查看

### 1. 后端日志
```bash
# 实时查看应用日志
tail -f backend/logs/app.log

# 查看错误日志
grep "ERROR" backend/logs/app.log

# 查看最近的日志
tail -n 100 backend/logs/app.log
```

### 2. 代理服务器日志
代理服务器的日志直接输出到控制台，可以查看请求转发情况。

### 3. 数据库日志
```bash
# MySQL错误日志位置
# Windows: C:\ProgramData\MySQL\MySQL Server 8.0\Data\*.err
# Linux: /var/log/mysql/error.log
# macOS: /usr/local/var/mysql/*.err
```

## 部署建议

### 1. 开发环境
- 使用开发模式启动所有服务
- 启用调试模式和详细日志
- 使用热重载功能

### 2. 测试环境
- 使用生产模式配置
- 启用性能监控
- 进行压力测试

### 3. 生产环境
- 使用Nginx作为反向代理
- 配置HTTPS证书
- 启用日志轮转
- 设置自动备份

## 系统维护

### 1. 定期备份
```bash
# 数据库备份
mysqldump -u root -p study_tour_system > backup_$(date +%Y%m%d).sql

# 文件备份
tar -czf uploads_backup_$(date +%Y%m%d).tar.gz backend/uploads/
```

### 2. 日志清理
```bash
# 清理旧日志文件
find backend/logs/ -name "*.log.*" -mtime +30 -delete
```

### 3. 依赖更新
```bash
# 更新Python依赖
pip list --outdated
pip install --upgrade package_name

# 更新Node.js依赖
npm outdated
npm update
```

## 技术支持

如遇到问题，请按以下步骤排查：
1. 查看相关日志文件
2. 检查配置文件设置
3. 验证网络连接
4. 重启相关服务
5. 查阅项目文档

更多详细信息请参考项目目录下的各个README文件和技术文档。
