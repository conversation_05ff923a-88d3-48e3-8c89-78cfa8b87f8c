# 鸿雁智游 - 快速启动指南

## 一键启动脚本

为了简化启动流程，我们提供了快速启动脚本。

### Windows 启动脚本

创建 `start_system.bat` 文件：

```batch
@echo off
echo ========================================
echo 鸿雁智游个性化旅游系统启动脚本
echo ========================================

echo.
echo [1/4] 检查MySQL服务...
net start mysql >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ MySQL服务已启动
) else (
    echo ✗ MySQL服务启动失败，请手动启动
    pause
    exit /b 1
)

echo.
echo [2/4] 启动后端服务...
cd backend
call venv\Scripts\activate
start "后端服务" cmd /k "python app.py"
cd ..

echo.
echo [3/4] 启动代理服务器...
start "代理服务器" cmd /k "node proxy-server.js"

echo.
echo [4/4] 启动前端服务...
cd frontend_logged\travel_system_logged
start "前端服务" cmd /k "npm run serve"
cd ..\..

echo.
echo ========================================
echo 系统启动完成！
echo ========================================
echo 后端服务: http://localhost:5000
echo 代理服务: http://localhost:3000  
echo 前端服务: http://localhost:8080
echo ========================================
echo.
echo 按任意键退出...
pause >nul
```

### macOS/Linux 启动脚本

创建 `start_system.sh` 文件：

```bash
#!/bin/bash

echo "========================================"
echo "鸿雁智游个性化旅游系统启动脚本"
echo "========================================"

# 检查MySQL服务
echo ""
echo "[1/4] 检查MySQL服务..."
if pgrep mysql > /dev/null; then
    echo "✓ MySQL服务已运行"
else
    echo "启动MySQL服务..."
    # macOS
    if [[ "$OSTYPE" == "darwin"* ]]; then
        brew services start mysql
    # Linux
    else
        sudo systemctl start mysql
    fi
    
    if [ $? -eq 0 ]; then
        echo "✓ MySQL服务启动成功"
    else
        echo "✗ MySQL服务启动失败"
        exit 1
    fi
fi

# 启动后端服务
echo ""
echo "[2/4] 启动后端服务..."
cd backend
source venv/bin/activate
python app.py &
BACKEND_PID=$!
echo "✓ 后端服务已启动 (PID: $BACKEND_PID)"
cd ..

# 启动代理服务器
echo ""
echo "[3/4] 启动代理服务器..."
node proxy-server.js &
PROXY_PID=$!
echo "✓ 代理服务器已启动 (PID: $PROXY_PID)"

# 启动前端服务
echo ""
echo "[4/4] 启动前端服务..."
cd frontend_logged/travel_system_logged
npm run serve &
FRONTEND_PID=$!
echo "✓ 前端服务已启动 (PID: $FRONTEND_PID)"
cd ../..

echo ""
echo "========================================"
echo "系统启动完成！"
echo "========================================"
echo "后端服务: http://localhost:5000"
echo "代理服务: http://localhost:3000"
echo "前端服务: http://localhost:8080"
echo "========================================"
echo ""
echo "进程ID记录:"
echo "后端: $BACKEND_PID"
echo "代理: $PROXY_PID" 
echo "前端: $FRONTEND_PID"
echo ""
echo "要停止所有服务，请运行: ./stop_system.sh"
echo ""

# 保存PID到文件
echo $BACKEND_PID > .backend.pid
echo $PROXY_PID > .proxy.pid
echo $FRONTEND_PID > .frontend.pid
```

### 停止脚本 (macOS/Linux)

创建 `stop_system.sh` 文件：

```bash
#!/bin/bash

echo "========================================"
echo "停止鸿雁智游系统服务"
echo "========================================"

# 读取PID文件并停止服务
if [ -f .backend.pid ]; then
    BACKEND_PID=$(cat .backend.pid)
    kill $BACKEND_PID 2>/dev/null
    echo "✓ 后端服务已停止"
    rm .backend.pid
fi

if [ -f .proxy.pid ]; then
    PROXY_PID=$(cat .proxy.pid)
    kill $PROXY_PID 2>/dev/null
    echo "✓ 代理服务已停止"
    rm .proxy.pid
fi

if [ -f .frontend.pid ]; then
    FRONTEND_PID=$(cat .frontend.pid)
    kill $FRONTEND_PID 2>/dev/null
    echo "✓ 前端服务已停止"
    rm .frontend.pid
fi

echo "========================================"
echo "所有服务已停止"
echo "========================================"
```

## 快速验证步骤

### 1. 环境检查
```bash
# 检查Python版本
python --version

# 检查Node.js版本  
node --version

# 检查MySQL服务
mysql --version
```

### 2. 依赖安装检查
```bash
# 检查Python依赖
cd backend
pip list | grep Flask

# 检查Node.js依赖
cd ..
npm list express

# 检查前端依赖
cd frontend_logged/travel_system_logged
npm list vue
```

### 3. 配置文件检查
- 确认 `backend/config.py` 中数据库连接信息正确
- 确认 `proxy-server.js` 中IP地址配置正确
- 确认 `.env` 文件存在且API密钥已配置

### 4. 服务验证
启动系统后，依次访问以下地址验证服务状态：

1. **后端服务**: `http://localhost:5000/api/health`
   - 应返回: `{"status": "ok", "message": "服务运行正常"}`

2. **代理服务**: `http://localhost:3000/health`
   - 应返回: `{"status": "ok", "message": "代理服务器运行正常"}`

3. **前端服务**: `http://localhost:8080`
   - 应显示系统登录页面

## 常见启动问题

### 1. 端口占用
```bash
# 查看端口占用情况
netstat -ano | findstr :5000  # Windows
lsof -i :5000                 # macOS/Linux

# 杀死占用进程
taskkill /PID 进程ID /F       # Windows  
kill -9 进程ID                # macOS/Linux
```

### 2. 虚拟环境问题
```bash
# 重新创建虚拟环境
cd backend
rm -rf venv
python -m venv venv
source venv/bin/activate  # macOS/Linux
venv\Scripts\activate     # Windows
pip install -r requirements.txt
```

### 3. 数据库连接问题
```bash
# 测试数据库连接
cd backend
python test_db_connection.py

# 如果连接失败，检查配置
cat config.py | grep SQLALCHEMY_DATABASE_URI
```

### 4. 前端编译问题
```bash
# 清除缓存重新安装
cd frontend_logged/travel_system_logged
rm -rf node_modules package-lock.json
npm install
```

## 开发模式 vs 生产模式

### 开发模式特点
- 启用热重载
- 详细错误信息
- 调试工具可用
- 性能较慢

### 生产模式配置
```bash
# 后端生产模式
export FLASK_ENV=production
gunicorn -w 4 -b 0.0.0.0:5000 wsgi:app

# 前端生产构建
npm run build

# 使用Nginx部署静态文件
```

## 性能优化建议

### 1. 数据库优化
- 定期执行 `OPTIMIZE TABLE` 命令
- 监控慢查询日志
- 适当增加缓存配置

### 2. 后端优化
- 启用Redis缓存
- 使用连接池
- 优化算法复杂度

### 3. 前端优化
- 启用Gzip压缩
- 使用CDN加速
- 图片懒加载

## 监控和日志

### 实时监控命令
```bash
# 监控系统资源
top
htop

# 监控网络连接
netstat -an | grep :5000

# 实时查看日志
tail -f backend/logs/app.log
```

### 日志分析
```bash
# 统计错误日志
grep "ERROR" backend/logs/app.log | wc -l

# 查看最近的API调用
grep "API" backend/logs/app.log | tail -20

# 分析响应时间
grep "响应时间" backend/logs/app.log | awk '{print $NF}' | sort -n
```

## 备份和恢复

### 快速备份
```bash
# 数据库备份
mysqldump -u root -p study_tour_system > backup_$(date +%Y%m%d_%H%M%S).sql

# 上传文件备份
tar -czf uploads_backup_$(date +%Y%m%d).tar.gz backend/uploads/
```

### 快速恢复
```bash
# 恢复数据库
mysql -u root -p study_tour_system < backup_file.sql

# 恢复上传文件
tar -xzf uploads_backup.tar.gz -C backend/
```

使用这些脚本和指南，您可以快速启动和管理鸿雁智游系统！
