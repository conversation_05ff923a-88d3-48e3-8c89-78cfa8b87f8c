# 个性化旅游推荐系统文档

本目录包含个性化旅游推荐系统的各个模块文档。

## 模块文档

- [用户认证模块](用户认证模块.md)
- [用户管理模块](用户管理模块.md)
- [地点模块](地点模块.md)
- [路径规划模块](路径规划模块.md)
- [文章模块](文章模块.md)
- [文章评分模块](文章评分模块.md)
- [文章收藏模块](文章收藏模块.md)
- [推荐模块](推荐模块.md)
- [高级推荐模块](高级推荐模块.md) - 使用高效查找和排序算法实现
- [AI 生成模块](AI%20生成模块.md)
- [文件上传模块](文件上传模块.md)
- [美食推荐模块](美食推荐模块.md)
- [后端API总览](后端API总览.md) - 所有后端模块功能和API路径总览

## 算法文档

- [后端算法实现](backend_algorithms.pdf)

## 系统架构

系统采用前后端分离架构：

- 前端：Vue.js
- 后端：Flask (Python)
- 数据库：MySQL

## 主要功能

1. 用户注册、登录和个人信息管理
2. 地点浏览、搜索和详情查看
3. 旅行路线规划和优化
4. 旅行日记发布和社区互动
5. 个性化推荐（基于内容、协同过滤和混合推荐）
6. 高效查找和排序算法实现的推荐系统
7. AI 生成内容（地点描述、旅行计划等）
8. 文件上传和管理

## 开发团队

- 组长：XXX
- 成员：XXX, XXX, XXX, XXX

## 更新日志

- 2023-12-01: 初始版本
- 2023-12-15: 添加文章模块
- 2024-01-10: 添加推荐模块
- 2024-01-25: 添加 AI 生成模块
- 2024-02-15: 添加高级推荐模块（使用高效查找和排序算法）
