<template>
  <div class="modern-container">
    <!-- 顶部导航栏 -->
    <header>
      <nav>
        <a class="nav-items active">首页</a>
        <a class="nav-items">景点推荐</a>
        <a class="nav-items">路线规划</a>
        <a class="nav-items">旅游日记</a>
        <a class="nav-items">联系我们</a>
      </nav>
    </header>

    <!-- 主视觉区域 -->
    <section class="hero-section">
      <div class="overlay"></div>
      <div class="title">
        <h3>探索中国之美</h3>
        <h1>鸿雁智游</h1>
      </div>
      <router-link to="/login" id="btn">开始体验</router-link>
      <div class="slider">
        <i class="fa-solid fa-chevron-left"></i>
        <i class="fa-solid fa-chevron-right"></i>
      </div>
    </section>
    <div class="sec chinese-style" id="about">
      <div class="chinese-art-container">
        <img :src="chineseArtImg" alt="中国风艺术" class="chinese-art-bg">
      </div>
      <h2 class="section-title chinese-title">鸿雁智游</h2>
      <div class="section-content">
        <div class="chinese-decoration left"></div>
        <div class="content-wrapper">
          <p>欢迎使用我们的个性化旅游系统，这是一个为旅行爱好者设计的全方位旅游助手。无论您是计划一次短途旅行，还是准备一次长途冒险，我们的系统都能为您提供最优质的服务。</p>
          <p>我们的系统提供了丰富的功能，包括旅游路线规划、景点推荐、旅游日记分享等。通过先进的算法，我们能够根据您的兴趣和偏好，为您推荐最适合的旅游目的地和路线。</p>
        </div>
        <div class="chinese-decoration right"></div>
      </div>
    </div>
    <div class="sec chinese-style" id="features">
      <h2 class="section-title chinese-title">核心功能</h2>
      <div class="chinese-pattern-divider"></div>
      <div class="feature-grid">
        <div class="feature-item">
          <h3>旅游路线规划</h3>
          <p>根据您的位置和目的地，规划最优旅游路线，支持多点路径规划和不同交通工具选择。</p>
        </div>
        <div class="feature-item">
          <h3>景点推荐</h3>
          <p>基于您的兴趣和历史浏览记录，推荐最适合您的景点，让您的旅行更加个性化。</p>
        </div>
        <div class="feature-item">
          <h3>旅游日记</h3>
          <p>记录您的旅行经历，分享美好瞬间，与其他旅行者交流心得。</p>
        </div>
        <div class="feature-item">
          <h3>AI 生成</h3>
          <p>利用 AI 技术，根据您的照片和描述，生成精美的旅游动画和回忆。</p>
        </div>
      </div>
    </div>
    <div class="sec chinese-style" id="contact">
      <h2 class="section-title chinese-title">联系我们</h2>
      <div class="chinese-pattern-divider"></div>
      <div class="section-content">
        <p>如果您有任何问题或建议，请随时联系我们。我们的团队将竭诚为您服务。</p>
        <div class="contact-info chinese-contact">
          <div class="contact-item">
            <div class="contact-icon email-icon"></div>
            <p>邮箱：<EMAIL></p>
          </div>
          <div class="contact-item">
            <div class="contact-icon phone-icon"></div>
            <p>电话：************</p>
          </div>
        </div>
      </div>
      <div class="chinese-footer-decoration"></div>
    </div>
  </div>
</template>

<script>
import { onMounted } from 'vue'
// 导入图片
import chineseArtImg from '@/assets/Chinese_art_1.png'
import summerPalaceImg from '@/assets/Summer_Palace2.jpg'
import forbiddenCityImg from '@/assets/forbidden_city.jpg'
import disneyImg from '@/assets/Disney_Shanghai.jpg'

export default {
  name: 'ParallaxHome',
  setup() {
    // 图片轮播功能
    let currentSlide = 0;
    const backgroundImages = [
      summerPalaceImg,
      forbiddenCityImg,
      disneyImg
    ];

    const changeBackground = () => {
      const heroSection = document.querySelector('.hero-section');
      if (heroSection) {
        heroSection.style.backgroundImage = `url(${backgroundImages[currentSlide]})`;
        currentSlide = (currentSlide + 1) % backgroundImages.length;
      }
    };

    onMounted(() => {
      // 设置初始背景图
      changeBackground();

      // 设置轮播间隔
      setInterval(changeBackground, 5000);

      // 添加左右箭头点击事件
      const leftArrow = document.querySelector('.fa-chevron-left');
      const rightArrow = document.querySelector('.fa-chevron-right');

      if (leftArrow) {
        leftArrow.addEventListener('click', () => {
          currentSlide = (currentSlide - 1 + backgroundImages.length) % backgroundImages.length;
          changeBackground();
        });
      }

      if (rightArrow) {
        rightArrow.addEventListener('click', () => {
          currentSlide = (currentSlide + 1) % backgroundImages.length;
          changeBackground();
        });
      }
    });

    return {
      chineseArtImg,
      summerPalaceImg,
      forbiddenCityImg,
      disneyImg
    }
  }
}
</script>

<style scoped>
/* 导入字体 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Inter', 'STZhongsong', 'SimSun', sans-serif;
  scroll-behavior: smooth;
}

.modern-container {
  min-height: 100vh;
  overflow-x: hidden;
}

/* 顶部导航栏样式 */
header {
  position: fixed;
  top: 0;
  z-index: 1000;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

nav {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 5%;
  background-color: rgba(255, 255, 255, 0.2);
  height: 50px;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.2);
  margin-top: 0;
}

nav a {
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 2px;
  color: white;
  width: 110px;
  text-align: center;
  padding: 15px 0;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s;
}

.active {
  background-color: rgba(232, 195, 125, 0.8);
  color: #1c0522;
}

nav a:hover {
  background-color: rgba(232, 195, 125, 0.8);
  color: #1c0522;
}

nav:hover > a:not(:hover) {
  background-color: transparent;
  color: white;
}

/* 主视觉区域样式 */
.hero-section {
  position: relative;
  width: 100%;
  height: 100vh;
  background-size: cover;
  background-position: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  transition: background-image 1s ease;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.7));
  z-index: 1;
}

.title {
  position: relative;
  z-index: 2;
  text-align: center;
  margin-bottom: 40px;
}

.title h3 {
  font-size: 1.8rem;
  font-weight: 400;
  letter-spacing: 15px;
  color: white;
  margin-bottom: 20px;
  animation: fadeInUp 1s ease-out forwards;
}

.title h1 {
  font-size: 6rem;
  font-weight: 800;
  letter-spacing: 20px;
  text-transform: uppercase;
  color: white;
  margin: 0;
  animation: fadeInUp 1.2s ease-out forwards;
  text-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  background: linear-gradient(to bottom, #ffffff, #e8c37d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

#btn {
  position: relative;
  z-index: 2;
  text-decoration: none;
  display: inline-block;
  padding: 15px 50px;
  border-radius: 50px;
  background: linear-gradient(135deg, #e8c37d, #d4a76a);
  color: #1c0522;
  font-size: 1.2em;
  font-weight: 600;
  letter-spacing: 3px;
  text-transform: uppercase;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  animation: fadeInUp 1.4s ease-out forwards;
}

#btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.5s ease;
}

#btn:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
  background: linear-gradient(135deg, #f0d08a, #e8c37d);
}

#btn:hover::before {
  left: 100%;
}

.slider {
  position: absolute;
  bottom: 50px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 50px;
  z-index: 2;
}

.slider i {
  font-size: 36px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
}

.slider i:hover {
  color: #e8c37d;
  transform: scale(1.2);
}

/* 动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.sec {
  position: relative;
  padding: 100px 5%;
  background: #fff;
  color: #333;
}

/* 中国风样式 */
.chinese-style {
  position: relative;
  background: #f9f9f9;
  overflow: hidden;
}

.chinese-style:nth-child(odd) {
  background: #fff;
}

.chinese-art-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.05;
  z-index: 1;
  overflow: hidden;
}

.chinese-art-bg {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  filter: saturate(0.8) contrast(1.2);
}

.section-title {
  font-size: 2.5rem;
  margin-bottom: 30px;
  color: #1c0522;
  text-align: center;
  position: relative;
  padding-bottom: 15px;
  font-weight: 700;
  z-index: 2;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, transparent, #e8c37d, transparent);
}

.section-content {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.content-wrapper {
  position: relative;
  z-index: 2;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(232, 195, 125, 0.3);
  text-align: center;
}

.content-wrapper p {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #555;
  margin-bottom: 20px;
}

.chinese-pattern-divider {
  height: 30px;
  width: 300px;
  margin: 0 auto 40px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='20' viewBox='0 0 100 20' fill='%23e8c37d'%3E%3Cpath d='M0,10 C30,20 70,0 100,10 L100,0 L0,0 Z'/%3E%3C/svg%3E");
  background-repeat: repeat-x;
  background-size: 100px 20px;
  opacity: 0.6;
}

.chinese-footer-decoration {
  height: 40px;
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='20' viewBox='0 0 100 20' fill='%23e8c37d'%3E%3Cpath d='M0,0 C30,10 70,10 100,0 L100,20 L0,20 Z'/%3E%3C/svg%3E");
  background-repeat: repeat-x;
  background-size: 100px 20px;
  opacity: 0.4;
}

.section-title {
  font-size: 3.5em;
  margin-bottom: 30px;
  color: #fff;
  text-align: center;
  position: relative;
  padding-bottom: 15px;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 3px;
  background: linear-gradient(90deg, transparent, #7597de, transparent);
}

.section-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.sec p {
  font-size: 1.2em;
  color: #fff;
  margin-bottom: 20px;
  line-height: 1.6;
}

/* 添加特性网格样式 */
.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin: 40px auto 0;
  max-width: 1200px;
}

.feature-item {
  background: #fff;
  padding: 40px 30px;
  border-radius: 15px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
  border: 1px solid rgba(232, 195, 125, 0.2);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  text-align: center;
}

.feature-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, #e8c37d, #d4a76a);
}

.feature-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.feature-item h3 {
  font-size: 1.5rem;
  margin-bottom: 15px;
  color: #1c0522;
  font-weight: 600;
  position: relative;
  display: inline-block;
}

.feature-item h3::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 40px;
  height: 2px;
  background: #e8c37d;
}

.feature-item p {
  color: #555;
  line-height: 1.8;
  font-size: 1rem;
}

.feature-icon {
  font-size: 3rem;
  color: #e8c37d;
  margin-bottom: 20px;
  display: inline-block;
}

.contact-info {
  margin: 30px auto 0;
  background: #fff;
  padding: 30px;
  border-radius: 15px;
  display: inline-block;
  text-align: center;
  width: auto;
  min-width: 300px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(232, 195, 125, 0.2);
}

.chinese-contact {
  position: relative;
  z-index: 2;
}

.contact-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 20px;
  position: relative;
  text-align: left;
}

.contact-item:last-child {
  margin-bottom: 0;
}

.contact-icon {
  width: 40px;
  height: 40px;
  margin-right: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #e8c37d;
  background-color: rgba(232, 195, 125, 0.1);
  border-radius: 50%;
  padding: 10px;
}

.contact-item p {
  color: #555;
  font-size: 1rem;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  nav {
    padding: 10px 2%;
    gap: 5px;
  }

  nav a {
    font-size: 14px;
    width: auto;
    padding: 10px 15px;
  }

  .hero-section {
    height: 80vh;
  }

  .title h3 {
    font-size: 1.2rem;
    letter-spacing: 8px;
  }

  .title h1 {
    font-size: 3rem;
    letter-spacing: 10px;
  }

  #btn {
    font-size: 1em;
    padding: 12px 30px;
  }

  .slider {
    padding: 0 20px;
    bottom: 30px;
  }

  .slider i {
    font-size: 24px;
  }

  .sec {
    padding: 60px 20px;
  }

  .section-title {
    font-size: 2rem;
  }

  .section-content {
    padding: 0 10px;
  }

  .content-wrapper {
    padding: 20px;
  }

  .feature-grid {
    grid-template-columns: 1fr;
    padding: 0;
    gap: 20px;
  }

  .feature-item {
    padding: 30px 20px;
  }

  .chinese-pattern-divider {
    width: 200px;
  }

  .contact-info {
    min-width: auto;
    width: 100%;
    padding: 20px;
  }

  .contact-item {
    flex-direction: row;
    margin-bottom: 15px;
  }

  .contact-icon {
    width: 30px;
    height: 30px;
    font-size: 1.2rem;
  }
}
</style>
