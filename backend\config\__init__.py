"""
配置模块
包含Flask应用和AI服务的所有配置
"""
import os
from dotenv import load_dotenv

# 从.env加载环境变量
load_dotenv()

class Config:
    # Flask应用配置
    SECRET_KEY = os.getenv('SECRET_KEY') or 'your-secret-key-here'
    # 修改为实际的数据库连接信息
    SQLALCHEMY_DATABASE_URI = os.getenv('DATABASE_URL') or 'mysql+pymysql://root:AN20050225@localhost/study_tour_system'
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # 文件上传配置
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'uploads')
    MAX_CONTENT_LENGTH = 50 * 1024 * 1024  # 50MB限制

    # DeepSeek配置
    DEEPSEEK_API_KEY = os.getenv('DEEPSEEK_API_KEY') or '***********************************'
    DEEPSEEK_MODEL = os.getenv('DEEPSEEK_MODEL') or 'deepseek-chat'
    DEEPSEEK_API_BASE = os.getenv('DEEPSEEK_API_BASE') or 'https://api.deepseek.com/v1'
    DEEPSEEK_TEMPERATURE = float(os.getenv('DEEPSEEK_TEMPERATURE') or '0.7')
    DEEPSEEK_MAX_TOKENS = int(os.getenv('DEEPSEEK_MAX_TOKENS') or '1000')

    # OpenAI配置（保留但不使用）
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', '')
    OPENAI_MODEL = os.getenv('OPENAI_MODEL', 'gpt-3.5-turbo')
    OPENAI_TEMPERATURE = float(os.getenv('OPENAI_TEMPERATURE', '0.7'))
    OPENAI_MAX_TOKENS = int(os.getenv('OPENAI_MAX_TOKENS', '1000'))

    # 百度文心一言配置（保留但不使用）
    BAIDU_API_KEY = os.getenv('BAIDU_API_KEY', '')
    BAIDU_SECRET_KEY = os.getenv('BAIDU_SECRET_KEY', '')
    BAIDU_MODEL = os.getenv('BAIDU_MODEL', 'ERNIE-Bot-4')

    # AI通用配置
    DEFAULT_PROVIDER = os.getenv('DEFAULT_PROVIDER') or 'deepseek'
    ENABLE_CACHE = os.getenv('ENABLE_AI_CACHE', 'True').lower() == 'true'
    CACHE_EXPIRATION = int(os.getenv('AI_CACHE_EXPIRATION', '3600'))

    # AI功能特定配置
    SUMMARY_MAX_LENGTH = int(os.getenv('SUMMARY_MAX_LENGTH', '200'))
    PLAN_DETAIL_LEVEL = os.getenv('PLAN_DETAIL_LEVEL', 'medium')  # 'simple', 'medium', 'detailed'

__all__ = ['Config']
