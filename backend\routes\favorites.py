from flask import Blueprint, request
from models.article import Article, ArticleFavorite
from models.user import User
from utils.database import db
from utils.response import success, error

favorites_bp = Blueprint('favorites', __name__)

@favorites_bp.route('/add', methods=['POST'])
def add_favorite():
    """添加文章到收藏"""
    data = request.get_json()

    if not data or not data.get('user_id') or not data.get('article_id'):
        return error('Missing required fields')

    user_id = data['user_id']
    article_id = data['article_id']

    # 检查用户是否存在
    user = User.query.get(user_id)
    if not user:
        return error('User not found')

    # 检查文章是否存在
    article = Article.query.get(article_id)
    if not article:
        return error('Article not found')

    # 检查是否已经收藏
    existing_favorite = ArticleFavorite.query.filter_by(
        user_id=user_id,
        article_id=article_id
    ).first()

    if existing_favorite:
        return error('Article already in favorites')

    # 添加收藏
    favorite = ArticleFavorite(user_id=user_id, article_id=article_id)
    db.session.add(favorite)
    db.session.commit()

    return success({}, 'Article added to favorites')

@favorites_bp.route('/remove', methods=['POST'])
def remove_favorite():
    """从收藏中移除文章"""
    data = request.get_json()

    if not data or not data.get('user_id') or not data.get('article_id'):
        return error('Missing required fields')

    user_id = data['user_id']
    article_id = data['article_id']

    # 检查收藏是否存在
    favorite = ArticleFavorite.query.filter_by(
        user_id=user_id,
        article_id=article_id
    ).first()

    if not favorite:
        return error('Article not in favorites')

    # 移除收藏
    db.session.delete(favorite)
    db.session.commit()

    return success({}, 'Article removed from favorites')

@favorites_bp.route('/list/<int:user_id>', methods=['GET'])
def list_favorites(user_id):
    """获取用户的收藏列表"""
    # 检查用户是否存在
    user = User.query.get(user_id)
    if not user:
        return error('User not found')

    # 获取用户的收藏
    favorites = ArticleFavorite.query.filter_by(user_id=user_id).all()

    # 获取收藏的文章详情
    articles = []
    for favorite in favorites:
        article = Article.query.get(favorite.article_id)
        if article:
            # 获取文章作者信息
            author = User.query.get(article.user_id)
            author_name = author.username if author else 'Unknown'
            author_avatar = author.avatar if author else None

            # 解压文章内容
            content = article.content
            if content:
                try:
                    # 使用Huffman编码解压
                    from services.article_service import ArticleService
                    import json
                    service = ArticleService()
                    # 将JSON字符串解析为字典
                    if article.huffman_codes:
                        huffman_codes = json.loads(article.huffman_codes)
                        content = service.decompress_text(content, huffman_codes)
                    elif isinstance(content, bytes):
                        # 尝试使用UTF-8解码
                        content = content.decode('utf-8')
                except Exception as e:
                    print(f"Error decompressing article {article.article_id}: {e}")
                    content = "内容解压失败"

            # 构建文章信息
            article_info = article.to_dict()
            article_info['author'] = author_name
            article_info['author_name'] = author_name  # 添加author_name字段以兼容前端
            article_info['author_avatar'] = author_avatar
            article_info['content'] = content
            # 确保时间包含时区信息
            if favorite.created_at:
                if favorite.created_at.tzinfo is None:
                    # 如果时间没有时区信息，添加UTC时区
                    import datetime
                    favorited_at = favorite.created_at.replace(tzinfo=datetime.timezone.utc)
                    article_info['favorited_at'] = favorited_at.isoformat()
                else:
                    # 已有时区信息
                    article_info['favorited_at'] = favorite.created_at.isoformat()
            else:
                article_info['favorited_at'] = None

            # 获取点赞、收藏和评论数量
            from models.article_like import ArticleLike
            from models.article_comment import ArticleComment

            article_info['likes_count'] = ArticleLike.query.filter_by(article_id=article.article_id).count()
            article_info['comments_count'] = ArticleComment.query.filter_by(article_id=article.article_id).count()
            article_info['favorites_count'] = ArticleFavorite.query.filter_by(article_id=article.article_id).count()

            articles.append(article_info)

    return success({'favorites': articles}, 'Favorites retrieved successfully')

@favorites_bp.route('/check', methods=['POST'])
def check_favorite():
    """检查文章是否已收藏"""
    data = request.get_json()

    if not data or not data.get('user_id') or not data.get('article_id'):
        return error('Missing required fields')

    user_id = data['user_id']
    article_id = data['article_id']

    # 检查是否已经收藏
    favorite = ArticleFavorite.query.filter_by(
        user_id=user_id,
        article_id=article_id
    ).first()

    is_favorite = favorite is not None

    return success({'is_favorite': is_favorite}, 'Favorite status checked')

@favorites_bp.route('/count/<int:article_id>', methods=['GET'])
def get_favorite_count(article_id):
    """获取文章的收藏数量"""
    # 检查文章是否存在
    article = Article.query.get(article_id)
    if not article:
        return error('Article not found')

    # 获取收藏数量
    count = ArticleFavorite.query.filter_by(article_id=article_id).count()

    return success({'count': count}, 'Favorite count retrieved successfully')
