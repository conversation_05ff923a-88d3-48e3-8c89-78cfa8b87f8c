from datetime import datetime

def calculate_hybrid_score(journal, user_id):
    """混合推荐算法实现"""
    try:
        # 确保数据安全
        views = journal.views if journal.views is not None else 0
        total_raters = journal.total_raters if journal.total_raters is not None else 0
        average_rating = journal.average_rating if journal.average_rating is not None else 0

        # 热度计算（基于时间衰减）
        hours_since_creation = (datetime.utcnow() - journal.created_at).total_seconds() / 3600
        hotness = views * (0.95 ** hours_since_creation)  # 每小时衰减5%

        # 评分计算（加权平均）
        rating_weight = min(total_raters / 100, 1.0) if total_raters > 0 else 0  # 评分人数越多权重越高
        rating = average_rating * rating_weight * 0.3

        # 个人偏好模拟（需替换为实际用户行为分析）
        user_preference = 0.1 * get_user_preference_simulation(user_id, journal)

        return hotness + rating + user_preference
    except Exception as e:
        print(f"Error calculating score: {e}")
        return 0  # 出错时返回默认分数

def get_user_preference_simulation(user_id, journal):
    """用户偏好模拟（临时方案）"""
    # 待替换为实际用户行为分析逻辑
    return 0.5  # 返回随机模拟值