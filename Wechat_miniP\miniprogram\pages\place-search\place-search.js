// place-search.js - 景点搜索页面
const API_BASE_URL = 'http://10.129.241.148:5000/api';

Page({
  data: {
    searchKeyword: '',
    searchResults: [],
    popularLocations: [],
    loading: false,
    showPopular: true,
    hasSearched: false // 是否已经搜索过
  },

  // 搜索延迟定时器
  searchTimer: null,

  onLoad() {
    console.log('景点搜索页面加载');
    this.loadPopularLocations();
  },

  onShow() {
    // 设置tabbar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 1
      });
    }
  },

  // 构建查询字符串
  buildQueryString(params) {
    const queryParts = [];
    for (const [key, value] of Object.entries(params)) {
      if (value !== undefined && value !== null && value !== '') {
        queryParts.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
      }
    }
    return queryParts.join('&');
  },

  // 加载热门景点
  async loadPopularLocations() {
    try {
      console.log('正在加载热门景点...');
      
      const queryString = this.buildQueryString({ limit: 10 });
      const url = `${API_BASE_URL}/path/popular-locations?${queryString}`;
      
      console.log('请求URL:', url);
      
      wx.request({
        url: url,
        method: 'GET',
        header: {
          'Content-Type': 'application/json'
        },
        success: (res) => {
          console.log('热门景点响应:', res);
          
          if (res.statusCode === 200) {
            const locations = Array.isArray(res.data) ? res.data : [];
            this.setData({
              popularLocations: locations,
              showPopular: true
            });
            console.log('热门景点加载成功:', locations);
          } else {
            console.error('获取热门景点失败:', res.statusCode, res.data);
          }
        },
        fail: (err) => {
          console.error('获取热门景点请求失败:', err);
        }
      });
    } catch (error) {
      console.error('加载热门景点失败:', error);
    }
  },

  // 搜索景点
  async searchLocations(keyword) {
    try {
      console.log('正在搜索景点:', keyword);
      
      const queryString = this.buildQueryString({ 
        name: keyword,
        limit: 20 
      });
      const url = `${API_BASE_URL}/path/search-locations?${queryString}`;
      
      console.log('搜索URL:', url);
      
      return new Promise((resolve) => {
        wx.request({
          url: url,
          method: 'GET',
          header: {
            'Content-Type': 'application/json'
          },
          success: (res) => {
            console.log('搜索景点响应:', res);
            
            if (res.statusCode === 200) {
              const locations = Array.isArray(res.data) ? res.data : [];
              console.log('搜索结果解析:', locations);
              resolve(locations);
            } else {
              console.error('搜索景点失败:', res.statusCode, res.data);
              resolve([]);
            }
          },
          fail: (err) => {
            console.error('搜索景点请求失败:', err);
            resolve([]);
          }
        });
      });
    } catch (error) {
      console.error('搜索景点失败:', error);
      return [];
    }
  },

  // 输入框变化
  onSearchInput(e) {
    const keyword = e.detail.value;
    this.setData({ 
      searchKeyword: keyword,
      // 只有在没有搜索过且没有关键词时才显示热门景点
      showPopular: !keyword && !this.data.hasSearched
    });

    // 自动搜索功能
    if (keyword.trim()) {
      // 清除之前的定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }
      
      // 设置新的定时器，延迟1秒后自动搜索
      this.searchTimer = setTimeout(() => {
        this.handleSearch();
      }, 1000);
    } else if (!keyword.trim()) {
      // 如果清空了输入框，清除定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }
    }
  },

  // 执行搜索
  async handleSearch() {
    const keyword = this.data.searchKeyword.trim();
    
    if (!keyword) {
      wx.showToast({
        title: '请输入搜索关键词',
        icon: 'none'
      });
      return;
    }

    this.setData({ 
      loading: true,
      showPopular: false,
      hasSearched: true // 标记已经搜索过
    });

    try {
      const results = await this.searchLocations(keyword);
      
      this.setData({
        searchResults: results
      });

      if (results.length === 0) {
        wx.showToast({
          title: '未找到相关景点',
          icon: 'none'
        });
      } else {
        wx.showToast({
          title: `找到${results.length}个结果`,
          icon: 'success'
        });
      }
    } catch (error) {
      console.error('搜索失败:', error);
      wx.showToast({
        title: '搜索失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 清空搜索
  onClearSearch() {
    this.setData({
      searchKeyword: '',
      searchResults: [],
      showPopular: true,
      hasSearched: false // 重置搜索状态
    });
  },

  // 点击热门景点
  onPopularTap(e) {
    const index = e.currentTarget.dataset.index;
    const location = this.data.popularLocations[index];

    // 跳转到景点详情页面
    wx.navigateTo({
      url: `/pages/place-detail/place-detail?id=${location.location_id}`,
      fail: (error) => {
        console.error('跳转失败:', error);
        wx.showToast({
          title: '跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 点击搜索结果
  onResultTap(e) {
    const index = e.currentTarget.dataset.index;
    const location = this.data.searchResults[index];

    // 跳转到景点详情页面
    wx.navigateTo({
      url: `/pages/place-detail/place-detail?id=${location.location_id}`,
      fail: (error) => {
        console.error('跳转失败:', error);
        wx.showToast({
          title: '跳转失败',
          icon: 'none'
        });
      }
    });
  }
});
