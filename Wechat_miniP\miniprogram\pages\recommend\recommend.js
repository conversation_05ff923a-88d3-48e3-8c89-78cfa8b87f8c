// recommend.js
Component({
  data: {
    recommendedPlaces: [],
    loading: false,
    currentPage: 1,
    hasMore: true,
    categories: [
      { id: 'all', name: '全部', active: true },
      { id: 'culture', name: '文化古迹', active: false },
      { id: 'nature', name: '自然风光', active: false },
      { id: 'entertainment', name: '娱乐休闲', active: false },
      { id: 'food', name: '美食推荐', active: false }
    ]
  },

  pageLifetimes: {
    show: function() {
      if (typeof this.getTabBar === 'function' && this.getTabBar()) {
        this.getTabBar().setData({
          selected: 0 // 推荐页面在tabBar中的索引
        });
      }
      this.loadRecommendations();
    }
  },

  methods: {
    // 加载推荐内容
    loadRecommendations: function() {
      const that = this;
      this.setData({ loading: true });

      wx.request({
        url: 'http://localhost:5000/api/advanced-recommend/for-you',
        method: 'POST',
        data: {
          user_id: 1, // 临时用户ID
          limit: 10
        },
        success: function(res) {
          if (res.data.success) {
            that.setData({
              recommendedPlaces: res.data.data.recommendations || [],
              loading: false
            });
          } else {
            that.setData({
              recommendedPlaces: that.getMockData(),
              loading: false
            });
          }
        },
        fail: function(error) {
          console.error('加载推荐失败:', error);
          that.setData({
            recommendedPlaces: that.getMockData(),
            loading: false
          });
        }
      });
    },

    // 获取模拟数据
    getMockData: function() {
      return [
        {
          id: 1,
          name: '故宫博物院',
          category: 'culture',
          rating: 4.8,
          image: '/images/assets/forbidden_city.jpg',
          description: '明清两朝的皇家宫殿，世界文化遗产',
          tags: ['历史', '文化', '古建筑']
        },
        {
          id: 2,
          name: '外滩',
          category: 'nature',
          rating: 4.7,
          image: '/images/assets/waitan.jpg',
          description: '上海的标志性景观，黄浦江畔的万国建筑博览群',
          tags: ['夜景', '建筑', '江景']
        },
        {
          id: 3,
          name: '上海迪士尼乐园',
          category: 'entertainment',
          rating: 4.9,
          image: '/images/assets/Disney_Shanghai.jpg',
          description: '充满奇幻色彩的主题乐园',
          tags: ['主题乐园', '娱乐', '亲子']
        }
      ];
    },

    // 分类切换
    onCategoryTap: function(e) {
      const categoryId = e.currentTarget.dataset.id;
      const categories = this.data.categories.map(function(cat) {
        return Object.assign({}, cat, { active: cat.id === categoryId });
      });
      
      this.setData({ categories: categories });
      this.loadRecommendations();
    },

    // 景点详情
    onPlaceTap: function(e) {
      const id = e.currentTarget.dataset.id;
      wx.navigateTo({
        url: '/pages/place-detail/place-detail?id=' + id
      });
    },

    // 下拉刷新
    onPullDownRefresh: function() {
      this.setData({
        currentPage: 1,
        hasMore: true
      });
      this.loadRecommendations();
      wx.stopPullDownRefresh();
    },

    // 上拉加载更多
    onReachBottom: function() {
      if (this.data.hasMore && !this.data.loading) {
        this.setData({
          currentPage: this.data.currentPage + 1
        });
        this.loadMoreRecommendations();
      }
    },

    // 加载更多推荐
    loadMoreRecommendations: function() {
      // 模拟加载更多数据
      const that = this;
      this.setData({ loading: true });
      
      setTimeout(function() {
        const newPlaces = that.getMockData();
        that.setData({
          recommendedPlaces: that.data.recommendedPlaces.concat(newPlaces),
          loading: false,
          hasMore: false // 模拟没有更多数据
        });
      }, 1000);
    }
  }
})
