// my-diaries.js
Page({
  data: {
    diaryList: [],
    loading: true,
    totalLikes: 0,
    totalViews: 0,
    userId: 1 // 默认用户ID
  },

  onLoad: function() {
    console.log('我的日记页面加载');
    this.initUserId();
    this.loadMyDiaries();
  },

  onShow: function() {
    // 每次显示页面时刷新数据
    this.loadMyDiaries();
  },

  // 加载我的日记
  loadMyDiaries: function() {
    const that = this;
    this.setData({ loading: true });

    wx.request({
      url: 'http://localhost:5000/api/articles/user/' + this.data.userId,
      method: 'GET',
      success: function(res) {
        console.log('我的日记API响应:', res);

        if (res.data.code === 0 && res.data.data) {
          const diaryList = res.data.data.map(function(item) {
            // 处理标签
            let tags = [];
            if (item.tags) {
              try {
                if (Array.isArray(item.tags)) {
                  tags = item.tags;
                } else {
                  tags = JSON.parse(item.tags);
                }
              } catch (e) {
                console.error('解析标签失败:', e);
                tags = [];
              }
            }

            // 处理图片URL
            let image_url = item.image_url;
            if (image_url && !image_url.startsWith('http')) {
              image_url = 'http://localhost:5000' + image_url;
            }

            return Object.assign({}, item, {
              created_at: that.formatDate(item.created_at),
              tags: tags,
              image_url: image_url
            });
          });

          // 计算总点赞数和浏览数
          let totalLikes = 0;
          let totalViews = 0;
          diaryList.forEach(function(item) {
            totalLikes += item.likes_count || 0;
            totalViews += item.views_count || 0;
          });

          that.setData({
            diaryList: diaryList,
            totalLikes: totalLikes,
            totalViews: totalViews,
            loading: false
          });

          console.log('我的日记加载成功，共', diaryList.length, '篇');
        } else {
          console.error('API返回错误:', res.data);
          that.setData({ 
            diaryList: [],
            loading: false 
          });
        }
      },
      fail: function(error) {
        console.error('加载我的日记失败:', error);
        that.setData({ 
          diaryList: [],
          loading: false 
        });
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        });
      }
    });
  },

  // 格式化日期
  formatDate: function(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    const now = new Date();
    const diff = now - date;
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (days === 0) {
      return '今天';
    } else if (days === 1) {
      return '昨天';
    } else if (days < 7) {
      return days + '天前';
    } else {
      return date.getFullYear() + '年' + (date.getMonth() + 1) + '月' + date.getDate() + '日';
    }
  },

  // 跳转到日记详情
  goToDiaryDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/pages/diary-detail/diary-detail?id=' + id
    });
  },

  // 跳转到写日记页面
  goToCreate: function() {
    wx.navigateTo({
      url: '/pages/diary-create/diary-create'
    });
  },

  // 初始化用户ID
  initUserId: function() {
    const userInfo = wx.getStorageSync('userInfo');
    const userId = wx.getStorageSync('userId');

    if (userInfo && userInfo.user_id) {
      this.setData({ userId: userInfo.user_id });
    } else if (userId) {
      this.setData({ userId: userId });
    }
  },

  // 返回
  goBack: function() {
    wx.navigateBack();
  }
});
