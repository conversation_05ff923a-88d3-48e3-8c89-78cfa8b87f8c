#!/usr/bin/env python3
"""
音乐API路由
"""

from flask import Blueprint
from utils.response import success, error
import os

music_bp = Blueprint('music', __name__)

@music_bp.route('', methods=['GET'])
def get_music_list():
    """
    获取可用的背景音乐列表
    """
    try:
        music_dir = os.path.join('uploads', 'music')
        music_list = []

        # 预定义的音乐信息
        music_info = {
            'peaceful_journey.mp3': {
                'name': '宁静之旅',
                'genre': '轻音乐',
                'mood': '宁静'
            },
            'adventure_time.mp3': {
                'name': '冒险时光',
                'genre': '轻快',
                'mood': '活泼'
            },
            'cultural_heritage.mp3': {
                'name': '文化传承',
                'genre': '古典',
                'mood': '文艺'
            },
            'epic_landscape.mp3': {
                'name': '壮丽山河',
                'genre': '史诗',
                'mood': '震撼'
            },
            'nostalgic_memories.mp3': {
                'name': '怀旧回忆',
                'genre': '怀旧',
                'mood': '怀旧'
            },
            'spring_breeze.mp3': {
                'name': '春风拂面',
                'genre': '自然',
                'mood': '温馨'
            }
        }

        # 检查音乐文件夹是否存在
        if os.path.exists(music_dir):
            # 扫描音乐文件
            for filename in os.listdir(music_dir):
                if filename.lower().endswith(('.mp3', '.wav', '.m4a', '.aac')):
                    file_path = os.path.join(music_dir, filename)
                    file_size = os.path.getsize(file_path)

                    # 获取预定义信息或使用默认值
                    info = music_info.get(filename, {
                        'name': filename.replace('.mp3', '').replace('_', ' ').title(),
                        'genre': '背景音乐',
                        'mood': '通用'
                    })

                    music_list.append({
                        'filename': filename,
                        'name': info['name'],
                        'duration': 180,  # 默认3分钟，实际可以用ffprobe获取
                        'size': file_size,
                        'genre': info['genre'],
                        'mood': info['mood']
                    })

        # 如果没有找到音乐文件，返回默认列表
        if not music_list:
            music_list = [
                {
                    'filename': 'peaceful_journey.mp3',
                    'name': '宁静之旅',
                    'duration': 180,
                    'size': 4500000,
                    'genre': '轻音乐',
                    'mood': '宁静'
                },
                {
                    'filename': 'adventure_time.mp3',
                    'name': '冒险时光',
                    'duration': 210,
                    'size': 5200000,
                    'genre': '轻快',
                    'mood': '活泼'
                }
            ]

        return success(music_list, 'Music list retrieved successfully')

    except Exception as e:
        return error(f'Error retrieving music list: {str(e)}')

@music_bp.route('/<filename>', methods=['GET'])
def get_music_file(filename):
    """
    获取音乐文件
    """
    try:
        # 这里应该返回实际的音乐文件
        # 目前返回一个占位响应
        return success({
            'filename': filename,
            'url': f'/static/music/{filename}',
            'message': 'Music file URL (placeholder)'
        }, 'Music file URL retrieved')

    except Exception as e:
        return error(f'Error retrieving music file: {str(e)}')
