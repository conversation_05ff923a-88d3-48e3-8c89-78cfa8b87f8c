#!/usr/bin/env python
"""
数据库重建脚本
用于快速删除并重新创建study_tour_system数据库，并导入SQL文件
"""
import os
import sys
import argparse
import subprocess
import platform

def colorize(text, color):
    """为文本添加颜色（仅在支持ANSI颜色的终端中有效）"""
    colors = {
        'red': '\033[91m',
        'green': '\033[92m',
        'yellow': '\033[93m',
        'cyan': '\033[96m',
        'end': '\033[0m'
    }

    # Windows命令提示符不支持ANSI颜色
    if platform.system() == 'Windows' and not os.environ.get('TERM'):
        return text

    return f"{colors.get(color, '')}{text}{colors['end']}"

def run_mysql_command(command, mysql_params, db_name=None):
    """运行MySQL命令"""
    cmd = ['mysql'] + mysql_params
    if db_name:
        cmd.append(db_name)

    try:
        process = subprocess.Popen(
            cmd,
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        stdout, stderr = process.communicate(command)

        if process.returncode != 0:
            return False, stderr
        return True, stdout
    except Exception as e:
        return False, str(e)

def import_sql_file(sql_file, mysql_params, db_name):
    """导入SQL文件"""
    try:
        # 检测文件编码
        encodings = ['utf-8', 'latin1', 'gbk', 'gb2312']
        detected_encoding = None

        # 尝试不同的编码打开文件
        for encoding in encodings:
            try:
                with open(sql_file, 'r', encoding=encoding) as f:
                    # 读取前1000个字符来测试编码
                    f.read(1000)
                    detected_encoding = encoding
                    break
            except UnicodeDecodeError:
                continue

        if detected_encoding is None:
            return False, f"无法检测SQL文件编码，尝试了以下编码: {', '.join(encodings)}"

        print(colorize(f"检测到SQL文件编码: {detected_encoding}", "yellow"))

        # 使用检测到的编码读取文件
        with open(sql_file, 'r', encoding=detected_encoding) as f:
            sql_content = f.read()

        # 添加设置字符集的SQL命令
        sql_content = f"""
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;
SET character_set_client = utf8mb4;
SET character_set_connection = utf8mb4;
SET character_set_results = utf8mb4;
SET character_set_server = utf8mb4;
SET collation_connection = utf8mb4_unicode_ci;
SET collation_server = utf8mb4_unicode_ci;

{sql_content}
"""

        # 使用临时文件
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', encoding='utf-8', suffix='.sql', delete=False) as temp_file:
            temp_file_path = temp_file.name
            temp_file.write(sql_content)

        try:
            # 使用临时文件导入
            cmd = ['mysql'] + mysql_params + [db_name, '--default-character-set=utf8mb4']

            with open(temp_file_path, 'r', encoding='utf-8') as f:
                process = subprocess.Popen(
                    cmd,
                    stdin=f,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                stdout, stderr = process.communicate()

            if process.returncode != 0:
                return False, stderr
            return True, stdout
        finally:
            # 删除临时文件
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    except Exception as e:
        import traceback
        traceback.print_exc()
        return False, str(e)

def find_sql_file():
    """
    自动查找SQL文件路径，支持多种可能的路径配置
    """
    # 可能的SQL文件路径列表
    possible_paths = []

    # 1. 基于当前脚本位置的相对路径（推荐方式）
    script_dir = os.path.dirname(os.path.abspath(__file__))
    backend_dir = os.path.dirname(script_dir)
    relative_path = os.path.join(backend_dir, 'database', 'study_tour_system.sql')
    possible_paths.append(relative_path)

    # 2. 队友电脑的固定路径
    teammate_path = r'D:\personalized-travel-system\backend\database\study_tour_system.sql'
    possible_paths.append(teammate_path)

    # 3. 其他可能的路径
    # 当前工作目录下的相对路径
    cwd_path = os.path.join(os.getcwd(), 'backend', 'database', 'study_tour_system.sql')
    possible_paths.append(cwd_path)

    # 上级目录的相对路径
    parent_path = os.path.join(os.path.dirname(os.getcwd()), 'backend', 'database', 'study_tour_system.sql')
    possible_paths.append(parent_path)

    # 4. 常见的项目根目录路径
    common_roots = [
        r'C:\4th_semester\data_structure\personalized-travel-system',
        r'D:\personalized-travel-system',
        r'C:\personalized-travel-system',
        r'E:\personalized-travel-system'
    ]

    for root in common_roots:
        common_path = os.path.join(root, 'backend', 'database', 'study_tour_system.sql')
        possible_paths.append(common_path)

    # 查找第一个存在的文件
    for path in possible_paths:
        if os.path.exists(path):
            return path

    # 如果都不存在，返回相对路径作为默认值
    return relative_path

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='数据库重建脚本')

    # 自动查找SQL文件路径
    default_sql_file = find_sql_file()

    parser.add_argument('--sql-file', default=default_sql_file, help='SQL文件路径')
    parser.add_argument('--user', default='root', help='数据库用户名')
    parser.add_argument('--password', default='', help='数据库密码')
    parser.add_argument('--host', default='localhost', help='数据库主机')
    parser.add_argument('--db-name', default='study_tour_system', help='数据库名称')
    parser.add_argument('--skip-drop', action='store_true', help='跳过删除数据库步骤')

    args = parser.parse_args()

    # 如果没有提供密码，提示用户输入
    if not args.password:
        import getpass
        args.password = getpass.getpass("请输入MySQL密码: ")

    # 显示脚本信息
    print(colorize("\n数据库重建脚本", "cyan"))
    print(colorize("===========================================", "cyan"))
    print(colorize(f"数据库名称: {args.db_name}", "yellow"))
    print(colorize(f"SQL文件路径: {args.sql_file}", "yellow"))

    # 显示路径查找信息
    if args.sql_file == find_sql_file():
        print(colorize("✅ 自动找到SQL文件路径", "green"))
    else:
        print(colorize("📝 使用手动指定的SQL文件路径", "yellow"))

    print(colorize("===========================================", "cyan"))
    print()

    # 检查SQL文件是否存在
    if not os.path.exists(args.sql_file):
        print(colorize(f"错误: SQL文件 '{args.sql_file}' 不存在!", "red"))
        print(colorize("\n尝试查找的路径:", "yellow"))

        # 显示所有尝试的路径
        possible_paths = []
        script_dir = os.path.dirname(os.path.abspath(__file__))
        backend_dir = os.path.dirname(script_dir)
        relative_path = os.path.join(backend_dir, 'database', 'study_tour_system.sql')
        possible_paths.append(relative_path)

        teammate_path = r'D:\personalized-travel-system\backend\database\study_tour_system.sql'
        possible_paths.append(teammate_path)

        cwd_path = os.path.join(os.getcwd(), 'backend', 'database', 'study_tour_system.sql')
        possible_paths.append(cwd_path)

        parent_path = os.path.join(os.path.dirname(os.getcwd()), 'backend', 'database', 'study_tour_system.sql')
        possible_paths.append(parent_path)

        common_roots = [
            r'C:\4th_semester\data_structure\personalized-travel-system',
            r'D:\personalized-travel-system',
            r'C:\personalized-travel-system',
            r'E:\personalized-travel-system'
        ]

        for root in common_roots:
            common_path = os.path.join(root, 'backend', 'database', 'study_tour_system.sql')
            possible_paths.append(common_path)

        for i, path in enumerate(possible_paths, 1):
            exists = "✅" if os.path.exists(path) else "❌"
            print(colorize(f"  {i}. {exists} {path}", "yellow"))

        print(colorize(f"\n请使用 --sql-file 参数指定正确的SQL文件路径", "cyan"))
        print(colorize(f"例如: python {os.path.basename(__file__)} --sql-file /path/to/study_tour_system.sql", "cyan"))
        return 1

    # 构建MySQL连接参数
    mysql_params = [f"-h{args.host}", f"-u{args.user}"]
    if args.password:
        # 注意：在某些MySQL版本中，密码和-p之间不能有空格
        mysql_params.append(f"-p{args.password}")

    # 打印连接信息（不显示密码）
    connection_info = f"使用 {args.user}@{args.host} 连接到MySQL"
    print(colorize(connection_info, "yellow"))

    # 删除数据库（如果不跳过）
    if not args.skip_drop:
        print(colorize(f"正在删除数据库 '{args.db_name}'...", "yellow"))
        success, output = run_mysql_command(f"DROP DATABASE IF EXISTS {args.db_name};", mysql_params)
        if not success:
            print(colorize(f"警告: 删除数据库时出错: {output}", "yellow"))
            print(colorize("继续执行...", "yellow"))
        else:
            print(colorize("数据库删除成功!", "green"))

    # 创建数据库
    print(colorize(f"正在创建数据库 '{args.db_name}'...", "yellow"))
    create_db_command = f"""
    CREATE DATABASE IF NOT EXISTS {args.db_name}
    CHARACTER SET utf8mb4
    COLLATE utf8mb4_unicode_ci;
    """
    success, output = run_mysql_command(create_db_command, mysql_params)
    if not success:
        print(colorize(f"错误: 创建数据库时出错: {output}", "red"))
        return 1
    else:
        print(colorize("数据库创建成功!", "green"))

    # 导入SQL文件
    print(colorize(f"正在导入SQL文件 '{args.sql_file}'...", "yellow"))
    success, output = import_sql_file(args.sql_file, mysql_params, args.db_name)
    if not success:
        print(colorize(f"错误: 导入SQL文件时出错: {output}", "red"))
        return 1
    else:
        print(colorize("SQL文件导入成功!", "green"))

    # 创建评分和收藏表
    print(colorize("正在创建评分和收藏表...", "yellow"))
    rating_favorite_tables_sql = """
    -- 创建地点评分表
    CREATE TABLE IF NOT EXISTS location_ratings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        location_id INT NOT NULL,
        rating INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY uix_user_location_rating (user_id, location_id),
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
        FOREIGN KEY (location_id) REFERENCES locations(location_id) ON DELETE CASCADE
    );

    -- 创建地点收藏表
    CREATE TABLE IF NOT EXISTS location_favorites (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        location_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY uix_user_location_favorite (user_id, location_id),
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
        FOREIGN KEY (location_id) REFERENCES locations(location_id) ON DELETE CASCADE
    );

    -- 创建文章评分表
    CREATE TABLE IF NOT EXISTS article_scores (
        score_id INT AUTO_INCREMENT PRIMARY KEY,
        article_id INT NOT NULL,
        user_id INT NOT NULL,
        score TINYINT NOT NULL CHECK (score BETWEEN 1 AND 5),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (article_id) REFERENCES articles(article_id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
        UNIQUE KEY unique_article_user_score (article_id, user_id)
    );

    -- 创建文章评论表
    CREATE TABLE IF NOT EXISTS article_comments (
        comment_id INT AUTO_INCREMENT PRIMARY KEY,
        article_id INT NOT NULL,
        user_id INT NOT NULL,
        content TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (article_id) REFERENCES articles(article_id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
        INDEX idx_article_id (article_id),
        INDEX idx_user_id (user_id)
    );

    -- 创建文章收藏表（如果不存在）
    CREATE TABLE IF NOT EXISTS article_favorites (
        favorite_id INT AUTO_INCREMENT PRIMARY KEY,
        article_id INT NOT NULL,
        user_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (article_id) REFERENCES articles(article_id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
        UNIQUE KEY unique_article_user_favorite (article_id, user_id)
    );

    -- 创建餐馆收藏表
    CREATE TABLE IF NOT EXISTS restaurant_favorite (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        restaurant_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
        FOREIGN KEY (restaurant_id) REFERENCES restaurant(id) ON DELETE CASCADE,
        UNIQUE KEY unique_restaurant_user_favorite (restaurant_id, user_id)
    );

    -- 创建餐馆评论表
    CREATE TABLE IF NOT EXISTS restaurant_comment (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        restaurant_id INT NOT NULL,
        content TEXT NOT NULL,
        rating FLOAT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
        FOREIGN KEY (restaurant_id) REFERENCES restaurant(id) ON DELETE CASCADE,
        INDEX idx_restaurant_id (restaurant_id),
        INDEX idx_user_id (user_id)
    );

    -- 添加索引以提高查询性能
    CREATE INDEX idx_location_ratings_user_id ON location_ratings(user_id);
    CREATE INDEX idx_location_ratings_location_id ON location_ratings(location_id);
    CREATE INDEX idx_location_favorites_user_id ON location_favorites(user_id);
    CREATE INDEX idx_location_favorites_location_id ON location_favorites(location_id);
    CREATE INDEX idx_article_scores_user_id ON article_scores(user_id);
    CREATE INDEX idx_article_scores_article_id ON article_scores(article_id);
    CREATE INDEX idx_article_favorites_user_id ON article_favorites(user_id);
    CREATE INDEX idx_article_favorites_article_id ON article_favorites(article_id);
    CREATE INDEX idx_restaurant_favorite_user_id ON restaurant_favorite(user_id);
    CREATE INDEX idx_restaurant_favorite_restaurant_id ON restaurant_favorite(restaurant_id);
    CREATE INDEX idx_restaurant_comment_user_id ON restaurant_comment(user_id);
    CREATE INDEX idx_restaurant_comment_restaurant_id ON restaurant_comment(restaurant_id);
    """

    success, output = run_mysql_command(rating_favorite_tables_sql, mysql_params, args.db_name)
    if not success:
        print(colorize(f"警告: 创建评分和收藏表时出错: {output}", "yellow"))
        print(colorize("继续执行...", "yellow"))
    else:
        print(colorize("评分和收藏表创建成功!", "green"))

    # 添加示例文章数据（包含标签）
    print(colorize("正在添加示例文章数据...", "yellow"))
    sample_articles_sql = """
    -- 添加示例文章数据（包含标签）
    INSERT INTO articles (title, location, content, tags, popularity, evaluation, user_id, created_at) VALUES
    ('北京大学校园游记', '北京大学', 0x48656C6C6F, '["大学", "校园", "文化古迹"]', 15, 4.5, 1, NOW()),
    ('清华园的春天', '清华大学', 0x48656C6C6F, '["大学", "自然景观", "春天"]', 23, 4.2, 1, NOW()),
    ('颐和园赏荷记', '颐和园', 0x48656C6C6F, '["公园", "自然景观", "荷花"]', 45, 4.8, 1, NOW()),
    ('故宫博物院深度游', '故宫博物院', 0x48656C6C6F, '["博物馆", "文化古迹", "历史"]', 67, 4.9, 1, NOW()),
    ('香山红叶节游记', '香山公园', 0x48656C6C6F, '["公园", "自然景观", "红叶"]', 34, 4.3, 1, NOW()),
    ('八达岭长城攀登记', '八达岭长城', 0x48656C6C6F, '["古建筑", "长城", "历史"]', 89, 4.7, 1, NOW()),
    ('雍和宫祈福之旅', '雍和宫', 0x48656C6C6F, '["寺庙", "祈福", "文化"]', 28, 4.1, 1, NOW()),
    ('北京动物园熊猫馆', '北京动物园', 0x48656C6C6F, '["动物园", "熊猫", "亲子"]', 52, 4.4, 1, NOW()),
    ('凤凰岭徒步探险', '凤凰岭', 0x48656C6C6F, '["游山", "徒步", "自然"]', 41, 4.6, 1, NOW()),
    ('昆明湖泛舟记', '昆明湖', 0x48656C6C6F, '["玩水", "泛舟", "休闲"]', 36, 4.0, 1, NOW());
    """

    success, output = run_mysql_command(sample_articles_sql, mysql_params, args.db_name)
    if not success:
        print(colorize(f"警告: 添加示例文章数据时出错: {output}", "yellow"))
        print(colorize("继续执行...", "yellow"))
    else:
        print(colorize("示例文章数据添加成功!", "green"))

    # 询问是否优化AUTO_INCREMENT值
    optimize_auto_increment = input(colorize("是否优化表的AUTO_INCREMENT值？(y/n): ", "yellow")).strip().lower()
    if optimize_auto_increment == 'y':
        # 询问是否需要手动设置特定表的AUTO_INCREMENT值
        manual_set = input(colorize("是否需要手动设置特定表的AUTO_INCREMENT值？(y/n): ", "yellow")).strip().lower()

        if manual_set == 'y':
            table_name = input(colorize("请输入表名: ", "yellow")).strip()
            auto_inc_value = input(colorize(f"请输入表 '{table_name}' 的AUTO_INCREMENT值: ", "yellow")).strip()

            try:
                auto_inc_value = int(auto_inc_value)
                if auto_inc_value > 0:
                    # 设置指定表的AUTO_INCREMENT值
                    reset_query = f"ALTER TABLE {table_name} AUTO_INCREMENT = {auto_inc_value};"
                    success, output = run_mysql_command(reset_query, mysql_params, args.db_name)
                    if success:
                        print(colorize(f"表 '{table_name}' 的AUTO_INCREMENT值已手动设置为 {auto_inc_value}", "green"))
                    else:
                        print(colorize(f"警告: 无法设置表 '{table_name}' 的AUTO_INCREMENT值: {output}", "yellow"))
                else:
                    print(colorize("错误: AUTO_INCREMENT值必须大于0", "red"))
            except ValueError:
                print(colorize("错误: 请输入有效的整数", "red"))
        print(colorize("正在优化AUTO_INCREMENT值...", "yellow"))

        # 获取所有表名
        tables_query = "SHOW TABLES;"
        success, output = run_mysql_command(tables_query, mysql_params, args.db_name)

        if success:
            # 解析表名
            tables = [line.strip() for line in output.split('\n') if line.strip()]
            # 移除表头
            if tables and tables[0].startswith('Tables_in_'):
                tables = tables[1:]

            # 特殊表处理列表 - 这些表需要特殊处理
            special_tables = {
                'vertexes': {'description': '顶点表', 'special_handling': True},
                'location_browse_history': {'description': '地点浏览历史表', 'special_handling': True}
            }

            # 优化每个表的AUTO_INCREMENT值
            for table in tables:
                # 检查是否是特殊表
                if table in special_tables:
                    print(colorize(f"  特殊处理表 '{table}' ({special_tables[table]['description']})", "cyan"))

                    # 对于vertexes表，直接设置AUTO_INCREMENT为1
                    if table == 'vertexes':
                        # 首先检查表结构
                        desc_query = f"DESCRIBE {table};"
                        success, desc_output = run_mysql_command(desc_query, mysql_params, args.db_name)

                        if success:
                            # 检查是否有AUTO_INCREMENT列
                            has_auto_increment = False
                            primary_key_column = None

                            lines = desc_output.split('\n')
                            for line in lines[1:]:  # 跳过表头
                                if not line.strip():
                                    continue

                                parts = line.split('\t')
                                if len(parts) >= 6 and 'auto_increment' in parts[5].lower():
                                    has_auto_increment = True
                                    primary_key_column = parts[0]
                                    break

                            if has_auto_increment and primary_key_column:
                                # 获取最大ID
                                max_id_query = f"SELECT MAX({primary_key_column}) FROM {table};"
                                success, max_id_output = run_mysql_command(max_id_query, mysql_params, args.db_name)

                                if success:
                                    max_id_lines = [line.strip() for line in max_id_output.split('\n') if line.strip()]
                                    if len(max_id_lines) >= 2:
                                        max_id_str = max_id_lines[1]
                                        if max_id_str.lower() != 'null':
                                            try:
                                                max_id = int(max_id_str)
                                                next_id = max_id + 1

                                                # 设置AUTO_INCREMENT
                                                reset_query = f"ALTER TABLE {table} AUTO_INCREMENT = {next_id};"
                                                success, output = run_mysql_command(reset_query, mysql_params, args.db_name)
                                                if success:
                                                    print(colorize(f"  表 '{table}' AUTO_INCREMENT值已设置为 {next_id}", "green"))
                                                else:
                                                    print(colorize(f"  警告: 无法设置表 '{table}' 的AUTO_INCREMENT值: {output}", "yellow"))
                                            except ValueError:
                                                print(colorize(f"  警告: 表 '{table}' 的最大ID不是数字", "yellow"))
                                        else:
                                            # 如果MAX返回NULL，设置为1
                                            reset_query = f"ALTER TABLE {table} AUTO_INCREMENT = 1;"
                                            success, output = run_mysql_command(reset_query, mysql_params, args.db_name)
                                            if success:
                                                print(colorize(f"  表 '{table}' AUTO_INCREMENT值已设置为 1", "green"))
                                            else:
                                                print(colorize(f"  警告: 无法设置表 '{table}' 的AUTO_INCREMENT值: {output}", "yellow"))
                                    else:
                                        print(colorize(f"  警告: 无法解析表 '{table}' 的最大ID", "yellow"))
                                else:
                                    print(colorize(f"  警告: 无法获取表 '{table}' 的最大ID", "yellow"))
                            else:
                                print(colorize(f"  表 '{table}' 没有AUTO_INCREMENT列", "yellow"))
                        else:
                            print(colorize(f"  警告: 无法获取表 '{table}' 的结构", "yellow"))

                    # 对于location_browse_history表，特殊处理
                    elif table == 'location_browse_history':
                        # 首先检查表结构
                        desc_query = f"DESCRIBE {table};"
                        success, desc_output = run_mysql_command(desc_query, mysql_params, args.db_name)

                        if success:
                            # 检查是否有AUTO_INCREMENT列
                            has_auto_increment = False
                            primary_key_column = None

                            lines = desc_output.split('\n')
                            for line in lines[1:]:  # 跳过表头
                                if not line.strip():
                                    continue

                                parts = line.split('\t')
                                if len(parts) >= 6 and 'auto_increment' in parts[5].lower():
                                    has_auto_increment = True
                                    primary_key_column = parts[0]
                                    break

                            if has_auto_increment and primary_key_column:
                                # 获取最大ID
                                max_id_query = f"SELECT MAX({primary_key_column}) FROM {table};"
                                success, max_id_output = run_mysql_command(max_id_query, mysql_params, args.db_name)

                                if success:
                                    max_id_lines = [line.strip() for line in max_id_output.split('\n') if line.strip()]
                                    if len(max_id_lines) >= 2:
                                        max_id_str = max_id_lines[1]
                                        if max_id_str.lower() != 'null':
                                            try:
                                                max_id = int(max_id_str)
                                                next_id = max_id + 1

                                                # 设置AUTO_INCREMENT
                                                reset_query = f"ALTER TABLE {table} AUTO_INCREMENT = {next_id};"
                                                success, output = run_mysql_command(reset_query, mysql_params, args.db_name)
                                                if success:
                                                    print(colorize(f"  表 '{table}' AUTO_INCREMENT值已设置为 {next_id}", "green"))
                                                else:
                                                    print(colorize(f"  警告: 无法设置表 '{table}' 的AUTO_INCREMENT值: {output}", "yellow"))
                                            except ValueError:
                                                print(colorize(f"  警告: 表 '{table}' 的最大ID不是数字", "yellow"))
                                        else:
                                            # 如果MAX返回NULL，设置为1
                                            reset_query = f"ALTER TABLE {table} AUTO_INCREMENT = 1;"
                                            success, output = run_mysql_command(reset_query, mysql_params, args.db_name)
                                            if success:
                                                print(colorize(f"  表 '{table}' AUTO_INCREMENT值已设置为 1", "green"))
                                            else:
                                                print(colorize(f"  警告: 无法设置表 '{table}' 的AUTO_INCREMENT值: {output}", "yellow"))
                                    else:
                                        print(colorize(f"  警告: 无法解析表 '{table}' 的最大ID", "yellow"))
                                else:
                                    print(colorize(f"  警告: 无法获取表 '{table}' 的最大ID", "yellow"))
                            else:
                                print(colorize(f"  表 '{table}' 没有AUTO_INCREMENT列", "yellow"))
                        else:
                            print(colorize(f"  警告: 无法获取表 '{table}' 的结构", "yellow"))

                    # 跳过对这个特殊表的常规处理
                    continue

                # 常规表处理
                # 检查表是否有主键
                pk_query = f"SHOW KEYS FROM {table} WHERE Key_name = 'PRIMARY';"
                success, pk_output = run_mysql_command(pk_query, mysql_params, args.db_name)

                if not success or not pk_output.strip():
                    print(colorize(f"  跳过表 '{table}': 没有主键", "yellow"))
                    continue

                # 获取主键列名
                pk_lines = [line.strip() for line in pk_output.split('\n') if line.strip()]
                if len(pk_lines) < 2:  # 至少应该有表头和一行数据
                    print(colorize(f"  跳过表 '{table}': 无法确定主键", "yellow"))
                    continue

                # 解析主键列名（假设第4列是Column_name）
                pk_parts = pk_lines[1].split('\t')
                if len(pk_parts) < 4:
                    print(colorize(f"  跳过表 '{table}': 无法解析主键信息", "yellow"))
                    continue

                pk_column = pk_parts[4] if len(pk_parts) > 4 else pk_parts[3]

                # 检查表是否有数据
                count_query = f"SELECT COUNT(*) FROM {table};"
                success, count_output = run_mysql_command(count_query, mysql_params, args.db_name)

                if not success:
                    print(colorize(f"  跳过表 '{table}': 无法获取记录数", "yellow"))
                    continue

                # 解析记录数
                count_lines = [line.strip() for line in count_output.split('\n') if line.strip()]
                if len(count_lines) < 2:
                    print(colorize(f"  跳过表 '{table}': 无法解析记录数", "yellow"))
                    continue

                record_count = int(count_lines[1])

                if record_count == 0:
                    # 表没有数据，重置AUTO_INCREMENT为1
                    reset_query = f"ALTER TABLE {table} AUTO_INCREMENT = 1;"
                    success, output = run_mysql_command(reset_query, mysql_params, args.db_name)
                    if success:
                        # 验证设置是否生效
                        verify_query = f"SHOW TABLE STATUS LIKE '{table}';"
                        success, verify_output = run_mysql_command(verify_query, mysql_params, args.db_name)
                        if success:
                            # 解析AUTO_INCREMENT值
                            auto_inc_value = None
                            lines = verify_output.split('\n')
                            if len(lines) >= 2:  # 至少有表头和一行数据
                                columns = lines[0].split('\t')
                                values = lines[1].split('\t')

                                # 找到Auto_increment列的索引
                                auto_inc_index = -1
                                for i, col in enumerate(columns):
                                    if col.lower() == 'auto_increment':
                                        auto_inc_index = i
                                        break

                                if auto_inc_index >= 0 and auto_inc_index < len(values):
                                    auto_inc_value = values[auto_inc_index]

                            if auto_inc_value and auto_inc_value != 'NULL' and auto_inc_value != '1':
                                # 如果AUTO_INCREMENT不是1，再次尝试设置
                                reset_query = f"ALTER TABLE {table} AUTO_INCREMENT = 1;"
                                run_mysql_command(reset_query, mysql_params, args.db_name)
                                print(colorize(f"  表 '{table}' 没有数据，AUTO_INCREMENT值已强制重置为1 (原值: {auto_inc_value})", "green"))
                            else:
                                print(colorize(f"  表 '{table}' 没有数据，AUTO_INCREMENT值已重置为1", "green"))
                        else:
                            print(colorize(f"  表 '{table}' 没有数据，AUTO_INCREMENT值已重置为1", "green"))
                    else:
                        print(colorize(f"  警告: 无法重置表 '{table}' 的AUTO_INCREMENT值: {output}", "yellow"))
                else:
                    # 表有数据，设置AUTO_INCREMENT为最大ID+1
                    max_id_query = f"SELECT MAX({pk_column}) FROM {table};"
                    success, max_id_output = run_mysql_command(max_id_query, mysql_params, args.db_name)

                    if not success:
                        print(colorize(f"  跳过表 '{table}': 无法获取最大ID", "yellow"))
                        continue

                    # 解析最大ID
                    max_id_lines = [line.strip() for line in max_id_output.split('\n') if line.strip()]
                    if len(max_id_lines) < 2:
                        print(colorize(f"  跳过表 '{table}': 无法解析最大ID", "yellow"))
                        continue

                    try:
                        max_id_str = max_id_lines[1]
                        if max_id_str.lower() == 'null':
                            # 如果MAX(id)返回NULL，说明表中可能有记录但主键为NULL
                            # 这种情况下设置AUTO_INCREMENT为1
                            next_id = 1
                        else:
                            max_id = int(max_id_str)
                            next_id = max_id + 1

                        # 设置AUTO_INCREMENT为最大ID+1或1
                        reset_query = f"ALTER TABLE {table} AUTO_INCREMENT = {next_id};"
                        success, output = run_mysql_command(reset_query, mysql_params, args.db_name)

                        if success:
                            # 验证设置是否生效
                            verify_query = f"SHOW TABLE STATUS LIKE '{table}';"
                            success, verify_output = run_mysql_command(verify_query, mysql_params, args.db_name)
                            if success:
                                # 解析AUTO_INCREMENT值
                                auto_inc_value = None
                                lines = verify_output.split('\n')
                                if len(lines) >= 2:  # 至少有表头和一行数据
                                    columns = lines[0].split('\t')
                                    values = lines[1].split('\t')

                                    # 找到Auto_increment列的索引
                                    auto_inc_index = -1
                                    for i, col in enumerate(columns):
                                        if col.lower() == 'auto_increment':
                                            auto_inc_index = i
                                            break

                                    if auto_inc_index >= 0 and auto_inc_index < len(values):
                                        auto_inc_value = values[auto_inc_index]

                                if auto_inc_value and auto_inc_value != 'NULL' and int(auto_inc_value) != next_id:
                                    # 如果AUTO_INCREMENT不是期望值，再次尝试设置
                                    reset_query = f"ALTER TABLE {table} AUTO_INCREMENT = {next_id};"
                                    run_mysql_command(reset_query, mysql_params, args.db_name)
                                    print(colorize(f"  表 '{table}' 有 {record_count} 条记录，AUTO_INCREMENT值已强制设置为 {next_id} (原值: {auto_inc_value})", "green"))
                                else:
                                    print(colorize(f"  表 '{table}' 有 {record_count} 条记录，AUTO_INCREMENT值已设置为 {next_id}", "green"))
                            else:
                                print(colorize(f"  表 '{table}' 有 {record_count} 条记录，AUTO_INCREMENT值已设置为 {next_id}", "green"))
                        else:
                            print(colorize(f"  警告: 无法设置表 '{table}' 的AUTO_INCREMENT值: {output}", "yellow"))
                    except ValueError:
                        print(colorize(f"  跳过表 '{table}': 最大ID不是数字", "yellow"))

            print(colorize("AUTO_INCREMENT值优化完成!", "green"))
        else:
            print(colorize(f"警告: 无法获取表列表: {output}", "yellow"))
            print(colorize("继续执行...", "yellow"))

    # 设置权限
    print(colorize("正在设置数据库权限...", "yellow"))
    grant_command = f"""
    GRANT ALL PRIVILEGES ON {args.db_name}.* TO '{args.user}'@'{args.host}';
    FLUSH PRIVILEGES;
    """
    success, output = run_mysql_command(grant_command, mysql_params)
    if not success:
        print(colorize(f"警告: 设置权限时出错: {output}", "yellow"))
        print(colorize("继续执行...", "yellow"))
    else:
        print(colorize("权限设置成功!", "green"))

    print()
    print(colorize("===========================================", "cyan"))
    print(colorize(f"数据库 '{args.db_name}' 重建完成!", "green"))
    print(colorize("可以使用以下命令连接到数据库:", "cyan"))
    password_param = "-p" if args.password else ""
    print(colorize(f"mysql -h{args.host} -u{args.user} {password_param} {args.db_name}", "yellow"))
    print(colorize("===========================================", "cyan"))

    return 0

if __name__ == "__main__":
    sys.exit(main())
