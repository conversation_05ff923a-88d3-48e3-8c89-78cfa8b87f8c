<template>
  <div class="flight-booking">
    <h1 class="page-title">机票预订</h1>

    <!-- 搜索表单 -->
    <div class="search-form-container">
      <el-form :model="searchForm" label-position="top" class="search-form">
        <el-row :gutter="20">
          <!-- 行程类型 -->
          <el-col :xs="24" :sm="24" :md="24" :lg="24">
            <el-form-item>
              <el-radio-group v-model="searchForm.tripType" class="trip-type-group">
                <el-radio-button label="roundTrip">往返</el-radio-button>
                <el-radio-button label="oneWay">单程</el-radio-button>
                <el-radio-button label="multiCity">多程</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>

          <!-- 出发地和目的地 -->
          <el-col :xs="24" :sm="12" :md="8" :lg="8">
            <el-form-item label="出发城市">
              <el-autocomplete
                v-model="searchForm.departureCity"
                :fetch-suggestions="queryCity"
                placeholder="请输入出发城市"
                class="full-width"
              ></el-autocomplete>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="8" :lg="8">
            <el-form-item label="到达城市">
              <el-autocomplete
                v-model="searchForm.arrivalCity"
                :fetch-suggestions="queryCity"
                placeholder="请输入到达城市"
                class="full-width"
              ></el-autocomplete>
            </el-form-item>
          </el-col>

          <!-- 日期选择 -->
          <el-col :xs="24" :sm="12" :md="8" :lg="8">
            <el-form-item label="出发日期">
              <el-date-picker
                v-model="searchForm.departureDate"
                type="date"
                placeholder="选择出发日期"
                class="full-width"
                :disabled-date="disablePastDates"
              ></el-date-picker>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="8" :lg="8" v-if="searchForm.tripType === 'roundTrip'">
            <el-form-item label="返程日期">
              <el-date-picker
                v-model="searchForm.returnDate"
                type="date"
                placeholder="选择返程日期"
                class="full-width"
                :disabled-date="disableInvalidReturnDates"
              ></el-date-picker>
            </el-form-item>
          </el-col>

          <!-- 乘客信息 -->
          <el-col :xs="24" :sm="12" :md="8" :lg="8">
            <el-form-item label="乘客">
              <el-select v-model="searchForm.passengers" placeholder="选择乘客数量" class="full-width">
                <el-option
                  v-for="i in 10"
                  :key="i"
                  :label="`${i} 位乘客`"
                  :value="i"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 舱位等级 -->
          <el-col :xs="24" :sm="12" :md="8" :lg="8">
            <el-form-item label="舱位等级">
              <el-select v-model="searchForm.cabinClass" placeholder="选择舱位等级" class="full-width">
                <el-option label="经济舱" value="economy"></el-option>
                <el-option label="高端经济舱" value="premiumEconomy"></el-option>
                <el-option label="商务舱" value="business"></el-option>
                <el-option label="头等舱" value="first"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 搜索按钮 -->
        <div class="search-button-container">
          <el-button type="primary" @click="searchFlights" :loading="loading" class="search-button">
            搜索航班
          </el-button>
        </div>
      </el-form>
    </div>

    <!-- 搜索结果 -->
    <div v-if="showResults" class="search-results">
      <h2 class="results-title">航班搜索结果</h2>

      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>

      <div v-else-if="flights.length === 0" class="no-results">
        <el-empty description="没有找到符合条件的航班"></el-empty>
      </div>

      <div v-else class="flight-list">
        <!-- 去程航班 -->
        <div class="flight-direction">
          <h3>{{ searchForm.departureCity }} → {{ searchForm.arrivalCity }}</h3>
          <p class="flight-date">{{ formatDate(searchForm.departureDate) }}</p>
        </div>

        <el-card v-for="(flight, index) in flights" :key="index" class="flight-card">
          <div class="flight-info">
            <div class="airline-info">
              <img :src="getAirlineLogo(flight.airline)" alt="航空公司Logo" class="airline-logo">
              <div>
                <div class="airline-name">{{ flight.airline }}</div>
                <div class="flight-number">{{ flight.flightNumber }}</div>
              </div>
            </div>

            <div class="time-info">
              <div class="departure">
                <div class="time">{{ flight.departureTime }}</div>
                <div class="airport">{{ flight.departureAirport }}</div>
              </div>

              <div class="flight-duration">
                <div class="duration-line"></div>
                <div class="duration-text">{{ flight.duration }}</div>
              </div>

              <div class="arrival">
                <div class="time">{{ flight.arrivalTime }}</div>
                <div class="airport">{{ flight.arrivalAirport }}</div>
              </div>
            </div>

            <div class="price-info">
              <div class="price">¥{{ flight.price }}</div>
              <el-button type="primary" size="small" @click="bookFlight(flight)">预订</el-button>
            </div>
          </div>
        </el-card>

        <!-- 返程航班 (如果是往返行程) -->
        <div v-if="searchForm.tripType === 'roundTrip' && returnFlights.length > 0" class="return-flights">
          <div class="flight-direction">
            <h3>{{ searchForm.arrivalCity }} → {{ searchForm.departureCity }}</h3>
            <p class="flight-date">{{ formatDate(searchForm.returnDate) }}</p>
          </div>

          <el-card v-for="(flight, index) in returnFlights" :key="`return-${index}`" class="flight-card">
            <div class="flight-info">
              <div class="airline-info">
                <img :src="getAirlineLogo(flight.airline)" alt="航空公司Logo" class="airline-logo">
                <div>
                  <div class="airline-name">{{ flight.airline }}</div>
                  <div class="flight-number">{{ flight.flightNumber }}</div>
                </div>
              </div>

              <div class="time-info">
                <div class="departure">
                  <div class="time">{{ flight.departureTime }}</div>
                  <div class="airport">{{ flight.departureAirport }}</div>
                </div>

                <div class="flight-duration">
                  <div class="duration-line"></div>
                  <div class="duration-text">{{ flight.duration }}</div>
                </div>

                <div class="arrival">
                  <div class="time">{{ flight.arrivalTime }}</div>
                  <div class="airport">{{ flight.arrivalAirport }}</div>
                </div>
              </div>

              <div class="price-info">
                <div class="price">¥{{ flight.price }}</div>
                <el-button type="primary" size="small" @click="bookFlight(flight)">预订</el-button>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive,watch } from 'vue';
import { ElMessage } from 'element-plus';
import { flightTemplates} from '@/assets/data/flightData'
const generatePrice = (basePrice, departureDate) => {
  if (!departureDate) return basePrice;
  const date = new Date(departureDate);
  const dayOffset = date.getDate() % 7;
  const fluctuation = [0, 50, 100, -30, 20, -50, 80];
  return basePrice + fluctuation[dayOffset];
};
// 搜索表单数据
const searchForm = reactive({
  tripType: 'roundTrip',
  departureCity: '',
  arrivalCity: '',
  departureDate: '',
  returnDate: '',
  passengers: 1,
  cabinClass: 'economy'
});
watch(
  () => searchForm.returnDate, // 监听returnDate的响应式值
  (newVal) => {
    if (newVal && searchForm.departureDate) {
      const returnDate = new Date(newVal);
      const departureDate = new Date(searchForm.departureDate);
      
      // 检查返程日期是否早于出发日期
      if (returnDate < departureDate) {
        ElMessage.warning('返程日期不能早于出发日期');
        searchForm.returnDate = null; // 清空返程日期
      }
    }
  }
);

// 状态变量
const loading = ref(false);
const showResults = ref(false);
const flights = ref([]);
const returnFlights = ref([]);

// 城市建议数据
const cities = [
  { value: '北京', label: '北京 (PEK)' },
  { value: '上海', label: '上海 (SHA/PVG)' },
  { value: '广州', label: '广州 (CAN)' },
  { value: '深圳', label: '深圳 (SZX)' },
  { value: '成都', label: '成都 (CTU)' },
  { value: '杭州', label: '杭州 (HGH)' },
  { value: '西安', label: '西安 (XIY)' },
  { value: '重庆', label: '重庆 (CKG)' },
  { value: '南京', label: '南京 (NKG)' },
  { value: '厦门', label: '厦门 (XMN)' }
];
const logoMap = {
  '中国国航': require('../../src/assets/airlogo/中国国航.jpg'),
  '东方航空': require('../../src/assets/airlogo/东方航空.jpg'),
  '海南航空':require('../../src/assets/airlogo/海南航空.jpg'),
  '中国联合航空':require('../../src/assets/airlogo/中国联合航空.jpg'),
  '吉祥航空':require('../../src/assets/airlogo/吉祥航空.jpg'),
  '南方航空':require('../../src/assets/airlogo/南方航空.jpg'),
  '华夏航空':require('../../src/assets/airlogo/华夏航空.jpg'),
  '成都航空':require('../../src/assets/airlogo/成都航空.jpg'),
  '深圳航空':require('../../src/assets/airlogo/深圳航空.jpg'),
  '四川航空':require('../../src/assets/airlogo/四川航空.jpg'),
  '首都航空':require('../../src/assets/airlogo/首都航空.jpg'),
  '西藏航空':require('../../src/assets/airlogo/西藏航空.jpg'),
  '长龙航空':require('../../src/assets/airlogo/长龙航空.jpg'),
  '河北航空':require('../../src/assets/airlogo/河北航空.jpg'),
  '厦门航空':require('../../src/assets/airlogo/厦门航空.jpg'), 
  '海航旗下福航':require('../../src/assets/airlogo/海航旗下福航.jpg'),
  '山东航空':require('../../src/assets/airlogo/山东航空.jpg'),
  '海航旗下天津航':require('../../src/assets/airlogo/海航旗下天津航.jpg'),
  '海航旗下长安航空':require('../../src/assets/airlogo/海航旗下长安航空.jpg'),
};

const getAirlineLogo = (airline) => {
  const airlineName = airline.replace(/\s+/g, '');
  return logoMap[airlineName] || require('../../src/assets/airlogo/default.jpg');
};
// 城市搜索建议
const queryCity = (queryString, callback) => {
  const results = queryString
    ? cities.filter(city => city.value.includes(queryString) || city.label.includes(queryString))
    : cities;
  callback(results);
};
 
// 禁用过去的日期
const disablePastDates = (date) => {
  return date < new Date(new Date().setHours(0, 0, 0, 0));
};

// 禁用无效的返程日期（早于出发日期）
const disableInvalidReturnDates = (date) => {
  if (!searchForm.departureDate) return disablePastDates(date);
  return date < searchForm.departureDate;
};

// 格式化日期
const formatDate = (date) => {
  if (!date) return '';
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
  const weekday = weekdays[d.getDay()];
  return `${year}年${month}月${day}日 ${weekday}`;
};

// 生成模拟航班数据
const generateMockFlights = (departure, arrival) => {
  const routeKey = `${departure}-${arrival}`;
  return (flightTemplates[routeKey] || []).map(flight => ({
    ...flight,
    price: generatePrice(flight.price, searchForm.departureDate)
  }));
};

// 搜索航班
const searchFlights = () => {
  // 表单验证
  if (!searchForm.departureCity) {
    ElMessage.warning('请输入出发城市');
    return;
  }
  if (!searchForm.arrivalCity) {
    ElMessage.warning('请输入到达城市');
    return;
  }
  if (searchForm.departureCity === searchForm.arrivalCity) {
    ElMessage.warning('始发地和目的地不能相同');
    return;
  }
  if (!searchForm.departureDate) {
    ElMessage.warning('请选择出发日期');
    return;
  }
  if (searchForm.tripType === 'roundTrip' && !searchForm.returnDate) {
    ElMessage.warning('请选择返程日期');
    return;
  }

  loading.value = true;
  showResults.value = true;

  setTimeout(() => {
    flights.value = generateMockFlights(searchForm.departureCity, searchForm.arrivalCity);

    if (searchForm.tripType === 'roundTrip') {
      returnFlights.value = generateMockFlights(searchForm.arrivalCity, searchForm.departureCity);
    } else {
      returnFlights.value = [];
    }

    loading.value = false;
  }, 1500);
};

// 预订航班
const bookFlight = (flight) => {
  ElMessage.success(`已选择 ${flight.airline} ${flight.flightNumber} 航班，即将跳转到预订页面`);
};
</script>
<style scoped>
.flight-booking {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.page-title {
  font-size: 2rem;
  margin-bottom: 2rem;
  color: #333;
  text-align: center;
  margin-top: -10px;
}

.search-form-container {
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.search-form {
  width: 100%;
}

.full-width {
  width: 100%;
}

.trip-type-group {
  margin-bottom: 1rem;
}

.search-button-container {
  display: flex;
  justify-content: center;
  margin-top: 1.5rem;
}

.search-button {
  padding: 12px 36px;
  font-size: 1.1rem;
}

.search-results {
  margin-top: 3rem;
}

.results-title {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: #333;
}

.loading-container {
  padding: 2rem;
}

.no-results {
  padding: 3rem;
  text-align: center;
}

.flight-direction {
  margin: 2rem 0 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #eee;
}

.flight-date {
  color: #666;
  margin-top: 0.5rem;
}

.flight-card {
  margin-bottom: 1rem;
}

.flight-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.airline-info {
  display: flex;
  align-items: center;
  width: 20%;
}

.airline-logo {
  width: 40px;
  height: 40px;
  margin-right: 1rem;
  border-radius: 50%;
}

.airline-name {
  font-weight: bold;
}

.flight-number {
  color: #666;
  font-size: 0.9rem;
}

.time-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 50%;
}

.departure, .arrival {
  text-align: center;
}

.time {
  font-size: 1.2rem;
  font-weight: bold;
}

.airport {
  color: #666;
  font-size: 0.9rem;
}

.flight-duration {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  width: 40%;
}

.duration-line {
  height: 2px;
  background-color: #ddd;
  width: 100%;
  position: relative;
}

.duration-line::before, .duration-line::after {
  content: '';
  position: absolute;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #ddd;
  top: -2px;
}

.duration-line::before {
  left: 0;
}

.duration-line::after {
  right: 0;
}

.duration-text {
  font-size: 0.8rem;
  color: #666;
  margin-top: 0.5rem;
}

.price-info {
  text-align: right;
  width: 20%;
}

.price {
  font-size: 1.5rem;
  font-weight: bold;
  color: #f56c6c;
  margin-bottom: 0.5rem;
}

.return-flights {
  margin-top: 3rem;
}

@media (max-width: 768px) {
  .flight-info {
    flex-direction: column;
    align-items: flex-start;
  }

  .airline-info, .time-info, .price-info {
    width: 100%;
    margin-bottom: 1rem;
  }

  .time-info {
    order: -1;
  }
}
</style>