import request from '@/utils/request';

/**
 * 获取所有地点
 * @returns {Promise} - 返回所有地点的Promise
 */
export function getAllLocations() {
  return request({
    url: '/api/locations',
    method: 'get'
  });
}

/**
 * 获取地点详情
 * @param {number} locationId - 地点ID
 * @returns {Promise} - 返回地点详情的Promise
 */
export function getLocationDetail(locationId) {
  return request({
    url: `/api/locations/${locationId}`,
    method: 'get'
  });
}

/**
 * 根据ID获取地点信息
 * @param {number} locationId - 地点ID
 * @returns {Promise} - 返回地点信息的Promise
 */
export function getLocationById(locationId) {
  return request({
    url: `/api/locations/${locationId}`,
    method: 'get'
  });
}

/**
 * 按条件查询地点
 * @param {Object} params - 查询参数
 * @param {string} [params.name] - 地点名称，支持模糊查询
 * @param {number} [params.type] - 地点类型，0: 学校, 1: 景点
 * @param {string} [params.keyword] - 关键词，支持模糊查询
 * @param {number} [params.sortOrder] - 排序方式，0: 按人气排序, 1: 按评分排序
 * @returns {Promise} - 返回查询结果的Promise
 */
export function queryLocations(params) {
  return request({
    url: '/api/locations/query',
    method: 'get',
    params
  });
}

/**
 * 更新地点浏览计数
 * @param {number} locationId - 地点ID
 * @param {Object} data - 请求数据
 * @param {number} data.user_id - 用户ID
 * @returns {Promise} - 返回更新结果的Promise
 */
export function updateLocationBrowseCount(locationId, data) {
  return request({
    url: `/api/locations/browse/${locationId}`,
    method: 'post',
    data
  });
}

/**
 * 对地点进行评分
 * @param {Object} data - 评分数据
 * @param {number} data.location_id - 地点ID
 * @param {number} data.user_id - 用户ID
 * @param {number} data.rating - 评分（1-5）
 * @returns {Promise} - 返回评分结果的Promise
 */
export function rateLocation(data) {
  return request({
    url: '/api/locations/rate',
    method: 'post',
    data
  });
}

/**
 * 获取用户对地点的评分
 * @param {number} userId - 用户ID
 * @param {number} locationId - 地点ID
 * @returns {Promise} - 返回用户评分的Promise
 */
export function getUserRating(userId, locationId) {
  return request({
    url: `/api/locations/user/${userId}/location/${locationId}`,
    method: 'get'
  });
}

/**
 * 收藏地点
 * @param {Object} data - 收藏数据
 * @param {number} data.location_id - 地点ID
 * @param {number} data.user_id - 用户ID
 * @returns {Promise} - 返回收藏结果的Promise
 */
export function favoriteLocation(data) {
  return request({
    url: '/api/location_favorite/favorite',
    method: 'post',
    data
  });
}

/**
 * 取消收藏地点
 * @param {Object} data - 取消收藏数据
 * @param {number} data.location_id - 地点ID
 * @param {number} data.user_id - 用户ID
 * @returns {Promise} - 返回取消收藏结果的Promise
 */
export function unfavoriteLocation(data) {
  return request({
    url: '/api/location_favorite/unfavorite',
    method: 'post',
    data
  });
}

/**
 * 检查地点是否已收藏
 * @param {Object} data - 检查数据
 * @param {number} data.location_id - 地点ID
 * @param {number} data.user_id - 用户ID
 * @returns {Promise} - 返回检查结果的Promise
 */
export function checkFavorite(data) {
  return request({
    url: '/api/location_favorite/check',
    method: 'post',
    data
  });
}

/**
 * 获取用户收藏的所有地点
 * @param {number} userId - 用户ID
 * @returns {Promise} - 返回用户收藏地点的Promise
 */
export function getUserFavorites(userId) {
  return request({
    url: `/api/location_favorite/user/${userId}`,
    method: 'get'
  });
}

/**
 * 搜索地点
 * @param {Object} params - 搜索参数
 * @param {string} params.keyword - 搜索关键词
 * @returns {Promise} - 返回搜索结果的Promise
 */
export function searchLocations(params) {
  return request({
    url: '/api/locations/search',
    method: 'get',
    params
  });
}

/**
 * 模糊搜索地点
 * @param {string} query - 搜索关键词
 * @param {number} limit - 返回结果数量限制
 * @returns {Promise} - 返回搜索结果的Promise
 */
export function fuzzySearchLocations(query, limit = 10) {
  return request({
    url: '/api/locations/fuzzy_search',
    method: 'get',
    params: { query, limit }
  });
}

// 默认导出对象，包含所有API函数
const locationApi = {
  getAllLocations,
  getLocationDetail,
  getLocationById,
  queryLocations,
  updateLocationBrowseCount,
  rateLocation,
  getUserRating,
  favoriteLocation,
  unfavoriteLocation,
  checkFavorite,
  getUserFavorites,
  searchLocations,
  fuzzySearch: fuzzySearchLocations
};

export default locationApi;
