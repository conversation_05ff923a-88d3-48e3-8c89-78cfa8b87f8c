"""
文本搜索工具类
实现BM25算法进行全文检索
"""

import math
import re
import jieba
from typing import List, Dict, Tuple, Any
from collections import defaultdict, Counter


class BM25:
    """
    BM25算法实现
    用于文档相关性评分和排序
    """
    
    def __init__(self, k1: float = 1.5, b: float = 0.75):
        """
        初始化BM25算法
        
        Args:
            k1: 控制词频饱和度的参数，通常取值1.2-2.0
            b: 控制文档长度归一化的参数，通常取值0.75
        """
        self.k1 = k1
        self.b = b
        self.documents = []
        self.doc_freqs = []
        self.idf = {}
        self.doc_len = []
        self.avgdl = 0
        
    def fit(self, documents: List[str]):
        """
        训练BM25模型
        
        Args:
            documents: 文档列表
        """
        self.documents = documents
        self.doc_freqs = []
        
        # 分词并计算词频
        for doc in documents:
            words = self._tokenize(doc)
            self.doc_freqs.append(Counter(words))
            self.doc_len.append(len(words))
        
        # 计算平均文档长度
        self.avgdl = sum(self.doc_len) / len(self.doc_len) if self.doc_len else 0
        
        # 计算IDF
        self._calculate_idf()
    
    def _tokenize(self, text: str) -> List[str]:
        """
        文本分词
        
        Args:
            text: 输入文本
            
        Returns:
            分词结果列表
        """
        # 移除标点符号和特殊字符
        text = re.sub(r'[^\w\s]', ' ', text)
        # 使用jieba分词
        words = jieba.lcut(text.lower())
        # 过滤空字符串和单字符
        return [word for word in words if len(word) > 1]
    
    def _calculate_idf(self):
        """计算IDF值"""
        N = len(self.documents)
        all_words = set()
        
        # 收集所有词汇
        for doc_freq in self.doc_freqs:
            all_words.update(doc_freq.keys())
        
        # 计算每个词的IDF
        for word in all_words:
            containing_docs = sum(1 for doc_freq in self.doc_freqs if word in doc_freq)
            self.idf[word] = math.log((N - containing_docs + 0.5) / (containing_docs + 0.5) + 1)
    
    def get_scores(self, query: str) -> List[float]:
        """
        计算查询与所有文档的BM25得分
        
        Args:
            query: 查询字符串
            
        Returns:
            得分列表
        """
        query_words = self._tokenize(query)
        scores = []
        
        for i, doc_freq in enumerate(self.doc_freqs):
            score = 0
            doc_len = self.doc_len[i]
            
            for word in query_words:
                if word in doc_freq:
                    # 计算BM25得分
                    tf = doc_freq[word]
                    idf = self.idf.get(word, 0)
                    
                    # BM25公式
                    numerator = tf * (self.k1 + 1)
                    denominator = tf + self.k1 * (1 - self.b + self.b * doc_len / self.avgdl)
                    score += idf * (numerator / denominator)
            
            scores.append(score)
        
        return scores
    
    def search(self, query: str, top_k: int = 10) -> List[Tuple[int, float]]:
        """
        搜索最相关的文档
        
        Args:
            query: 查询字符串
            top_k: 返回前k个结果
            
        Returns:
            (文档索引, 得分)的列表，按得分降序排列
        """
        scores = self.get_scores(query)
        # 按得分排序
        scored_docs = [(i, score) for i, score in enumerate(scores)]
        scored_docs.sort(key=lambda x: x[1], reverse=True)
        
        return scored_docs[:top_k]


class TextSearchEngine:
    """
    文本搜索引擎
    提供全文检索功能
    """
    
    def __init__(self):
        self.bm25 = BM25()
        self.articles = []
        self.article_contents = []
        
    def index_articles(self, articles: List[Dict[str, Any]]):
        """
        为文章建立索引
        
        Args:
            articles: 文章列表，每个文章包含id, title, content等字段
        """
        self.articles = articles
        self.article_contents = []
        
        # 提取文章内容用于索引
        for article in articles:
            # 合并标题和内容进行索引
            content = f"{article.get('title', '')} {article.get('content', '')}"
            self.article_contents.append(content)
        
        # 训练BM25模型
        self.bm25.fit(self.article_contents)
    
    def search(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        搜索文章
        
        Args:
            query: 搜索查询
            limit: 返回结果数量限制
            
        Returns:
            搜索结果列表
        """
        if not self.articles:
            return []
        
        # 使用BM25搜索
        results = self.bm25.search(query, limit)
        
        # 构建返回结果
        search_results = []
        for doc_idx, score in results:
            if score > 0:  # 只返回有相关性的结果
                article = self.articles[doc_idx].copy()
                article['search_score'] = score
                article['highlighted_content'] = self._highlight_keywords(
                    article.get('content', ''), query
                )
                search_results.append(article)
        
        return search_results
    
    def find_sentences_with_keywords(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """
        查找包含关键词的句子
        
        Args:
            query: 搜索查询
            limit: 返回结果数量限制
            
        Returns:
            包含关键词的句子列表
        """
        query_words = self.bm25._tokenize(query)
        sentences_with_keywords = []
        
        for i, article in enumerate(self.articles):
            content = article.get('content', '')
            title = article.get('title', '')
            
            # 按句号、问号、感叹号分割句子
            sentences = re.split(r'[。！？\n]', content)
            
            for sentence in sentences:
                sentence = sentence.strip()
                if len(sentence) < 5:  # 过滤太短的句子
                    continue
                
                # 检查句子是否包含查询词
                sentence_words = self.bm25._tokenize(sentence)
                if any(word in sentence_words for word in query_words):
                    sentences_with_keywords.append({
                        'article_id': article.get('article_id'),
                        'title': title,
                        'sentence': sentence,
                        'highlighted_sentence': self._highlight_keywords(sentence, query)
                    })
        
        # 按相关性排序（简单的关键词匹配数量）
        def relevance_score(item):
            sentence_words = self.bm25._tokenize(item['sentence'])
            return sum(1 for word in query_words if word in sentence_words)
        
        sentences_with_keywords.sort(key=relevance_score, reverse=True)
        return sentences_with_keywords[:limit]
    
    def _highlight_keywords(self, text: str, query: str) -> str:
        """
        在文本中高亮关键词
        
        Args:
            text: 原始文本
            query: 查询关键词
            
        Returns:
            高亮后的文本
        """
        query_words = self.bm25._tokenize(query)
        highlighted_text = text
        
        for word in query_words:
            # 使用HTML标记高亮关键词
            pattern = re.compile(re.escape(word), re.IGNORECASE)
            highlighted_text = pattern.sub(f'<mark>{word}</mark>', highlighted_text)
        
        return highlighted_text
