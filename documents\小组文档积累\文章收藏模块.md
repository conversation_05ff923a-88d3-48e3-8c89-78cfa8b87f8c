# 文章收藏模块 API 文档

本文档详细描述了 Flask 版本后端的文章收藏模块 API 接口及其在 Postman 中的测试用例。

## 目录

1. [添加文章到收藏](#添加文章到收藏)
2. [从收藏中移除文章](#从收藏中移除文章)
3. [获取用户的收藏列表](#获取用户的收藏列表)
4. [检查文章是否已收藏](#检查文章是否已收藏)

## 添加文章到收藏

### 接口信息

- **URL**: `/api/favorites/add`
- **方法**: `POST`
- **描述**: 添加文章到用户的收藏列表
- **请求体**: JSON

### 请求参数

| 参数名     | 类型    | 必填 | 描述    |
| ---------- | ------- | ---- | ------- |
| user_id    | integer | 是   | 用户 ID |
| article_id | integer | 是   | 文章 ID |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Article added to favorites successfully",
    "data": {}
}
```

#### 错误响应

- **参数缺失** (400 Bad Request)

```json
{
    "code": 1,
    "message": "Missing required parameters",
    "data": null
}
```

- **用户不存在** (404 Not Found)

```json
{
    "code": 1,
    "message": "User not found",
    "data": null
}
```

- **文章不存在** (404 Not Found)

```json
{
    "code": 1,
    "message": "Article not found",
    "data": null
}
```

- **文章已收藏** (400 Bad Request)

```json
{
    "code": 1,
    "message": "Article already in favorites",
    "data": null
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `POST`
4. 输入 URL: `http://localhost:5000/api/favorites/add`
5. 选择 Body 选项卡，选择 raw，设置类型为 JSON
6. 输入请求体:

```json
{
    "user_id": 1,
    "article_id": 1
}
```

7. 点击 Send 按钮发送请求

## 从收藏中移除文章

### 接口信息

- **URL**: `/api/favorites/remove`
- **方法**: `POST`
- **描述**: 从用户的收藏列表中移除文章
- **请求体**: JSON

### 请求参数

| 参数名     | 类型    | 必填 | 描述    |
| ---------- | ------- | ---- | ------- |
| user_id    | integer | 是   | 用户 ID |
| article_id | integer | 是   | 文章 ID |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Article removed from favorites successfully",
    "data": {}
}
```

#### 错误响应

- **参数缺失** (400 Bad Request)

```json
{
    "code": 1,
    "message": "Missing required parameters",
    "data": null
}
```

- **用户不存在** (404 Not Found)

```json
{
    "code": 1,
    "message": "User not found",
    "data": null
}
```

- **文章不存在** (404 Not Found)

```json
{
    "code": 1,
    "message": "Article not found",
    "data": null
}
```

- **文章未收藏** (404 Not Found)

```json
{
    "code": 1,
    "message": "Article not in favorites",
    "data": null
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `POST`
4. 输入 URL: `http://localhost:5000/api/favorites/remove`
5. 选择 Body 选项卡，选择 raw，设置类型为 JSON
6. 输入请求体:

```json
{
    "user_id": 1,
    "article_id": 1
}
```

7. 点击 Send 按钮发送请求

## 获取用户的收藏列表

### 接口信息

- **URL**: `/api/favorites/list/{user_id}`
- **方法**: `GET`
- **描述**: 获取用户的收藏文章列表
- **URL 参数**: user_id - 用户 ID

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Favorites retrieved successfully",
    "data": {
        "favorites": [
            {
                "article_id": 1,
                "title": "测试文章",
                "content": "这是一篇测试文章",
                "user_id": 2,
                "author": "testuser2",
                "location_id": 1,
                "location_name": "北京大学",
                "popularity": 10,
                "evaluation": 4.5,
                "image_url": "/uploads/images/image123.jpg",
                "video_url": "/uploads/videos/video123.mp4",
                "created_at": "2023-05-01T12:00:00Z",
                "updated_at": "2023-05-01T12:00:00Z",
                "favorited_at": "2023-05-02T12:00:00Z"
            }
        ]
    }
}
```

#### 错误响应

- **用户不存在** (404 Not Found)

```json
{
    "code": 1,
    "message": "User not found",
    "data": null
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `GET`
4. 输入 URL: `http://localhost:5000/api/favorites/list/1` (替换 `1` 为实际的用户 ID)
5. 点击 Send 按钮发送请求

## 检查文章是否已收藏

### 接口信息

- **URL**: `/api/favorites/check`
- **方法**: `POST`
- **描述**: 检查文章是否已被用户收藏
- **请求体**: JSON

### 请求参数

| 参数名     | 类型    | 必填 | 描述    |
| ---------- | ------- | ---- | ------- |
| user_id    | integer | 是   | 用户 ID |
| article_id | integer | 是   | 文章 ID |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Favorite status retrieved successfully",
    "data": {
        "is_favorited": true
    }
}
```

#### 错误响应

- **参数缺失** (400 Bad Request)

```json
{
    "code": 1,
    "message": "Missing required parameters",
    "data": null
}
```

- **用户不存在** (404 Not Found)

```json
{
    "code": 1,
    "message": "User not found",
    "data": null
}
```

- **文章不存在** (404 Not Found)

```json
{
    "code": 1,
    "message": "Article not found",
    "data": null
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `POST`
4. 输入 URL: `http://localhost:5000/api/favorites/check`
5. 选择 Body 选项卡，选择 raw，设置类型为 JSON
6. 输入请求体:

```json
{
    "user_id": 1,
    "article_id": 1
}
```

7. 点击 Send 按钮发送请求

## 文章收藏模型

文章收藏模型包含以下字段：

| 字段名     | 类型     | 描述         |
| ---------- | -------- | ------------ |
| user_id    | integer  | 用户 ID      |
| article_id | integer  | 文章 ID      |
| created_at | datetime | 收藏创建时间 |

## 文章收藏功能说明

1. **收藏关系**：用户和文章之间是多对多的关系，一个用户可以收藏多篇文章，一篇文章可以被多个用户收藏。
2. **收藏时间**：系统会记录用户收藏文章的时间，可用于按时间顺序显示收藏列表。
3. **收藏状态**：用户可以随时添加或移除收藏，系统会实时更新收藏状态。
4. **收藏列表**：用户可以查看自己的收藏列表，包含文章的基本信息和收藏时间。

## 完整的 Postman 测试流程

以下是一个完整的测试流程，按照顺序执行可以测试文章收藏模块的所有功能：

### 1. 添加文章到收藏

```
POST http://localhost:5000/api/favorites/add
Content-Type: application/json

{
    "user_id": 1,
    "article_id": 1
}
```

### 2. 检查文章是否已收藏

```
POST http://localhost:5000/api/favorites/check
Content-Type: application/json

{
    "user_id": 1,
    "article_id": 1
}
```

### 3. 获取用户的收藏列表

```
GET http://localhost:5000/api/favorites/list/1
```

### 4. 从收藏中移除文章

```
POST http://localhost:5000/api/favorites/remove
Content-Type: application/json

{
    "user_id": 1,
    "article_id": 1
}
```

### 5. 再次检查文章是否已收藏

```
POST http://localhost:5000/api/favorites/check
Content-Type: application/json

{
    "user_id": 1,
    "article_id": 1
}
```

## 代码实现

文章收藏模块的核心代码位于 `backend/routes/favorites.py` 文件中。以下是关键部分的代码实现：

### 添加文章到收藏

```python
@favorites_bp.route('/add', methods=['POST'])
def add_favorite():
    """
    Add article to favorites
    """
    try:
        data = request.get_json()

        # Validate required fields
        if not data or 'user_id' not in data or 'article_id' not in data:
            return error('Missing required parameters')

        user_id = data['user_id']
        article_id = data['article_id']

        # Check if user exists
        user = User.query.get(user_id)
        if not user:
            return error('User not found')

        # Check if article exists
        article = Article.query.get(article_id)
        if not article:
            return error('Article not found')

        # Check if already favorited
        favorite = ArticleFavorite.query.filter_by(
            user_id=user_id, article_id=article_id).first()

        if favorite:
            return error('Article already in favorites')

        # Add to favorites
        favorite = ArticleFavorite(user_id=user_id, article_id=article_id)
        db.session.add(favorite)
        db.session.commit()

        return success({}, 'Article added to favorites successfully')

    except Exception as e:
        db.session.rollback()
        return error(str(e))
```

## 注意事项

1. 用户只能收藏存在的文章，系统会检查文章是否存在。
2. 用户不能重复收藏同一篇文章，系统会检查文章是否已收藏。
3. 收藏列表按收藏时间倒序排列，最新收藏的文章排在前面。
4. 当文章被删除时，相关的收藏记录也会被自动删除（通过外键约束实现）。
5. 当用户被删除时，该用户的所有收藏记录也会被自动删除（通过外键约束实现）。