# 后端API总览

本文档提供个性化旅游推荐系统后端所有模块的功能概述和API路径总览。

## 系统架构

- **后端框架**: Flask (Python)
- **数据库**: MySQL
- **基础URL**: `http://localhost:5000`
- **响应格式**: JSON

## 模块概览

### 1. 用户认证模块 (`/api/auth`)

**功能**: 用户注册、登录、身份验证

| 功能     | HTTP方法 | API路径              | 描述         |
| -------- | -------- | -------------------- | ------------ |
| 用户注册 | POST     | `/api/auth/register` | 新用户注册   |
| 用户登录 | POST     | `/api/auth/login`    | 用户登录验证 |

### 2. 用户管理模块 (`/api/user`)

**功能**: 用户信息管理、头像上传、邮箱更新、密码修改

| 功能         | HTTP方法 | API路径                         | 描述               |
| ------------ | -------- | ------------------------------- | ------------------ |
| 获取用户信息 | GET      | `/api/user/{user_id}`           | 获取指定用户信息   |
| 更新用户头像 | POST     | `/api/user/avatar`              | 上传并更新用户头像 |
| 更新用户邮箱 | PUT      | `/api/user/email`               | 更新用户邮箱地址   |
| 更新用户密码 | PUT      | `/api/user/password`            | 修改用户密码       |
| 更新用户名   | PUT      | `/api/user/username`            | 修改用户名         |
| 获取用户收藏 | GET      | `/api/user/{user_id}/favorites` | 获取用户收藏的文章 |

### 3. 文章模块 (`/api/articles`)

**功能**: 文章发布、查看、编辑、删除、搜索，支持Huffman压缩

| 功能         | HTTP方法 | API路径                                       | 描述                                           |
| ------------ | -------- | --------------------------------------------- | ---------------------------------------------- |
| 发布文章     | POST     | `/api/articles`                               | 发布新文章（支持JSON和表单）                   |
| 获取文章详情 | GET      | `/api/articles/{article_id}`                  | 获取指定文章详情                               |
| 获取文章列表 | GET      | `/api/articles`                               | 获取文章列表（支持分页排序）                   |
| 更新文章     | PUT      | `/api/articles/{article_id}`                  | 更新文章内容                                   |
| 删除文章     | DELETE   | `/api/articles/{article_id}`                  | 删除文章                                       |
| 搜索文章     | GET      | `/api/articles/search`                        | 按标题或地点搜索文章                           |
| 模糊搜索     | GET      | `/api/articles/fuzzy_search`                  | 模糊搜索文章（AI模块用）                       |
| 协同过滤推荐 | POST     | `/api/articles/collaborative-recommendations` | 基于协同过滤的文章推荐                         |
| 为您推荐     | POST     | `/api/articles/for-you`                       | 智能推荐文章（登录用户用协同过滤，游客用热门） |
| 全文检索     | POST     | `/api/articles/full-text-search`              | 使用BM25算法进行全文检索                       |
| 搜索建议     | POST     | `/api/articles/search-suggestions`            | 获取包含关键词的句子建议                       |

### 4. 文章评分模块 (`/api/article_score`)

**功能**: 文章评分系统

| 功能         | HTTP方法 | API路径                           | 描述             |
| ------------ | -------- | --------------------------------- | ---------------- |
| 评分文章     | POST     | `/api/article_score/rate`         | 对文章进行评分   |
| 获取文章评分 | GET      | `/api/article_score/{article_id}` | 获取文章评分信息 |

### 5. 文章收藏模块 (`/api/favorites`)

**功能**: 文章收藏和取消收藏

| 功能         | HTTP方法 | API路径                 | 描述           |
| ------------ | -------- | ----------------------- | -------------- |
| 收藏文章     | POST     | `/api/favorites/add`    | 收藏文章       |
| 取消收藏     | DELETE   | `/api/favorites/remove` | 取消收藏文章   |
| 检查收藏状态 | GET      | `/api/favorites/check`  | 检查是否已收藏 |

### 6. 文章评论模块 (`/api/article_comment`)

**功能**: 文章评论系统

| 功能         | HTTP方法 | API路径                             | 描述             |
| ------------ | -------- | ----------------------------------- | ---------------- |
| 添加评论     | POST     | `/api/article_comment/add`          | 添加文章评论     |
| 获取评论列表 | GET      | `/api/article_comment/{article_id}` | 获取文章评论列表 |
| 删除评论     | DELETE   | `/api/article_comment/{comment_id}` | 删除评论         |

### 7. 文章点赞模块 (`/api/article_like`)

**功能**: 文章点赞系统

| 功能         | HTTP方法 | API路径                    | 描述           |
| ------------ | -------- | -------------------------- | -------------- |
| 点赞文章     | POST     | `/api/article_like/like`   | 点赞文章       |
| 取消点赞     | DELETE   | `/api/article_like/unlike` | 取消点赞       |
| 检查点赞状态 | GET      | `/api/article_like/check`  | 检查是否已点赞 |

### 8. 地点模块 (`/api/locations`)

**功能**: 地点信息管理、浏览记录、评分

| 功能         | HTTP方法 | API路径                               | 描述             |
| ------------ | -------- | ------------------------------------- | ---------------- |
| 获取所有地点 | GET      | `/api/locations`                      | 获取所有地点信息 |
| 获取地点详情 | GET      | `/api/locations/{location_id}`        | 获取指定地点详情 |
| 搜索地点     | GET      | `/api/locations/search`               | 按名称搜索地点   |
| 记录浏览     | POST     | `/api/locations/{location_id}/browse` | 记录地点浏览     |
| 评分地点     | POST     | `/api/locations/{location_id}/rate`   | 对地点评分       |
| 获取地点评分 | GET      | `/api/locations/{location_id}/rating` | 获取地点评分     |

### 9. 地点收藏模块 (`/api/location-favorites`, `/api/location_favorite`)

**功能**: 地点收藏管理

| 功能         | HTTP方法 | API路径                             | 描述               |
| ------------ | -------- | ----------------------------------- | ------------------ |
| 收藏地点     | POST     | `/api/location-favorites/add`       | 收藏地点           |
| 取消收藏     | DELETE   | `/api/location-favorites/remove`    | 取消收藏地点       |
| 获取收藏列表 | GET      | `/api/location-favorites/{user_id}` | 获取用户收藏的地点 |

### 10. 路径规划模块 (`/api/path`)

**功能**: 单目的地和多目的地路径规划，使用Dijkstra和TSP算法

| 功能         | HTTP方法 | API路径                        | 描述                   |
| ------------ | -------- | ------------------------------ | ---------------------- |
| 获取所有顶点 | GET      | `/api/path/vertices`           | 获取所有地点顶点       |
| 获取所有边   | GET      | `/api/path/edges`              | 获取所有路径边         |
| 获取顶点信息 | GET      | `/api/path/vertex/{vertex_id}` | 获取单个顶点详情       |
| 批量获取顶点 | GET/POST | `/api/path/vertices-by-ids`    | 批量获取顶点信息       |
| 路径规划     | POST     | `/api/path/plan`               | 单/多目的地路径规划    |
| 获取附近景点 | GET/POST | `/api/path/spots`              | 获取附近景点           |
| 获取所有顶点 | GET      | `/api/path/all-vertices`       | 获取所有顶点（无过滤） |

### 11. 推荐模块 (`/api/recommend`)

**功能**: 基础推荐功能（重定向到高级推荐）

| 功能       | HTTP方法 | API路径                        | 描述           |
| ---------- | -------- | ------------------------------ | -------------- |
| 为您推荐   | POST     | `/api/recommend`               | 个性化推荐     |
| 热门地点   | GET      | `/api/recommend/popular`       | 热门地点推荐   |
| 高评分地点 | GET      | `/api/recommend/rating`        | 高评分地点推荐 |
| 协同过滤   | POST     | `/api/recommend/collaborative` | 协同过滤推荐   |
| 内容推荐   | POST     | `/api/recommend/content`       | 基于内容推荐   |
| 混合推荐   | POST     | `/api/recommend/hybrid`        | 混合推荐算法   |

### 12. 高级推荐模块 (`/api/advanced-recommend`)

**功能**: 高效推荐算法实现，使用堆数据结构优化

| 功能       | HTTP方法 | API路径                                 | 描述                 |
| ---------- | -------- | --------------------------------------- | -------------------- |
| 为您推荐   | POST     | `/api/advanced-recommend/for-you`       | 个性化推荐           |
| 热门地点   | GET      | `/api/advanced-recommend/popular`       | 热门地点推荐         |
| 高评分地点 | GET      | `/api/advanced-recommend/top-rated`     | 高评分地点推荐       |
| 协同过滤   | POST     | `/api/advanced-recommend/collaborative` | 协同过滤推荐         |
| 内容推荐   | POST     | `/api/advanced-recommend/content`       | 基于内容推荐         |
| 混合推荐   | POST     | `/api/advanced-recommend/hybrid`        | 混合推荐算法         |
| 所有地点   | GET/POST | `/api/advanced-recommend/all-locations` | 按热度排序的所有地点 |

### 13. 美食推荐模块 (`/api/food`)

**功能**: 餐厅和美食推荐，使用高效排序算法

| 功能         | HTTP方法 | API路径                                   | 描述               |
| ------------ | -------- | ----------------------------------------- | ------------------ |
| 获取推荐餐厅 | GET      | `/api/food/recommendations`               | 获取个性化餐厅推荐 |
| 获取热门美食 | GET      | `/api/food/popular`                       | 获取热门餐厅       |
| 获取特色美食 | GET      | `/api/food/special`                       | 获取特色餐厅       |
| 搜索餐厅     | GET      | `/api/food/search`                        | 按关键词搜索餐厅   |
| 获取附近美食 | GET      | `/api/food/nearby`                        | 获取附近餐厅       |
| 获取菜系类型 | GET      | `/api/food/cuisine-types`                 | 获取所有菜系类型   |
| 餐厅名称建议 | GET      | `/api/food/suggestions`                   | 获取餐厅名称建议   |
| 协同过滤推荐 | GET      | `/api/food/collaborative-recommendations` | 基于协同过滤的推荐 |
| 模糊搜索菜系 | GET      | `/api/food/fuzzy_search_cuisine`          | 模糊搜索菜系       |
| 模糊搜索餐厅 | GET      | `/api/food/fuzzy_search_restaurants`      | 模糊搜索餐厅       |
| 获取餐厅详情 | GET      | `/api/food/restaurant/{restaurant_id}`    | 获取指定餐厅详情   |
| 餐厅评分     | POST     | `/api/food/rate`                          | 对餐厅进行评分     |
| 获取餐厅评分 | GET      | `/api/food/{restaurant_id}/rating`        | 获取餐厅评分信息   |

### 14. 餐厅用户交互模块 (`/api/restaurant`)

**功能**: 餐厅收藏、评论、评分

| 功能         | HTTP方法 | API路径                                    | 描述               |
| ------------ | -------- | ------------------------------------------ | ------------------ |
| 收藏餐厅     | POST     | `/api/restaurant/favorite`                 | 收藏餐厅           |
| 取消收藏     | DELETE   | `/api/restaurant/unfavorite`               | 取消收藏餐厅       |
| 检查收藏状态 | GET      | `/api/restaurant/check-favorite`           | 检查是否已收藏     |
| 获取收藏列表 | GET      | `/api/restaurant/favorites/{user_id}`      | 获取用户收藏的餐厅 |
| 评论餐厅     | POST     | `/api/restaurant/comment`                  | 评论餐厅           |
| 获取餐厅评论 | GET      | `/api/restaurant/{restaurant_id}/comments` | 获取餐厅评论       |
| 餐厅评分     | POST     | `/api/restaurant/rating`                   | 对餐厅评分         |
| 获取餐厅评分 | GET      | `/api/restaurant/rating/{restaurant_id}`   | 获取餐厅评分       |

### 15. 购物车模块 (`/api/cart`)

**功能**: 购物车管理（用于美食订购）

| 功能         | HTTP方法 | API路径                     | 描述             |
| ------------ | -------- | --------------------------- | ---------------- |
| 添加到购物车 | POST     | `/api/cart/add`             | 添加商品到购物车 |
| 获取购物车   | GET      | `/api/cart/{user_id}`       | 获取用户购物车   |
| 更新购物车   | PUT      | `/api/cart/update`          | 更新购物车商品   |
| 删除商品     | DELETE   | `/api/cart/remove`          | 从购物车删除商品 |
| 清空购物车   | DELETE   | `/api/cart/clear/{user_id}` | 清空购物车       |

### 16. AI生成模块 (`/api/ai`)

**功能**: AI内容生成，包括地点描述、旅行计划、美食推荐等

| 功能             | HTTP方法 | API路径                                   | 描述             |
| ---------------- | -------- | ----------------------------------------- | ---------------- |
| 生成地点描述     | POST     | `/api/ai/generate_location_description`   | 生成地点描述     |
| 生成旅行计划     | POST     | `/api/ai/generate_travel_plan`            | 生成旅行计划     |
| 生成推荐理由     | POST     | `/api/ai/generate_recommendation_reason`  | 生成推荐理由     |
| 生成日记动画     | POST     | `/api/ai/generate_diary_animation`        | 生成旅行日记动画 |
| 生成简单日记动画 | POST     | `/api/ai/generate_simple_diary_animation` | 生成简单日记动画 |
| 生成美食推荐     | POST     | `/api/ai/generate_food_recommendation`    | 智能美食推荐     |
| 生成图片         | POST     | `/api/ai/generate_image`                  | AI图片生成       |
| 生成文章总结     | POST     | `/api/ai/generate_article_summary`        | 生成文章总结     |
| 生成路线描述     | POST     | `/api/ai/generate_route_description`      | 生成路线描述     |

### 17. 文件上传模块 (`/api/upload`)

**功能**: 文件上传管理，支持图片、视频、头像等

| 功能         | HTTP方法 | API路径                        | 描述         |
| ------------ | -------- | ------------------------------ | ------------ |
| 上传图片     | POST     | `/api/upload/image`            | 上传图片文件 |
| 上传视频     | POST     | `/api/upload/video`            | 上传视频文件 |
| 上传头像     | POST     | `/api/upload/avatar`           | 上传用户头像 |
| 上传地点图片 | POST     | `/api/upload/location-image`   | 上传地点图片 |
| 上传餐厅图片 | POST     | `/api/upload/restaurant-image` | 上传餐厅图片 |
| 上传菜品图片 | POST     | `/api/upload/dish-image`       | 上传菜品图片 |

## 技术特性

### 1. 数据压缩
- **Huffman编码**: 文章内容使用Huffman编码压缩，节省存储空间

### 2. 推荐算法优化
- **堆数据结构**: 使用堆优化热门内容排序，时间复杂度从O(n log n)降至O(n log k)
- **增量更新**: 支持实时更新推荐结果
- **多种算法**: 支持协同过滤、内容推荐、混合推荐

### 3. 路径规划算法
- **Dijkstra算法**: 单目的地最短路径
- **TSP算法**: 多目的地路径优化（Held-Karp、模拟退火）
- **多种策略**: 最短距离、最短时间、可骑行优先

### 4. 文件管理
- **多格式支持**: 支持图片（png, jpg, jpeg, gif, webp）和视频（mp4, webm, ogg, mov）
- **安全上传**: 文件类型验证和安全文件名处理
- **分类存储**: 按类型分目录存储文件

### 5. 响应格式统一
- **标准响应**: 统一的JSON响应格式
- **错误处理**: 完善的错误处理和状态码
- **日志记录**: 详细的操作日志记录

## 注意事项

1. **认证**: 部分API需要用户登录认证
2. **权限**: 某些操作需要验证用户权限（如删除文章需要是作者）
3. **文件大小**: 上传文件有大小限制
4. **并发**: 系统支持并发访问，使用数据库事务保证数据一致性
5. **缓存**: 推荐结果支持缓存机制提高性能

## 开发和测试

- **测试工具**: 推荐使用Postman进行API测试
- **文档**: 每个模块都有详细的API文档和测试用例
- **日志**: 系统提供详细的日志记录便于调试
