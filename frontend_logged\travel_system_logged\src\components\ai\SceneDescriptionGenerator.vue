<template>
  <div class="scene-description-generator">
    <div class="generator-header">
      <h2>智能场景描述生成</h2>
      <p>根据提示词或图片生成详细的场景描述，用于动画制作或内容创作</p>
    </div>

    <div class="form-section">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
        <!-- 场景提示词 -->
        <el-form-item label="场景描述" prop="prompt">
          <el-input
            v-model="form.prompt"
            type="textarea"
            :rows="4"
            placeholder="请输入场景描述，如：北京长城日落、故宫雪景、西湖春色等"
            maxlength="500"
            show-word-limit
          />
          <div class="prompt-examples">
            <span>示例：</span>
            <el-tag
              v-for="example in promptExamples"
              :key="example"
              @click="setPrompt(example)"
              style="margin: 5px; cursor: pointer;"
              size="small"
            >
              {{ example }}
            </el-tag>
          </div>
        </el-form-item>

        <!-- 图片上传 -->
        <el-form-item label="参考图片">
          <el-upload
            ref="uploadRef"
            :file-list="fileList"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            :before-upload="beforeUpload"
            :auto-upload="false"
            accept="image/*"
            list-type="picture-card"
            :limit="3"
          >
            <i class="el-icon-plus"></i>
            <template #tip>
              <div class="el-upload__tip">
                支持 jpg/png 格式，最多上传3张图片，每张不超过5MB
              </div>
            </template>
          </el-upload>
        </el-form-item>

        <!-- 描述风格 -->
        <el-form-item label="描述风格">
          <el-radio-group v-model="form.style">
            <el-radio label="写实">写实风格</el-radio>
            <el-radio label="浪漫">浪漫风格</el-radio>
            <el-radio label="史诗">史诗风格</el-radio>
            <el-radio label="简约">简约风格</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 生成按钮 -->
        <el-form-item>
          <el-button
            type="primary"
            @click="generateDescription"
            :loading="loading"
            size="large"
            style="width: 200px"
          >
            <i class="el-icon-picture"></i>
            {{ loading ? '生成中...' : '生成描述' }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 生成结果 -->
    <div v-if="generatedDescription" class="result-section">
      <div class="result-header">
        <h3>生成的场景描述</h3>
        <div class="result-actions">
          <el-button @click="copyToClipboard" size="small">
            <i class="el-icon-document-copy"></i>
            复制描述
          </el-button>
          <el-button @click="regenerateDescription" size="small" type="primary">
            <i class="el-icon-refresh"></i>
            重新生成
          </el-button>
        </div>
      </div>

      <div class="description-content">
        <div class="description-text">
          <MarkdownRenderer :content="generatedDescription" />
        </div>
        <div class="description-meta">
          <span>字数: {{ generatedDescription.length }}</span>
          <span>风格: {{ form.style }}</span>
          <span v-if="generationTime">生成时间: {{ generationTime }}秒</span>
        </div>
      </div>
    </div>

    <!-- 历史记录 -->
    <div v-if="historyDescriptions.length > 0" class="history-section">
      <h3>最近生成的描述</h3>
      <div class="history-list">
        <div
          v-for="(item, index) in historyDescriptions"
          :key="index"
          class="history-item"
          @click="loadHistoryItem(item)"
        >
          <div class="history-prompt">{{ item.prompt }}</div>
          <div class="history-preview">{{ item.description.substring(0, 100) }}...</div>
          <div class="history-time">{{ formatTime(item.timestamp) }}</div>
        </div>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="error-section">
      <el-alert
        :title="error"
        type="error"
        :closable="false"
        show-icon
      />
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import aiApi from '@/api/ai'
import MarkdownRenderer from '@/components/common/MarkdownRenderer.vue'

export default {
  name: 'SceneDescriptionGenerator',
  components: {
    MarkdownRenderer
  },
  emits: ['generated'],
  setup(props, { emit }) {
    const formRef = ref(null)
    const uploadRef = ref(null)
    const loading = ref(false)
    const error = ref('')
    const generatedDescription = ref('')
    const generationTime = ref(0)
    const fileList = ref([])
    const historyDescriptions = ref([])

    const form = reactive({
      prompt: '',
      style: '写实'
    })

    const rules = {
      prompt: [
        { required: true, message: '请输入场景描述', trigger: 'blur' },
        { min: 2, max: 500, message: '场景描述长度在 2 到 500 个字符', trigger: 'blur' }
      ]
    }

    const promptExamples = [
      '北京长城日落',
      '故宫雪景',
      '西湖春色',
      '泰山日出',
      '九寨沟秋色',
      '桂林山水',
      '黄山云海',
      '丽江古城夜景'
    ]

    // 设置提示词
    const setPrompt = (prompt) => {
      form.prompt = prompt
    }

    // 文件上传前检查
    const beforeUpload = (file) => {
      const isImage = file.type.startsWith('image/')
      const isLt5M = file.size / 1024 / 1024 < 5

      if (!isImage) {
        ElMessage.error('只能上传图片文件!')
        return false
      }
      if (!isLt5M) {
        ElMessage.error('图片大小不能超过 5MB!')
        return false
      }
      return false // 阻止自动上传
    }

    // 文件变化处理
    const handleFileChange = (file, files) => {
      fileList.value = files
    }

    // 文件移除处理
    const handleFileRemove = (file, files) => {
      fileList.value = files
    }

    // 生成场景描述
    const generateDescription = async () => {
      try {
        await formRef.value.validate()

        const startTime = Date.now()
        loading.value = true
        error.value = ''
        generatedDescription.value = ''

        // 创建FormData
        const formData = new FormData()
        formData.append('prompt', `${form.prompt}（${form.style}风格）`)

        // 添加图片文件
        fileList.value.forEach((file) => {
          if (file.raw) {
            formData.append('images', file.raw)
          }
        })

        const response = await aiApi.generateSceneDescription(formData)

        if (response.status === 'generated' || response.status === 'generated_fallback') {
          generatedDescription.value = response.description
          generationTime.value = ((Date.now() - startTime) / 1000).toFixed(1)

          // 添加到历史记录
          addToHistory(form.prompt, generatedDescription.value)

          emit('generated', generatedDescription.value)
          ElMessage.success('场景描述生成成功！')

          if (response.status === 'generated_fallback') {
            ElMessage.warning('使用了备用生成方案')
          }
        } else {
          error.value = response.message || '生成失败'
          ElMessage.error(error.value)
        }
      } catch (error) {
        console.error('生成场景描述失败:', error)
        error.value = '生成失败，请稍后重试'
        ElMessage.error('生成失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }

    // 重新生成描述
    const regenerateDescription = () => {
      generateDescription()
    }

    // 复制到剪贴板
    const copyToClipboard = async () => {
      try {
        await navigator.clipboard.writeText(generatedDescription.value)
        ElMessage.success('描述已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
        ElMessage.error('复制失败')
      }
    }

    // 添加到历史记录
    const addToHistory = (prompt, description) => {
      const historyItem = {
        prompt,
        description,
        timestamp: new Date(),
        style: form.style
      }

      historyDescriptions.value.unshift(historyItem)

      // 限制历史记录数量
      if (historyDescriptions.value.length > 5) {
        historyDescriptions.value = historyDescriptions.value.slice(0, 5)
      }

      // 保存到本地存储
      localStorage.setItem('scene_description_history', JSON.stringify(historyDescriptions.value))
    }

    // 加载历史记录项
    const loadHistoryItem = (item) => {
      form.prompt = item.prompt
      form.style = item.style
      generatedDescription.value = item.description
    }

    // 格式化时间
    const formatTime = (timestamp) => {
      const date = new Date(timestamp)
      return date.toLocaleString('zh-CN')
    }

    // 加载历史记录
    const loadHistory = () => {
      try {
        const saved = localStorage.getItem('scene_description_history')
        if (saved) {
          historyDescriptions.value = JSON.parse(saved)
        }
      } catch (error) {
        console.error('加载历史记录失败:', error)
      }
    }

    onMounted(() => {
      loadHistory()
    })

    return {
      formRef,
      uploadRef,
      form,
      rules,
      loading,
      error,
      generatedDescription,
      generationTime,
      fileList,
      historyDescriptions,
      promptExamples,
      setPrompt,
      beforeUpload,
      handleFileChange,
      handleFileRemove,
      generateDescription,
      regenerateDescription,
      copyToClipboard,
      loadHistoryItem,
      formatTime
    }
  }
}
</script>

<style scoped>
.scene-description-generator {
  max-width: 800px;
  margin: 0 auto;
}

.generator-header {
  text-align: center;
  margin-bottom: 40px;
}

.generator-header h2 {
  color: #333;
  margin-bottom: 10px;
}

.generator-header p {
  color: #666;
  font-size: 1rem;
}

.form-section {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 15px;
  margin-bottom: 30px;
}

.prompt-examples {
  margin-top: 10px;
  color: #666;
  font-size: 0.9rem;
}

.prompt-examples span {
  margin-right: 10px;
}

.result-section {
  margin-top: 30px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e9ecef;
}

.result-actions {
  display: flex;
  gap: 10px;
}

.description-content {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 15px;
}

.description-text {
  background: white;
  padding: 25px;
  border-radius: 10px;
  line-height: 1.8;
  color: #333;
  font-size: 1.1rem;
  margin-bottom: 20px;
  border-left: 4px solid #6f42c1;
}

.description-meta {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
  color: #666;
  padding: 0 10px;
}

.history-section {
  margin-top: 40px;
  background: #f8f9fa;
  padding: 25px;
  border-radius: 15px;
}

.history-section h3 {
  color: #333;
  margin-bottom: 20px;
}

.history-list {
  display: grid;
  gap: 15px;
}

.history-item {
  background: white;
  padding: 20px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.history-item:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.history-prompt {
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.history-preview {
  color: #666;
  line-height: 1.6;
  margin-bottom: 8px;
}

.history-time {
  font-size: 0.8rem;
  color: #999;
}

.error-section {
  margin-top: 20px;
}

@media (max-width: 768px) {
  .result-header {
    flex-direction: column;
    gap: 15px;
  }

  .result-actions {
    width: 100%;
    justify-content: center;
  }

  .description-meta {
    flex-direction: column;
    gap: 5px;
    text-align: center;
  }
}
</style>
