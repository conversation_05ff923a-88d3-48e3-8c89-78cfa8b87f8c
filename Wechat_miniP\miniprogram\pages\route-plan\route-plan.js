// route-plan.js
Component({
  data: {
    // 地图相关
    mapCenter: {
      longitude: 116.3588,
      latitude: 39.9615
    },
    mapScale: 16,
    markers: [],
    polylines: [],

    // 地点数据
    locations: [],
    locationNames: [],

    // 选择的地点
    startLocationIndex: -1,
    endLocationIndex: -1,
    startLocationName: '',
    endLocationName: '',
    selectedStartId: null,
    selectedEndId: null,

    // 搜索相关
    startSearchText: '',
    endSearchText: '',
    filteredStartLocations: [],
    filteredEndLocations: [],
    showStartSuggestions: false,
    showEndSuggestions: false,

    // 策略选择
    selectedTransportMode: 'walking',
    selectedStrategy: 0,

    // 路线规划
    isLoading: false,
    routeResult: null,

    // 计算属性
    canPlan: false
  },

  pageLifetimes: {
    show: function() {
      if (typeof this.getTabBar === 'function' && this.getTabBar()) {
        this.getTabBar().setData({
          selected: 2
        });
      }
      this.loadLocations();
    }
  },

  methods: {
    // 加载地点数据
    loadLocations: function() {
      const that = this;
      wx.showLoading({ title: '加载地点数据...' });

      wx.request({
        url: 'http://localhost:5000/api/path/vertices',
        method: 'GET',
        success: function(res) {
          if (res.data && Array.isArray(res.data)) {
            const locations = res.data;
            const locationNames = locations.map(function(loc) {
              return loc.label;
            });

            that.setData({
              locations: locations,
              locationNames: locationNames
            });

            wx.showToast({
              title: '已加载' + locations.length + '个地点',
              icon: 'success'
            });
          } else {
            wx.showModal({
              title: '数据加载失败',
              content: '服务器返回的数据格式不正确',
              showCancel: false
            });
          }
        },
        fail: function(error) {
          console.error('加载地点数据失败:', error);
          wx.showModal({
            title: '加载失败',
            content: '地点数据加载失败，请检查网络连接',
            showCancel: false
          });
        },
        complete: function() {
          wx.hideLoading();
        }
      });
    },

    // 坐标转换函数
    calculateCoordinates: function(x, y) {
      const longitude = 116.35526546085191 + 0.000010265146964114 * x;
      const latitude = 39.95804755710804 + 0.0000091118507731447 * y;
      return {
        lng: longitude,
        lat: latitude
      };
    },

    // 起点搜索输入
    onStartSearchInput: function(e) {
      const value = e.detail.value;
      this.setData({ startSearchText: value });
      this.filterStartLocations(value);
    },

    // 起点搜索获得焦点
    onStartSearchFocus: function() {
      this.setData({ showStartSuggestions: true });
      if (this.data.startSearchText) {
        this.filterStartLocations(this.data.startSearchText);
      }
    },

    // 起点搜索失去焦点
    onStartSearchBlur: function() {
      const that = this;
      setTimeout(function() {
        that.setData({ showStartSuggestions: false });
      }, 200);
    },

    // 过滤起点位置
    filterStartLocations: function(searchText) {
      if (!searchText.trim()) {
        this.setData({ filteredStartLocations: [] });
        return;
      }

      const searchLower = searchText.toLowerCase().trim();
      const filtered = this.data.locations.filter(function(location) {
        return location.label && location.label.toLowerCase().includes(searchLower);
      }).slice(0, 10);

      this.setData({ filteredStartLocations: filtered });
    },

    // 选择起点
    selectStartLocation: function(e) {
      const location = e.currentTarget.dataset.location;

      this.setData({
        selectedStartId: location.vertex_id,
        startSearchText: location.label,
        startLocationName: location.label,
        showStartSuggestions: false
      });

      this.updateCanPlan();
    },

    // 终点搜索输入
    onEndSearchInput: function(e) {
      const value = e.detail.value;
      this.setData({ endSearchText: value });
      this.filterEndLocations(value);
    },

    // 终点搜索获得焦点
    onEndSearchFocus: function() {
      this.setData({ showEndSuggestions: true });
      if (this.data.endSearchText) {
        this.filterEndLocations(this.data.endSearchText);
      }
    },

    // 终点搜索失去焦点
    onEndSearchBlur: function() {
      const that = this;
      setTimeout(function() {
        that.setData({ showEndSuggestions: false });
      }, 200);
    },

    // 过滤终点位置
    filterEndLocations: function(searchText) {
      if (!searchText.trim()) {
        this.setData({ filteredEndLocations: [] });
        return;
      }

      const searchLower = searchText.toLowerCase().trim();
      const filtered = this.data.locations.filter(function(location) {
        return location.label && location.label.toLowerCase().includes(searchLower);
      }).slice(0, 10);

      this.setData({ filteredEndLocations: filtered });
    },

    // 选择终点
    selectEndLocation: function(e) {
      const location = e.currentTarget.dataset.location;

      this.setData({
        selectedEndId: location.vertex_id,
        endSearchText: location.label,
        endLocationName: location.label,
        showEndSuggestions: false
      });

      this.updateCanPlan();
    },

    // 更新是否可以规划路线
    updateCanPlan: function() {
      const canPlan = this.data.selectedStartId && this.data.selectedEndId;
      this.setData({ canPlan: canPlan });
    },

    // 交通方式选择
    onTransportModeChange: function(e) {
      this.setData({ selectedTransportMode: e.detail.value });
    },

    // 策略选择
    onStrategyChange: function(e) {
      this.setData({ selectedStrategy: e.detail.value });
    },

    // 开始路线规划
    startPlanning: function() {
      if (!this.data.canPlan) {
        wx.showToast({
          title: '请选择起点和终点',
          icon: 'none'
        });
        return;
      }

      const that = this;
      this.setData({ isLoading: true });

      const requestData = {
        start_vertex_id: this.data.selectedStartId,
        end_vertex_id: this.data.selectedEndId,
        strategy: this.data.selectedStrategy === 0 ? 'shortest_distance' : 'shortest_time'
      };

      wx.request({
        url: 'http://localhost:5000/api/path/plan-route',
        method: 'POST',
        data: requestData,
        success: function(res) {
          if (res.data.success) {
            that.handleRouteSuccess(res.data.data);
          } else {
            wx.showToast({
              title: '路线规划失败',
              icon: 'none'
            });
          }
        },
        fail: function(error) {
          console.error('路线规划失败:', error);
          wx.showToast({
            title: '网络错误',
            icon: 'none'
          });
        },
        complete: function() {
          that.setData({ isLoading: false });
        }
      });
    },

    // 处理路线规划成功
    handleRouteSuccess: function(data) {
      console.log('路线规划成功:', data);
      
      const routeResult = {
        totalDistance: this.formatDistance(data.total_distance || 0),
        totalTime: this.formatDuration(data.total_time || 0),
        pathDetails: data.path || []
      };

      this.setData({ routeResult: routeResult });

      wx.showToast({
        title: '路线规划完成',
        icon: 'success'
      });
    },

    // 格式化距离
    formatDistance: function(distance) {
      if (distance < 1000) {
        return Math.round(distance) + ' 米';
      } else {
        return (distance / 1000).toFixed(2) + ' 公里';
      }
    },

    // 格式化时间
    formatDuration: function(minutes) {
      if (minutes < 60) {
        return Math.round(minutes) + ' 分钟';
      } else {
        const hours = Math.floor(minutes / 60);
        const remainingMinutes = Math.round(minutes % 60);
        if (remainingMinutes === 0) {
          return hours + ' 小时';
        } else {
          return hours + ' 小时 ' + remainingMinutes + ' 分钟';
        }
      }
    }
  }
})
