import axios from 'axios'

/**
 * 多条件查询景点
 * @param {Object} params - 查询参数
 * @param {number} [params.startVertexId] - 起始顶点ID
 * @param {string} [params.locationName] - 起始地点名称
 * @param {number} params.distance - 搜索距离（米）
 * @param {string|number} [params.typeVal] - 场所类型
 * @param {string[]} [params.keywords] - 关键词列表
 * @param {number} [params.limit=15] - 返回结果数量
 * @returns {Promise} 包含场所数据的Promise
 */
export const getSpotsByCriteria = async (params) => {
  const requestBody = {
    start_vertex_id: params.startVertexId,
    location_name: params.locationName,
    distance: params.distance,
    types: params.typeVal ? [params.typeVal] : [],
    keywords: params.keywords || [],
    limit: params.limit || 15
  }
  return axios.post('/api/path/spots-by-criteria', requestBody)
}
export function getAllTypes() {
  return axios.get('/api/path/all-types')
}
/**
 * 基于路径的附近场所查询
 * @param {Object} params - 查询参数
 * @param {number} [params.startVertexId] - 起始顶点ID
 * @param {string} [params.locationName] - 起始地点名称
 * @param {number} params.maxDistance - 最大距离（米）
 * @param {number} [params.strategy=0] - 路径策略
 * @returns {Promise} 包含场所数据的Promise
 */
export const getNearbySpotsByPath = async (params) => {
  const requestBody = {
    start_vertex_id: params.startVertexId,
    location_name: params.locationName,
    distance: params.maxDistance,
    strategy: params.strategy || 0
  }
  return axios.post('/api/path/nearby-spots', requestBody)
}

/**
 * 按名称搜索场所
 * @param {Object} params - 查询参数
 * @param {string} params.name - 场所名称关键词
 * @param {number} [params.type] - 场所类型
 * @param {number} [params.x] - 坐标X
 * @param {number} [params.y] - 坐标Y
 * @returns {Promise} 包含场所数据的Promise
 */
export const searchSpotsByName = async (params) => {
  return axios.get('/api/path/search-by-name', {
    params: {
      name: params.name,
      type: params.type,
      x: params.x,
      y: params.y
    }
  })
}

/**
 * 获取地点建议
 * @param {string} query - 搜索关键词
 * @param {number} [limit=10] - 返回建议数量
 * @returns {Promise} 包含建议数据的Promise
 */
export const getLocationSuggestions = async (query, limit = 10) => {
  return axios.get('/api/path/location-suggestions', {
    params: {
      q: query,
      limit: limit
    }
  })
}

// ===== 朝阳公园相关API =====

/**
 * 获取朝阳公园所有地点
 * @returns {Promise} 包含地点数据的Promise
 */
export const getChaoyangParkVertices = async () => {
  return axios.get('/api/path/chaoyang-park/vertices')
}

/**
 * 按类型获取朝阳公园地点
 * @param {string} [type] - 地点类型
 * @returns {Promise} 包含地点数据的Promise
 */
export const getChaoyangParkVerticesByType = async (type = null) => {
  const params = type ? { type } : {}
  return axios.get('/api/path/chaoyang-park/vertices/by-type', { params })
}

/**
 * 获取朝阳公园所有地点类型
 * @returns {Promise} 包含类型数据的Promise
 */
export const getChaoyangParkTypes = async () => {
  return axios.get('/api/path/chaoyang-park/types')
}

/**
 * 获取朝阳公园指定地点信息
 * @param {number} vertexId - 地点ID
 * @returns {Promise} 包含地点详情的Promise
 */
export const getChaoyangParkVertex = async (vertexId) => {
  return axios.get(`/api/path/chaoyang-park/vertex/${vertexId}`)
}

export function getAllVertices() {
  return axios.get('/api/path/vertices')  
}
/**
 * 按距离获取所有地点
 * @param {Object} params - 查询参数
 * @param {number} params.startVertexId - 起始顶点ID
 * @param {number} params.distance - 距离（米）
 * @param {number} [limit=115] - 返回建议数量
 * @param {number} page - 页数
 * @param {number} per_page - 每页数量  
 * @returns {Promise} 包含建议数据的Promise
 */
export const searchSpotsByStart = (params) => {
  return axios.post('/api/path/spots-by-start', {
    start_vertex_id: params.startVertexId,
    location_name: params.locationName,
    distance: params.distance,
    limit: params.limit,
    page: 1,
    per_page: 200
  })
}