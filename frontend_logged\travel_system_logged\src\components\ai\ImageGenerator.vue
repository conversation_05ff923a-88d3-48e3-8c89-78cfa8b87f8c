<template>
  <div class="image-generator">
    <!-- 生成器头部 -->
    <div class="generator-header">
      <h2>
        <i class="el-icon-picture"></i>
        AI图片生成
      </h2>
      <p>基于腾讯混元大模型，根据文字描述生成精美图片</p>
    </div>

    <!-- 生成表单 -->
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="generation-form"
    >
      <el-form-item label="图片描述" prop="prompt" required>
        <el-input
          v-model="form.prompt"
          type="textarea"
          :rows="4"
          placeholder="请详细描述您想要生成的图片，例如：一个美丽的日落海滩，金色的沙滩，椰子树，蓝色的海水，温暖的阳光..."
          maxlength="500"
          show-word-limit
        />
        <div class="prompt-tips">
          <p><strong>提示：</strong>描述越详细，生成的图片效果越好</p>
          <div class="example-prompts">
            <span>示例：</span>
            <el-tag
              v-for="example in examplePrompts"
              :key="example"
              size="small"
              @click="useExample(example)"
              style="margin: 2px; cursor: pointer;"
            >
              {{ example }}
            </el-tag>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="图片尺寸" prop="size">
        <el-select v-model="form.size" placeholder="选择图片尺寸">
          <el-option label="正方形 (1024x1024)" value="1024x1024" />
          <el-option label="横向 (1280x720)" value="1280x720" />
          <el-option label="竖向 (720x1280)" value="720x1280" />
          <el-option label="宽屏 (1920x1080)" value="1920x1080" />
        </el-select>
      </el-form-item>

      <el-form-item label="图片风格" prop="style">
        <el-select v-model="form.style" placeholder="选择图片风格">
          <el-option label="自然写实" value="realistic" />
          <el-option label="艺术插画" value="artistic" />
          <el-option label="动漫风格" value="anime" />
          <el-option label="油画风格" value="oil_painting" />
          <el-option label="水彩风格" value="watercolor" />
          <el-option label="素描风格" value="sketch" />
        </el-select>
      </el-form-item>

      <el-form-item label="生成数量" prop="count">
        <el-slider
          v-model="form.count"
          :min="1"
          :max="4"
          :marks="{ 1: '1张', 2: '2张', 3: '3张', 4: '4张' }"
          show-stops
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          @click="generateImages"
          :loading="loading"
          :disabled="!form.prompt.trim()"
          size="large"
          style="width: 200px;"
        >
          <i class="el-icon-magic-stick"></i>
          {{ loading ? getLoadingText() : '生成图片' }}
        </el-button>
        <el-button
          v-if="generatedImages.length > 0"
          @click="clearResults"
          size="large"
          style="margin-left: 10px;"
        >
          <i class="el-icon-refresh-left"></i>
          清空结果
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 错误提示 -->
    <el-alert
      v-if="error"
      :title="error"
      type="error"
      :closable="false"
      style="margin: 20px 0;"
    />

    <!-- 生成进度 -->
    <div v-if="loading" class="generation-progress">
      <div class="progress-header">
        <h3>
          <i class="el-icon-loading"></i>
          正在生成图片...
        </h3>
        <p>{{ getLoadingText() }}</p>
      </div>
      <el-progress
        :percentage="progress"
        :stroke-width="8"
        status="success"
        :show-text="false"
      />
      <div class="progress-tips">
        <p>• 图片生成通常需要10-30秒</p>
        <p>• 复杂描述可能需要更长时间</p>
        <p>• 请耐心等待，不要刷新页面</p>
      </div>
    </div>

    <!-- 生成结果 -->
    <div v-if="generatedImages.length > 0" class="result-section">
      <div class="result-header">
        <h3>
          <i class="el-icon-picture-outline"></i>
          生成结果
        </h3>
        <div class="result-actions">
          <el-button @click="downloadAll" size="small" type="success">
            <i class="el-icon-download"></i>
            下载全部
          </el-button>
          <el-button @click="regenerateImages" size="small" type="primary">
            <i class="el-icon-refresh"></i>
            重新生成
          </el-button>
        </div>
      </div>

      <!-- 生成信息 -->
      <div class="generation-info">
        <div class="info-item">
          <span class="label">描述：</span>
          <span class="value">{{ form.prompt }}</span>
        </div>
        <div class="info-item">
          <span class="label">尺寸：</span>
          <span class="value">{{ form.size }}</span>
        </div>
        <div class="info-item">
          <span class="label">风格：</span>
          <span class="value">{{ getStyleLabel(form.style) }}</span>
        </div>
        <div class="info-item">
          <span class="label">生成时间：</span>
          <span class="value">{{ generationTime }}秒</span>
        </div>
        <div v-if="lastProvider" class="info-item">
          <span class="label">AI服务：</span>
          <span class="value">
            <el-tag :type="getProviderTagType(lastProvider)" size="small">
              {{ getProviderName(lastProvider) }}
            </el-tag>
          </span>
        </div>
      </div>

      <!-- 图片网格 -->
      <div class="images-grid">
        <div
          v-for="(image, index) in generatedImages"
          :key="index"
          class="image-card"
        >
          <div class="image-container">
            <img
              :src="image.url"
              :alt="`生成的图片 ${index + 1}`"
              @load="handleImageLoad"
              @error="handleImageError"
            />
            <div class="image-overlay">
              <el-button
                type="primary"
                size="small"
                @click="previewImage(image.url)"
                circle
              >
                <i class="el-icon-zoom-in"></i>
              </el-button>
              <el-button
                type="success"
                size="small"
                @click="downloadImage(image.url, index)"
                circle
              >
                <i class="el-icon-download"></i>
              </el-button>
            </div>
          </div>
          <div class="image-info">
            <p>图片 {{ index + 1 }}</p>
            <el-tag size="mini" type="info">{{ form.size }}</el-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- 图片预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      title="图片预览"
      width="80%"
      center
    >
      <div class="preview-container">
        <img :src="previewImageUrl" alt="预览图片" style="max-width: 100%; max-height: 70vh;" />
      </div>
      <template #footer>
        <el-button @click="previewVisible = false">关闭</el-button>
        <el-button type="primary" @click="downloadImage(previewImageUrl, 'preview')">
          <i class="el-icon-download"></i>
          下载图片
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import aiApi from '@/api/ai'

export default {
  name: 'ImageGenerator',
  emits: ['generated'],
  setup(props, { emit }) {
    const formRef = ref(null)
    const loading = ref(false)
    const error = ref('')
    const progress = ref(0)
    const generatedImages = ref([])
    const generationTime = ref(0)
    const previewVisible = ref(false)
    const previewImageUrl = ref('')
    const lastProvider = ref('')

    // 表单数据
    const form = reactive({
      prompt: '',
      size: '1024x1024',
      style: 'realistic',
      count: 1
    })

    // 表单验证规则
    const rules = {
      prompt: [
        { required: true, message: '请输入图片描述', trigger: 'blur' },
        { min: 10, message: '描述至少需要10个字符', trigger: 'blur' }
      ]
    }

    // 示例提示词
    const examplePrompts = [
      '美丽的日落海滩，金色沙滩，椰子树',
      '雪山下的湖泊，倒影清晰，蓝天白云',
      '古典中式庭院，亭台楼阁，荷花池',
      '现代都市夜景，霓虹灯光，车水马龙',
      '森林中的小木屋，阳光透过树叶',
      '樱花盛开的公园，粉色花瓣飞舞'
    ]

    // 使用示例提示词
    const useExample = (example) => {
      form.prompt = example
    }

    // 获取风格标签
    const getStyleLabel = (style) => {
      const styleMap = {
        realistic: '自然写实',
        artistic: '艺术插画',
        anime: '动漫风格',
        oil_painting: '油画风格',
        watercolor: '水彩风格',
        sketch: '素描风格'
      }
      return styleMap[style] || style
    }

    // 获取提供商名称
    const getProviderName = (provider) => {
      const providerMap = {
        doubao: '火山方舟豆包',
        hunyuan: '腾讯混元',
        openai: 'OpenAI',
        wenxin: '百度文心一格'
      }
      return providerMap[provider] || '备用服务'
    }

    // 获取提供商标签类型
    const getProviderTagType = (provider) => {
      const typeMap = {
        doubao: 'success',
        hunyuan: 'primary',
        openai: 'warning',
        wenxin: 'info'
      }
      return typeMap[provider] || 'default'
    }

    // 获取加载文本
    const getLoadingText = () => {
      const texts = [
        '正在理解您的描述...',
        '正在生成图片...',
        '正在优化图片质量...',
        '即将完成...'
      ]
      return texts[Math.floor(progress.value / 25)] || texts[0]
    }

    // 生成图片
    const generateImages = async () => {
      try {
        await formRef.value.validate()

        loading.value = true
        error.value = ''
        progress.value = 0
        generatedImages.value = []

        const startTime = Date.now()

        // 模拟进度更新
        const progressInterval = setInterval(() => {
          if (progress.value < 90) {
            progress.value += Math.random() * 10
          }
        }, 1000)

        const requestData = {
          prompt: form.prompt,
          size: form.size,
          style: form.style,
          count: form.count
        }

        console.log('发送图片生成请求:', requestData)
        const response = await aiApi.generateImage(requestData)

        clearInterval(progressInterval)
        progress.value = 100

        console.log('图片生成响应:', response)

        if (response.code === 0) {
          generatedImages.value = response.data.images || []
          generationTime.value = ((Date.now() - startTime) / 1000).toFixed(1)

          // 保存使用的AI服务提供商
          lastProvider.value = response.data.provider || 'unknown'
          const providerName = getProviderName(lastProvider.value)

          emit('generated', generatedImages.value)
          ElMessage.success(`成功使用${providerName}生成${generatedImages.value.length}张图片！`)
        } else {
          error.value = response.message || '生成失败'
          ElMessage.error(`生成失败: ${error.value}`)
        }
      } catch (error) {
        console.error('生成图片失败:', error)
        error.value = '生成失败，请稍后重试'
        ElMessage.error('生成失败，请稍后重试')
      } finally {
        loading.value = false
        progress.value = 0
      }
    }

    // 重新生成图片
    const regenerateImages = () => {
      generateImages()
    }

    // 清空结果
    const clearResults = () => {
      generatedImages.value = []
      error.value = ''
    }

    // 预览图片
    const previewImage = (url) => {
      previewImageUrl.value = url
      previewVisible.value = true
    }

    // 下载图片
    const downloadImage = async (url, index) => {
      try {
        const response = await fetch(url)
        const blob = await response.blob()
        const downloadUrl = window.URL.createObjectURL(blob)

        const link = document.createElement('a')
        link.href = downloadUrl
        link.download = `generated_image_${index}_${Date.now()}.png`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        window.URL.revokeObjectURL(downloadUrl)
        ElMessage.success('图片下载成功')
      } catch (error) {
        console.error('下载图片失败:', error)
        ElMessage.error('下载失败，请稍后重试')
      }
    }

    // 下载全部图片
    const downloadAll = async () => {
      for (let i = 0; i < generatedImages.value.length; i++) {
        await downloadImage(generatedImages.value[i].url, i + 1)
        // 添加延迟避免浏览器阻止多个下载
        await new Promise(resolve => setTimeout(resolve, 500))
      }
    }

    // 处理图片加载
    const handleImageLoad = () => {
      // 图片加载成功
    }

    // 处理图片加载错误
    const handleImageError = (event) => {
      console.error('图片加载失败:', event)
      ElMessage.error('图片加载失败')
    }

    return {
      formRef,
      form,
      rules,
      loading,
      error,
      progress,
      generatedImages,
      generationTime,
      previewVisible,
      previewImageUrl,
      lastProvider,
      examplePrompts,
      useExample,
      getStyleLabel,
      getProviderName,
      getProviderTagType,
      getLoadingText,
      generateImages,
      regenerateImages,
      clearResults,
      previewImage,
      downloadImage,
      downloadAll,
      handleImageLoad,
      handleImageError
    }
  }
}
</script>

<style scoped>
.image-generator {
  max-width: 1000px;
  margin: 0 auto;
}

.generator-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15px;
  color: white;
}

.generator-header h2 {
  margin: 0 0 10px 0;
  font-size: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.generator-header p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

.generation-form {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.prompt-tips {
  margin-top: 10px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.prompt-tips p {
  margin: 0 0 10px 0;
  color: #666;
  font-size: 0.9rem;
}

.example-prompts {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 5px;
}

.example-prompts span {
  color: #666;
  font-size: 0.9rem;
  margin-right: 5px;
}

.generation-progress {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
  text-align: center;
}

.progress-header h3 {
  margin: 0 0 10px 0;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.progress-header p {
  margin: 0 0 20px 0;
  color: #666;
}

.progress-tips {
  margin-top: 20px;
  text-align: left;
}

.progress-tips p {
  margin: 5px 0;
  color: #666;
  font-size: 0.9rem;
}

.result-section {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.result-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-header h3 {
  margin: 0;
  font-size: 1.4rem;
  display: flex;
  align-items: center;
  gap: 10px;
}

.generation-info {
  padding: 20px 30px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item .label {
  font-weight: 600;
  color: #333;
  min-width: 80px;
}

.info-item .value {
  color: #666;
  flex: 1;
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 25px;
  padding: 30px;
}

.image-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.image-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.image-container {
  position: relative;
  overflow: hidden;
}

.image-container img {
  width: 100%;
  height: auto;
  min-height: 200px;
  max-height: 400px;
  object-fit: contain;
  transition: transform 0.2s ease;
  background-color: #f5f5f5;
}

.image-container:hover img {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.image-container:hover .image-overlay {
  opacity: 1;
}

.image-info {
  padding: 15px;
  background: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.image-info p {
  margin: 0;
  font-weight: 600;
  color: #333;
}

.preview-container {
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .generator-header h2 {
    font-size: 1.6rem;
  }

  .generation-form {
    padding: 20px;
  }

  .images-grid {
    grid-template-columns: 1fr;
    padding: 20px;
  }

  .result-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
}
</style>
