# 协同过滤测试数据生成脚本使用说明

这个脚本用于生成测试用户、文章、浏览记录等数据，以便测试协同过滤推荐功能。生成的数据具有特定的模式，便于测试协同过滤算法的效果。

## 特点

1. **统一的用户凭据**：所有生成的用户使用相同的密码格式
   - 用户名格式：`user_1`, `user_2`, ...
   - 密码：所有用户密码都是 `123456`

2. **多种浏览模式**：支持多种地点浏览模式
   - `random`：完全随机浏览
   - `clustered`：聚类浏览（用户倾向于浏览特定类型的地点）
   - `similar_groups`：相似用户组（不同组的用户有不同的浏览偏好）

3. **多种评分模式**：支持多种文章评分模式
   - `random`：完全随机评分
   - `similar_groups`：相似用户组（同组用户有相似的评分偏好）

## 使用方法

在命令行中运行：

```bash
# 使用默认参数
python backend/scripts/generate_cf_test_data.py

# 指定用户数量和文章数量
python backend/scripts/generate_cf_test_data.py --users 30 --articles 5

# 指定浏览模式和评分模式
python backend/scripts/generate_cf_test_data.py --browse-pattern clustered --score-pattern similar_groups

# 指定用户ID起始值（如果想避免与现有用户ID冲突）
python backend/scripts/generate_cf_test_data.py --start-id 20
```

## 参数说明

| 参数 | 描述 | 默认值 |
|------|------|--------|
| `--users` | 要生成的用户数量 | 20 |
| `--articles` | 每个用户生成的文章数量 | 3 |
| `--browse-pattern` | 浏览模式类型（random, clustered, similar_groups） | similar_groups |
| `--score-pattern` | 评分模式类型（random, similar_groups） | similar_groups |
| `--start-id` | 用户ID起始值 | 1 |

## 生成的数据

脚本会生成以下数据：

1. **用户**：具有统一格式的用户名和密码
2. **地点浏览记录**：根据选择的浏览模式生成
3. **文章**：每个用户会撰写一定数量的文章
4. **文章评分**：根据选择的评分模式生成

## 测试协同过滤

生成数据后，您可以使用以下API测试协同过滤推荐功能：

```
POST http://localhost:5000/api/recommend/collaborative
Content-Type: application/json

{
  "user_id": 1
}
```

```
POST http://localhost:5000/api/recommend/hybrid
Content-Type: application/json

{
  "user_id": 1,
  "limit": 10,
  "weights": {
    "content": 1.0,
    "collaborative": 1.0,
    "popularity": 0.5
  }
}
```

## 注意事项

1. 运行脚本前，确保数据库中已有地点数据，否则脚本将无法生成浏览记录和文章
2. 脚本会检查用户是否已存在，如果存在则跳过创建
3. 如果您想完全重置数据，请先清空相关表（users, location_browse_history, location_browse_counts, articles, article_scores）
