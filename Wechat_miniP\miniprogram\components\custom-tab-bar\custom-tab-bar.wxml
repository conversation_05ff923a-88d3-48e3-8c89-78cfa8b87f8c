<view class="tab-bar">
  <view class="tab-bar-border"></view>
  <view wx:for="{{list}}" wx:key="index" class="tab-bar-item" data-path="{{item.pagePath}}" data-index="{{index}}" bindtap="switchTab">
    <!-- 使用文字图标，更稳定 -->
    <view class="tab-bar-icon {{selected === index ? 'active' : ''}}">{{item.iconText}}</view>
    <view class="tab-bar-text {{selected === index ? 'active' : ''}}">{{item.text}}</view>
  </view>
</view>
