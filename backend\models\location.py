from utils.database import db

class Location(db.Model):
    """
    Location model - represents a place in the travel system
    Aligned with Java version's Location model
    """
    __tablename__ = 'locations'
    __table_args__ = {'extend_existing': True}

    location_id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    # 0 is school, 1 is scenic spot
    type = db.Column(db.Integer, nullable=False)
    keyword = db.Column(db.String(255))
    popularity = db.Column(db.Integer, default=0)
    evaluation = db.Column(db.Integer, default=0)
    # 新增图片字段
    image_url = db.Column(db.String(255), nullable=True, default='/uploads/images/default_location.jpg')
    description = db.Column(db.Text, nullable=True)
    address = db.Column(db.String(255), nullable=True)

    def to_dict(self):
        """Convert location to dictionary"""
        # 严格按照数据库中的image_url字段处理图片URL
        image_url = self.image_url

        if image_url:
            # 如果数据库中有image_url值
            if image_url.startswith('http'):
                # 如果已经是完整URL，直接使用
                final_image_url = image_url
            elif image_url.startswith('/uploads'):
                # 如果已经是相对路径，直接使用
                final_image_url = image_url
            else:
                # 如果只是文件名（如'清华大学.jpg'），添加路径前缀
                final_image_url = f'/uploads/locations/{image_url}'
        else:
            # 如果数据库中没有image_url值，使用默认图片
            final_image_url = '/uploads/locations/default_location.jpg'

        return {
            'location_id': self.location_id,
            'name': self.name,
            'type': self.type,
            'keyword': self.keyword,
            'popularity': self.popularity,
            'evaluation': round(self.evaluation) if self.evaluation is not None else 0,  # 四舍五入为整数
            'image_url': final_image_url,
            'description': self.description,
            'address': self.address
        }


