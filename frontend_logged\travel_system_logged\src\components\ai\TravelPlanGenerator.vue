<template>
  <div class="travel-plan-generator">
    <div class="generator-header">
      <h2>智能旅游计划生成</h2>
      <p>选择您想要游览的地点和天数，AI将为您生成详细的旅游计划</p>
    </div>

    <div class="form-section">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
        <!-- 地点输入 -->
        <el-form-item label="旅游地点" prop="locations" required>
          <div class="location-input-container">
            <el-autocomplete
              v-model="currentLocationInput"
              :fetch-suggestions="queryLocationSuggestions"
              placeholder="请输入地点名称，支持模糊搜索"
              clearable
              @select="handleLocationSelect"
              :trigger-on-focus="true"
              :highlight-first-item="true"
              :debounce="300"
              style="width: 100%"
              @keyup.enter="addLocationFromInput"
            >
              <template #default="{ item }">
                <div class="location-suggestion-item">
                  <div class="location-name">{{ item.name }}</div>
                  <div class="location-type">{{ getLocationTypeText(item.type) }}</div>
                </div>
              </template>
            </el-autocomplete>

            <!-- 已选择的地点标签 -->
            <div v-if="form.locations.length > 0" class="selected-locations">
              <el-tag
                v-for="(location, index) in form.locations"
                :key="index"
                closable
                @close="removeLocation(index)"
                type="primary"
                class="location-tag"
              >
                {{ location }}
              </el-tag>
            </div>
          </div>
          <div class="form-tip">
            支持输入地点名称进行模糊搜索，如"故宫"、"长城"等。按回车或选择建议项添加地点。
          </div>
        </el-form-item>

        <!-- 旅游天数 -->
        <el-form-item label="旅游天数" prop="days">
          <el-input-number
            v-model="form.days"
            :min="1"
            :max="30"
            placeholder="请输入旅游天数"
            style="width: 200px"
          />
          <span style="margin-left: 10px; color: #666;">天</span>
        </el-form-item>

        <!-- 用户偏好 -->
        <el-form-item label="旅游偏好" prop="preferences">
          <el-checkbox-group v-model="form.preferences">
            <el-checkbox label="文化">文化古迹</el-checkbox>
            <el-checkbox label="自然">自然风光</el-checkbox>
            <el-checkbox label="美食">美食体验</el-checkbox>
            <el-checkbox label="购物">购物娱乐</el-checkbox>
            <el-checkbox label="历史">历史探索</el-checkbox>
            <el-checkbox label="艺术">艺术欣赏</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <!-- 生成按钮 -->
        <el-form-item>
          <el-button
            type="primary"
            @click="generatePlan"
            :loading="loading"
            size="large"
            style="width: 200px"
          >
            <i class="el-icon-magic-stick"></i>
            {{ getLoadingText() }}
          </el-button>
          <div v-if="loading" class="loading-tips">
            <p>AI正在为您生成个性化旅游计划...</p>
            <p>这可能需要30-60秒，请耐心等待</p>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <!-- 生成结果 -->
    <div v-if="generatedPlan" class="result-section">
      <div class="result-header">
        <h3>
          <i class="el-icon-map-location"></i>
          您的专属旅游计划
        </h3>
        <div class="result-actions">
          <el-button type="success" @click="copyToClipboard" size="small">
            <i class="el-icon-document-copy"></i>
            复制计划
          </el-button>
          <el-button type="primary" @click="generatePlan" size="small">
            <i class="el-icon-refresh"></i>
            重新生成
          </el-button>
        </div>
      </div>

      <div class="plan-content">
        <!-- 计划概览 -->
        <div class="plan-overview">
          <div class="overview-card">
            <div class="overview-icon">
              <i class="el-icon-calendar"></i>
            </div>
            <div class="overview-info">
              <div class="overview-title">行程天数</div>
              <div class="overview-value">{{ generatedPlan.days?.length || form.days }}天</div>
            </div>
          </div>
          <div class="overview-card">
            <div class="overview-icon">
              <i class="el-icon-location"></i>
            </div>
            <div class="overview-info">
              <div class="overview-title">游览地点</div>
              <div class="overview-value">{{ form.locations.length }}个</div>
            </div>
          </div>
          <div class="overview-card">
            <div class="overview-icon">
              <i class="el-icon-star-on"></i>
            </div>
            <div class="overview-info">
              <div class="overview-title">主题偏好</div>
              <div class="overview-value">{{ form.preferences.join('、') }}</div>
            </div>
          </div>
        </div>

        <!-- 计划标题 -->
        <div class="plan-title">
          <h2>{{ generatedPlan.title || `${form.locations.join('、')}${form.days}日游` }}</h2>
          <p class="plan-subtitle">为您精心定制的旅游行程安排</p>
        </div>

        <!-- 每日行程 -->
        <div class="plan-days">
          <div v-for="day in generatedPlan.days" :key="day.day" class="day-item">
            <div class="day-header">
              <div class="day-badge">
                <span class="day-number">DAY {{ day.day }}</span>
                <span class="day-date">第{{ day.day }}天</span>
              </div>
              <div class="day-summary">
                <h3>{{ getDayTitle(day) }}</h3>
                <p>{{ getDayLocationCount(day) }}个精彩地点等您探索</p>
              </div>
            </div>

            <div class="day-timeline">
              <div v-for="(location, index) in (day.locations || day.schedule)" :key="index" class="timeline-item">
                <div class="timeline-marker">
                  <div class="timeline-dot"></div>
                  <div v-if="index < (day.locations || day.schedule).length - 1" class="timeline-line"></div>
                </div>
                <div class="timeline-content">
                  <div class="location-card">
                    <div class="location-header">
                      <h4>{{ location.name || location.location_name || location.activity }}</h4>
                      <span class="location-time">{{ location.time || getDefaultTime(index) }}</span>
                    </div>
                    <div class="location-description">
                      <MarkdownRenderer :content="location.description" />
                    </div>
                    <div class="location-tags">
                      <el-tag size="small" type="info">{{ getLocationTypeByName(location.name || location.location_name) }}</el-tag>
                      <el-tag size="small" type="success">推荐游览</el-tag>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 旅游贴士 -->
        <div class="plan-tips">
          <div class="tips-header">
            <i class="el-icon-info"></i>
            <h3>贴心旅游提示</h3>
          </div>
          <div class="tips-grid">
            <div class="tip-card">
              <div class="tip-icon">
                <i class="el-icon-time"></i>
              </div>
              <div class="tip-content">
                <h4>最佳游览时间</h4>
                <p>建议每个景点预留2-3小时，避开高峰期游览体验更佳</p>
              </div>
            </div>
            <div class="tip-card">
              <div class="tip-icon">
                <i class="el-icon-position"></i>
              </div>
              <div class="tip-content">
                <h4>交通建议</h4>
                <p>建议使用公共交通或网约车，部分景点可步行到达</p>
              </div>
            </div>
            <div class="tip-card">
              <div class="tip-icon">
                <i class="el-icon-camera"></i>
              </div>
              <div class="tip-content">
                <h4>拍照提示</h4>
                <p>{{ form.preferences.includes('文化') ? '文化景点适合拍摄建筑细节' : '记录美好旅游时光' }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="error-section">
      <el-alert
        :title="error"
        type="error"
        :closable="false"
        show-icon
      />
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import aiApi from '@/api/ai'
import axios from 'axios'
import MarkdownRenderer from '@/components/common/MarkdownRenderer.vue'

export default {
  name: 'TravelPlanGenerator',
  components: {
    MarkdownRenderer
  },
  emits: ['generated'],
  setup(props, { emit }) {
    const formRef = ref(null)
    const loading = ref(false)
    const error = ref('')
    const generatedPlan = ref(null)
    const currentLocationInput = ref('')

    const form = reactive({
      locations: [],
      days: 3,
      preferences: ['文化']
    })

    const rules = {
      locations: [
        { required: true, message: '请选择至少一个地点', trigger: 'change' }
      ],
      days: [
        { required: true, message: '请输入旅游天数', trigger: 'blur' }
      ]
    }

    // 获取地点类型文本
    const getLocationTypeText = (type) => {
      const typeMap = {
        0: '教育',
        1: '文化',
        2: '自然',
        3: '娱乐'
      }
      return typeMap[type] || '未知'
    }

    // 地点建议查询 - 参考旅游日记的实现
    const queryLocationSuggestions = async (queryString, callback) => {
      console.log('TravelPlanGenerator - queryLocationSuggestions 被调用，查询字符串:', queryString)

      if (!queryString || queryString.trim().length < 1) {
        console.log('TravelPlanGenerator - 查询字符串为空，返回空数组')
        callback([])
        return
      }

      try {
        console.log('TravelPlanGenerator - 准备调用API，URL:', `http://localhost:5000/api/locations/query`)
        console.log('TravelPlanGenerator - 请求参数:', { name: queryString, sortOrder: '0' })

        // 调用后端API获取地点建议
        const response = await axios.get(`http://localhost:5000/api/locations/query`, {
          params: {
            name: queryString,
            sortOrder: '0' // 按人气排序
          }
        })

        console.log('TravelPlanGenerator - API响应:', response)
        console.log('TravelPlanGenerator - 响应数据:', response.data)

        if (response.data && Array.isArray(response.data)) {
          // 转换数据格式，添加value字段供el-autocomplete使用
          const suggestions = response.data.slice(0, 10).map(location => ({
            ...location,
            value: location.name, // el-autocomplete需要的value字段
            name: location.name,
            type: location.type,
            location_id: location.location_id
          }))

          console.log('TravelPlanGenerator - 转换后的建议数据:', suggestions)
          callback(suggestions)
        } else {
          console.log('TravelPlanGenerator - 响应数据格式不正确或为空')
          callback([])
        }
      } catch (error) {
        console.error('TravelPlanGenerator - 获取地点建议失败:', error)
        console.error('TravelPlanGenerator - 错误详情:', error.response)
        callback([])
      }
    }

    // 处理地点选择
    const handleLocationSelect = (item) => {
      console.log('TravelPlanGenerator - 选择了地点:', item)
      if (item.name && !form.locations.includes(item.name)) {
        form.locations.push(item.name)
        currentLocationInput.value = '' // 清空输入框

        // 根据选择的地点数量调整建议天数
        if (form.locations.length > 0 && form.days < form.locations.length) {
          form.days = Math.min(form.locations.length, 7)
        }
      }
    }

    // 从输入框添加地点（按回车时）
    const addLocationFromInput = () => {
      const locationName = currentLocationInput.value.trim()
      if (locationName && !form.locations.includes(locationName)) {
        form.locations.push(locationName)
        currentLocationInput.value = '' // 清空输入框

        // 根据选择的地点数量调整建议天数
        if (form.locations.length > 0 && form.days < form.locations.length) {
          form.days = Math.min(form.locations.length, 7)
        }
      }
    }

    // 移除地点
    const removeLocation = (index) => {
      form.locations.splice(index, 1)
    }

    // 获取加载文本
    const getLoadingText = () => {
      return loading.value ? '生成中...' : '生成旅游计划'
    }

    // 生成旅游计划
    const generatePlan = async () => {
      try {
        await formRef.value.validate()

        loading.value = true
        error.value = ''
        generatedPlan.value = null

        const requestData = {
          location_names: form.locations,
          days: form.days,
          preferences: form.preferences.join(', ')
        }

        console.log('发送AI生成请求:', requestData)
        const response = await aiApi.generateTravelPlanByName(requestData)

        console.log('AI API响应:', response) // 修复：axios拦截器已经返回了response.data

        if (response.code === 0) {
          generatedPlan.value = response.data.plan
          emit('generated', generatedPlan.value)
          ElMessage.success('旅游计划生成成功！')
        } else {
          error.value = response.message || '生成失败'
          console.error('AI生成失败:', response) // 修复：axios拦截器已经返回了response.data
          ElMessage.error(`生成失败: ${error.value}`)
        }
      } catch (error) {
        console.error('生成旅游计划失败:', error)
        console.error('错误详情:', error.response?.data) // 添加更详细的错误日志

        // 处理不同类型的错误
        let errorMessage = '生成失败，请稍后重试'

        if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
          errorMessage = 'AI生成超时，请稍后重试。如果问题持续，系统将使用备用方案生成计划。'
        } else if (error.response?.data?.message) {
          errorMessage = `生成失败: ${error.response.data.message}`
        } else if (error.message) {
          if (error.message.includes('Network Error')) {
            errorMessage = '网络连接错误，请检查网络连接后重试'
          } else {
            errorMessage = `生成失败: ${error.message}`
          }
        }

        error.value = errorMessage
        ElMessage.error(errorMessage)
      } finally {
        loading.value = false
      }
    }

    // 复制到剪贴板
    const copyToClipboard = async () => {
      try {
        const planText = formatPlanForCopy(generatedPlan.value)
        await navigator.clipboard.writeText(planText)
        ElMessage.success('计划已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
        ElMessage.error('复制失败')
      }
    }

    // 获取每日标题
    const getDayTitle = (day) => {
      if (day.title) return day.title
      const locations = day.locations || day.schedule || []
      if (locations.length > 0) {
        const firstLocation = locations[0].name || locations[0].location_name || locations[0].activity
        return `探索${firstLocation}等地`
      }
      return `第${day.day}天行程`
    }

    // 获取每日地点数量
    const getDayLocationCount = (day) => {
      const locations = day.locations || day.schedule || []
      return locations.length
    }

    // 获取默认时间
    const getDefaultTime = (index) => {
      const times = ['09:00-12:00', '13:00-16:00', '16:30-18:30', '19:00-21:00']
      return times[index] || '全天'
    }

    // 根据地点名称获取类型
    const getLocationTypeByName = (locationName) => {
      if (!locationName) return '景点'

      // 简单的关键词匹配
      if (locationName.includes('大学') || locationName.includes('学院')) return '教育'
      if (locationName.includes('博物馆') || locationName.includes('故宫') || locationName.includes('寺') || locationName.includes('庙')) return '文化'
      if (locationName.includes('公园') || locationName.includes('山') || locationName.includes('湖') || locationName.includes('海')) return '自然'
      if (locationName.includes('商场') || locationName.includes('街') || locationName.includes('广场')) return '娱乐'

      return '景点'
    }

    // 格式化计划用于复制
    const formatPlanForCopy = (plan) => {
      let text = `${plan.title || '旅游计划'}\n\n`

      plan.days.forEach(day => {
        text += `第${day.day}天 - ${getDayTitle(day)}\n`
        const locations = day.locations || day.schedule || []
        locations.forEach((location, index) => {
          const name = location.name || location.location_name || location.activity
          const time = location.time || getDefaultTime(index)
          text += `${time}: ${name}\n`
          text += `${location.description}\n\n`
        })
        text += '\n'
      })

      text += '旅游小贴士:\n'
      text += '• 建议每个景点预留2-3小时，避开高峰期游览体验更佳\n'
      text += '• 建议使用公共交通或网约车，部分景点可步行到达\n'
      text += '• 记录美好旅游时光，留下珍贵回忆\n'

      return text
    }

    return {
      formRef,
      form,
      rules,
      loading,
      error,
      generatedPlan,
      currentLocationInput,
      getLocationTypeText,
      queryLocationSuggestions,
      handleLocationSelect,
      addLocationFromInput,
      removeLocation,
      generatePlan,
      copyToClipboard,
      getLoadingText,
      getDayTitle,
      getDayLocationCount,
      getDefaultTime,
      getLocationTypeByName
    }
  }
}
</script>

<style scoped>
.travel-plan-generator {
  max-width: 800px;
  margin: 0 auto;
}

.generator-header {
  text-align: center;
  margin-bottom: 40px;
}

.generator-header h2 {
  color: #333;
  margin-bottom: 10px;
}

.generator-header p {
  color: #666;
  font-size: 1rem;
}

.form-section {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 15px;
  margin-bottom: 30px;
}

.form-tip {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
}

.location-input-container {
  width: 100%;
}

.selected-locations {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.location-tag {
  margin: 0;
}

.location-suggestion-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.location-name {
  font-weight: 500;
  color: #333;
}

.location-type {
  font-size: 12px;
  color: #8492a6;
  background: #f0f2f5;
  padding: 2px 6px;
  border-radius: 3px;
}

.result-section {
  margin-top: 30px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15px;
  color: white;
}

.result-header h3 {
  margin: 0;
  font-size: 1.4rem;
  display: flex;
  align-items: center;
  gap: 10px;
}

.result-actions {
  display: flex;
  gap: 10px;
}

.plan-content {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 15px;
}

/* 计划概览 */
.plan-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.overview-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.overview-card:hover {
  transform: translateY(-2px);
}

.overview-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.overview-info {
  flex: 1;
}

.overview-title {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 5px;
}

.overview-value {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
}

/* 计划标题 */
.plan-title {
  text-align: center;
  margin-bottom: 40px;
}

.plan-title h2 {
  color: #333;
  margin-bottom: 10px;
  font-size: 2rem;
  font-weight: 700;
}

.plan-subtitle {
  color: #666;
  font-size: 1.1rem;
  margin: 0;
}

/* 每日行程 */
.day-item {
  margin-bottom: 40px;
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.day-item:hover {
  transform: translateY(-2px);
}

.day-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 25px 30px;
  display: flex;
  align-items: center;
  gap: 20px;
}

.day-badge {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(255, 255, 255, 0.2);
  padding: 15px;
  border-radius: 12px;
  min-width: 80px;
}

.day-number {
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 5px;
}

.day-date {
  font-size: 1.1rem;
  font-weight: 700;
}

.day-summary {
  flex: 1;
}

.day-summary h3 {
  margin: 0 0 8px 0;
  font-size: 1.4rem;
  font-weight: 600;
}

.day-summary p {
  margin: 0;
  opacity: 0.9;
  font-size: 1rem;
}

/* 时间轴样式 */
.day-timeline {
  padding: 30px;
}

.timeline-item {
  display: flex;
  margin-bottom: 25px;
  position: relative;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-marker {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 20px;
  position: relative;
}

.timeline-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: 3px solid white;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  z-index: 2;
}

.timeline-line {
  width: 2px;
  height: 60px;
  background: linear-gradient(to bottom, #667eea, #e9ecef);
  margin-top: 8px;
}

.timeline-content {
  flex: 1;
}

.location-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  border-left: 4px solid #667eea;
  transition: all 0.2s ease;
}

.location-card:hover {
  background: white;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transform: translateX(5px);
}

.location-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.location-header h4 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

.location-time {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
}

.location-description {
  margin-bottom: 15px;
}

.location-description p {
  margin: 0;
  color: #666;
  line-height: 1.6;
  font-size: 0.95rem;
}

.location-tags {
  display: flex;
  gap: 8px;
}

/* 旅游贴士 */
.plan-tips {
  margin-top: 40px;
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.tips-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 25px;
  color: #333;
}

.tips-header i {
  font-size: 1.3rem;
  color: #ffd700;
}

.tips-header h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.tips-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.tip-card {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 12px;
  border-left: 4px solid #ffd700;
  transition: all 0.2s ease;
}

.tip-card:hover {
  background: #fff;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.tip-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.1rem;
  margin-bottom: 15px;
}

.tip-content h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
}

.tip-content p {
  margin: 0;
  color: #666;
  line-height: 1.6;
  font-size: 0.95rem;
}

.error-section {
  margin-top: 20px;
}

.loading-tips {
  margin-top: 15px;
  padding: 15px;
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
  color: #1e40af;
  text-align: center;
}

.loading-tips p {
  margin: 5px 0;
  font-size: 0.9rem;
}

.loading-tips p:first-child {
  font-weight: 600;
}

@media (max-width: 768px) {
  .schedule-item {
    flex-direction: column;
  }

  .schedule-time {
    width: auto;
    margin-bottom: 10px;
  }

  .result-header {
    flex-direction: column;
    gap: 15px;
  }
}
</style>
