<template>
  <div class="food-recommendation-generator">
    <div class="generator-header">
      <h2>智能美食推荐</h2>
      <p>输入您喜欢的菜系，AI将为您推荐优质餐厅并生成详细描述</p>
    </div>

    <div class="form-section">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <!-- 菜系选择 -->
        <el-form-item label="喜欢菜系" prop="cuisineTypes" required>
          <el-select
            v-model="form.cuisineTypes"
            multiple
            filterable
            remote
            reserve-keyword
            placeholder="请输入菜系名称进行搜索"
            :remote-method="searchCuisines"
            :loading="searchLoading"
            style="width: 100%"
            @change="handleCuisineChange"
          >
            <el-option
              v-for="cuisine in cuisineOptions"
              :key="cuisine.cuisine_type"
              :label="cuisine.cuisine_type"
              :value="cuisine.cuisine_type"
            >
              {{ cuisine.cuisine_type }}
            </el-option>
          </el-select>
          <div class="form-tip">
            支持输入菜系名称进行模糊搜索，如"川菜"、"粤菜"等
          </div>
        </el-form-item>

        <!-- 地点偏好 -->
        <el-form-item label="地点偏好" prop="locationPreference">
          <el-input
            v-model="form.locationPreference"
            placeholder="请输入地点偏好，如'市中心'、'商业区'等（可选）"
            style="width: 100%"
          />
        </el-form-item>

        <!-- 预算范围 -->
        <el-form-item label="预算范围" prop="budgetRange">
          <el-radio-group v-model="form.budgetRange">
            <el-radio label="经济">经济实惠 (人均50元以下)</el-radio>
            <el-radio label="中等">中等消费 (人均50-150元)</el-radio>
            <el-radio label="高端">高端消费 (人均150元以上)</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 推荐数量 -->
        <el-form-item label="推荐数量" prop="limit">
          <el-input-number
            v-model="form.limit"
            :min="1"
            :max="10"
            controls-position="right"
            style="width: 200px"
          />
          <span class="form-tip">建议选择3-5家餐厅</span>
        </el-form-item>

        <!-- 特殊需求 -->
        <el-form-item label="特殊需求" prop="specialRequirements">
          <el-checkbox-group v-model="form.specialRequirements">
            <el-checkbox label="环境优雅">环境优雅</el-checkbox>
            <el-checkbox label="服务周到">服务周到</el-checkbox>
            <el-checkbox label="特色菜品">特色菜品</el-checkbox>
            <el-checkbox label="性价比高">性价比高</el-checkbox>
            <el-checkbox label="适合聚餐">适合聚餐</el-checkbox>
            <el-checkbox label="网红打卡">网红打卡</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <!-- 生成按钮 -->
        <el-form-item>
          <el-button
            type="primary"
            @click="generateRecommendation"
            :loading="loading"
            size="large"
            style="width: 200px"
          >
            <i class="el-icon-food"></i>
            {{ getLoadingText() }}
          </el-button>
          <div v-if="loading" class="loading-tips">
            <p>AI正在分析您的偏好...</p>
            <p>这可能需要20-40秒，请耐心等待</p>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <!-- 生成结果 -->
    <div v-if="generatedRecommendation" class="result-section">
      <div class="result-header">
        <h3>智能美食推荐</h3>
        <el-button @click="copyToClipboard" type="text">
          <i class="el-icon-document-copy"></i>
          复制推荐
        </el-button>
      </div>

      <div class="recommendation-content">
        <!-- 菜系总结 -->
        <div v-if="generatedRecommendation.cuisine_summary" class="summary-section">
          <h4>菜系分析</h4>
          <div class="summary-box">
            <div class="cuisine-info">
              <div class="preferred-cuisines">
                <span class="label">偏好菜系:</span>
                <div class="cuisine-tags">
                  <el-tag
                    v-for="cuisine in generatedRecommendation.cuisine_summary.preferred_cuisines"
                    :key="cuisine"
                    type="success"
                    style="margin-right: 8px; margin-bottom: 8px;"
                  >
                    {{ cuisine }}
                  </el-tag>
                </div>
              </div>
              <div class="characteristics">
                <span class="label">特点分析:</span>
                <p>{{ generatedRecommendation.cuisine_summary.characteristics }}</p>
              </div>
              <div class="flavor-profile">
                <span class="label">口味特色:</span>
                <p>{{ generatedRecommendation.cuisine_summary.flavor_profile }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 餐厅推荐 -->
        <div v-if="generatedRecommendation.restaurant_recommendations" class="restaurants-section">
          <h4>推荐餐厅</h4>
          <div class="restaurants-grid">
            <div
              v-for="restaurant in generatedRecommendation.restaurant_recommendations"
              :key="restaurant.restaurant_id"
              class="restaurant-card"
            >
              <div class="restaurant-header">
                <div class="restaurant-name">{{ restaurant.name }}</div>
                <div class="restaurant-rating">
                  <i class="el-icon-star-on"></i>
                  <span>{{ restaurant.evaluation || 'N/A' }}</span>
                </div>
              </div>

              <div class="restaurant-info">
                <div class="info-item">
                  <span class="info-label">菜系:</span>
                  <span>{{ restaurant.cuisine_type }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">人均:</span>
                  <span class="price">¥{{ restaurant.average_price || '待定' }}</span>
                </div>
              </div>

              <div class="restaurant-description">
                <h5>AI推荐理由</h5>
                <p>{{ restaurant.ai_description }}</p>
              </div>

              <div v-if="restaurant.signature_dishes" class="signature-dishes">
                <h5>招牌菜品</h5>
                <div class="dishes-tags">
                  <el-tag
                    v-for="dish in restaurant.signature_dishes"
                    :key="dish"
                    type="warning"
                    size="small"
                    style="margin-right: 5px; margin-bottom: 5px;"
                  >
                    {{ dish }}
                  </el-tag>
                </div>
              </div>

              <div class="recommendation-reason">
                <h5>推荐理由</h5>
                <p>{{ restaurant.recommendation_reason }}</p>
              </div>

              <div class="dining-tips">
                <h5>用餐建议</h5>
                <p>{{ restaurant.dining_tips }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 整体推荐总结 -->
        <div v-if="generatedRecommendation.overall_recommendation" class="overall-section">
          <h4>总结建议</h4>
          <div class="overall-box">
            <p>{{ generatedRecommendation.overall_recommendation }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误信息 -->
    <div v-if="error" class="error-section">
      <el-alert :title="error" type="error" show-icon />
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import aiApi from '@/api/ai'
import searchApi from '@/api/search'

export default {
  name: 'FoodRecommendationGenerator',
  emits: ['generated'],
  setup(props, { emit }) {
    const formRef = ref(null)
    const loading = ref(false)
    const searchLoading = ref(false)
    const error = ref('')
    const generatedRecommendation = ref(null)
    const cuisineOptions = ref([])

    const form = reactive({
      cuisineTypes: [],
      locationPreference: '',
      budgetRange: '中等',
      limit: 5,
      specialRequirements: ['特色菜品']
    })

    const rules = {
      cuisineTypes: [
        { required: true, message: '请选择至少一种菜系', trigger: 'change' }
      ]
    }

    // 搜索菜系
    const searchCuisines = async (query) => {
      if (!query) {
        cuisineOptions.value = []
        return
      }

      searchLoading.value = true
      try {
        const response = await searchApi.fuzzySearchCuisine(query, 10)
        if (response.data.code === 0) {
          cuisineOptions.value = response.data.data
        }
      } catch (error) {
        console.error('搜索菜系失败:', error)
      } finally {
        searchLoading.value = false
      }
    }

    // 处理菜系选择变化
    const handleCuisineChange = (cuisineTypes) => {
      // 可以在这里添加一些逻辑，比如根据菜系调整预算建议等
      console.log('选择的菜系:', cuisineTypes)
    }

    // 获取加载文本
    const getLoadingText = () => {
      return loading.value ? '生成中...' : '生成推荐'
    }

    // 生成美食推荐
    const generateRecommendation = async () => {
      try {
        await formRef.value.validate()

        loading.value = true
        error.value = ''
        generatedRecommendation.value = null

        const requestData = {
          cuisine_types: form.cuisineTypes,
          location_preference: form.locationPreference,
          budget_range: form.budgetRange,
          limit: form.limit,
          special_requirements: form.specialRequirements.join(', ')
        }

        console.log('发送AI美食推荐请求:', requestData)
        const response = await aiApi.generateFoodRecommendation(requestData)

        console.log('AI API响应:', response)

        if (response.code === 0) {
          generatedRecommendation.value = response.data.recommendation
          emit('generated', generatedRecommendation.value)
          ElMessage.success('美食推荐生成成功！')
        } else {
          error.value = response.message || '生成失败'
          console.error('AI生成失败:', response)
          ElMessage.error(`生成失败: ${error.value}`)
        }
      } catch (error) {
        console.error('生成美食推荐失败:', error)
        console.error('错误详情:', error.response?.data)

        let errorMessage = '生成失败，请稍后重试'

        if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
          errorMessage = 'AI生成超时，请稍后重试'
        } else if (error.response?.data?.message) {
          errorMessage = `生成失败: ${error.response.data.message}`
        } else if (error.message) {
          if (error.message.includes('Network Error')) {
            errorMessage = '网络连接错误，请检查网络连接后重试'
          } else {
            errorMessage = `生成失败: ${error.message}`
          }
        }

        error.value = errorMessage
        ElMessage.error(errorMessage)
      } finally {
        loading.value = false
      }
    }

    // 复制到剪贴板
    const copyToClipboard = async () => {
      try {
        const recommendationText = formatRecommendationForCopy(generatedRecommendation.value, form)
        await navigator.clipboard.writeText(recommendationText)
        ElMessage.success('美食推荐已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
        ElMessage.error('复制失败')
      }
    }

    // 格式化推荐用于复制
    const formatRecommendationForCopy = (recommendation, formData) => {
      let text = `智能美食推荐\n\n`
      text += `偏好菜系: ${formData.cuisineTypes.join(', ')}\n`
      text += `预算范围: ${formData.budgetRange}\n`
      text += `特殊需求: ${formData.specialRequirements.join(', ')}\n\n`

      if (recommendation.cuisine_summary) {
        text += `菜系分析:\n`
        text += `特点: ${recommendation.cuisine_summary.characteristics}\n`
        text += `口味: ${recommendation.cuisine_summary.flavor_profile}\n\n`
      }

      if (recommendation.restaurant_recommendations) {
        text += `推荐餐厅:\n`
        recommendation.restaurant_recommendations.forEach((restaurant, index) => {
          text += `${index + 1}. ${restaurant.name}\n`
          text += `   菜系: ${restaurant.cuisine_type}\n`
          text += `   评分: ${restaurant.evaluation || 'N/A'}\n`
          text += `   人均: ¥${restaurant.average_price || '待定'}\n`
          text += `   推荐理由: ${restaurant.recommendation_reason}\n`
          if (restaurant.signature_dishes) {
            text += `   招牌菜: ${restaurant.signature_dishes.join(', ')}\n`
          }
          text += `   用餐建议: ${restaurant.dining_tips}\n\n`
        })
      }

      if (recommendation.overall_recommendation) {
        text += `总结建议:\n${recommendation.overall_recommendation}\n`
      }

      return text
    }

    // 初始化时加载热门菜系
    onMounted(async () => {
      try {
        const response = await searchApi.fuzzySearchCuisine('', 10)
        if (response.data.code === 0) {
          cuisineOptions.value = response.data.data
        }
      } catch (error) {
        console.error('加载热门菜系失败:', error)
      }
    })

    return {
      formRef,
      form,
      rules,
      loading,
      searchLoading,
      error,
      generatedRecommendation,
      cuisineOptions,
      searchCuisines,
      handleCuisineChange,
      getLoadingText,
      generateRecommendation,
      copyToClipboard
    }
  }
}
</script>

<style scoped>
.food-recommendation-generator {
  max-width: 900px;
  margin: 0 auto;
}

.generator-header {
  text-align: center;
  margin-bottom: 40px;
}

.generator-header h2 {
  color: #333;
  margin-bottom: 10px;
}

.generator-header p {
  color: #666;
  font-size: 1rem;
}

.form-section {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 15px;
  margin-bottom: 30px;
}

.form-tip {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
}

.loading-tips {
  margin-top: 15px;
  padding: 15px;
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
  color: #1e40af;
  text-align: center;
}

.loading-tips p {
  margin: 5px 0;
  font-size: 0.9rem;
}

.loading-tips p:first-child {
  font-weight: 600;
}

.result-section {
  margin-top: 30px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e9ecef;
}

.recommendation-content {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 15px;
}

.summary-section h4,
.restaurants-section h4,
.overall-section h4 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.2rem;
}

.summary-box {
  background: white;
  padding: 25px;
  border-radius: 10px;
  border-left: 4px solid #67c23a;
  margin-bottom: 20px;
}

.cuisine-info > div {
  margin-bottom: 15px;
}

.cuisine-info > div:last-child {
  margin-bottom: 0;
}

.label {
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  display: block;
}

.cuisine-tags {
  display: flex;
  flex-wrap: wrap;
}

.characteristics p,
.flavor-profile p {
  margin: 0;
  color: #666;
  line-height: 1.6;
}

.restaurants-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.restaurant-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #f56c6c;
}

.restaurant-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.restaurant-name {
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
}

.restaurant-rating {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #f56c6c;
  font-weight: 600;
}

.restaurant-info {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.info-label {
  color: #666;
  font-size: 0.9rem;
}

.price {
  color: #f56c6c;
  font-weight: 600;
}

.restaurant-description,
.signature-dishes,
.recommendation-reason,
.dining-tips {
  margin-bottom: 15px;
}

.restaurant-description:last-child,
.signature-dishes:last-child,
.recommendation-reason:last-child,
.dining-tips:last-child {
  margin-bottom: 0;
}

.restaurant-description h5,
.signature-dishes h5,
.recommendation-reason h5,
.dining-tips h5 {
  color: #333;
  margin-bottom: 8px;
  font-size: 1rem;
}

.restaurant-description p,
.recommendation-reason p,
.dining-tips p {
  margin: 0;
  color: #666;
  line-height: 1.6;
  font-size: 0.95rem;
}

.dishes-tags {
  display: flex;
  flex-wrap: wrap;
}

.overall-box {
  background: white;
  padding: 20px;
  border-radius: 10px;
  border-left: 4px solid #409eff;
}

.overall-box p {
  margin: 0;
  line-height: 1.8;
  color: #333;
  font-size: 1rem;
}

.error-section {
  margin-top: 20px;
}

@media (max-width: 768px) {
  .result-header {
    flex-direction: column;
    gap: 15px;
  }

  .restaurants-grid {
    grid-template-columns: 1fr;
  }

  .restaurant-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .restaurant-info {
    flex-direction: column;
    gap: 10px;
  }
}
</style>
