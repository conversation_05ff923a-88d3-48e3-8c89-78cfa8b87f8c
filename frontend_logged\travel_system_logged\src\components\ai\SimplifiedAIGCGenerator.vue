<template>
  <div class="simplified-aigc-generator">
    <!-- 头部介绍 -->
    <div class="aigc-header">
      <h2>🎬 AIGC动画生成器</h2>
      <p>使用AI技术为您的旅游日记生成动画风格的视觉体验</p>

      <!-- 功能特色 -->
      <div class="feature-showcase">
        <div class="feature-item">
          <span class="feature-icon">🎨</span>
          <span class="feature-text">豆包文生图</span>
        </div>
        <div class="feature-item">
          <span class="feature-icon">🎬</span>
          <span class="feature-text">豆包文生视频</span>
        </div>
        <div class="feature-item">
          <span class="feature-icon">🖼️</span>
          <span class="feature-text">豆包图生视频</span>
        </div>
        <div class="feature-item">
          <span class="feature-icon">🎵</span>
          <span class="feature-text">智能配乐</span>
        </div>
      </div>
    </div>

    <!-- 生成表单 -->
    <div class="generation-form">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <!-- 选择日记 -->
        <el-form-item label="选择日记" prop="articleId" required>
          <el-select
            v-model="form.articleId"
            placeholder="请选择要生成AIGC动画的旅游日记"
            style="width: 100%;"
            @change="handleArticleChange"
          >
            <el-option
              v-for="article in articles"
              :key="article.article_id"
              :label="article.title"
              :value="article.article_id"
            >
              <div class="article-option">
                <div class="article-title">{{ article.title }}</div>
                <div class="article-meta">
                  {{ formatDate(article.created_at) }} · {{ article.content?.length || 0 }}字
                </div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 动画风格 -->
        <el-form-item label="动画风格" prop="animationStyle">
          <el-radio-group v-model="form.animationStyle">
            <el-radio-button label="温馨">🌸 温馨</el-radio-button>
            <el-radio-button label="活泼">🎉 活泼</el-radio-button>
            <el-radio-button label="文艺">🎨 文艺</el-radio-button>
            <el-radio-button label="震撼">⚡ 震撼</el-radio-button>
            <el-radio-button label="怀旧">📸 怀旧</el-radio-button>
          </el-radio-group>
        </el-form-item>

        <!-- 动画时长 -->
        <el-form-item label="动画时长" prop="duration">
          <el-radio-group v-model="form.duration">
            <el-radio-button label="短">📱 短片 (30-60秒)</el-radio-button>
            <el-radio-button label="中等">🎬 中等 (1-2分钟)</el-radio-button>
            <el-radio-button label="长">📺 长片 (2-3分钟)</el-radio-button>
          </el-radio-group>
        </el-form-item>

        <!-- 重点元素 -->
        <el-form-item label="重点元素" prop="focusElements">
          <el-checkbox-group v-model="form.focusElements">
            <el-checkbox label="风景">🏞️ 风景</el-checkbox>
            <el-checkbox label="建筑">🏛️ 建筑</el-checkbox>
            <el-checkbox label="人物">👥 人物</el-checkbox>
            <el-checkbox label="美食">🍜 美食</el-checkbox>
            <el-checkbox label="文化">🎭 文化</el-checkbox>
            <el-checkbox label="情感">💝 情感</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <!-- AI图片数量 -->
        <el-form-item label="AI图片数量">
          <el-select v-model="form.imageCount" placeholder="选择AI生成图片数量" style="width: 100%;">
            <el-option label="3张图片 (快速)" :value="3" />
            <el-option label="6张图片 (推荐)" :value="6" />
            <el-option label="9张图片 (丰富)" :value="9" />
            <el-option label="12张图片 (完整)" :value="12" />
          </el-select>
          <div class="form-tip">更多图片会让轮播视频更丰富，但生成时间更长</div>
        </el-form-item>

        <!-- 背景音乐 -->
        <el-form-item label="背景音乐">
          <el-select
            v-model="form.backgroundMusic"
            placeholder="选择背景音乐（可选）"
            clearable
            style="width: 100%;"
          >
            <el-option
              v-for="music in availableMusic"
              :key="music.filename"
              :label="music.name"
              :value="music.filename"
            >
              <div class="music-option">
                <span>{{ music.name }}</span>
                <span class="music-duration">{{ formatDuration(music.duration) }}</span>
              </div>
            </el-option>
          </el-select>
          <div class="form-tip">
            系统会根据视频时长智能截取或循环播放音乐
          </div>
        </el-form-item>

        <!-- 生成按钮 -->
        <el-form-item>
          <el-button
            type="primary"
            @click="generateAIGC"
            :loading="loading"
            size="large"
            class="generate-btn"
          >
            <span v-if="!loading">🎬 生成AIGC动画</span>
            <span v-else>{{ loadingText }}</span>
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 生成结果 -->
    <div v-if="result" class="result-section">
      <h3>🎉 轮播视频生成完成</h3>

      <!-- 轮播视频 -->
      <div v-if="result.video_result?.success" class="slideshow-video">
        <div class="video-container">
          <video
            :src="result.video_result.video_url"
            controls
            class="result-video"
            poster=""
          >
            您的浏览器不支持视频播放
          </video>
        </div>

        <!-- 视频信息 -->
        <div class="video-info-panel">
          <div class="info-item">
            <span class="info-label">总时长:</span>
            <span class="info-value">{{ formatDuration(result.video_result.duration) }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">文件大小:</span>
            <span class="info-value">{{ formatFileSize(result.video_result.file_size) }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">媒体数量:</span>
            <span class="info-value">{{ result.video_result.media_count }} 个</span>
          </div>
          <div class="info-item">
            <span class="info-label">AI图片:</span>
            <span class="info-value">{{ result.video_result.ai_images_count }} 张</span>
          </div>
          <div class="info-item">
            <span class="info-label">用户图片:</span>
            <span class="info-value">{{ result.video_result.user_images_count }} 张</span>
          </div>
          <div class="info-item">
            <span class="info-label">用户视频:</span>
            <span class="info-value">{{ result.video_result.user_videos_count }} 个</span>
          </div>
        </div>
      </div>

      <!-- 生成失败 -->
      <div v-else-if="result.video_result && !result.video_result.success" class="generation-failed">
        <el-alert
          title="视频生成失败"
          :description="result.video_result.error"
          type="error"
          :closable="false"
          show-icon
        />
      </div>

      <!-- 内容分析信息 -->
      <div v-if="result.content_analysis" class="content-analysis">
        <h4>📝 内容分析</h4>
        <div class="analysis-info">
          <div class="analysis-item">
            <span class="analysis-label">标题:</span>
            <span class="analysis-value">{{ result.content_analysis.title }}</span>
          </div>
          <div class="analysis-item">
            <span class="analysis-label">地点:</span>
            <span class="analysis-value">{{ result.content_analysis.location }}</span>
          </div>
          <div class="analysis-item">
            <span class="analysis-label">关键词:</span>
            <span class="analysis-value">{{ result.content_analysis.keywords?.join(', ') }}</span>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="result-actions">
        <el-button @click="downloadResult" type="primary" size="large">
          <el-icon><Download /></el-icon>
          下载视频
        </el-button>
        <el-button @click="shareResult" type="success" size="large">
          <el-icon><Share /></el-icon>
          分享视频
        </el-button>
        <el-button @click="regenerate" type="warning" size="large">
          <el-icon><Refresh /></el-icon>
          重新生成
        </el-button>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="error-section">
      <el-alert
        :title="error"
        type="error"
        :closable="false"
        show-icon
      />
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Download, Share, Refresh } from '@element-plus/icons-vue'
import axios from 'axios'

export default {
  name: 'SimplifiedAIGCGenerator',
  components: {
    Download,
    Share,
    Refresh
  },
  setup() {
    const route = useRoute()
    const formRef = ref()
    const loading = ref(false)
    const loadingText = ref('生成中...')
    const result = ref(null)
    const error = ref('')
    const articles = ref([])
    const availableMusic = ref([])

    const form = reactive({
      articleId: null,
      animationStyle: '温馨',
      duration: '中等',
      focusElements: ['风景', '情感'],
      imageCount: 6,  // 默认6张图片
      backgroundMusic: null
    })

    const rules = {
      articleId: [
        { required: true, message: '请选择日记', trigger: 'change' }
      ]
    }

    // 获取文章列表
    const fetchArticles = async () => {
      try {
        console.log('🔍 开始获取文章列表...')
        const response = await axios.get('http://localhost:5000/api/articles')
        console.log('📡 文章API响应:', response.data)

        if (response.data.code === 0) {
          articles.value = response.data.data.articles || []
          console.log('✅ 成功获取文章列表:', articles.value.length, '篇文章')
          console.log('📝 文章数据:', articles.value)
        } else {
          console.error('❌ 文章API返回错误:', response.data.message)
        }
      } catch (err) {
        console.error('❌ 获取文章列表失败:', err)
        ElMessage.error('获取文章列表失败，请检查网络连接')
      }
    }

    // 获取音乐列表
    const fetchMusic = async () => {
      try {
        console.log('🎵 开始获取音乐列表...')
        const response = await axios.get('http://localhost:5000/api/music')
        console.log('📡 音乐API响应:', response.data)

        if (response.data.code === 0) {
          availableMusic.value = response.data.data || []
          console.log('✅ 成功获取音乐列表:', availableMusic.value.length, '首音乐')
          console.log('🎵 音乐数据:', availableMusic.value)
        } else {
          console.error('❌ 音乐API返回错误:', response.data.message)
        }
      } catch (err) {
        console.error('❌ 获取音乐列表失败:', err)
        ElMessage.error('获取音乐列表失败，请检查网络连接')
      }
    }

    // 生成AIGC动画
    const generateAIGC = async () => {
      try {
        await formRef.value.validate()

        loading.value = true
        error.value = ''
        result.value = null
        loadingText.value = 'AI正在分析日记内容...'

        const response = await axios.post('http://localhost:5000/api/ai/generate_unified_aigc', {
          article_id: form.articleId,
          animation_style: form.animationStyle,
          duration: form.duration,
          focus_elements: form.focusElements.join(', '),
          image_count: form.imageCount,
          background_music: form.backgroundMusic
        })

        if (response.data.code === 0) {
          result.value = response.data.data
          ElMessage.success('🎬 轮播视频生成成功！')
        } else {
          error.value = response.data.message || '生成失败'
          ElMessage.error(error.value)
        }
      } catch (err) {
        console.error('AIGC生成失败:', err)
        error.value = `生成失败: ${err.message}`
        ElMessage.error('生成失败，请检查网络连接')
      } finally {
        loading.value = false
        loadingText.value = '生成中...'
      }
    }

    // 其他方法
    const handleArticleChange = () => {
      // 文章选择变化处理
    }

    const formatDate = (date) => {
      return new Date(date).toLocaleDateString()
    }

    const formatDuration = (duration) => {
      if (!duration) return '0秒'
      const minutes = Math.floor(duration / 60)
      const seconds = Math.floor(duration % 60)
      return minutes > 0 ? `${minutes}分${seconds}秒` : `${seconds}秒`
    }

    const formatFileSize = (bytes) => {
      if (!bytes) return '0 B'
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(1024))
      return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
    }

    const downloadResult = () => {
      ElMessage.info('下载功能开发中...')
    }

    const shareResult = () => {
      ElMessage.info('分享功能开发中...')
    }

    const regenerate = () => {
      result.value = null
      error.value = ''
    }

    // 检查URL参数并预选文章
    const checkUrlParams = () => {
      const articleId = route.query.articleId
      if (articleId) {
        form.articleId = parseInt(articleId, 10)
        console.log('从URL参数预选文章ID:', form.articleId)
      }
    }

    onMounted(() => {
      fetchArticles()
      fetchMusic()
      // 在获取文章列表后检查URL参数
      setTimeout(() => {
        checkUrlParams()
      }, 500)
    })

    return {
      formRef,
      form,
      rules,
      loading,
      loadingText,
      result,
      error,
      articles,
      availableMusic,
      generateAIGC,
      handleArticleChange,
      formatDate,
      formatDuration,
      formatFileSize,
      downloadResult,
      shareResult,
      regenerate
    }
  }
}
</script>

<style scoped>
.simplified-aigc-generator {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* 头部样式 */
.aigc-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15px;
  color: white;
}

.aigc-header h2 {
  margin: 0 0 10px 0;
  font-size: 2rem;
  font-weight: 600;
}

.aigc-header p {
  margin: 0 0 20px 0;
  opacity: 0.9;
  font-size: 1.1rem;
}

.feature-showcase {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin-top: 20px;
}

.feature-item {
  background: rgba(255, 255, 255, 0.1);
  padding: 15px;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  backdrop-filter: blur(10px);
}

.feature-icon {
  font-size: 1.5rem;
}

.feature-text {
  font-size: 0.9rem;
  font-weight: 500;
}

/* 表单样式 */
.generation-form {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.article-option {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.article-title {
  font-weight: 500;
  color: #333;
}

.article-meta {
  font-size: 12px;
  color: #8492a6;
}

.music-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.music-duration {
  color: #409EFF;
  font-size: 12px;
  font-weight: bold;
}

.form-tip {
  margin-top: 5px;
  font-size: 12px;
  color: #8492a6;
}

.generate-btn {
  width: 200px;
  height: 50px;
  font-size: 16px;
  font-weight: 600;
}

/* 结果样式 */
.result-section {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.result-section h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.5rem;
}

.main-video {
  margin-bottom: 30px;
}

.result-video {
  width: 100%;
  max-width: 800px;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.processing-state {
  text-align: center;
  padding: 40px;
  background: #f8f9fa;
  border-radius: 10px;
}

.processing-state p {
  margin: 10px 0;
  color: #666;
}

/* 轮播视频样式 */
.slideshow-video {
  margin-bottom: 30px;
}

.video-container {
  text-align: center;
  margin-bottom: 20px;
}

.video-info-panel {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.info-label {
  font-size: 12px;
  color: #8492a6;
  margin-bottom: 5px;
}

.info-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.generation-failed {
  margin-bottom: 30px;
}

/* 内容分析样式 */
.content-analysis {
  margin-bottom: 30px;
}

.content-analysis h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 1.2rem;
}

.analysis-info {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  display: grid;
  gap: 15px;
}

.analysis-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.analysis-label {
  font-weight: 600;
  color: #666;
  min-width: 60px;
}

.analysis-value {
  color: #333;
  flex: 1;
}

/* AI图片预览样式 */
.ai-images-preview,
.user-media-preview {
  margin-bottom: 30px;
}

.ai-images-preview h4,
.user-images h4,
.user-videos h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 1.2rem;
}

.images-grid,
.videos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.image-item,
.video-item {
  background: #f8f9fa;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.image-item img,
.user-video {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.image-info,
.video-info {
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.image-title,
.video-title {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.image-type,
.video-type {
  font-size: 12px;
  color: #8492a6;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
  background-color: #f3f4f6;
}

/* 操作按钮 */
.result-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 30px;
}

/* 错误样式 */
.error-section {
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .simplified-aigc-generator {
    padding: 15px;
  }

  .aigc-header {
    padding: 20px;
  }

  .aigc-header h2 {
    font-size: 1.5rem;
  }

  .feature-showcase {
    grid-template-columns: repeat(2, 1fr);
  }

  .generation-form,
  .result-section {
    padding: 20px;
  }

  .generate-btn {
    width: 100%;
  }

  .result-actions {
    flex-direction: column;
  }

  .images-grid,
  .videos-grid {
    grid-template-columns: 1fr;
  }
}
</style>
