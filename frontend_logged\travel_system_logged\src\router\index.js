import { createRouter, createWebHistory } from 'vue-router'
import UserCenter from '@/views/UserCenter.vue'
import { ElMessage } from 'element-plus'

const routes = [
  {
    path: '/',
    name: 'LoginEntry',
    component: () => import('@/views/LoginEntry.vue')
  },
  {
    path: '/home',
    name: 'Home',
    component: () => import('@/views/HomeView.vue'),
    meta: {
      requiresAuth: true,
      allowedRoles: ['user', 'guest']
    }
  },
  {
    path: '/user-center',
    name: 'UserCenter',
    component: UserCenter,
    meta: {
      requiresAuth: true, // 需要登录才能访问
      allowedRoles: ['user'] // 只允许注册用户访问，游客不能访问
    }
  },
  {
    path: '/diary',
    name: 'DiaryCommunity',
    component: () => import('@/views/DiaryCommunity.vue')
  },
  {
    path: '/diary/detail/:id',
    name: 'DiaryDetail',
    component: () => import('@/views/DiaryDetail.vue'),
    props: true
  },
  {
    path: '/search',
    name: 'PlaceSearch',
    component: () => import('@/views/PlaceSearch.vue'),
    props: (route) => ({ query: route.query.q })
  },
  {
    path: '/place/:id',
    name: 'PlaceDetail',
    component: () => import('@/views/PlaceDetail.vue'),
    props: true
  },
  {
    path: '/recommend',
    name: 'RecommendAdvise',
    component: () => import('@/views/RecommendAdvise.vue'),
    props: (route) => ({ query: route.query.q })
  },
  {
    path: '/route',
    name: 'RoutePlan',
    component: () => import('@/views/RoutePlan.vue'),
    children: [
      {
        path: '',
        redirect: '/route/bupt'
      },
      {
        path: 'bupt',
        name: 'BUPTNavigation',
        component: () => import('@/views/BUPTNavigation.vue'),
        meta: {
          title: '北京邮电大学导航'
        }
      },
      {
        path: 'chaoyang-park',
        name: 'ChaoyangParkNavigation',
        component: () => import('@/views/ChaoyangParkNavigation.vue'),
        meta: {
          title: '北京朝阳公园导航'
        }
      }
    ]
  },
  {
    path: '/flight',
    name: 'FlightBooking',
    component: () => import('@/views/FlightBooking.vue'),
  },
  {
    path: '/food',
    name: 'FoodRecommend',
    component: () => import('@/views/FoodRecommend.vue'),
  },
  {
    path: '/food/detail/:id',
    name: 'FoodDetail',
    component: () => import('@/views/FoodDetail.vue'),
    props: true,
    meta: {
      title: '美食详情'
    }
  },
  {
    path: '/location/:id',
    name: 'LocationDetail',
    component: () => import('@/views/LocationDetail.vue'),
    props: true,
    meta: {
      title: '景点详情'
    }
  },
  {
    path: '/ai-generator',
    name: 'AIGenerator',
    component: () => import('@/views/AIGenerator.vue'),
    meta: {
      title: 'AI智能生成中心'
    }
  },
  {
    path: '/test-ai',
    name: 'TestAI',
    component: () => import('@/views/TestAI.vue'),
    meta: {
      title: 'AI功能测试'
    }
  },
  {
    path: '/user-debug',
    name: 'UserDebug',
    component: () => import('@/views/UserDebug.vue'),
    meta: {
      title: '用户信息调试'
    }
  },
  {
    path: '/test-ai-page',
    name: 'TestAIPage',
    component: () => import('@/views/TestAIPage.vue'),
    meta: {
      title: 'AI功能测试页面'
    }
  }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})

// 路由守卫
// router.beforeEach((to, from, next) => {
//   const userType = localStorage.getItem('userType')
//   const token = localStorage.getItem('authToken')

//   if (to.meta.requiresAuth && (!userType || !token)) {
//     // 如果需要认证但用户未登录，重定向到登录页
//     next('/')
//   } else {
//     next()
//   }
// })
router.beforeEach((to, from, next) => {
  // 获取用户信息
  const userType = localStorage.getItem('userType')
  const token = localStorage.getItem('authToken') || localStorage.getItem('token')
  const currentUser = localStorage.getItem('currentUser')

  // 判断用户是否已登录
  const isLoggedIn = !!currentUser || !!token || userType === 'guest'

  console.log('路由守卫检查:', {
    from: from.path,
    to: to.path,
    userType,
    isLoggedIn,
    requiresAuth: to.meta.requiresAuth,
    allowedRoles: to.meta.allowedRoles
  })

  // 特殊处理：如果是从用户中心页面点击logo跳转到首页，直接允许
  if (from.path === '/user-center' && to.path === '/home') {
    console.log('从用户中心跳转到首页，直接放行')
    return next()
  }

  // 需要认证的页面
  if (to.meta.requiresAuth) {
    // 检查用户是否已登录
    if (!isLoggedIn) {
      console.log('用户未登录，重定向到登录页')
      ElMessage.warning('请先登录后再访问此页面')
      return next('/')
    }

    // 检查用户角色是否有权限访问
    if (to.meta.allowedRoles && !to.meta.allowedRoles.includes(userType)) {
      console.log('用户类型无权访问:', userType, '需要角色:', to.meta.allowedRoles)

      // 如果是游客模式尝试访问个人中心，引导用户去登录
      if (userType === 'guest' && to.name === 'UserCenter') {
        console.log('游客尝试访问个人中心，重定向到登录页')
        ElMessage.warning('游客模式无法访问个人中心，请先登录')
        return next('/')
      }

      ElMessage.warning('您当前的用户类型无法访问此页面')
      return next('/home')
    }
  }

  // 其他情况放行
  next()
})

export default router