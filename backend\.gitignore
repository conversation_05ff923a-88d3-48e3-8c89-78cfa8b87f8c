# === 通用忽略规则 ===
*.log
*.tmp
*.bak
*.swp
*.swo
.DS_Store
.idea/
.vscode/
*.code-workspace
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# === Python 后端 (Flask) ===
# 虚拟环境
/venv/
/env/
/.venv/
# Python 缓存文件
__pycache__/
*.py[cod]
*$py.class
*.egg-info/
*.egg
.pytest_cache/
.coverage
htmlcov/
.env



# Flask 缓存和敏感配置
instance/
*.env
*.secret
.flaskenv

# === Node.js 前端 (Vue) ===
# 依赖目录
/node_modules/
/dist/
/.quasar/
/.nuxt/
/.output/

# 构建产物
/build/
/public/
/static/
*.js.map
*.css.map

# Vue CLI 缓存
/.vuepress/dist/
/.vite/
/.cache/

# === 开发工具 ===
# 系统文件
Thumbs.db
ehthumbs.db

# 编辑器临时文件
*.sublime-*
*.komodoproject
*.sublime-workspace
*.komodoproject
*.sublime-project

# 开发文档（如果需要共享文档，请移除此行）
# /documents

# 忽略上传文件目录
/uploads/
/media/
/static/user_uploads/

# Flask 敏感配置
/backend/instance/
/instance/
*.env
*.secret
.env.local
.env.*.local


# 开发环境文件
.venv/
venv/
ENV/
env/
env.bak/
venv.bak/
pythonenv*



# 日志文件
/logs/
*.log.*

# 缓存文件
.webassets-cache/
.sass-cache/

# Jupyter Notebook
.ipynb_checkpoints

# Pytest
.pytest_cache/
.coverage
htmlcov/
coverage.xml
*.cover

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Celery
celerybeat-schedule
celerybeat.pid

# 本地开发配置
local_settings.py

# 临时文件
.temp/
.tmp/
tmp/
temp/

# 后端不需要src文件夹，但前端项目需要
# 如果后端有src文件夹需要忽略，可以使用相对路径
# ./src/


