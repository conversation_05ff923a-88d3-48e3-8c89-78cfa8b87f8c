<!--diary-create.wxml-->
<view class="page">
  <!-- 顶部导航栏 -->
  <view class="nav-header">
    <view class="nav-left" bindtap="goBack">
      <text class="iconfont icon-arrow-left"></text>
    </view>
    <view class="nav-title">写日记</view>
    <view class="nav-right" bindtap="publishDiary">
      <text class="publish-btn">发布</text>
    </view>
  </view>

  <view class="container">
    <!-- 标题输入 -->
    <view class="form-item title-section">
      <view class="form-label">
        <text class="label-icon">📝</text>
        <text>标题</text>
        <text class="required">*</text>
      </view>
      <input class="form-input title-input" placeholder="请输入日记标题..." value="{{title}}" bindinput="onTitleInput" maxlength="50" />
      <view class="char-count">{{title.length}}/50</view>
    </view>

    <!-- 地点选择 -->
    <view class="form-item location-section">
      <view class="form-label">
        <text class="label-icon">📍</text>
        <text>地点</text>
        <text class="optional">(可选)</text>
      </view>
      <view class="location-selector" bindtap="selectLocation">
        <text class="location-text {{location ? '' : 'placeholder'}}">{{location || '点击选择旅行地点'}}</text>
        <text class="location-arrow">▼</text>
      </view>
    </view>

    <!-- 内容编辑 -->
    <view class="form-item content-section">
      <view class="form-label">
        <text class="label-icon">✍️</text>
        <text>内容</text>
        <text class="required">*</text>
      </view>
      <textarea class="content-textarea" placeholder="请输入日记内容，分享你的旅行故事..." value="{{content}}" bindinput="onContentInput" maxlength="2000" auto-height />
      <view class="char-count">{{content.length}}/2000</view>
    </view>

    <!-- 图片上传 -->
    <view class="form-item image-section">
      <view class="form-label">
        <text class="label-icon">📷</text>
        <text>图片</text>
        <text class="optional">(可选)</text>
      </view>
      <view class="image-upload">
        <view class="image-list">
          <view class="image-item" wx:for="{{imageList}}" wx:key="index">
            <image src="{{item}}" mode="aspectFill" bindtap="previewImage" data-index="{{index}}" />
            <view class="image-delete" bindtap="deleteImage" data-index="{{index}}">
              <text class="iconfont icon-close"></text>
            </view>
          </view>
          <view class="image-add" wx:if="{{imageList.length < 6}}" bindtap="chooseImage">
            <text class="iconfont icon-camera"></text>
            <text class="add-text">添加图片</text>
          </view>
        </view>
        <view class="image-tip">
          <text class="tip-icon">💡</text>
          <text>最多可添加6张图片，支持JPG、PNG格式</text>
        </view>
      </view>
    </view>

    <!-- 标签 -->
    <view class="form-item tag-section">
      <view class="form-label">
        <text class="label-icon">🏷️</text>
        <text>标签</text>
        <text class="optional">(可选)</text>
      </view>
      <view class="tag-container">
        <view class="tag-list">
          <view class="tag-item {{item.selected ? 'selected' : ''}}" wx:for="{{tagOptions}}" wx:key="id" data-id="{{item.id}}" bindtap="toggleTag">
            <text class="tag-text">{{item.name}}</text>
            <text class="tag-check" wx:if="{{item.selected}}">✓</text>
          </view>
        </view>
        <view class="custom-tag">
          <input class="tag-input" placeholder="添加自定义标签" value="{{customTag}}" bindinput="onCustomTagInput" />
          <view class="tag-add-btn" bindtap="addCustomTag">
            <text class="iconfont icon-add"></text>
          </view>
        </view>
        <view class="tag-tip">
          <text class="tip-icon">💡</text>
          <text>选择或添加标签，让更多人发现你的日记</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <view class="btn-secondary" bindtap="saveDraft">
        <text class="iconfont icon-save"></text>
        <text>保存草稿</text>
      </view>
      <view class="btn-primary" bindtap="publishDiary">
        <text class="iconfont icon-send"></text>
        <text>发布日记</text>
      </view>
    </view>

    <!-- 底部安全区域 -->
    <view class="safe-area"></view>
  </view>
</view>
