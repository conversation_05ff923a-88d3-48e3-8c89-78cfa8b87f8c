.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50px;
  background: white;
  display: flex;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 9999;
  box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.1);
  border-top: 1px solid #e5e5e5;
}

.tab-bar-border {
  background-color: rgba(0, 0, 0, 0.33);
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 1px;
  transform: scaleY(0.5);
}

.tab-bar-item {
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  padding: 4px 0;
}

.tab-bar-icon {
  width: 22px;
  height: 22px;
  margin-bottom: 2px;
  display: block;
}

.tab-bar-icon-text {
  font-size: 20px;
  margin-bottom: 2px;
  line-height: 1;
}

.tab-bar-text {
  font-size: 10px;
  line-height: 1.2;
  font-weight: 400;
}
