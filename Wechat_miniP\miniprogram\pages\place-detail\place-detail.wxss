/* place-detail.wxss */
.page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

/* 顶部导航栏 */
.nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-left, .nav-right {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  font-size: 32rpx;
  transition: all 0.3s ease;
}

.nav-left:active, .nav-right:active {
  transform: scale(0.9);
  background: rgba(102, 126, 234, 0.2);
}

.nav-right.favorited {
  background: rgba(255, 71, 87, 0.1);
  color: #ff4757;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 60vh;
  gap: 30rpx;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid rgba(102, 126, 234, 0.2);
  border-top: 6rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: white;
  font-size: 28rpx;
  font-weight: 500;
}

/* 景点详情 */
.place-detail {
  background: rgba(255, 255, 255, 0.95);
  margin: 20rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 景点图片区域 */
.place-image-section {
  position: relative;
  height: 500rpx;
  overflow: hidden;
}

.place-image {
  width: 100%;
  height: 100%;
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 60rpx 30rpx 30rpx;
  color: white;
}

.place-title {
  font-size: 42rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
}

.place-type {
  font-size: 26rpx;
  opacity: 0.9;
  padding: 6rpx 16rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  display: inline-block;
}

/* 景点信息 */
.place-info {
  padding: 30rpx;
}

.info-header {
  margin-bottom: 30rpx;
}

.place-name {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.place-rating {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 20rpx;
  display: inline-flex;
}

.rating-icon {
  font-size: 28rpx;
}

.rating-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #667eea;
}

.rating-count {
  font-size: 24rpx;
  color: #666;
}

/* 基本信息 */
.basic-info {
  margin-bottom: 30rpx;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx 20rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12rpx;
  margin-bottom: 12rpx;
}

.info-icon {
  font-size: 28rpx;
  width: 40rpx;
  text-align: center;
}

.info-label {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
  width: 80rpx;
}

.info-value {
  flex: 1;
  font-size: 26rpx;
  color: #333;
}

/* 景点描述 */
.place-description {
  margin-bottom: 30rpx;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.section-icon {
  font-size: 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.description-content {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16rpx;
  padding: 30rpx;
  border-left: 6rpx solid #667eea;
}

.description-text {
  font-size: 28rpx;
  line-height: 1.8;
  color: #333;
  text-align: justify;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 16rpx;
  margin-bottom: 30rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 20rpx 16rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.action-btn:active {
  transform: scale(0.95);
}

.action-btn.primary {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
}

.action-btn.secondary {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #666;
}

.action-btn.favorited {
  background: linear-gradient(45deg, #ff4757, #ff3742);
  color: white;
}

.btn-icon {
  font-size: 36rpx;
}

.btn-text {
  font-size: 24rpx;
  font-weight: 500;
}

/* 相关景点 */
.related-places {
  padding: 30rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
}

.related-list {
  white-space: nowrap;
  padding: 20rpx 0;
}

.related-item {
  display: inline-block;
  width: 200rpx;
  margin-right: 20rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.related-item:active {
  transform: scale(0.95);
}

.related-image {
  width: 100%;
  height: 120rpx;
}

.related-info {
  padding: 16rpx;
}

.related-name {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.related-rating {
  display: flex;
  align-items: center;
  gap: 4rpx;
  font-size: 22rpx;
  color: #666;
}

/* 安全区域 */
.safe-area {
  height: 40rpx;
}

/* 错误状态 */
.error-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80vh;
  padding: 60rpx;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  padding: 60rpx 40rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  text-align: center;
}

.error-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10rpx);
  }
  60% {
    transform: translateY(-5rpx);
  }
}

.error-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.error-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  line-height: 1.6;
}

.error-action {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 20rpx 40rpx;
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.4);
  transition: transform 0.3s ease;
}

.error-action:active {
  transform: scale(0.95);
}
