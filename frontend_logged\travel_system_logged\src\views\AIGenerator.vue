<template>
  <div class="ai-generator">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="container">
        <h1 class="page-title">
          🎨 AI智能生成中心
        </h1>
        <p class="page-subtitle">利用人工智能技术，为您提供个性化的旅游内容生成服务</p>
      </div>
    </div>

    <!-- 功能选择器 -->
    <div class="function-selector">
      <div class="container">
        <div class="selector-tabs">
          <div
            v-for="tab in tabs"
            :key="tab.key"
            class="tab-item"
            :class="{ active: currentTab === tab.key }"
            @click="switchTab(tab.key)"
          >
            <div class="tab-icon">
              {{ tab.emoji }}
            </div>
            <div class="tab-content">
              <h3>{{ tab.title }}</h3>
              <p>{{ tab.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 功能内容区域 -->
    <div class="content-area">
      <div class="container">
        <div class="content-panel">
          <!-- 智能旅游计划生成 -->
          <div v-show="currentTab === 'travel-plan'">
            <h2 class="panel-title">🎯 智能旅游计划生成</h2>
            <SmartTravelPlanGenerator />
          </div>

          <!-- 文章摘要生成 -->
          <div v-show="currentTab === 'article-summary'">
            <h2 class="panel-title">📝 文章摘要生成</h2>
            <SmartArticleSummaryGenerator />
          </div>

          <!-- 地点描述生成 -->
          <div v-show="currentTab === 'location-description'">
            <h2 class="panel-title">📍 地点描述生成</h2>
            <SmartLocationDescriptionGenerator />
          </div>

          <!-- AIGC旅游动画生成 -->
          <div v-show="currentTab === 'travel-animation'">
            <SimplifiedAIGCGenerator />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import SmartTravelPlanGenerator from '@/components/ai/SmartTravelPlanGenerator.vue'
import SmartArticleSummaryGenerator from '@/components/ai/SmartArticleSummaryGenerator.vue'
import SmartLocationDescriptionGenerator from '@/components/ai/SmartLocationDescriptionGenerator.vue'
import SimplifiedAIGCGenerator from '@/components/ai/SimplifiedAIGCGenerator.vue'

export default {
  name: 'AIGenerator',
  components: {
    SmartTravelPlanGenerator,
    SmartArticleSummaryGenerator,
    SmartLocationDescriptionGenerator,
    SimplifiedAIGCGenerator
  },
  setup() {
    const currentTab = ref('travel-plan')

    const tabs = ref([
      {
        key: 'travel-plan',
        title: '智能旅游计划',
        description: '基于您的旅游地点、天数和偏好，智能生成个性化旅游计划',
        emoji: '🗺️'
      },
      {
        key: 'article-summary',
        title: '文章摘要生成',
        description: '为您已发布的旅游日记生成精炼的摘要内容',
        emoji: '📝'
      },
      {
        key: 'location-description',
        title: '地点描述生成',
        description: '根据景点信息生成详细的地点描述和游览建议',
        emoji: '📍'
      },
      {
        key: 'travel-animation',
        title: 'AIGC旅游动画',
        description: '基于您的日记内容和媒体文件生成精美的旅游动画',
        emoji: '🎬'
      }
    ])

    // 切换标签页
    const switchTab = (tabKey) => {
      currentTab.value = tabKey
    }

    return {
      currentTab,
      tabs,
      switchTab
    }
  }
}
</script>

<style scoped>
.ai-generator {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.page-header {
  padding: 80px 0 60px;
  text-align: center;
  color: white;
}

.page-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.page-title i {
  margin-right: 15px;
  color: #ffd700;
}

.page-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  line-height: 1.6;
}

.function-selector {
  padding: 40px 0;
  background: rgba(255, 255, 255, 0.05);
}

.selector-tabs {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.tab-item {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15px;
  padding: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 20px;
  border: 2px solid transparent;
  backdrop-filter: blur(10px);
}

.tab-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.95);
}

.tab-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
}

.tab-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  flex-shrink: 0;
}

.tab-item.active .tab-icon {
  background: rgba(255, 255, 255, 0.2);
}

.tab-content {
  flex: 1;
  text-align: left;
}

.tab-content h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.tab-item.active .tab-content h3 {
  color: white;
}

.tab-content p {
  color: #666;
  line-height: 1.5;
  font-size: 0.95rem;
}

.tab-item.active .tab-content p {
  color: rgba(255, 255, 255, 0.9);
}

.content-area {
  background: white;
  min-height: 600px;
  padding: 50px 0;
}

.content-panel {
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.panel-title {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 30px;
  color: #333;
  text-align: center;
  padding-bottom: 15px;
  border-bottom: 2px solid #f0f0f0;
}

@media (max-width: 768px) {
  .page-title {
    font-size: 2rem;
  }

  .selector-tabs {
    grid-template-columns: 1fr;
  }

  .tab-item {
    padding: 20px;
    gap: 15px;
  }

  .tab-icon {
    width: 50px;
    height: 50px;
    font-size: 1.3rem;
  }

  .tab-content h3 {
    font-size: 1.2rem;
  }

  .tab-content p {
    font-size: 0.9rem;
  }

  .content-panel {
    padding: 25px 20px;
  }

  .panel-title {
    font-size: 1.5rem;
  }
}
</style>
