# 数据库重建脚本使用说明

这个Python脚本用于快速删除并重新创建 `study_tour_system` 数据库，并从指定的SQL文件导入数据。

## 准备工作

1. 确保已安装MySQL或MariaDB
2. 确保MySQL命令行工具（mysql）可以在命令行中访问
3. 确保SQL文件位于 `C:\4th_semester\data_structure\personalized-travel-system\backend\database\study_tour_system.sql`

## 使用方法

在命令行中运行：

```bash
# 基本用法（会提示输入密码）
python backend/scripts/recreate_database.py

# 在命令行中提供密码（不安全，密码会显示在命令历史中）
python backend/scripts/recreate_database.py --user "your_user" --password "your_password"

# 使用不同的用户名（会提示输入密码）
python backend/scripts/recreate_database.py --user "your_user"

# 跳过删除数据库步骤
python backend/scripts/recreate_database.py --skip-drop
```

## 参数说明

脚本支持以下参数：

| 参数        | 描述                   | 默认值                                                                                           |
| ----------- | ---------------------- | ------------------------------------------------------------------------------------------------ |
| --sql-file  | 要导入的SQL文件路径    | C:\4th_semester\data_structure\personalized-travel-system\backend\database\study_tour_system.sql |
| --user      | 数据库用户名           | root                                                                                             |
| --password  | 数据库密码             | (空)                                                                                             |
| --host      | 数据库主机             | localhost                                                                                        |
| --db-name   | 要创建的数据库名称     | study_tour_system                                                                                |
| --skip-drop | 是否跳过删除数据库步骤 | false                                                                                            |

## 故障排除

1. **错误: SQL文件不存在**
   - 确保SQL文件路径正确，并且文件存在

2. **错误: 创建数据库时出错**
   - 确保MySQL服务正在运行
   - 确保提供的用户名和密码正确
   - 确保用户有创建数据库的权限

3. **错误: 导入SQL文件时出错**
   - 检查SQL文件是否有语法错误
   - 确保SQL文件使用正确的字符编码（推荐UTF-8）

4. **警告: 设置权限时出错**
   - 这通常不会影响数据库的创建和导入
   - 确保用户有授予权限的权限
