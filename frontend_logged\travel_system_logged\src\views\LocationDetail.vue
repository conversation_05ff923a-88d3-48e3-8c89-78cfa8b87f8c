<template>
  <div class="location-detail-container" v-loading="isLoading">
    <div v-if="!isLoading && location" class="location-content">
      <!-- 顶部导航栏 -->
      <div class="top-nav">
        <el-button @click="goBack" icon="Back" plain>返回</el-button>
      </div>

      <!-- 景点基本信息 -->
      <div class="location-header">
        <div class="location-image-container">
          <img :src="locationImage" alt="景点图片" class="location-image" />
        </div>
        <div class="location-info">
          <h1 class="location-name">{{ location.name }}</h1>
          <div class="location-meta">
            <div class="rating-section">
              <span class="rating-value">⭐ {{ location.evaluation }}</span>
              <span class="rating-count">({{ location.popularity }} 人气)</span>
            </div>
            <div class="category-with-favorite">
              <div class="tags">
                <span v-for="(tag, index) in locationTags" :key="index" class="tag">
                  {{ tag }}
                </span>
              </div>
              <el-button
                @click="toggleFavorite"
                :type="isFavorite ? 'danger' : 'default'"
                :loading="favoriteLoading"
                size="default"
                round
                class="favorite-btn"
              >
                <el-icon class="favorite-icon">
                  <StarFilled v-if="isFavorite" />
                  <Star v-else />
                </el-icon>
                {{ isFavorite ? '已收藏' : '收藏' }}
              </el-button>
            </div>
          </div>
          <div class="location-address">
            <el-icon><Location /></el-icon>
            <span>{{ location.address }}</span>
          </div>
        </div>
      </div>

      <!-- 详细信息 -->
      <div class="location-details">
        <el-tabs>
          <el-tab-pane label="景点介绍">
            <div class="description-section">
              <h3>景点介绍</h3>
              <p v-if="location.description">{{ location.description }}</p>
              <p v-else>暂无详细介绍</p>
            </div>

            <!-- 景点评分区域 -->
            <div class="rating-section">
              <h3>为景点评分</h3>
              <div class="rating-content">
                <div class="rating-stars">
                  <span class="rating-label">您的评分：</span>
                  <el-rate
                    v-model="userRating"
                    :colors="['#FFCDD2', '#FF5722', '#E53935']"
                    :show-text="true"
                    :texts="['1分', '2分', '3分', '4分', '5分']"
                  ></el-rate>
                </div>
                <el-button
                  type="primary"
                  @click="submitRating"
                  :disabled="!userRating || ratingLoading"
                  :loading="ratingLoading"
                >
                  {{ userHasRated ? '更新评分' : '提交评分' }}
                </el-button>
              </div>
              <p class="rating-tip">
                {{ userHasRated ? '您已经对这个景点进行了评分，可以随时更新您的评分。' : '您的评分将帮助其他用户了解这个景点的质量。' }}
              </p>
            </div>
          </el-tab-pane>
          <el-tab-pane label="基本信息">
            <div class="info-section">
              <h3>基本信息</h3>
              <ul class="info-list">
                <li>
                  <span class="info-label">景点名称:</span>
                  <span class="info-value">{{ location.name }}</span>
                </li>
                <li>
                  <span class="info-label">景点类型:</span>
                  <span class="info-value">{{ getLocationType(location.type) }}</span>
                </li>
                <li>
                  <span class="info-label">景点地址:</span>
                  <span class="info-value">{{ location.address }}</span>
                </li>
                <li>
                  <span class="info-label">人气指数:</span>
                  <span class="info-value">{{ location.popularity }}</span>
                </li>
                <li>
                  <span class="info-label">评分:</span>
                  <span class="info-value">{{ location.evaluation }}</span>
                </li>
                <li>
                  <span class="info-label">关键词:</span>
                  <span class="info-value">{{ location.keyword }}</span>
                </li>
              </ul>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 加载失败提示 -->
    <el-empty v-if="!isLoading && !location" description="无法获取景点信息">
      <el-button @click="goBack">返回</el-button>
    </el-empty>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElButton, ElTabs, ElTabPane, ElEmpty, ElIcon, vLoading, ElMessage } from 'element-plus';
import { Location, Star, StarFilled } from '@element-plus/icons-vue';
import axios from 'axios';
import {
  updateLocationBrowseCount,
  rateLocation,
  getUserRating
} from '@/api/location';
import { getCurrentUserId } from '@/utils/userUtils';


const route = useRoute();
const router = useRouter();
const isLoading = ref(true);
const location = ref(null);
const userRating = ref(0);
const currentUser = ref(null);
const userHasRated = ref(false);
const ratingLoading = ref(false);
const isFavorite = ref(false);
const favoriteLoading = ref(false);

// 计算属性：景点标签
const locationTags = computed(() => {
  if (!location.value || !location.value.keyword) return [];
  return location.value.keyword.split(',').map(tag => tag.trim());
});

// 计算属性：景点图片
const locationImage = computed(() => {
  if (!location.value) return '';

  // 严格按照后端返回的image_url字段构建完整URL
  console.log(`LocationDetail - 处理景点 ${location.value.name} (ID: ${location.value.location_id}) 的图片URL:`, location.value.image_url);

  if (location.value.image_url) {
    // 后端已经处理过image_url，直接构建完整URL
    if (location.value.image_url.startsWith('http')) {
      // 如果已经是完整URL，直接使用
      console.log('LocationDetail - 使用完整URL:', location.value.image_url);
      return location.value.image_url;
    } else {
      // 如果是相对路径（后端已经处理为/uploads/locations/xxx.jpg格式），添加服务器地址
      const fullUrl = `http://localhost:5000${location.value.image_url}`;
      console.log('LocationDetail - 使用后端处理的相对路径:', fullUrl);
      return fullUrl;
    }
  } else {
    // 没有图片URL时使用默认图片
    const defaultUrl = `http://localhost:5000/uploads/locations/default_location.jpg`;
    console.log('LocationDetail - 使用默认图片:', defaultUrl);
    return defaultUrl;
  }
});

// 获取景点类型名称
const getLocationType = (type) => {
  const types = {
    0: '学校',
    1: '景点'
  };
  return types[type] || '未知';
};

// 返回上一页
const goBack = () => {
  router.back();
};

// 获取景点详情
const fetchLocationDetail = async () => {
  isLoading.value = true;
  try {
    const locationId = route.params.id;
    if (!locationId) {
      ElMessage.error('景点ID无效');
      return;
    }

    console.log('获取景点详情，ID:', locationId);

    // 使用axios直接调用，便于调试
    const response = await axios.get(`http://localhost:5000/api/locations/${locationId}`);
    console.log('景点详情响应:', response);

    if (response.data && response.data.code === 0 && response.data.data) {
      location.value = response.data.data;
      console.log('景点详情:', location.value);

      // 浏览计数将在 fetchCurrentUser 中处理，避免重复调用
    } else {
      console.error('获取景点详情失败:', response.data);
      ElMessage.error(response.data?.message || '获取景点详情失败');
    }
  } catch (error) {
    console.error('获取景点详情出错:', error);

    // 详细记录错误信息
    if (error.response) {
      console.error('错误响应:', error.response.data);
      console.error('错误状态码:', error.response.status);
      ElMessage.error(`获取景点详情失败: ${error.response.data?.message || '服务器错误'} (${error.response.status})`);
    } else if (error.request) {
      console.error('请求未收到响应:', error.request);
      ElMessage.error('获取景点详情失败: 服务器无响应，请检查网络或联系管理员');
    } else {
      console.error('请求配置错误:', error.message);
      ElMessage.error(`获取景点详情失败: ${error.message}`);
    }
  } finally {
    isLoading.value = false;
  }
};

// 更新浏览计数
const updateBrowseCount = async () => {
  try {
    if (!location.value) {
      console.warn('无法更新浏览计数：地点信息不存在');
      return;
    }

    // 获取当前用户ID
    let userId = null;

    // 1. 从currentUser获取
    if (currentUser.value) {
      userId = currentUser.value.user_id || currentUser.value.id;
    }

    // 2. 从localStorage获取
    if (!userId) {
      userId = localStorage.getItem('userId');
    }

    // 3. 从localStorage获取用户信息
    if (!userId) {
      const userStr = localStorage.getItem('currentUser');
      if (userStr) {
        try {
          const user = JSON.parse(userStr);
          userId = user.user_id || user.id;
        } catch (e) {
          console.error('解析用户信息失败:', e);
        }
      }
    }

    if (!userId) {
      console.warn('无法更新浏览计数：用户ID不存在');
      return;
    }

    console.log('正在更新浏览计数:', {
      location_id: location.value.location_id,
      user_id: userId
    });

    const response = await updateLocationBrowseCount(location.value.location_id, {
      user_id: parseInt(userId)
    });

    console.log('浏览计数更新响应:', response);

    // 更新本地显示的人气值
    if (response && response.data && response.data.code === 0) {
      console.log('浏览计数已更新');
      // 立即更新本地显示的人气值
      location.value.popularity = (location.value.popularity || 0) + 1;
      console.log('本地人气值已更新为:', location.value.popularity);
    }
  } catch (error) {
    console.error('更新浏览计数失败:', error);
  }
};

// 获取用户评分
const fetchUserRating = async () => {
  try {
    if (!location.value) {
      console.warn('无法获取用户评分：地点信息不存在');
      return;
    }

    // 获取当前登录用户ID
    let userId = getCurrentUserId();
    if (!userId) {
      console.warn('用户未登录，无法获取评分');
      return;
    }
    console.log('获取用户评分 - 用户ID:', userId);

    console.log('正在获取用户评分:', {
      user_id: userId,
      location_id: location.value.location_id
    });

    const response = await getUserRating(userId, location.value.location_id);
    console.log('获取用户评分响应:', response);

    if (response && response.data && response.data.rating) {
      userRating.value = response.data.rating;
      userHasRated.value = true;
      console.log('用户评分已获取:', userRating.value);
    } else {
      console.log('用户尚未对此地点评分');
      userRating.value = 0;
      userHasRated.value = false;
    }
  } catch (error) {
    console.error('获取用户评分失败:', error);
    userRating.value = 0;
    userHasRated.value = false;
  }
};

// 检查收藏状态
const checkFavoriteStatus = async () => {
  try {
    if (!location.value) {
      console.warn('无法检查收藏状态：地点信息不存在');
      return;
    }

    // 获取当前登录用户ID
    let userId = getCurrentUserId();
    if (!userId) {
      console.warn('用户未登录，无法检查收藏状态');
      return;
    }
    console.log('检查收藏状态 - 用户ID:', userId);

    // 确保用户ID和地点ID都是数字类型
    const data = {
      user_id: parseInt(userId, 10),
      location_id: parseInt(location.value.location_id, 10)
    };

    // 检查ID是否有效
    if (isNaN(data.user_id) || isNaN(data.location_id)) {
      console.error('无效的用户ID或地点ID:', data);
      return;
    }

    console.log('正在检查收藏状态:', data);

    try {
      // 使用axios直接调用
      console.log('正在发送检查收藏状态请求:', data);
      const response = await axios.post('http://localhost:5000/api/location_favorite/check', data);
      console.log('检查收藏状态响应:', response);

      if (response.data && response.data.code === 0) {
        isFavorite.value = response.data.data.is_favorite;
        console.log('收藏状态:', isFavorite.value ? '已收藏' : '未收藏');
      } else {
        console.error('检查收藏状态失败:', response.data);
        isFavorite.value = false;
      }
    } catch (apiError) {
      console.error('API调用失败:', apiError);

      // 详细记录错误信息
      if (apiError.response) {
        console.error('错误响应:', apiError.response.data);
        console.error('错误状态码:', apiError.response.status);
      } else if (apiError.request) {
        console.error('请求未收到响应:', apiError.request);
      } else {
        console.error('请求配置错误:', apiError.message);
      }

      isFavorite.value = false;
    }
  } catch (error) {
    console.error('检查收藏状态失败:', error);
    isFavorite.value = false;
  }
};

// 切换收藏状态
const toggleFavorite = async () => {
  try {
    // 获取当前登录用户ID
    let userId = getCurrentUserId();
    if (!userId) {
      ElMessage.warning('请先登录后再收藏');
      router.push('/login');
      return;
    }
    console.log('切换收藏状态 - 用户ID:', userId);

    favoriteLoading.value = true;

    // 确保用户ID和地点ID都是数字类型
    const data = {
      user_id: parseInt(userId, 10),
      location_id: parseInt(location.value.location_id, 10)
    };

    // 检查ID是否有效
    if (isNaN(data.user_id) || isNaN(data.location_id)) {
      console.error('无效的用户ID或地点ID:', data);
      ElMessage.error('无法完成操作：无效的ID');
      favoriteLoading.value = false;
      return;
    }

    console.log('正在切换收藏状态:', {
      ...data,
      current_status: isFavorite.value ? '已收藏' : '未收藏'
    });

    let response;
    try {
      if (isFavorite.value) {
        // 取消收藏 - 使用axios直接调用
        console.log('正在发送取消收藏请求:', data);
        response = await axios.post('http://localhost:5000/api/location_favorite/unfavorite', data);
        console.log('取消收藏响应:', response);

        if (response.data && response.data.code === 0) {
          isFavorite.value = false;
          ElMessage.success('已取消收藏');
        } else {
          console.error('取消收藏失败:', response.data);
          ElMessage.error(response.data?.message || '取消收藏失败');
        }
      } else {
        // 添加收藏 - 使用axios直接调用
        console.log('正在发送添加收藏请求:', data);
        response = await axios.post('http://localhost:5000/api/location_favorite/favorite', data);
        console.log('添加收藏响应:', response);

        if (response.data && response.data.code === 0) {
          isFavorite.value = true;
          ElMessage.success('已添加到收藏');
        } else {
          console.error('添加收藏失败:', response.data);
          ElMessage.error(response.data?.message || '添加收藏失败');
        }
      }
    } catch (apiError) {
      console.error('API调用失败:', apiError);

      // 详细记录错误信息
      if (apiError.response) {
        console.error('错误响应:', apiError.response.data);
        console.error('错误状态码:', apiError.response.status);
        ElMessage.error(`操作失败: ${apiError.response.data?.message || '服务器错误'} (${apiError.response.status})`);
      } else if (apiError.request) {
        console.error('请求未收到响应:', apiError.request);
        ElMessage.error('操作失败: 服务器无响应，请检查网络或联系管理员');
      } else {
        console.error('请求配置错误:', apiError.message);
        ElMessage.error(`操作失败: ${apiError.message}`);
      }
    }
  } catch (error) {
    console.error('切换收藏状态失败:', error);
    ElMessage.error('操作失败，请稍后重试');
  } finally {
    favoriteLoading.value = false;
  }
};

// 提交评分
const submitRating = async () => {
  try {
    // 获取当前登录用户ID
    let userId = getCurrentUserId();
    if (!userId) {
      ElMessage.warning('请先登录后再评分');
      router.push('/login');
      return;
    }
    console.log('提交评分 - 用户ID:', userId);

    if (!userRating.value) {
      ElMessage.warning('请选择评分');
      return;
    }

    ratingLoading.value = true;

    console.log('正在提交评分:', {
      location_id: location.value.location_id,
      user_id: userId,
      rating: userRating.value
    });

    // 提交评分
    const response = await rateLocation({
      location_id: location.value.location_id,
      user_id: userId,
      rating: userRating.value
    });

    console.log('评分提交响应:', response);

    if (response && response.data) {
      ElMessage.success(userHasRated.value ? '评分已更新，感谢您的反馈！' : '评分提交成功，感谢您的反馈！');
      userHasRated.value = true;

      // 更新当前页面的评分显示
      if (response.data.new_rating) {
        location.value.evaluation = response.data.new_rating;
        console.log('地点评分已更新:', location.value.evaluation);
      }
    } else {
      ElMessage.error('评分提交失败');
    }
  } catch (error) {
    console.error('评分提交失败:', error);
    ElMessage.error('评分提交失败，请稍后重试');
  } finally {
    ratingLoading.value = false;
  }
};

// 获取当前用户信息
const fetchCurrentUser = async () => {
  try {
    if (location.value) {
      fetchUserRating();
      checkFavoriteStatus();
      updateBrowseCount();
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
   // currentUser.value = null;
  }
};

watch(location, (newLocation) => {
  if (newLocation && currentUser.value) {
    fetchUserRating();
    checkFavoriteStatus();
  }
});

// 组件挂载时获取景点详情和用户信息
onMounted(() => {
  fetchLocationDetail();
  fetchCurrentUser();
});
</script>

<style scoped>
.location-detail-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.top-nav {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.location-header {
  display: flex;
  margin-bottom: 30px;
  gap: 30px;
}

.location-image-container {
  flex: 0 0 40%;
}

.location-image {
  width: 100%;
  height: 300px;
  object-fit: cover;
  border-radius: 8px;
}

.location-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.location-name {
  margin: 0 0 15px 0;
  font-size: 28px;
  color: #333;
}

.location-meta {
  margin-bottom: 20px;
}

.rating-section {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.rating-value {
  font-size: 18px;
  color: #ffd700;
}

.rating-count {
  color: #666;
}

/* 分类和收藏按钮的容器 */
.category-with-favorite {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.tag {
  background-color: #f0f0f0;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 14px;
  color: #555;
}

/* 收藏按钮样式 */
.favorite-btn {
  margin-left: 16px;
  padding: 8px 16px;
  font-size: 14px;
  transition: all 0.3s ease;
  min-width: 100px;
}

.favorite-btn .favorite-icon {
  margin-right: 6px;
  font-size: 16px;
  transition: transform 0.2s ease;
}

.favorite-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.favorite-btn:hover .favorite-icon {
  transform: scale(1.15);
}

/* 收藏按钮的特殊效果 */
.favorite-btn.el-button--danger .favorite-icon {
  color: #f56c6c;
  animation: pulse 2s infinite;
}

.favorite-btn.el-button--danger:hover {
  background-color: #f78989;
  border-color: #f78989;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.08);
  }
  100% {
    transform: scale(1);
  }
}

.location-address {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 16px;
}

.location-details {
  margin-top: 30px;
}

.description-section, .info-section {
  padding: 20px 0;
}

.description-section h3, .info-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 20px;
  color: #333;
}

.description-section p {
  line-height: 1.6;
  color: #555;
}

.info-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.info-list li {
  display: flex;
  margin-bottom: 12px;
  line-height: 1.5;
}

.info-label {
  flex: 0 0 100px;
  font-weight: bold;
  color: #666;
}

.info-value {
  flex: 1;
  color: #333;
}

/* 评分区域样式 */
.rating-section {
  margin-top: 30px;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.rating-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 15px 0;
}

.rating-stars {
  display: flex;
  align-items: center;
  gap: 10px;
}

.rating-label {
  font-size: 16px;
  color: #555;
}

.rating-tip {
  font-size: 14px;
  color: #999;
  margin-top: 10px;
}
</style>
