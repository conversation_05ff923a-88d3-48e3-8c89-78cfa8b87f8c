"""
Database migration script
This script creates a new database for the Flask application and imports data from the Java application
"""
import os
import sys
import pymysql
import json  # Used in migrate_articles function

try:
    from dotenv import load_dotenv
except ImportError:
    print("python-dotenv not installed. Installing...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "python-dotenv"])
    from dotenv import load_dotenv

# Add the parent directory to the path so we can import from the backend package
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables
load_dotenv()

# Database configuration
JAVA_DB_CONFIG = {
    'host': os.getenv('JAVA_DB_HOST', 'localhost'),
    'user': os.getenv('JAVA_DB_USER', 'root'),
    'password': os.getenv('JAVA_DB_PASSWORD', 'AN20050225'),
    'db': os.getenv('JAVA_DB_NAME', 'study_tour_system'),
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

FLASK_DB_CONFIG = {
    'host': os.getenv('FLASK_DB_HOST', 'localhost'),
    'user': os.getenv('FLASK_DB_USER', 'root'),
    'password': os.getenv('FLASK_DB_PASSWORD', 'AN20050225'),
    'db': os.getenv('FLASK_DB_NAME', 'travel_system'),
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

def create_flask_database():
    """Create the Flask database if it doesn't exist"""
    # Connect to MySQL without specifying a database
    conn = pymysql.connect(
        host=FLASK_DB_CONFIG['host'],
        user=FLASK_DB_CONFIG['user'],
        password=FLASK_DB_CONFIG['password'],
        charset=FLASK_DB_CONFIG['charset'],
        cursorclass=FLASK_DB_CONFIG['cursorclass']
    )

    try:
        with conn.cursor() as cursor:
            # Create database if it doesn't exist
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {FLASK_DB_CONFIG['db']} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print(f"Database {FLASK_DB_CONFIG['db']} created or already exists")
    finally:
        conn.close()

def migrate_locations():
    """Migrate locations from Java database to Flask database"""
    # Connect to Java database
    java_conn = pymysql.connect(**JAVA_DB_CONFIG)

    # Connect to Flask database
    flask_conn = pymysql.connect(**FLASK_DB_CONFIG)

    try:
        # Get locations from Java database
        with java_conn.cursor() as cursor:
            cursor.execute("SELECT * FROM locations")
            locations = cursor.fetchall()

        # Insert locations into Flask database
        with flask_conn.cursor() as cursor:
            # Clear existing locations
            cursor.execute("TRUNCATE TABLE locations")

            # Insert locations
            for location in locations:
                cursor.execute(
                    """
                    INSERT INTO locations (location_id, name, type, keyword, popularity, evaluation)
                    VALUES (%s, %s, %s, %s, %s, %s)
                    """,
                    (
                        location['location_id'],
                        location['name'],
                        location['type'],
                        location['keyword'],
                        location['popularity'],
                        location['evaluation']
                    )
                )

        # Commit changes
        flask_conn.commit()
        print(f"Migrated {len(locations)} locations")
    finally:
        java_conn.close()
        flask_conn.close()

def migrate_vertices():
    """Migrate vertices from Java database to Flask database"""
    # Connect to Java database
    java_conn = pymysql.connect(**JAVA_DB_CONFIG)

    # Connect to Flask database
    flask_conn = pymysql.connect(**FLASK_DB_CONFIG)

    try:
        # Get vertices from Java database
        with java_conn.cursor() as cursor:
            cursor.execute("SELECT * FROM vertexes")
            vertices = cursor.fetchall()

        # Insert vertices into Flask database
        with flask_conn.cursor() as cursor:
            # Clear existing vertices
            cursor.execute("TRUNCATE TABLE vertexes")

            # Insert vertices
            for vertex in vertices:
                cursor.execute(
                    """
                    INSERT INTO vertexes (vertex_id, label, x, y, type)
                    VALUES (%s, %s, %s, %s, %s)
                    """,
                    (
                        vertex['vertex_id'],
                        vertex['label'],
                        vertex['x'],
                        vertex['y'],
                        vertex['type']
                    )
                )

        # Commit changes
        flask_conn.commit()
        print(f"Migrated {len(vertices)} vertices")
    finally:
        java_conn.close()
        flask_conn.close()

def migrate_edges():
    """Migrate edges from Java database to Flask database"""
    # Connect to Java database
    java_conn = pymysql.connect(**JAVA_DB_CONFIG)

    # Connect to Flask database
    flask_conn = pymysql.connect(**FLASK_DB_CONFIG)

    try:
        # Get edges from Java database
        with java_conn.cursor() as cursor:
            cursor.execute("SELECT * FROM edges")
            edges = cursor.fetchall()

        # Insert edges into Flask database
        with flask_conn.cursor() as cursor:
            # Clear existing edges
            cursor.execute("TRUNCATE TABLE edges")

            # Insert edges
            for edge in edges:
                cursor.execute(
                    """
                    INSERT INTO edges (edge_id, src_id, dest_id, weight, crowding, is_rideable)
                    VALUES (%s, %s, %s, %s, %s, %s)
                    """,
                    (
                        edge['edge_id'],
                        edge['src_id'],
                        edge['dest_id'],
                        edge['weight'],
                        edge['crowding'],
                        edge['is_rideable']
                    )
                )

        # Commit changes
        flask_conn.commit()
        print(f"Migrated {len(edges)} edges")
    finally:
        java_conn.close()
        flask_conn.close()

def migrate_users():
    """Migrate users from Java database to Flask database"""
    # Connect to Java database
    java_conn = pymysql.connect(**JAVA_DB_CONFIG)

    # Connect to Flask database
    flask_conn = pymysql.connect(**FLASK_DB_CONFIG)

    try:
        # Get users from Java database
        with java_conn.cursor() as cursor:
            cursor.execute("SELECT * FROM users")
            users = cursor.fetchall()

        # Insert users into Flask database
        with flask_conn.cursor() as cursor:
            # Clear existing users
            cursor.execute("TRUNCATE TABLE users")

            # Insert users
            for user in users:
                # Generate a password hash for the user
                from werkzeug.security import generate_password_hash
                password_hash = generate_password_hash(user['password'])

                cursor.execute(
                    """
                    INSERT INTO users (user_id, username, password, email, password_hash)
                    VALUES (%s, %s, %s, %s, %s)
                    """,
                    (
                        user['user_id'],
                        user['username'],
                        user['password'],  # 保留原始密码
                        f"{user['username']}@example.com",  # Generate an email
                        password_hash
                    )
                )

        # Commit changes
        flask_conn.commit()
        print(f"Migrated {len(users)} users")
    finally:
        java_conn.close()
        flask_conn.close()

def migrate_articles():
    """Migrate articles from Java database to Flask database"""
    # Connect to Java database
    java_conn = pymysql.connect(**JAVA_DB_CONFIG)

    # Connect to Flask database
    flask_conn = pymysql.connect(**FLASK_DB_CONFIG)

    try:
        # Get articles from Java database
        with java_conn.cursor() as cursor:
            cursor.execute("SELECT * FROM articles")
            articles = cursor.fetchall()

        # Insert articles into Flask database
        with flask_conn.cursor() as cursor:
            # Clear existing articles
            cursor.execute("TRUNCATE TABLE articles")

            # Insert articles
            for article in articles:
                # Import article service to handle Huffman coding
                from services.article_service import ArticleService
                service = ArticleService()

                # Get content
                content = article['content']

                # Compress content using Huffman coding
                huffman_tree = service.build_huffman_tree(content)
                huffman_codes = service.generate_huffman_codes(huffman_tree)
                compressed_content = service.compress_text(content, huffman_codes)

                # Convert Huffman codes to JSON string
                import json
                huffman_codes_json = json.dumps(huffman_codes)

                # 获取当前时间
                from datetime import datetime
                now = datetime.utcnow()

                cursor.execute(
                    """
                    INSERT INTO articles (article_id, user_id, title, content, huffman_codes, location_id, popularity, evaluation, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """,
                    (
                        article['article_id'],
                        article['user_id'],
                        article['title'],
                        compressed_content,
                        huffman_codes_json,
                        article['location_id'],
                        article['popularity'],
                        article['evaluation'],
                        now,  # 使用当前时间
                        now   # 使用当前时间
                    )
                )

        # Commit changes
        flask_conn.commit()
        print(f"Migrated {len(articles)} articles")
    except Exception as e:
        print(f"Error migrating articles: {e}")
    finally:
        java_conn.close()
        flask_conn.close()

def migrate_article_scores():
    """Migrate article scores from Java database to Flask database"""
    # Connect to Java database
    java_conn = pymysql.connect(**JAVA_DB_CONFIG)

    # Connect to Flask database
    flask_conn = pymysql.connect(**FLASK_DB_CONFIG)

    try:
        # Get article scores from Java database
        with java_conn.cursor() as cursor:
            cursor.execute("SELECT * FROM article_scores")
            article_scores = cursor.fetchall()

        # Insert article scores into Flask database
        with flask_conn.cursor() as cursor:
            # Clear existing article scores
            cursor.execute("TRUNCATE TABLE article_scores")

            # Insert article scores
            for score in article_scores:
                # 获取当前时间
                from datetime import datetime
                now = datetime.utcnow()

                cursor.execute(
                    """
                    INSERT INTO article_scores (user_id, article_id, score, created_at)
                    VALUES (%s, %s, %s, %s)
                    """,
                    (
                        score['user_id'],
                        score['article_id'],
                        score['score'],
                        now  # 使用当前时间
                    )
                )

        # Commit changes
        flask_conn.commit()
        print(f"Migrated {len(article_scores)} article scores")
    except Exception as e:
        print(f"Error migrating article scores: {e}")
    finally:
        java_conn.close()
        flask_conn.close()

def migrate_location_browse_counts():
    """Migrate location browse counts from Java database to Flask database"""
    # Connect to Java database
    java_conn = pymysql.connect(**JAVA_DB_CONFIG)

    # Connect to Flask database
    flask_conn = pymysql.connect(**FLASK_DB_CONFIG)

    try:
        # Get location browse counts from Java database
        with java_conn.cursor() as cursor:
            cursor.execute("SELECT * FROM location_browse_counts")
            browse_counts = cursor.fetchall()

        # Insert location browse counts into Flask database
        with flask_conn.cursor() as cursor:
            # Clear existing location browse counts
            cursor.execute("TRUNCATE TABLE location_browse_counts")

            # Insert location browse counts
            for count in browse_counts:
                cursor.execute(
                    """
                    INSERT INTO location_browse_counts (user_id, location_id, count)
                    VALUES (%s, %s, %s)
                    """,
                    (
                        count['user_id'],
                        count['location_id'],
                        count['count']
                    )
                )

        # Commit changes
        flask_conn.commit()
        print(f"Migrated {len(browse_counts)} location browse counts")
    except Exception as e:
        print(f"Error migrating location browse counts: {e}")
    finally:
        java_conn.close()
        flask_conn.close()

def main():
    """Main function"""
    print("Starting database migration...")

    # Create Flask database
    create_flask_database()

    # Import Flask models to create tables
    from app import create_app
    app = create_app()

    with app.app_context():
        from utils.database import db
        db.create_all()
        print("Created database tables")

    # Migrate data
    migrate_locations()
    migrate_vertices()
    migrate_edges()
    migrate_users()
    migrate_articles()
    migrate_article_scores()
    migrate_location_browse_counts()

    print("Database migration completed successfully")

if __name__ == "__main__":
    main()
