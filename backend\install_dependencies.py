#!/usr/bin/env python3
"""
后端依赖安装脚本
自动检查和安装项目所需的Python依赖包
"""

import subprocess
import sys
import os
import pkg_resources
from typing import List, <PERSON><PERSON>

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"   当前版本: Python {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
    return True

def check_virtual_environment():
    """检查是否在虚拟环境中"""
    in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
    
    if not in_venv:
        print("⚠️  警告: 建议在虚拟环境中安装依赖")
        print("   创建虚拟环境: python -m venv venv")
        print("   激活虚拟环境:")
        print("     Windows: venv\\Scripts\\activate")
        print("     macOS/Linux: source venv/bin/activate")
        
        response = input("是否继续安装? (y/N): ")
        if response.lower() != 'y':
            return False
    else:
        print("✅ 虚拟环境检查通过")
    
    return True

def get_installed_packages() -> dict:
    """获取已安装的包列表"""
    installed = {}
    try:
        for dist in pkg_resources.working_set:
            installed[dist.project_name.lower()] = dist.version
    except Exception as e:
        print(f"⚠️  获取已安装包列表失败: {e}")
    
    return installed

def read_requirements() -> List[str]:
    """读取requirements.txt文件"""
    requirements_file = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    
    if not os.path.exists(requirements_file):
        print(f"❌ 错误: 找不到requirements.txt文件: {requirements_file}")
        return []
    
    requirements = []
    try:
        with open(requirements_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                # 跳过注释和空行
                if line and not line.startswith('#'):
                    requirements.append(line)
    except Exception as e:
        print(f"❌ 错误: 读取requirements.txt失败: {e}")
        return []
    
    return requirements

def parse_requirement(req_line: str) -> Tuple[str, str]:
    """解析依赖行，返回包名和版本"""
    if '==' in req_line:
        name, version = req_line.split('==', 1)
        return name.strip().lower(), version.strip()
    elif '>=' in req_line:
        name, version = req_line.split('>=', 1)
        return name.strip().lower(), version.strip()
    else:
        return req_line.strip().lower(), ''

def check_dependencies() -> Tuple[List[str], List[str]]:
    """检查依赖，返回缺失和需要更新的包"""
    print("🔍 检查依赖包...")
    
    requirements = read_requirements()
    if not requirements:
        return [], []
    
    installed = get_installed_packages()
    missing = []
    outdated = []
    
    for req in requirements:
        pkg_name, req_version = parse_requirement(req)
        
        if pkg_name not in installed:
            missing.append(req)
            print(f"   ❌ 缺失: {pkg_name}")
        elif req_version and installed[pkg_name] != req_version:
            outdated.append(req)
            print(f"   ⚠️  版本不匹配: {pkg_name} (已安装: {installed[pkg_name]}, 需要: {req_version})")
        else:
            print(f"   ✅ 已安装: {pkg_name} ({installed[pkg_name]})")
    
    return missing, outdated

def install_packages(packages: List[str]) -> bool:
    """安装包列表"""
    if not packages:
        return True
    
    print(f"\n📦 安装 {len(packages)} 个包...")
    
    try:
        # 升级pip
        print("🔄 升级pip...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'])
        
        # 安装包
        for package in packages:
            print(f"📦 安装: {package}")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
        
        print("✅ 所有包安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 安装过程中出现错误: {e}")
        return False

def install_from_requirements() -> bool:
    """直接从requirements.txt安装"""
    requirements_file = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    
    print("📦 从requirements.txt安装所有依赖...")
    
    try:
        # 升级pip
        print("🔄 升级pip...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'])
        
        # 安装requirements.txt
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', requirements_file])
        
        print("✅ 所有依赖安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 安装过程中出现错误: {e}")
        return False

def main():
    """主函数"""
    print("🚀 个性化旅游系统 - 后端依赖安装脚本")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 检查虚拟环境
    if not check_virtual_environment():
        sys.exit(1)
    
    print("\n选择安装方式:")
    print("1. 检查并安装缺失的依赖 (推荐)")
    print("2. 重新安装所有依赖")
    print("3. 退出")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    if choice == '1':
        # 检查并安装缺失的依赖
        missing, outdated = check_dependencies()
        
        all_needed = missing + outdated
        if not all_needed:
            print("\n✅ 所有依赖都已正确安装!")
            return
        
        print(f"\n需要安装/更新 {len(all_needed)} 个包")
        response = input("是否继续? (Y/n): ")
        if response.lower() in ['', 'y', 'yes']:
            if install_packages(all_needed):
                print("\n🎉 依赖安装完成!")
            else:
                print("\n❌ 依赖安装失败")
                sys.exit(1)
    
    elif choice == '2':
        # 重新安装所有依赖
        response = input("确定要重新安装所有依赖? (y/N): ")
        if response.lower() == 'y':
            if install_from_requirements():
                print("\n🎉 所有依赖重新安装完成!")
            else:
                print("\n❌ 依赖安装失败")
                sys.exit(1)
    
    elif choice == '3':
        print("👋 退出安装")
        return
    
    else:
        print("❌ 无效选择")
        sys.exit(1)

if __name__ == '__main__':
    main()
