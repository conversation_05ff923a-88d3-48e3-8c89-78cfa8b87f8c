<!--settings.wxml-->
<view class="page">
  <!-- 顶部导航栏 -->
  <view class="nav-header">
    <view class="nav-left" bindtap="goBack">
      <text class="iconfont icon-arrow-left"></text>
    </view>
    <view class="nav-title">设置</view>
    <view class="nav-right"></view>
  </view>

  <view class="container">
    <!-- 个人信息设置 -->
    <view class="setting-section">
      <view class="section-title">个人信息</view>
      <view class="setting-list">
        <view class="setting-item" bindtap="editProfile">
          <view class="setting-left">
            <view class="setting-icon">👤</view>
            <view class="setting-content">
              <view class="setting-title">个人资料</view>
              <view class="setting-desc">修改昵称、头像等信息</view>
            </view>
          </view>
          <view class="setting-right">
            <text class="iconfont icon-arrow-right"></text>
          </view>
        </view>
      </view>
    </view>

    <!-- 隐私设置 -->
    <view class="setting-section">
      <view class="section-title">隐私设置</view>
      <view class="setting-list">
        <view class="setting-item">
          <view class="setting-left">
            <view class="setting-icon">🔒</view>
            <view class="setting-content">
              <view class="setting-title">日记可见性</view>
              <view class="setting-desc">设置日记的可见范围</view>
            </view>
          </view>
          <view class="setting-right">
            <switch checked="{{diaryPublic}}" bindchange="onDiaryPublicChange" />
          </view>
        </view>
        
        <view class="setting-item">
          <view class="setting-left">
            <view class="setting-icon">📍</view>
            <view class="setting-content">
              <view class="setting-title">位置信息</view>
              <view class="setting-desc">允许获取位置信息</view>
            </view>
          </view>
          <view class="setting-right">
            <switch checked="{{locationEnabled}}" bindchange="onLocationChange" />
          </view>
        </view>
      </view>
    </view>

    <!-- 通知设置 -->
    <view class="setting-section">
      <view class="section-title">通知设置</view>
      <view class="setting-list">
        <view class="setting-item">
          <view class="setting-left">
            <view class="setting-icon">🔔</view>
            <view class="setting-content">
              <view class="setting-title">推送通知</view>
              <view class="setting-desc">接收点赞、评论等通知</view>
            </view>
          </view>
          <view class="setting-right">
            <switch checked="{{pushEnabled}}" bindchange="onPushChange" />
          </view>
        </view>
      </view>
    </view>

    <!-- 其他设置 -->
    <view class="setting-section">
      <view class="section-title">其他</view>
      <view class="setting-list">
        <view class="setting-item" bindtap="clearCache">
          <view class="setting-left">
            <view class="setting-icon">🗑️</view>
            <view class="setting-content">
              <view class="setting-title">清除缓存</view>
              <view class="setting-desc">清除本地缓存数据</view>
            </view>
          </view>
          <view class="setting-right">
            <text class="cache-size">{{cacheSize}}</text>
            <text class="iconfont icon-arrow-right"></text>
          </view>
        </view>

        <view class="setting-item" bindtap="checkUpdate">
          <view class="setting-left">
            <view class="setting-icon">🔄</view>
            <view class="setting-content">
              <view class="setting-title">检查更新</view>
              <view class="setting-desc">检查应用更新</view>
            </view>
          </view>
          <view class="setting-right">
            <text class="iconfont icon-arrow-right"></text>
          </view>
        </view>
      </view>
    </view>

    <!-- 退出登录 -->
    <view class="logout-section">
      <view class="logout-btn" bindtap="logout">
        <text class="logout-text">退出登录</text>
      </view>
    </view>
  </view>
</view>
