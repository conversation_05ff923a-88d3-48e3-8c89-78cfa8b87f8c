<template>
  <div class="smart-article-summary-generator">
    <div class="generator-form">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <!-- 选择日记 -->
        <el-form-item label="选择日记" prop="articleId" required>
          <el-select
            v-model="form.articleId"
            filterable
            remote
            reserve-keyword
            placeholder="请输入日记标题进行搜索"
            :remote-method="searchArticles"
            :loading="searchLoading"
            style="width: 100%;"
            @change="handleArticleChange"
          >
            <el-option
              v-for="article in articleOptions"
              :key="article.article_id"
              :label="article.title"
              :value="article.article_id"
            >
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <div style="flex: 1;">
                  <div style="font-weight: 500; color: #333;">{{ article.title }}</div>
                  <div style="color: #8492a6; font-size: 12px; margin-top: 2px;">
                    <i class="el-icon-location"></i> {{ article.location }}
                    <span style="margin-left: 10px;">
                      <i class="el-icon-time"></i> {{ formatDate(article.created_at) }}
                    </span>
                  </div>
                </div>
              </div>
            </el-option>
          </el-select>
          <div class="form-tip">
            <div v-if="searchLoading" style="color: #409eff;">
              <i class="el-icon-loading"></i> 正在搜索您的日记...
            </div>
            <div v-else-if="articleOptions.length === 0" style="color: #f56c6c;">
              <i class="el-icon-warning"></i> 暂无日记数据，请先发布一些旅游日记
            </div>
            <div v-else style="color: #67c23a;">
              找到 {{ articleOptions.length }} 篇日记，支持输入标题进行搜索
            </div>
          </div>
        </el-form-item>

        <!-- 摘要长度 -->
        <el-form-item label="摘要长度" prop="maxLength">
          <el-slider
            v-model="form.maxLength"
            :min="50"
            :max="500"
            :step="50"
            :marks="{ 50: '简短', 200: '适中', 500: '详细' }"
            show-stops
            style="width: 300px;"
          />
          <span style="margin-left: 20px; color: #666;">{{ form.maxLength }}字以内</span>
        </el-form-item>

        <!-- 摘要风格 -->
        <el-form-item label="摘要风格" prop="style">
          <el-radio-group v-model="form.style">
            <el-radio label="简洁">简洁明了</el-radio>
            <el-radio label="详细">详细描述</el-radio>
            <el-radio label="情感">情感丰富</el-radio>
            <el-radio label="客观">客观记录</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 生成按钮 -->
        <el-form-item>
          <el-button
            type="primary"
            @click="generateSummary"
            :loading="loading"
            size="large"
            style="width: 200px;"
          >
            <i class="el-icon-document"></i>
            {{ loading ? '生成中...' : '生成摘要' }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 日记预览 -->
    <div v-if="selectedArticle" class="article-preview">
      <h3>日记预览</h3>
      <div class="preview-content">
        <div class="article-info">
          <h4>{{ selectedArticle.title }}</h4>
          <div class="article-meta">
            <span><i class="el-icon-location"></i> {{ selectedArticle.location }}</span>
            <span><i class="el-icon-time"></i> {{ formatDate(selectedArticle.created_at) }}</span>
            <span><i class="el-icon-document"></i> {{ selectedArticle.content?.length || 0 }}字</span>
          </div>
          <div class="article-content">
            {{ selectedArticle.content?.substring(0, 200) }}{{ selectedArticle.content?.length > 200 ? '...' : '' }}
          </div>
        </div>
      </div>
    </div>

    <!-- 生成结果 -->
    <div v-if="generatedSummary" class="result-section">
      <div class="result-header">
        <h3>📝 生成的摘要</h3>
        <div class="result-actions">
          <el-button @click="copyToClipboard" type="text">
            <i class="el-icon-document-copy"></i>
            复制摘要
          </el-button>
          <el-button @click="regenerateSummary" type="text">
            <i class="el-icon-refresh"></i>
            重新生成
          </el-button>
        </div>
      </div>

      <div class="summary-content">
        <div class="summary-stats">
          <div class="stat-item">
            <span class="label">原文长度:</span>
            <span class="value">{{ selectedArticle?.content?.length || 0 }}字</span>
          </div>
          <div class="stat-item">
            <span class="label">摘要长度:</span>
            <span class="value">{{ generatedSummary.length }}字</span>
          </div>
          <div class="stat-item">
            <span class="label">压缩比:</span>
            <span class="value">{{ compressionRatio }}%</span>
          </div>
          <div class="stat-item">
            <span class="label">风格:</span>
            <span class="value">{{ form.style }}</span>
          </div>
        </div>

        <div class="summary-text">
          <div class="text-content">
            {{ generatedSummary }}
          </div>
        </div>
      </div>
    </div>

    <!-- 错误信息 -->
    <div v-if="error" class="error-section">
      <el-alert :title="error" type="error" show-icon />
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'
import { getCurrentUserId } from '@/utils/userUtils'

export default {
  name: 'SmartArticleSummaryGenerator',
  setup() {
    const formRef = ref(null)
    const loading = ref(false)
    const searchLoading = ref(false)
    const error = ref('')
    const generatedSummary = ref('')
    const articleOptions = ref([])
    const selectedArticle = ref(null)

    const form = reactive({
      articleId: null,
      maxLength: 200,
      style: '简洁'
    })

    const rules = {
      articleId: [
        { required: true, message: '请选择一篇日记', trigger: 'change' }
      ]
    }

    // 计算压缩比
    const compressionRatio = computed(() => {
      if (!selectedArticle.value || !generatedSummary.value) return 0
      const originalLength = selectedArticle.value.content?.length || 0
      const summaryLength = generatedSummary.value.length
      if (originalLength === 0) return 0
      return Math.round((summaryLength / originalLength) * 100)
    })



    // 搜索文章
    const searchArticles = async (query) => {
      searchLoading.value = true
      try {
        const userId = getCurrentUserId()
        if (!userId) {
          ElMessage.warning('请先登录后再使用此功能')
          return
        }

        const response = await axios.get(`http://localhost:5000/api/articles/user/${userId}`)

        if (response.data && response.data.code === 0) {
          let articles = response.data.data || []

          // 如果有搜索词，进行前端过滤
          if (query && query.trim()) {
            const searchQuery = query.trim().toLowerCase()
            articles = articles.filter(article =>
              (article.title && article.title.toLowerCase().includes(searchQuery)) ||
              (article.location && article.location.toLowerCase().includes(searchQuery)) ||
              (article.content && article.content.toLowerCase().includes(searchQuery))
            )
          }

          articleOptions.value = articles.map(article => ({
            article_id: article.article_id,
            title: article.title || '无标题',
            location: article.location || article.location_name || '未知地点',
            content: article.content || '',
            created_at: article.created_at
          }))
        } else {
          articleOptions.value = []
        }
      } catch (error) {
        console.error('搜索文章失败:', error)
        articleOptions.value = []
        ElMessage.error('搜索文章失败，请稍后重试')
      } finally {
        searchLoading.value = false
      }
    }

    // 处理文章选择变化
    const handleArticleChange = (articleId) => {
      selectedArticle.value = articleOptions.value.find(
        article => article.article_id === articleId
      )
    }

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '未知'
      return new Date(dateString).toLocaleDateString('zh-CN')
    }

    // 生成摘要
    const generateSummary = async () => {
      try {
        await formRef.value.validate()

        loading.value = true
        error.value = ''
        generatedSummary.value = ''

        const requestData = {
          article_id: form.articleId,
          max_length: form.maxLength,
          style: form.style
        }

        const response = await axios.post('http://localhost:5000/api/ai/generate_article_summary', requestData)

        if (response.data.code === 0) {
          generatedSummary.value = response.data.data.summary
          ElMessage.success('摘要生成成功！')
        } else {
          error.value = response.data.message || '生成失败'
          ElMessage.error(error.value)
        }
      } catch (error) {
        console.error('生成摘要失败:', error)
        error.value = '生成失败，请稍后重试'
        ElMessage.error('生成失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }

    // 重新生成摘要
    const regenerateSummary = () => {
      generateSummary()
    }

    // 复制到剪贴板
    const copyToClipboard = async () => {
      try {
        await navigator.clipboard.writeText(generatedSummary.value)
        ElMessage.success('摘要已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
        ElMessage.error('复制失败')
      }
    }

    // 初始化时加载用户文章
    onMounted(() => {
      searchArticles('')
    })

    return {
      formRef,
      form,
      rules,
      loading,
      searchLoading,
      error,
      generatedSummary,
      articleOptions,
      selectedArticle,
      compressionRatio,
      searchArticles,
      handleArticleChange,
      formatDate,
      generateSummary,
      regenerateSummary,
      copyToClipboard
    }
  }
}
</script>

<style scoped>
.smart-article-summary-generator {
  max-width: 1000px;
  margin: 0 auto;
}

.generator-form {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 15px;
  margin-bottom: 30px;
}

.form-tip {
  margin-top: 8px;
  font-size: 12px;
}

.article-preview {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 15px;
  margin-bottom: 30px;
}

.article-preview h3 {
  color: #333;
  margin-bottom: 20px;
}

.preview-content {
  background: white;
  padding: 20px;
  border-radius: 10px;
}

.article-info h4 {
  color: #333;
  margin-bottom: 10px;
  font-size: 1.2rem;
}

.article-meta {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
  font-size: 0.9rem;
  color: #666;
}

.article-meta span {
  display: flex;
  align-items: center;
  gap: 5px;
}

.article-content {
  color: #666;
  line-height: 1.6;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.result-section {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.result-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-header h3 {
  margin: 0;
  font-size: 1.4rem;
}

.result-actions {
  display: flex;
  gap: 10px;
}

.summary-content {
  padding: 30px;
}

.summary-stats {
  display: flex;
  justify-content: space-around;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  margin-bottom: 25px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.stat-item .label {
  font-size: 0.9rem;
  color: #666;
}

.stat-item .value {
  font-weight: 600;
  color: #333;
  font-size: 1.1rem;
}

.summary-text {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 10px;
  padding: 25px;
}

.text-content {
  line-height: 1.8;
  color: #333;
  font-size: 1.1rem;
}

.error-section {
  margin-top: 20px;
}

@media (max-width: 768px) {
  .article-meta {
    flex-direction: column;
    gap: 10px;
  }

  .summary-stats {
    flex-direction: column;
    gap: 15px;
  }

  .result-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
}
</style>
