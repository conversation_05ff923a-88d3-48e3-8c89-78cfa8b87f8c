// place-detail.ts
Page({
  data: {
    placeId: 0,
    placeData: null as any,
    loading: true,
    isFavorited: false,
    relatedPlaces: [] as any[],
    userId: 1, // 临时用户ID，实际应该从全局状态获取
    placeImageUrl: '',
    placeTypeText: ''
  },

  onLoad(options: any) {
    console.log('景点详情页面参数:', options);

    if (options.id) {
      this.setData({
        placeId: parseInt(options.id)
      });
      this.loadPlaceDetail();
    } else {
      console.error('未获取到景点ID');
      this.setData({ loading: false });
      wx.showToast({
        title: '景点ID无效',
        icon: 'none'
      });
    }
  },

  // 加载景点详情
  async loadPlaceDetail() {
    this.setData({ loading: true });

    try {
      console.log('开始加载景点详情，ID:', this.data.placeId);

      const response = await this.requestPlaceDetail(this.data.placeId);

      // 修复响应格式判断：后端返回 code: 0 表示成功
      if (response.code === 0 && response.data) {
        const placeData = response.data;
        console.log('景点数据:', placeData);

        this.setData({
          placeData: placeData,
          loading: false,
          placeImageUrl: this.getPlaceImageUrl(placeData.image_url),
          placeTypeText: this.getPlaceTypeText(placeData.type)
        });

        // 加载相关景点
        this.loadRelatedPlaces();

        // 检查收藏状态
        this.checkFavoriteStatus();
      } else {
        console.error('API返回错误:', response);
        this.setData({ loading: false });

        let errorMessage = '加载失败';
        if (response.code === 404) {
          errorMessage = '景点不存在';
        } else if (response.message) {
          errorMessage = response.message;
        }

        wx.showToast({
          title: errorMessage,
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载景点详情失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: '网络连接失败',
        icon: 'none'
      });
    }
  },

  // 请求景点详情
  requestPlaceDetail(placeId: number) {
    return new Promise((resolve) => {
      wx.request({
        url: `http://localhost:5000/api/locations/${placeId}`,
        method: 'GET',
        success: (res) => {
          resolve(res.data);
        },
        fail: (error) => {
          resolve({ code: 1, message: '网络错误', error });
        }
      });
    });
  },

  // 加载相关景点
  async loadRelatedPlaces() {
    try {
      console.log('开始加载相关景点');

      const response = await this.requestAllPlaces();

      if (response.code === 0 && response.data) {
        // 过滤掉当前景点，取前5个作为相关景点
        const allPlaces = response.data;
        const relatedPlaces = allPlaces
          .filter((place: any) => place.location_id !== this.data.placeId)
          .slice(0, 5);

        this.setData({ relatedPlaces: relatedPlaces });
      }
    } catch (error) {
      console.error('加载相关景点失败:', error);
    }
  },

  // 请求所有景点
  requestAllPlaces() {
    return new Promise((resolve) => {
      wx.request({
        url: 'http://localhost:5000/api/locations',
        method: 'GET',
        success: (res) => {
          resolve(res.data);
        },
        fail: (error) => {
          resolve({ code: 1, message: '网络错误', error });
        }
      });
    });
  },

  // 检查收藏状态
  checkFavoriteStatus() {
    // 这里可以添加检查收藏状态的逻辑
    // 暂时设为false
    this.setData({ isFavorited: false });
  },

  // 获取景点类型文本
  getPlaceTypeText(type: number): string {
    const types: { [key: number]: string } = {
      0: '学校',
      1: '景点'
    };
    return types[type] || '未知';
  },

  // 获取景点图片URL
  getPlaceImageUrl(imageUrl: string): string {
    if (!imageUrl) {
      return 'http://localhost:5000/uploads/locations/default_location.jpg';
    }

    if (imageUrl.startsWith('http')) {
      return imageUrl;
    } else {
      return 'http://localhost:5000' + imageUrl;
    }
  },

  // 切换收藏状态
  toggleFavorite() {
    this.setData({
      isFavorited: !this.data.isFavorited
    });

    wx.showToast({
      title: this.data.isFavorited ? '已收藏' : '已取消收藏',
      icon: 'success'
    });
  },

  // 分享景点
  sharePlace() {
    wx.showShareMenu({
      withShareTicket: true
    });
  },

  // 分享给朋友
  onShareAppMessage() {
    return {
      title: this.data.placeData ? this.data.placeData.name : '景点详情',
      path: '/pages/place-detail/place-detail?id=' + this.data.placeId,
      imageUrl: this.data.placeImageUrl
    };
  },

  // 查看图片
  previewImage() {
    if (this.data.placeData) {
      wx.previewImage({
        urls: [this.data.placeImageUrl]
      });
    }
  },

  // 导航到景点
  navigateToPlace() {
    if (this.data.placeData) {
      wx.showModal({
        title: '导航提示',
        content: '是否打开地图应用进行导航？',
        success: (res) => {
          if (res.confirm) {
            wx.showToast({
              title: '导航功能开发中',
              icon: 'none'
            });
          }
        }
      });
    }
  },

  // 查看相关景点
  onRelatedPlaceTap(e: any) {
    const id = e.currentTarget.dataset.id;
    wx.redirectTo({
      url: '/pages/place-detail/place-detail?id=' + id
    });
  },

  // 返回
  goBack() {
    wx.navigateBack();
  }
})
