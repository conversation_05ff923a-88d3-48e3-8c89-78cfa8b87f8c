/* user-center.wxss */
.page {
  background-color: #f8f9fa;
  min-height: 100vh;
  padding-bottom: calc(50px + env(safe-area-inset-bottom));
}

/* 用户头部 */
.user-header {
  position: relative;
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  padding: 80rpx 30rpx 30rpx;
  margin-top: 40rpx;
  color: white;
}

.user-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  opacity: 0.8;
}

.user-info {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.avatar-container {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  margin-right: 30rpx;
}

.avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.avatar-edit {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background-color: #409EFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid white;
}

.avatar-edit .iconfont {
  font-size: 20rpx;
  color: white;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.user-desc {
  font-size: 26rpx;
  opacity: 0.8;
}

/* 登录登出按钮 */
.auth-buttons {
  margin-top: 20rpx;
}

.login-btn, .logout-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50rpx;
  padding: 15rpx 40rpx;
  font-size: 28rpx;
  line-height: 1.2;
}

.login-btn::after, .logout-btn::after {
  border: none;
}

.login-btn:hover, .logout-btn:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

/* 统计信息 */
.stats-container {
  display: flex;
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 20rpx;
  padding: 30rpx;
  backdrop-filter: blur(10rpx);
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-number {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 快捷功能 */
.quick-actions {
  margin: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.action-grid {
  display: flex;
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.action-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 10rpx;
}

.action-icon {
  font-size: 48rpx;
  margin-bottom: 15rpx;
}

.action-text {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

/* 菜单部分 */
.menu-section {
  margin: 0 30rpx 30rpx;
}

.menu-group {
  margin-bottom: 30rpx;
}

.group-title {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 15rpx;
  padding-left: 10rpx;
}

.menu-list {
  background-color: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.menu-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.menu-content {
  flex: 1;
}

.menu-title {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 5rpx;
}

.menu-desc {
  font-size: 24rpx;
  color: #999;
}

.menu-right {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.menu-badge {
  background-color: #ff4757;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  min-width: 20rpx;
  text-align: center;
}

.menu-right .iconfont {
  font-size: 24rpx;
  color: #ccc;
}

/* 版本信息 */
.version-info {
  text-align: center;
  padding: 30rpx;
}

.version-text {
  font-size: 24rpx;
  color: #999;
}
