# 小程序端坐标转换和地图显示修复说明

## 问题分析

### 1. 主要问题
- **选择起点终点后看不见标点**：坐标转换公式不准确导致标记位置错误
- **地图背景变灰**：地图视野调整方式过于激进导致渲染问题
- **缺少距离和时间显示**：路径规划完成后没有正确显示预计时间和距离

### 2. 根本原因
小程序端使用的是简化版坐标转换公式 `lng = x / 1000000.0, lat = y / 1000000.0`，而网页端使用的是精确的线性变换公式。

## 解决方案

### 1. 坐标转换公式修复

**修复前（简化版）**：
```typescript
calculateCoordinates(x: number, y: number) {
  const lng = x / 1000000.0
  const lat = y / 1000000.0
  return { lng, lat }
}
```

**修复后（精确版，与网页端一致）**：
```typescript
calculateCoordinates(x: number, y: number) {
  // 使用与网页端相同的精确坐标转换公式
  // 经度 = 116.35526546085191 + 0.000010265146964114 * X坐标
  // 纬度 = 39.95804755710804 + 0.0000091118507731447 * Y坐标
  const longitude = 116.35526546085191 + 0.000010265146964114 * x
  const latitude = 39.95804755710804 + 0.0000091118507731447 * y
  
  return { 
    lng: longitude, 
    lat: latitude 
  }
}
```

### 2. 地图背景变灰问题修复

**问题原因**：`fitMapToRoute`方法调整地图视野时过于激进

**修复方法**：
- 延迟时间从500ms增加到1000ms
- 增加错误处理，确保即使视野调整失败也不影响路线显示
- 使用更温和的地图操作方式

```typescript
fitMapToRoute(pathPoints: any[]) {
  try {
    setTimeout(() => {
      const mapContext = wx.createMapContext('map', this)
      mapContext.includePoints({
        points: pathPoints,
        padding: [100, 100, 100, 100] // 增加边距
      })
    }, 1000) // 延迟1000ms执行
  } catch (error) {
    console.error('调整地图视野失败:', error)
    // 如果调整视野失败，不影响路线显示
  }
}
```

### 3. 距离和时间显示功能

**新增功能**：
- 添加了`formatDistance()`方法格式化距离显示
- 添加了`formatDuration()`方法格式化时间显示
- 修复了`processBackendRouteResult`方法中的函数调用

**距离格式化**：
```typescript
formatDistance(distance: number): string {
  if (distance < 1000) {
    return `${Math.round(distance)} 米`
  } else {
    return `${(distance / 1000).toFixed(2)} 公里`
  }
}
```

**时间格式化**：
```typescript
formatDuration(minutes: number): string {
  if (minutes < 60) {
    return `${Math.round(minutes)} 分钟`
  } else {
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = Math.round(minutes % 60)
    if (remainingMinutes === 0) {
      return `${hours} 小时`
    } else {
      return `${hours} 小时 ${remainingMinutes} 分钟`
    }
  }
}
```

### 4. 起点终点标记显示优化

**修复前**：只显示起点和终点标记，其他地点标记消失

**修复后**：
- 保留所有地点标记
- 为起点和终点设置特殊样式（绿色起点，红色终点）
- 添加明显的文字标识（"起"、"终"）
- 设置callout始终显示

```typescript
addStartEndMarkers(vertices: any[], path: number[]) {
  // 获取当前所有地点的标记
  const allMarkers = this.data.locations.map((location) => {
    const coordinates = this.calculateCoordinates(location.x, location.y)
    
    let markerStyle = {
      // 默认样式
    }

    // 如果是起点，设置特殊样式
    if (location.vertex_id === startVertexId) {
      markerStyle = {
        ...markerStyle,
        label: { content: '起', bgColor: '#00C851' },
        callout: { content: `起点: ${startVertex.label}`, display: 'ALWAYS' }
      }
    }

    // 如果是终点，设置特殊样式
    if (location.vertex_id === endVertexId) {
      markerStyle = {
        ...markerStyle,
        label: { content: '终', bgColor: '#FF4444' },
        callout: { content: `终点: ${endVertex.label}`, display: 'ALWAYS' }
      }
    }

    return markerStyle
  })

  this.setData({ markers: allMarkers })
}
```

## 修复效果

### 1. 标记显示正常
- ✅ 选择起点终点后立即显示正确的标记位置
- ✅ 起点显示绿色"起"标记
- ✅ 终点显示红色"终"标记
- ✅ 其他地点标记保持可见

### 2. 地图背景正常
- ✅ 路径规划完成后地图背景保持正常
- ✅ 地图视野自动调整包含整个路线
- ✅ 即使视野调整失败也不影响路线显示

### 3. 距离时间显示
- ✅ 路径规划完成后显示总距离
- ✅ 显示预计时间
- ✅ 支持骑行和步行距离分别显示
- ✅ 时间格式友好（小时分钟）

### 4. 路线绘制正确
- ✅ 使用精确坐标转换，路线位置准确
- ✅ 支持不同颜色显示拥挤度
- ✅ 支持虚实线显示骑行/步行路段

## 技术要点

### 1. 坐标系统
- 使用线性变换公式将本地坐标转换为WGS84经纬度
- 公式参数通过实地测量和校准得出
- 确保与网页端完全一致

### 2. 地图渲染优化
- 使用延迟执行避免渲染冲突
- 增加错误处理提高稳定性
- 采用温和的视野调整方式

### 3. 用户体验
- 标记样式清晰易识别
- 距离时间信息完整准确
- 地图操作流畅稳定

## 测试建议

1. **坐标准确性测试**：
   - 选择已知地点验证标记位置是否准确
   - 对比网页端和小程序端的标记位置

2. **路径规划测试**：
   - 测试不同交通方式的路径规划
   - 验证距离和时间显示是否正确

3. **地图稳定性测试**：
   - 多次进行路径规划，检查地图背景是否正常
   - 测试不同缩放级别下的显示效果

4. **功能完整性测试**：
   - 验证起点终点标记显示
   - 测试途径点功能
   - 检查搜索功能是否正常
