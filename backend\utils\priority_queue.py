"""
Custom priority queue implementation
Aligned with Java version's MyPriorityQueue
"""
from typing import TypeVar, Generic, Callable, List, Any, Optional

T = TypeVar('T')

class MyPriorityQueue(Generic[T]):
    """Custom priority queue implementation"""
    
    def __init__(self, comparator: Callable[[T, T], int]):
        """
        Initialize the priority queue
        
        Args:
            comparator: Function to compare items
        """
        self.queue: List[Optional[T]] = [None] * 11  # Initial capacity
        self.size = 0
        self.comparator = comparator
    
    def size(self) -> int:
        """
        Get the size of the queue
        
        Returns:
            Number of items in the queue
        """
        return self.size
    
    def is_empty(self) -> bool:
        """
        Check if the queue is empty
        
        Returns:
            True if the queue is empty, False otherwise
        """
        return self.size == 0
    
    def _resize(self, capacity: int):
        """
        Resize the queue
        
        Args:
            capacity: New capacity
        """
        assert capacity > self.size
        temp = [None] * capacity
        for i in range(self.size):
            temp[i] = self.queue[i]
        self.queue = temp
    
    def poll(self) -> T:
        """
        Remove and return the highest priority item
        
        Returns:
            Highest priority item
        
        Raises:
            IndexError: If the queue is empty
        """
        if self.size == 0:
            raise IndexError("Priority queue is empty")
        
        item = self.queue[0]
        self.queue[0] = self.queue[self.size - 1]
        self.size -= 1
        self._sift_down(0)
        return item
    
    def peek(self) -> T:
        """
        Return the highest priority item without removing it
        
        Returns:
            Highest priority item
        
        Raises:
            IndexError: If the queue is empty
        """
        if self.size == 0:
            raise IndexError("Priority queue is empty")
        
        return self.queue[0]
    
    def offer(self, item: T):
        """
        Add an item to the queue
        
        Args:
            item: Item to add
        """
        if self.size == len(self.queue):
            self._resize(self.size * 2)
        
        self.queue[self.size] = item
        self.size += 1
        self._sift_up(self.size - 1)
    
    def _sift_up(self, k: int):
        """
        Sift up an item
        
        Args:
            k: Index of the item to sift up
        """
        while k > 0:
            parent = (k - 1) >> 1
            if self.comparator(self.queue[k], self.queue[parent]) >= 0:
                break
            
            self.queue[k], self.queue[parent] = self.queue[parent], self.queue[k]
            k = parent
    
    def _sift_down(self, k: int):
        """
        Sift down an item
        
        Args:
            k: Index of the item to sift down
        """
        half = self.size >> 1
        while k < half:
            child = (k << 1) + 1
            right = child + 1
            
            if right < self.size and self.comparator(self.queue[right], self.queue[child]) < 0:
                child = right
            
            if self.comparator(self.queue[k], self.queue[child]) <= 0:
                break
            
            self.queue[k], self.queue[child] = self.queue[child], self.queue[k]
            k = child
