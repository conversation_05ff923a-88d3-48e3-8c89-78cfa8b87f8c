let userTable = { 
  users: [
    {
      id: 1,
      username: 'admin',
      password: '123456', // 实际应用中应该使用加密密码
      email: '<EMAIL>',
      avatar: null,
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z'
    },
    {
      id: 2,
      username: 'test',
      password: 'test123', // 实际应用中应该使用加密密码
      email: '<EMAIL>',
      avatar: null,
      created_at: '2023-01-02T00:00:00Z',
      updated_at: '2023-01-02T00:00:00Z'
    }
  ],
   
 
} 
//从 localStorage 加载数据
try {
  const savedData = localStorage.getItem('mockUserData')
  if (savedData) {
    userTable = JSON.parse(savedData)
    console.log('已从 localStorage 加载用户数据:', userTable)
  } else {
    console.log('未找到保存的用户数据，使用默认数据')
  }
} catch (error) {
  console.error('加载用户数据失败:', error)
}
// 保存数据到 localStorage
const saveData = () => {
  try {
    localStorage.setItem('mockUserData', JSON.stringify(userTable))
    console.log('用户数据已保存到 localStorage:', userTable)
  } catch (error) {
    console.error('保存用户数据失败:', error)
  }
}
// 生成新的用户ID
const generateUserId = () => {
  const maxId = Math.max(...userTable.users.map(user => user.id), 0)
  return maxId + 1
}
// 获取当前时间戳
const getCurrentTimestamp = () => {
  return new Date().toISOString()
}
// 通过邮箱查找用户
export const findUserByEmail = (email) => {
  const user = userTable.users.find(user => user.email === email)
  // 确保返回的用户对象中 avatar 为 null 时不会被覆盖
  if (user) {
    return {
      ...user,
      avatar: user.avatar === undefined ? null : user.avatar
    }
  }
  return null
}

// 通过用户名查找用户
export const findUserByUsername = (username) => {
  const user = userTable.users.find(user => user.username === username)
  // 确保返回的用户对象中 avatar 为 null 时不会被覆盖
  if (user) {
    return {
      ...user,
      avatar: user.avatar === undefined ? null : user.avatar
    }
  }
  return null
}
// 通过ID查找用户
export const findUserById = (id) => {
  const user = userTable.users.find(user => user.id === id)
  // 确保返回的用户对象中 avatar 为 null 时不会被覆盖
  if (user) {
    return {
      ...user,
      avatar: user.avatar === undefined ? null : user.avatar
    }
  }
  return null
}
// 验证用户登录
export const validateLogin = (email, password) => {
  const user = findUserByEmail(email)
  if (!user) {
    return {
      success: false,
      message: '用户不存在'
    }
  }
  
  if (user.password !== password) {
    return {
      success: false,
      message: '密码错误'
    }
  }
  // 更新最后登录时间
  user.updated_at = getCurrentTimestamp()
  saveData() // 保存更新后的数据
  return {
    success: true,
    user: {
      id: user.id,
      username: user.username,
      email: user.email,
      avatar: user.avatar
    }
  }
}
// 注册新用户
export const registerUser = (userData) => {
  // 检查邮箱是否已存在
  if (findUserByEmail(userData.email)) {
    return {
      success: false,
      message: '该邮箱已被注册'
    }
  }
  // 检查用户名是否已存在
  if (findUserByUsername(userData.username)) {
    return {
      success: false,
      message: '该用户名已被使用'
    }
  }
  // 创建新用户
  const newUser = {
    id: generateUserId(),
    username: userData.username,
    password: userData.password, // 实际应用中应该加密
    email: userData.email,
    avatar: null, // 确保新用户头像为 null
    created_at: getCurrentTimestamp(),
    updated_at: getCurrentTimestamp()
  }
  // 添加新用户
  userTable.users.push(newUser)
  
  // 确保 userSettings 存在
  if (!userTable.userSettings) {
    userTable.userSettings = []
  }
  // 创建用户默认设置
  userTable.userSettings.push({
    user_id: newUser.id,
    theme: 'light',
    language: 'zh-CN',
    notifications: true
  })
  // 保存更新后的数据
  saveData()
  
  console.log('注册新用户成功:', newUser)
  console.log('当前用户表:', userTable.users)
  
  return {
    success: true,
    user: {
      id: newUser.id,
      username: newUser.username,
      email: newUser.email,
      avatar: newUser.avatar // 确保返回 null
    }
  }
}

// 更新用户头像
export const updateUserAvatar = (email, avatar) => {
  const user = userTable.users.find(u => u.email === email)
  if (user) {
    user.avatar = avatar
    user.updated_at = getCurrentTimestamp()
    saveData() // 保存更新后的数据到 localStorage
    return {
      success: true,
      avatar: avatar
    }
  }
  
  return {
    success: false,
    message: '用户不存在'
  }
}
// 获取用户设置
export const getUserSettings = (userId) => {
  return userTable.userSettings.find(setting => setting.user_id === userId)
}
// 更新用户设置
export const updateUserSettings = (userId, settings) => {
  const settingIndex = userTable.userSettings.findIndex(setting => setting.user_id === userId)
  
  if (settingIndex !== -1) {
    userTable.userSettings[settingIndex] = {
      ...userTable.userSettings[settingIndex],
      ...settings
    }
    saveData() // 保存更新后的数据
    return {
      success: true,
      settings: userTable.userSettings[settingIndex]
    }
  }
  
  return {
    success: false,
    message: '用户设置不存在'
  }
}

// 获取所有用户数据（仅用于开发测试）
export const getAllUsers = () => {
  return userTable.users
}

// 获取所有表数据（仅用于开发测试）
export const getAllTables = () => {
  return userTable
}

// 导出数据到 JSON 文件（仅用于开发测试）
export const exportDataToJson = () => {
  const dataStr = JSON.stringify(userTable, null, 2)
  const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)
  
  const exportFileDefaultName = 'userData.json'
  
  const linkElement = document.createElement('a')
  linkElement.setAttribute('href', dataUri)
  linkElement.setAttribute('download', exportFileDefaultName)
  linkElement.click()
}
// 从 JSON 文件导入数据（仅用于开发测试）
export const importDataFromJson = (jsonData) => {
  try {
    userTable = JSON.parse(jsonData)
    saveData() // 保存导入的数据
    return {
      success: true,
      message: '数据导入成功'
    }
  } catch (error) {
    return {
      success: false,
      message: '数据导入失败: ' + error.message
    }
  }
} 