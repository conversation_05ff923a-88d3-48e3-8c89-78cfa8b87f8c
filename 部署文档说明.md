# 鸿雁智游个性化旅游系统 - 部署文档说明

## 📋 文档概览

本项目提供了完整的部署和运行文档，帮助您快速搭建和运行鸿雁智游个性化旅游系统。

## 📚 文档列表

### 1. 环境配置说明.md
**用途**: 详细的环境配置指南
**内容包括**:
- 系统概述和技术架构
- 环境要求和开发工具
- Python、Node.js、MySQL环境配置
- 依赖安装和配置文件设置
- API密钥申请指南
- 常见问题解决方案

**适用人群**: 首次部署系统的开发者

### 2. 运行指南.md
**用途**: 系统启动和运行的详细步骤
**内容包括**:
- 系统启动顺序
- 各服务详细启动步骤
- 功能验证方法
- 性能监控指南
- 问题排查方法
- 日志查看和分析

**适用人群**: 需要启动和维护系统的开发者

### 3. 快速启动指南.md
**用途**: 简化的启动流程和脚本说明
**内容包括**:
- 一键启动脚本使用方法
- 快速验证步骤
- 常见启动问题解决
- 性能优化建议
- 监控和备份指南

**适用人群**: 熟悉系统的开发者和运维人员

## 🚀 启动脚本

### Windows 用户
- `start_system.bat` - 一键启动所有服务
- `check_system.bat` - 系统状态检查

### macOS/Linux 用户
- `start_system.sh` - 一键启动所有服务
- `stop_system.sh` - 停止所有服务
- `check_system.sh` - 系统状态检查

## 📖 使用建议

### 首次部署流程
1. **阅读环境配置说明** → `环境配置说明.md`
2. **按步骤配置环境** → 安装依赖、配置数据库、设置API密钥
3. **验证配置** → 运行 `check_system.sh` 或 `check_system.bat`
4. **启动系统** → 使用启动脚本或参考运行指南
5. **功能测试** → 验证各项功能正常工作

### 日常使用流程
1. **快速启动** → 使用 `start_system.sh` 或 `start_system.bat`
2. **状态检查** → 使用 `check_system.sh` 或 `check_system.bat`
3. **问题排查** → 参考运行指南中的排查方法

### 维护和优化
1. **性能监控** → 参考快速启动指南中的监控方法
2. **日志分析** → 使用运行指南中的日志分析命令
3. **备份恢复** → 按照快速启动指南中的备份方法

## 🔧 脚本权限设置

### Linux/macOS 用户需要设置执行权限:
```bash
chmod +x start_system.sh
chmod +x stop_system.sh
chmod +x check_system.sh
```

### Windows 用户可以直接双击运行 .bat 文件

## 📁 项目结构说明

```
personalized-travel-system/
├── backend/                    # 后端服务
│   ├── app.py                 # Flask应用入口
│   ├── config.py              # 配置文件
│   ├── requirements.txt       # Python依赖
│   └── venv/                  # Python虚拟环境
├── frontend_logged/           # Web前端
│   └── travel_system_logged/  # Vue.js应用
├── Wechat_miniP/             # 微信小程序
├── proxy-server.js           # 代理服务器
├── package.json              # Node.js依赖配置
├── start_system.sh           # Linux/macOS启动脚本
├── start_system.bat          # Windows启动脚本
├── stop_system.sh            # Linux/macOS停止脚本
├── check_system.sh           # Linux/macOS检查脚本
├── check_system.bat          # Windows检查脚本
├── 环境配置说明.md            # 环境配置文档
├── 运行指南.md               # 运行指南文档
├── 快速启动指南.md           # 快速启动文档
└── 部署文档说明.md           # 本文档
```

## 🌐 系统访问地址

启动成功后，可以通过以下地址访问系统：

- **Web前端**: http://localhost:8080
- **后端API**: http://localhost:5000
- **代理服务**: http://localhost:3000
- **API文档**: http://localhost:5000/api/docs (如果启用)

## 🔍 系统功能概览

### 核心功能
- **旅游推荐**: 基于协同过滤和内容推荐的智能推荐系统
- **路径规划**: 支持最短距离、最短时间、多点路径规划
- **场所查询**: 附近设施查询和距离排序
- **旅游日记**: 支持图片、视频上传和AIGC动画生成

### 技术特色
- **自实现算法**: 堆排序、哈希表、图算法等数据结构算法
- **AI集成**: DeepSeek大模型和豆包AIGC服务
- **多端支持**: Web端和微信小程序端
- **高性能**: 优化的查询和推荐算法

## 🆘 获取帮助

### 常见问题
1. **服务启动失败** → 查看 `运行指南.md` 中的问题排查部分
2. **依赖安装失败** → 参考 `环境配置说明.md` 中的解决方案
3. **数据库连接失败** → 检查MySQL服务和配置文件
4. **API调用失败** → 验证API密钥配置和网络连接

### 技术支持
- 查看项目文档和调试文档
- 运行系统检查脚本诊断问题
- 查看日志文件获取详细错误信息

## 📝 更新日志

- **v1.0** - 初始版本，包含完整的部署文档和启动脚本
- 支持Windows、macOS、Linux多平台部署
- 提供自动化的系统检查和启动脚本

## 🎯 下一步计划

- 添加Docker容器化部署支持
- 提供云服务器部署指南
- 增加自动化测试脚本
- 完善监控和告警系统

---

**开始使用**: 建议从 `环境配置说明.md` 开始阅读，然后按照文档步骤进行部署。

**快速体验**: 如果环境已配置，可以直接运行启动脚本快速启动系统。
