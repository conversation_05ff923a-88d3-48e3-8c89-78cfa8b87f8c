"""
Recommendation system factory
This module provides a factory for creating recommendation system instances
"""

from utils.algorithm_recommendation import AlgorithmRecommendationSystem

class RecommendationFactory:
    """
    Factory for creating recommendation system instances
    Uses the Singleton pattern to ensure only one instance of each type exists
    """
    
    _instance = None
    
    @classmethod
    def get_algorithm_recommendation_system(cls):
        """
        Get the algorithm-based recommendation system instance
        
        Returns:
            AlgorithmRecommendationSystem: The algorithm-based recommendation system
        """
        if cls._instance is None:
            cls._instance = AlgorithmRecommendationSystem()
        return cls._instance
    
    @classmethod
    def refresh_all_data(cls, force=False):
        """
        Refresh all data in the recommendation system
        
        Args:
            force: Force refresh even if cache is not expired
        """
        if cls._instance is not None:
            cls._instance.refresh_all_data(force=force)
