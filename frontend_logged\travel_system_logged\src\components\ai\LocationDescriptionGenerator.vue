<template>
  <div class="location-description-generator">
    <div class="generator-header">
      <h2>智能地点描述生成</h2>
      <p>为景点生成详细生动的描述文本，突出特色和魅力</p>
    </div>

    <div class="form-section">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
        <!-- 地点选择 -->
        <el-form-item label="选择地点" prop="locationId" required>
          <el-autocomplete
            v-model="currentLocationInput"
            :fetch-suggestions="queryLocationSuggestions"
            placeholder="请输入地点名称进行搜索"
            clearable
            @select="handleLocationSelect"
            :trigger-on-focus="true"
            :highlight-first-item="true"
            :debounce="300"
            style="width: 100%"
          >
            <template #default="{ item }">
              <div class="location-suggestion-item">
                <div class="location-name">{{ item.name }}</div>
                <div class="location-type">{{ getLocationTypeText(item.type) }}</div>
              </div>
            </template>
          </el-autocomplete>
          <div class="form-tip">
            支持输入地点名称或关键词进行模糊搜索
          </div>
        </el-form-item>

        <!-- 描述风格 -->
        <el-form-item label="描述风格" prop="style">
          <el-radio-group v-model="form.style">
            <el-radio label="简洁">简洁明了</el-radio>
            <el-radio label="详细">详细全面</el-radio>
            <el-radio label="文学">文学优美</el-radio>
            <el-radio label="实用">实用导向</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 描述重点 -->
        <el-form-item label="描述重点" prop="focus">
          <el-checkbox-group v-model="form.focus">
            <el-checkbox label="历史">历史背景</el-checkbox>
            <el-checkbox label="文化">文化内涵</el-checkbox>
            <el-checkbox label="建筑">建筑特色</el-checkbox>
            <el-checkbox label="自然">自然风光</el-checkbox>
            <el-checkbox label="体验">游览体验</el-checkbox>
            <el-checkbox label="交通">交通指南</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <!-- 生成按钮 -->
        <el-form-item>
          <el-button
            type="primary"
            @click="generateDescription"
            :loading="loading"
            size="large"
            style="width: 200px"
          >
            <i class="el-icon-place"></i>
            {{ loading ? '生成中...' : '生成描述' }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 地点预览 -->
    <div v-if="selectedLocation" class="location-preview">
      <h3>地点信息</h3>
      <div class="preview-content">
        <div class="location-info">
          <h4>{{ selectedLocation.name }}</h4>
          <div class="location-meta">
            <span><i class="el-icon-location"></i> {{ selectedLocation.type }}</span>
            <span v-if="selectedLocation.rating">
              <i class="el-icon-star-on"></i> {{ selectedLocation.rating }} 分
            </span>
          </div>
        </div>
        <div v-if="selectedLocation.description" class="location-desc">
          <strong>现有描述：</strong>
          {{ selectedLocation.description }}
        </div>
      </div>
    </div>

    <!-- 生成结果 -->
    <div v-if="generatedDescription" class="result-section">
      <div class="result-header">
        <h3>
          <i class="el-icon-document"></i>
          精彩地点描述
        </h3>
        <div class="result-actions">
          <el-button type="success" @click="copyToClipboard" size="small">
            <i class="el-icon-document-copy"></i>
            复制描述
          </el-button>
          <el-button type="primary" @click="regenerateDescription" size="small">
            <i class="el-icon-refresh"></i>
            重新生成
          </el-button>
        </div>
      </div>

      <div class="description-content">
        <!-- 地点信息卡片 -->
        <div v-if="selectedLocation" class="location-info-card">
          <div class="location-header">
            <div class="location-avatar">
              <i class="el-icon-location-outline"></i>
            </div>
            <div class="location-details">
              <h4>{{ selectedLocation.name }}</h4>
              <div class="location-meta">
                <el-tag size="small" :type="getLocationTagType(selectedLocation.type)">
                  {{ getLocationTypeText(selectedLocation.type) }}
                </el-tag>
                <span class="location-id">ID: {{ selectedLocation.location_id }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 描述内容 -->
        <div class="description-main">
          <div class="description-header">
            <h4>
              <i class="el-icon-edit-outline"></i>
              AI生成描述
            </h4>
            <div class="description-badges">
              <el-tag size="small" type="info">{{ form.style }}</el-tag>
              <el-tag size="small" type="warning">{{ form.focus.join(' · ') }}</el-tag>
            </div>
          </div>

          <div class="description-text">
            <div class="text-content">
              <MarkdownRenderer :content="generatedDescription" />
            </div>
            <div class="text-decoration">
              <div class="quote-mark">"</div>
            </div>
          </div>

          <div class="description-stats">
            <div class="stat-item">
              <i class="el-icon-document"></i>
              <span>{{ generatedDescription.length }} 字</span>
            </div>
            <div class="stat-item">
              <i class="el-icon-view"></i>
              <span>{{ form.style }}风格</span>
            </div>
            <div class="stat-item">
              <i class="el-icon-star-on"></i>
              <span>{{ form.focus.length }}个重点</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="error-section">
      <el-alert
        :title="error"
        type="error"
        :closable="false"
        show-icon
      />
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import aiApi from '@/api/ai'
import axios from 'axios'
import MarkdownRenderer from '@/components/common/MarkdownRenderer.vue'

export default {
  name: 'LocationDescriptionGenerator',
  components: {
    MarkdownRenderer
  },
  emits: ['generated'],
  setup(props, { emit }) {
    const formRef = ref(null)
    const loading = ref(false)
    const error = ref('')
    const generatedDescription = ref('')
    const selectedLocation = ref(null)
    const currentLocationInput = ref('')

    const form = reactive({
      locationId: null,
      locationName: '',
      style: '详细',
      focus: ['历史', '文化']
    })

    const rules = {
      locationId: [
        { required: true, message: '请选择一个地点', trigger: 'change' }
      ],
      style: [
        { required: true, message: '请选择描述风格', trigger: 'change' }
      ]
    }

    // 获取地点类型文本
    const getLocationTypeText = (type) => {
      const typeMap = {
        0: '教育',
        1: '文化',
        2: '自然',
        3: '娱乐'
      }
      return typeMap[type] || '未知'
    }

    // 获取地点标签类型
    const getLocationTagType = (type) => {
      const tagTypeMap = {
        0: 'primary',   // 教育
        1: 'warning',   // 文化
        2: 'success',   // 自然
        3: 'info'       // 娱乐
      }
      return tagTypeMap[type] || 'info'
    }

    // 地点建议查询 - 参考旅游日记的实现
    const queryLocationSuggestions = async (queryString, callback) => {
      console.log('LocationDescriptionGenerator - queryLocationSuggestions 被调用，查询字符串:', queryString)

      if (!queryString || queryString.trim().length < 1) {
        console.log('LocationDescriptionGenerator - 查询字符串为空，返回空数组')
        callback([])
        return
      }

      try {
        console.log('LocationDescriptionGenerator - 准备调用API')
        const response = await axios.get(`http://localhost:5000/api/locations/query`, {
          params: {
            name: queryString,
            sortOrder: '0'
          }
        })

        console.log('LocationDescriptionGenerator - API响应:', response.data)

        if (response.data && Array.isArray(response.data)) {
          const suggestions = response.data.slice(0, 10).map(location => ({
            ...location,
            value: location.name,
            name: location.name,
            type: location.type,
            location_id: location.location_id
          }))

          console.log('LocationDescriptionGenerator - 转换后的建议数据:', suggestions)
          callback(suggestions)
        } else {
          console.log('LocationDescriptionGenerator - 响应数据格式不正确或为空')
          callback([])
        }
      } catch (error) {
        console.error('LocationDescriptionGenerator - 获取地点建议失败:', error)
        callback([])
      }
    }

    // 处理地点选择
    const handleLocationSelect = (item) => {
      console.log('LocationDescriptionGenerator - 选择了地点:', item)
      if (item && item.location_id) {
        form.locationId = item.location_id
        form.locationName = item.name
        selectedLocation.value = item
        currentLocationInput.value = item.name
      }
    }

    // 生成描述
    const generateDescription = async () => {
      try {
        await formRef.value.validate()

        loading.value = true
        error.value = ''
        generatedDescription.value = ''

        const requestData = {
          location_id: form.locationId,
          style: form.style,
          focus: form.focus.join(', ')
        }

        const response = await aiApi.generateLocationDescription(requestData)

        if (response.code === 0) {
          generatedDescription.value = response.data.description
          emit('generated', generatedDescription.value)
          ElMessage.success('描述生成成功！')
        } else {
          error.value = response.message || '生成失败'
          ElMessage.error(error.value)
        }
      } catch (error) {
        console.error('生成描述失败:', error)
        error.value = '生成失败，请稍后重试'
        ElMessage.error('生成失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }

    // 重新生成描述
    const regenerateDescription = () => {
      generateDescription()
    }

    // 复制到剪贴板
    const copyToClipboard = async () => {
      try {
        await navigator.clipboard.writeText(generatedDescription.value)
        ElMessage.success('描述已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
        ElMessage.error('复制失败')
      }
    }

    return {
      formRef,
      form,
      rules,
      loading,
      error,
      generatedDescription,
      selectedLocation,
      currentLocationInput,
      getLocationTypeText,
      getLocationTagType,
      queryLocationSuggestions,
      handleLocationSelect,
      generateDescription,
      regenerateDescription,
      copyToClipboard
    }
  }
}
</script>

<style scoped>
.location-description-generator {
  max-width: 800px;
  margin: 0 auto;
}

.generator-header {
  text-align: center;
  margin-bottom: 40px;
}

.generator-header h2 {
  color: #333;
  margin-bottom: 10px;
}

.generator-header p {
  color: #666;
  font-size: 1rem;
}

.form-section {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 15px;
  margin-bottom: 30px;
}

.form-tip {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
}

.location-suggestion-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.location-name {
  font-weight: 500;
  color: #333;
}

.location-type {
  font-size: 12px;
  color: #8492a6;
  background: #f0f2f5;
  padding: 2px 6px;
  border-radius: 3px;
}

.location-preview {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 15px;
  margin-bottom: 30px;
}

.location-preview h3 {
  color: #333;
  margin-bottom: 20px;
}

.preview-content {
  background: white;
  padding: 20px;
  border-radius: 10px;
}

.location-info h4 {
  color: #333;
  margin-bottom: 10px;
  font-size: 1.2rem;
}

.location-meta {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
  font-size: 0.9rem;
  color: #666;
}

.location-meta span {
  display: flex;
  align-items: center;
  gap: 5px;
}

.location-desc {
  color: #666;
  line-height: 1.6;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.result-section {
  margin-top: 30px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
  border-radius: 15px;
  color: white;
}

.result-header h3 {
  margin: 0;
  font-size: 1.4rem;
  display: flex;
  align-items: center;
  gap: 10px;
}

.result-actions {
  display: flex;
  gap: 10px;
}

.description-content {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 15px;
}

/* 地点信息卡片 */
.location-info-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.location-header {
  display: flex;
  align-items: center;
  gap: 15px;
}

.location-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.3rem;
}

.location-details h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1.3rem;
  font-weight: 600;
}

.location-meta {
  display: flex;
  align-items: center;
  gap: 15px;
}

.location-id {
  font-size: 0.85rem;
  color: #666;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
}

/* 描述主体 */
.description-main {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.description-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e9ecef;
}

.description-header h4 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.description-badges {
  display: flex;
  gap: 8px;
}

.description-text {
  padding: 30px;
  position: relative;
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
}

.text-content {
  line-height: 1.8;
  color: #333;
  font-size: 1.1rem;
  position: relative;
  z-index: 2;
}

.text-decoration {
  position: absolute;
  top: 10px;
  right: 20px;
  z-index: 1;
}

.quote-mark {
  font-size: 6rem;
  color: rgba(23, 162, 184, 0.1);
  font-family: serif;
  line-height: 1;
}

.description-stats {
  display: flex;
  justify-content: space-around;
  padding: 20px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 0.9rem;
}

.stat-item i {
  color: #17a2b8;
}

.error-section {
  margin-top: 20px;
}

@media (max-width: 768px) {
  .result-header {
    flex-direction: column;
    gap: 15px;
  }

  .result-actions {
    width: 100%;
    justify-content: center;
  }

  .location-meta {
    flex-direction: column;
    gap: 10px;
  }

  .description-stats {
    flex-direction: column;
    gap: 5px;
    text-align: center;
  }
}
</style>
