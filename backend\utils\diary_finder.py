"""
游记查找工具类

该模块实现了各种游记查找算法，包括：
1. 二分查找
2. 哈希查找
3. Boyer<PERSON>Moore文本搜索
"""

from typing import List, Optional, Tuple
from models.article import Article
import re

class DiaryFinder:
    """游记查找工具类"""

    @staticmethod
    def interpolation_search_by_title(diaries: List[Article], title: str) -> Optional[Article]:
        """
        使用插值查找算法按标题精确查找游记（更高效的查找算法）
        对于均匀分布的数据，时间复杂度为O(log log n)

        Args:
            diaries: 已按标题排序的游记列表
            title: 要查找的标题

        Returns:
            找到的游记，未找到则返回None
        """
        if not diaries or not title:
            return None

        left, right = 0, len(diaries) - 1

        while left <= right and title >= diaries[left].title and title <= diaries[right].title:
            # 如果只有一个元素
            if left == right:
                if diaries[left].title == title:
                    return diaries[left]
                return None

            # 计算插值位置
            # 使用字符串的ASCII值进行插值计算
            left_val = DiaryFinder._string_to_numeric(diaries[left].title)
            right_val = DiaryFinder._string_to_numeric(diaries[right].title)
            target_val = DiaryFinder._string_to_numeric(title)

            # 避免除零错误
            if right_val == left_val:
                pos = left
            else:
                pos = left + int(((target_val - left_val) / (right_val - left_val)) * (right - left))

            # 确保pos在有效范围内
            pos = max(left, min(pos, right))

            if diaries[pos].title == title:
                return diaries[pos]
            elif diaries[pos].title < title:
                left = pos + 1
            else:
                right = pos - 1

        return None

    @staticmethod
    def _string_to_numeric(s: str) -> float:
        """
        将字符串转换为数值，用于插值查找

        Args:
            s: 输入字符串

        Returns:
            字符串的数值表示
        """
        if not s:
            return 0.0

        # 取前8个字符计算数值，避免溢出
        result = 0.0
        for i, char in enumerate(s[:8]):
            result += ord(char) * (256 ** (7 - i))

        return result

    @staticmethod
    def binary_search_by_title(diaries: List[Article], title: str) -> Optional[Article]:
        """
        使用二分查找算法按标题精确查找游记（保留作为备用方法）

        Args:
            diaries: 已按标题排序的游记列表
            title: 要查找的标题

        Returns:
            找到的游记，未找到则返回None
        """
        if not diaries or not title:
            return None

        # 二分查找
        left, right = 0, len(diaries) - 1

        while left <= right:
            mid = (left + right) // 2
            mid_title = diaries[mid].title

            if mid_title == title:
                return diaries[mid]
            elif mid_title < title:
                left = mid + 1
            else:
                right = mid - 1

        return None

    @staticmethod
    def hash_search_by_title(diaries: List[Article], title: str) -> Optional[Article]:
        """
        使用哈希表进行O(1)复杂度的标题精确查找

        Args:
            diaries: 游记列表
            title: 要查找的标题

        Returns:
            找到的游记，未找到则返回None
        """
        # 构建哈希表
        title_map = {diary.title: diary for diary in diaries}

        # O(1)查找
        return title_map.get(title)

    @staticmethod
    def boyer_moore_search(text: str, pattern: str) -> bool:
        """
        使用Boyer-Moore算法进行字符串匹配

        Args:
            text: 待搜索的文本
            pattern: 搜索模式

        Returns:
            是否匹配成功
        """
        if not pattern:
            return True

        if not text:
            return False

        # 将文本和模式转换为小写，用于不区分大小写的匹配
        text = text.lower()
        pattern = pattern.lower()

        n, m = len(text), len(pattern)

        if m > n:
            return False

        # 构建坏字符规则
        bad_char = {}
        for i in range(m - 1):
            bad_char[pattern[i]] = m - 1 - i

        # 模式串中最后一个字符的偏移量
        skip = bad_char.get(pattern[m - 1], m)
        bad_char[pattern[m - 1]] = skip

        # 搜索
        i = m - 1
        while i < n:
            j = m - 1
            k = i
            while j >= 0 and text[k] == pattern[j]:
                j -= 1
                k -= 1

            if j == -1:
                return True

            i += bad_char.get(text[i], m)

        return False

    @staticmethod
    def full_text_search(diaries: List[Article], keyword: str) -> List[Tuple[Article, float]]:
        """
        全文搜索，返回游记及其相关度得分

        Args:
            diaries: 游记列表
            keyword: 查询关键字

        Returns:
            匹配的游记列表及其相关度得分
        """
        if not keyword:
            return [(diary, 1.0) for diary in diaries]

        # 将关键字转换为小写，用于不区分大小写的匹配
        keyword_lower = keyword.lower()

        # 分词
        keywords = re.findall(r'\w+', keyword_lower)

        results = []
        for diary in diaries:
            # 获取游记内容（假设已解压）
            content = diary.content
            if isinstance(content, bytes) and hasattr(diary, 'huffman_codes') and diary.huffman_codes:
                try:
                    from services.article_service import ArticleService
                    import json
                    service = ArticleService()
                    huffman_codes = json.loads(diary.huffman_codes)
                    content = service.decompress_text(content, huffman_codes)
                except Exception:
                    content = ""

            # 如果内容仍然是bytes类型，尝试解码
            if isinstance(content, bytes):
                try:
                    content = content.decode('utf-8')
                except Exception:
                    content = ""

            # 计算相关度得分
            score = 0.0

            # 标题匹配权重更高
            title_lower = diary.title.lower() if diary.title else ""
            for word in keywords:
                if word in title_lower:
                    score += 0.6  # 标题匹配权重

            # 内容匹配
            content_lower = content.lower() if content else ""
            for word in keywords:
                if word in content_lower:
                    score += 0.3  # 内容匹配权重

                    # 计算出现次数，增加权重
                    count = content_lower.count(word)
                    score += min(count / 10, 0.3)  # 最多增加0.3的权重

            # 只返回相关度大于0的结果
            if score > 0:
                results.append((diary, score))

        # 按相关度降序排序
        results.sort(key=lambda x: x[1], reverse=True)

        return results

    # ===== 高效查找算法实现 =====

    @staticmethod
    def trie_search_by_prefix(diaries: List[Article], prefix: str) -> List[Article]:
        """
        使用Trie树进行前缀查找（更高效的前缀匹配）
        时间复杂度: O(m + k)，m是前缀长度，k是匹配结果数量

        Args:
            diaries: 游记列表
            prefix: 查找前缀

        Returns:
            匹配前缀的游记列表
        """
        if not prefix:
            return diaries

        # 构建Trie树
        trie = DiaryFinder._build_trie(diaries)

        # 查找前缀匹配的游记
        return DiaryFinder._search_trie(trie, prefix.lower())

    @staticmethod
    def _build_trie(diaries: List[Article]) -> dict:
        """
        构建Trie树

        Args:
            diaries: 游记列表

        Returns:
            Trie树根节点
        """
        root = {}

        for diary in diaries:
            if not diary.title:
                continue

            current = root
            title_lower = diary.title.lower()

            # 插入标题的每个字符
            for char in title_lower:
                if char not in current:
                    current[char] = {}
                current = current[char]

            # 在叶子节点存储游记对象
            if 'articles' not in current:
                current['articles'] = []
            current['articles'].append(diary)

        return root

    @staticmethod
    def _search_trie(trie: dict, prefix: str) -> List[Article]:
        """
        在Trie树中搜索前缀

        Args:
            trie: Trie树根节点
            prefix: 搜索前缀

        Returns:
            匹配的游记列表
        """
        current = trie

        # 遍历前缀的每个字符
        for char in prefix:
            if char not in current:
                return []  # 前缀不存在
            current = current[char]

        # 收集所有匹配的游记
        results = []
        DiaryFinder._collect_articles(current, results)

        return results

    @staticmethod
    def _collect_articles(node: dict, results: List[Article]):
        """
        递归收集Trie树节点下的所有游记

        Args:
            node: Trie树节点
            results: 结果列表
        """
        if 'articles' in node:
            results.extend(node['articles'])

        for key, child in node.items():
            if key != 'articles' and isinstance(child, dict):
                DiaryFinder._collect_articles(child, results)

    @staticmethod
    def fuzzy_search_with_levenshtein(diaries: List[Article], query: str, max_distance: int = 2) -> List[Tuple[Article, int]]:
        """
        使用Levenshtein距离进行模糊查找

        Args:
            diaries: 游记列表
            query: 查询字符串
            max_distance: 最大编辑距离

        Returns:
            匹配的游记及其编辑距离
        """
        if not query:
            return [(diary, 0) for diary in diaries]

        results = []
        query_lower = query.lower()

        for diary in diaries:
            if not diary.title:
                continue

            title_lower = diary.title.lower()
            distance = DiaryFinder._levenshtein_distance(query_lower, title_lower)

            if distance <= max_distance:
                results.append((diary, distance))

        # 按编辑距离排序
        results.sort(key=lambda x: x[1])

        return results

    @staticmethod
    def _levenshtein_distance(s1: str, s2: str) -> int:
        """
        计算两个字符串的Levenshtein距离（编辑距离）

        Args:
            s1: 第一个字符串
            s2: 第二个字符串

        Returns:
            编辑距离
        """
        if len(s1) < len(s2):
            return DiaryFinder._levenshtein_distance(s2, s1)

        if len(s2) == 0:
            return len(s1)

        previous_row = list(range(len(s2) + 1))

        for i, c1 in enumerate(s1):
            current_row = [i + 1]

            for j, c2 in enumerate(s2):
                # 计算插入、删除、替换的代价
                insertions = previous_row[j + 1] + 1
                deletions = current_row[j] + 1
                substitutions = previous_row[j] + (c1 != c2)

                current_row.append(min(insertions, deletions, substitutions))

            previous_row = current_row

        return previous_row[-1]
