from utils.database import db
import datetime

def get_utc_now():
    """Get current UTC time in a timezone-aware format"""
    return datetime.datetime.now(datetime.timezone.utc)

class LocationFavorite(db.Model):
    """
    Location favorite model - represents a user's favorite location
    """
    __tablename__ = 'location_favorites'
    __table_args__ = {'extend_existing': True}

    user_id = db.Column(db.Integer, db.ForeignKey('users.user_id'), primary_key=True)
    location_id = db.Column(db.Integer, db.<PERSON>ey('locations.location_id'), primary_key=True)
    created_at = db.Column(db.DateTime, default=get_utc_now)

    # Define relationships
    user = db.relationship('User', backref='location_favorites')
    location = db.relationship('Location', backref='favorites')

    def to_dict(self):
        """Convert location favorite to dictionary"""
        return {
            'user_id': self.user_id,
            'location_id': self.location_id,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
