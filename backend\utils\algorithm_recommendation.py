"""
Algorithm-based recommendation system
This module provides a recommendation system that uses pure algorithms instead of database queries
"""

from typing import Dict, List, Tuple, Set, Any, Optional
import heapq
from collections import Counter, defaultdict
import time
import math
import random
from models.location import Location
from models.location_browse_count import LocationBrowseCount
from models.location_browse import LocationBrowseHistory
from models.user import User
from models.article import Article, ArticleScore
from utils.database import db

class AlgorithmRecommendationSystem:
    """
    A recommendation system that uses pure algorithms instead of database queries
    All data is loaded into memory and processed using algorithms
    """

    def __init__(self):
        """Initialize the recommendation system"""
        # Data caches
        self.locations = {}  # location_id -> Location object
        self.users = {}      # user_id -> User object
        self.articles = {}   # article_id -> Article object

        # Browse history cache
        self.user_browse_history = defaultdict(lambda: defaultdict(int))  # user_id -> {location_id -> count}
        self.location_browse_count = defaultdict(int)  # location_id -> total browse count

        # Article score cache
        self.user_article_scores = defaultdict(dict)  # user_id -> {article_id -> score}

        # Similarity caches
        self.user_similarity_cache = {}  # (user_id1, user_id2) -> similarity score
        self.location_similarity_cache = {}  # (location_id1, location_id2) -> similarity score

        # Sorted lists
        self.locations_by_popularity = []  # List of (location_id, popularity)
        self.locations_by_rating = []      # List of (location_id, rating)
        self.articles_by_popularity = []   # List of (article_id, popularity)
        self.articles_by_rating = []       # List of (article_id, rating)

        # Cache control
        self.last_refresh_time = 0
        self.cache_expiration = 300  # 5 minutes

        # Load initial data
        self.refresh_all_data()

    def refresh_all_data(self, force=False):
        """
        Refresh all data from the database

        Args:
            force: Force refresh even if cache is not expired
        """
        current_time = time.time()
        if not force and current_time - self.last_refresh_time < self.cache_expiration:
            return

        # Load all locations
        self._load_locations()

        # Load all users
        self._load_users()

        # Load all articles
        self._load_articles()

        # Load browse history
        self._load_browse_history()

        # Load article scores
        self._load_article_scores()

        # Create sorted lists
        self._create_sorted_lists()

        # Update last refresh time
        self.last_refresh_time = current_time

    def _load_locations(self):
        """Load all locations from the database"""
        locations = Location.query.all()
        self.locations = {location.location_id: location for location in locations}

    def _load_users(self):
        """Load all users from the database"""
        users = User.query.all()
        self.users = {user.user_id: user for user in users}

    def _load_articles(self):
        """Load all articles from the database"""
        articles = Article.query.all()
        self.articles = {article.article_id: article for article in articles}

    def _load_browse_history(self):
        """Load all browse history from the database"""
        # Clear existing data
        self.user_browse_history = defaultdict(lambda: defaultdict(int))
        self.location_browse_count = defaultdict(int)

        # Load browse history
        browse_histories = LocationBrowseHistory.query.all()
        for history in browse_histories:
            self.user_browse_history[history.user_id][history.location_id] += 1
            self.location_browse_count[history.location_id] += 1

        # Load browse counts (for verification)
        browse_counts = LocationBrowseCount.query.all()
        for count in browse_counts:
            # If there's a discrepancy, use the count from the database
            if self.user_browse_history[count.user_id][count.location_id] != count.count:
                self.user_browse_history[count.user_id][count.location_id] = count.count

    def _load_article_scores(self):
        """Load all article scores from the database"""
        # Clear existing data
        self.user_article_scores = defaultdict(dict)

        # Load article scores
        article_scores = ArticleScore.query.all()
        for score in article_scores:
            self.user_article_scores[score.user_id][score.article_id] = score.score

    def _create_sorted_lists(self):
        """Create sorted lists for quick access using heap for optimization"""
        # For a system with ~200 locations, we'll use a more efficient approach
        # Pre-allocate max heap size (50 is more than enough for top-k queries)
        max_heap_size = 50

        # Use heapq for locations by popularity (more efficient for top-k)
        popularity_heap = []
        for loc_id, loc in self.locations.items():
            popularity = loc.popularity or 0
            # Use negative popularity for max heap (heapq is min heap by default)
            item = (-popularity, loc_id)
            if len(popularity_heap) < max_heap_size:
                heapq.heappush(popularity_heap, item)
            elif item < popularity_heap[0]:  # Only replace if better
                heapq.heappushpop(popularity_heap, item)

        # Convert heap to sorted list (negate back the popularity)
        self.locations_by_popularity = [(loc_id, -pop) for pop, loc_id in sorted(popularity_heap)]

        # Use heapq for locations by rating
        rating_heap = []
        for loc_id, loc in self.locations.items():
            rating = loc.evaluation or 0
            # Use negative rating for max heap
            item = (-rating, loc_id)
            if len(rating_heap) < max_heap_size:
                heapq.heappush(rating_heap, item)
            elif item < rating_heap[0]:  # Only replace if better
                heapq.heappushpop(rating_heap, item)

        # Convert heap to sorted list
        self.locations_by_rating = [(loc_id, -rating) for rating, loc_id in sorted(rating_heap)]

        # Use heapq for articles by popularity
        article_pop_heap = []
        for art_id, art in self.articles.items():
            popularity = art.popularity or 0
            # Use negative popularity for max heap
            item = (-popularity, art_id)
            if len(article_pop_heap) < max_heap_size:
                heapq.heappush(article_pop_heap, item)
            elif item < article_pop_heap[0]:  # Only replace if better
                heapq.heappushpop(article_pop_heap, item)

        # Convert heap to sorted list
        self.articles_by_popularity = [(art_id, -pop) for pop, art_id in sorted(article_pop_heap)]

        # Use heapq for articles by rating
        article_rating_heap = []
        for art_id, art in self.articles.items():
            rating = art.evaluation or 0
            # Use negative rating for max heap
            item = (-rating, art_id)
            if len(article_rating_heap) < max_heap_size:
                heapq.heappush(article_rating_heap, item)
            elif item < article_rating_heap[0]:  # Only replace if better
                heapq.heappushpop(article_rating_heap, item)

        # Convert heap to sorted list
        self.articles_by_rating = [(art_id, -rating) for rating, art_id in sorted(article_rating_heap)]

    # ===== Incremental Update Methods =====

    def update_location_popularity(self, location_id: int, new_popularity: float) -> None:
        """
        Update a location's popularity and maintain sorted lists without full resorting

        Args:
            location_id: Location ID to update
            new_popularity: New popularity value
        """
        # Check if location exists
        if location_id not in self.locations:
            return

        # Update location object
        self.locations[location_id].popularity = new_popularity

        # Update sorted list
        max_heap_size = len(self.locations_by_popularity)

        # Remove existing entry if present
        for i, (loc_id, _) in enumerate(self.locations_by_popularity):
            if loc_id == location_id:
                self.locations_by_popularity.pop(i)
                break

        # Add new entry in correct position (using binary search for efficiency)
        # We'll use a simple approach for small datasets
        inserted = False
        for i, (loc_id, pop) in enumerate(self.locations_by_popularity):
            if new_popularity > pop:
                self.locations_by_popularity.insert(i, (location_id, new_popularity))
                inserted = True
                break

        # If not inserted and list is not full, append to end
        if not inserted and len(self.locations_by_popularity) < max_heap_size:
            self.locations_by_popularity.append((location_id, new_popularity))

        # Trim if needed
        if len(self.locations_by_popularity) > max_heap_size:
            self.locations_by_popularity = self.locations_by_popularity[:max_heap_size]

    def update_location_rating(self, location_id: int, new_rating: float) -> None:
        """
        Update a location's rating and maintain sorted lists without full resorting

        Args:
            location_id: Location ID to update
            new_rating: New rating value
        """
        # Check if location exists
        if location_id not in self.locations:
            return

        # Update location object
        self.locations[location_id].evaluation = new_rating

        # Update sorted list
        max_heap_size = len(self.locations_by_rating)

        # Remove existing entry if present
        for i, (loc_id, _) in enumerate(self.locations_by_rating):
            if loc_id == location_id:
                self.locations_by_rating.pop(i)
                break

        # Add new entry in correct position
        inserted = False
        for i, (loc_id, rating) in enumerate(self.locations_by_rating):
            if new_rating > rating:
                self.locations_by_rating.insert(i, (location_id, new_rating))
                inserted = True
                break

        # If not inserted and list is not full, append to end
        if not inserted and len(self.locations_by_rating) < max_heap_size:
            self.locations_by_rating.append((location_id, new_rating))

        # Trim if needed
        if len(self.locations_by_rating) > max_heap_size:
            self.locations_by_rating = self.locations_by_rating[:max_heap_size]

    def update_article_popularity(self, article_id: int, new_popularity: float) -> None:
        """
        Update an article's popularity and maintain sorted lists without full resorting

        Args:
            article_id: Article ID to update
            new_popularity: New popularity value
        """
        # Check if article exists
        if article_id not in self.articles:
            return

        # Update article object
        self.articles[article_id].popularity = new_popularity

        # Update sorted list
        max_heap_size = len(self.articles_by_popularity)

        # Remove existing entry if present
        for i, (art_id, _) in enumerate(self.articles_by_popularity):
            if art_id == article_id:
                self.articles_by_popularity.pop(i)
                break

        # Add new entry in correct position
        inserted = False
        for i, (art_id, pop) in enumerate(self.articles_by_popularity):
            if new_popularity > pop:
                self.articles_by_popularity.insert(i, (article_id, new_popularity))
                inserted = True
                break

        # If not inserted and list is not full, append to end
        if not inserted and len(self.articles_by_popularity) < max_heap_size:
            self.articles_by_popularity.append((article_id, new_popularity))

        # Trim if needed
        if len(self.articles_by_popularity) > max_heap_size:
            self.articles_by_popularity = self.articles_by_popularity[:max_heap_size]

    def update_article_rating(self, article_id: int, new_rating: float) -> None:
        """
        Update an article's rating and maintain sorted lists without full resorting

        Args:
            article_id: Article ID to update
            new_rating: New rating value
        """
        # Check if article exists
        if article_id not in self.articles:
            return

        # Update article object
        self.articles[article_id].evaluation = new_rating

        # Update sorted list
        max_heap_size = len(self.articles_by_rating)

        # Remove existing entry if present
        for i, (art_id, _) in enumerate(self.articles_by_rating):
            if art_id == article_id:
                self.articles_by_rating.pop(i)
                break

        # Add new entry in correct position
        inserted = False
        for i, (art_id, rating) in enumerate(self.articles_by_rating):
            if new_rating > rating:
                self.articles_by_rating.insert(i, (article_id, new_rating))
                inserted = True
                break

        # If not inserted and list is not full, append to end
        if not inserted and len(self.articles_by_rating) < max_heap_size:
            self.articles_by_rating.append((article_id, new_rating))

        # Trim if needed
        if len(self.articles_by_rating) > max_heap_size:
            self.articles_by_rating = self.articles_by_rating[:max_heap_size]

    # ===== Recommendation Algorithms =====

    def get_popular_locations(self, limit: int = 10, location_type: Optional[int] = None) -> List[Tuple[int, float]]:
        """
        Get popular locations using in-memory sorted list

        Args:
            limit: Maximum number of locations to return
            location_type: Optional filter for location type

        Returns:
            List of tuples containing location ID and popularity score
        """
        # Refresh data if needed (only if cache expired)
        self.refresh_all_data()

        # If location type is specified, filter the locations
        if location_type is not None:
            filtered_locations = [(loc_id, score) for loc_id, score in self.locations_by_popularity
                                 if self.locations[loc_id].type == location_type]
            return filtered_locations[:limit]

        # Otherwise, return the top locations by popularity
        return self.locations_by_popularity[:limit]

    def get_top_rated_locations(self, limit: int = 10, location_type: Optional[int] = None) -> List[Tuple[int, float]]:
        """
        Get top rated locations using in-memory sorted list

        Args:
            limit: Maximum number of locations to return
            location_type: Optional filter for location type

        Returns:
            List of tuples containing location ID and rating score
        """
        # Refresh data if needed (only if cache expired)
        self.refresh_all_data()

        # If location type is specified, filter the locations
        if location_type is not None:
            filtered_locations = [(loc_id, score) for loc_id, score in self.locations_by_rating
                                 if self.locations[loc_id].type == location_type]
            return filtered_locations[:limit]

        # Otherwise, return the top locations by rating
        return self.locations_by_rating[:limit]

    def calculate_jaccard_similarity(self, set1: Set[int], set2: Set[int]) -> float:
        """
        Calculate Jaccard similarity between two sets

        Args:
            set1: First set
            set2: Second set

        Returns:
            Jaccard similarity score (0-1)
        """
        if not set1 or not set2:
            return 0.0

        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))

        return intersection / union if union > 0 else 0.0

    def calculate_weighted_jaccard_similarity(self, dict1: Dict[int, int], dict2: Dict[int, int]) -> float:
        """
        Calculate weighted Jaccard similarity between two dictionaries

        Args:
            dict1: First dictionary {id -> weight}
            dict2: Second dictionary {id -> weight}

        Returns:
            Weighted Jaccard similarity score (0-1)
        """
        if not dict1 or not dict2:
            return 0.0

        # Get common keys
        common_keys = set(dict1.keys()).intersection(set(dict2.keys()))

        # Calculate min and max sums
        min_sum = sum(min(dict1.get(k, 0), dict2.get(k, 0)) for k in common_keys)
        max_sum = sum(max(dict1.get(k, 0), dict2.get(k, 0)) for k in set(dict1.keys()).union(set(dict2.keys())))

        return min_sum / max_sum if max_sum > 0 else 0.0

    def calculate_cosine_similarity(self, dict1: Dict[int, int], dict2: Dict[int, int]) -> float:
        """
        Calculate cosine similarity between two dictionaries

        Args:
            dict1: First dictionary {id -> weight}
            dict2: Second dictionary {id -> weight}

        Returns:
            Cosine similarity score (0-1)
        """
        import math

        if not dict1 or not dict2:
            return 0.0

        # Get all keys
        all_keys = set(dict1.keys()) | set(dict2.keys())

        # Calculate dot product
        dot_product = sum(dict1.get(k, 0) * dict2.get(k, 0) for k in all_keys)

        # Calculate magnitudes
        magnitude1 = math.sqrt(sum(dict1.get(k, 0) ** 2 for k in all_keys))
        magnitude2 = math.sqrt(sum(dict2.get(k, 0) ** 2 for k in all_keys))

        # Avoid division by zero
        if magnitude1 == 0 or magnitude2 == 0:
            return 0.0

        # Calculate cosine similarity
        return dot_product / (magnitude1 * magnitude2)

    def find_similar_users(self, user_id: int, limit: int = 10) -> List[Tuple[int, float]]:
        """
        Find users similar to the given user based on browsing history using cosine similarity

        Args:
            user_id: User ID
            limit: Maximum number of similar users to return

        Returns:
            List of tuples containing user ID and similarity score
        """
        # Refresh data if needed
        self.refresh_all_data()

        # Get user's browsing history
        user_history = self.user_browse_history.get(user_id, {})

        # If user has no browsing history, return empty list
        if not user_history:
            return []

        # Calculate similarity with all other users
        similarities = []

        for other_user_id, other_history in self.user_browse_history.items():
            # Skip the user itself
            if other_user_id == user_id:
                continue

            # Skip users with no browsing history
            if not other_history:
                continue

            # Check if similarity is already cached
            cache_key = (min(user_id, other_user_id), max(user_id, other_user_id))
            if cache_key in self.user_similarity_cache:
                similarity = self.user_similarity_cache[cache_key]
            else:
                # Calculate cosine similarity instead of Jaccard
                similarity = self.calculate_cosine_similarity(user_history, other_history)
                # Cache the result
                self.user_similarity_cache[cache_key] = similarity

            # Add to list if similarity is positive
            if similarity > 0:
                similarities.append((other_user_id, similarity))

        # Sort by similarity (descending) and limit
        similarities.sort(key=lambda x: x[1], reverse=True)
        return similarities[:limit]

    def get_collaborative_filtering_recommendations(self, user_id: int, limit: int = 10) -> List[Tuple[int, float]]:
        """
        Get location recommendations using collaborative filtering

        Args:
            user_id: User ID
            limit: Maximum number of recommendations to return

        Returns:
            List of tuples containing location ID and score
        """
        # Refresh data if needed
        self.refresh_all_data()

        # Get user's browsing history
        user_history = self.user_browse_history.get(user_id, {})
        user_locations = set(user_history.keys())

        # If user has no browsing history, return popular locations
        if not user_history:
            return self.get_popular_locations(limit=limit)

        # Find similar users
        similar_users = self.find_similar_users(user_id, limit=20)  # Get more similar users for better recommendations

        # If no similar users found, return popular locations
        if not similar_users:
            return self.get_popular_locations(limit=limit)

        # Calculate scores for all locations
        location_scores = defaultdict(float)

        # For each similar user
        for similar_user_id, similarity in similar_users:
            # Get their browsing history
            similar_user_history = self.user_browse_history.get(similar_user_id, {})

            # For each location they browsed
            for location_id, count in similar_user_history.items():
                # Skip locations the user has already browsed
                if location_id in user_locations:
                    continue

                # Add weighted score
                location_scores[location_id] += similarity * count

        # If no recommendations found, return popular locations
        if not location_scores:
            return self.get_popular_locations(limit=limit)

        # Sort by score and limit
        top_locations = heapq.nlargest(limit, location_scores.items(), key=lambda x: x[1])

        return top_locations

    def get_content_based_recommendations(self, user_id: int, limit: int = 10) -> List[Tuple[int, float]]:
        """
        Get location recommendations using content-based filtering

        Args:
            user_id: User ID
            limit: Maximum number of recommendations to return

        Returns:
            List of tuples containing location ID and score
        """
        # Refresh data if needed
        self.refresh_all_data()

        # Get user's browsing history
        user_history = self.user_browse_history.get(user_id, {})
        user_locations = set(user_history.keys())

        # If user has no browsing history, return popular locations
        if not user_history:
            return self.get_popular_locations(limit=limit)

        # Analyze user's preferences
        location_types = Counter()
        keywords = Counter()

        for location_id, count in user_history.items():
            location = self.locations.get(location_id)
            if location:
                # Count location types
                location_types[location.type] += count

                # Count keywords
                if location.keyword:
                    for keyword in location.keyword.split(','):
                        keywords[keyword.strip()] += count

        # Get most common location types and keywords
        common_types = [t for t, _ in location_types.most_common()]
        common_keywords = [k for k, _ in keywords.most_common(10)]  # Limit to top 10 keywords

        # Calculate scores for all locations
        location_scores = defaultdict(float)

        for location_id, location in self.locations.items():
            # Skip locations the user has already browsed
            if location_id in user_locations:
                continue

            score = 0.0

            # Type matching
            if location.type in common_types:
                # Higher score for more common types
                type_rank = common_types.index(location.type)
                type_score = 1.0 / (type_rank + 1)  # 1.0 for most common, 0.5 for second, etc.
                score += type_score

            # Keyword matching
            if location.keyword:
                location_keywords = [k.strip() for k in location.keyword.split(',')]
                for keyword in location_keywords:
                    if keyword in common_keywords:
                        # Higher score for more common keywords
                        keyword_rank = common_keywords.index(keyword)
                        keyword_score = 1.0 / (keyword_rank + 1)
                        score += keyword_score

            # Add popularity factor (small weight)
            score += (location.popularity or 0) / 1000

            # Add to scores if positive
            if score > 0:
                location_scores[location_id] = score

        # If no recommendations found, return popular locations
        if not location_scores:
            return self.get_popular_locations(limit=limit)

        # Sort by score and limit
        top_locations = heapq.nlargest(limit, location_scores.items(), key=lambda x: x[1])

        return top_locations

    def get_hybrid_recommendations(self, user_id: int, limit: int = 10,
                                  weights: Dict[str, float] = None) -> List[Tuple[int, float]]:
        """
        Get location recommendations using hybrid filtering

        Args:
            user_id: User ID
            limit: Maximum number of recommendations to return
            weights: Dictionary of weights for each component (collaborative, content, popularity)

        Returns:
            List of tuples containing location ID and score
        """
        # Set default weights if not provided
        if weights is None:
            weights = {
                'collaborative': 1.0,
                'content': 1.0,
                'popularity': 0.5
            }

        # Get recommendations from each method
        collaborative_recs = dict(self.get_collaborative_filtering_recommendations(user_id, limit=limit*2))
        content_recs = dict(self.get_content_based_recommendations(user_id, limit=limit*2))
        popular_recs = dict(self.get_popular_locations(limit=limit*2))

        # Normalize scores
        def normalize_scores(scores):
            if not scores:
                return {}
            max_score = max(scores.values())
            return {k: v/max_score for k, v in scores.items()} if max_score > 0 else scores

        collaborative_recs = normalize_scores(collaborative_recs)
        content_recs = normalize_scores(content_recs)
        popular_recs = normalize_scores(popular_recs)

        # Combine all unique location IDs
        all_location_ids = set(collaborative_recs.keys()) | set(content_recs.keys()) | set(popular_recs.keys())

        # Calculate hybrid scores
        hybrid_scores = {}

        for location_id in all_location_ids:
            # Get scores from each method (default to 0 if not present)
            collaborative_score = collaborative_recs.get(location_id, 0) * weights['collaborative']
            content_score = content_recs.get(location_id, 0) * weights['content']
            popularity_score = popular_recs.get(location_id, 0) * weights['popularity']

            # Calculate weighted sum
            total_weight = weights['collaborative'] + weights['content'] + weights['popularity']
            hybrid_score = (collaborative_score + content_score + popularity_score) / total_weight

            hybrid_scores[location_id] = hybrid_score

        # Sort by score and limit
        top_locations = heapq.nlargest(limit, hybrid_scores.items(), key=lambda x: x[1])

        return top_locations

    def get_all_locations_with_collaborative(self, user_id: int, limit: int = 100) -> List[Tuple[int, float]]:
        """
        Get all locations sorted by collaborative filtering score

        Args:
            user_id: User ID
            limit: Maximum number of locations to return

        Returns:
            List of tuples containing location ID and score
        """
        # Refresh data if needed
        self.refresh_all_data()

        # Get user's browsing history
        user_history = self.user_browse_history.get(user_id, {})

        # If user has no browsing history, return popular locations
        if not user_history:
            return self.get_popular_locations(limit=limit)

        # Find similar users
        similar_users = self.find_similar_users(user_id, limit=20)

        # Calculate scores for all locations
        location_scores = defaultdict(float)

        # First, add user's own browsing history with high scores
        for location_id, count in user_history.items():
            location_scores[location_id] = count * 2  # Higher weight for user's own history

        # Then add scores from similar users
        for similar_user_id, similarity in similar_users:
            similar_user_history = self.user_browse_history.get(similar_user_id, {})
            for location_id, count in similar_user_history.items():
                location_scores[location_id] += similarity * count

        # Add a small score based on popularity for all locations
        for location_id, location in self.locations.items():
            if location_id not in location_scores:
                location_scores[location_id] = (location.popularity or 0) / 1000

        # Sort by score and limit
        top_locations = heapq.nlargest(limit, location_scores.items(), key=lambda x: x[1])

        return top_locations

    # ===== Article Recommendation Algorithms =====

    def get_popular_articles(self, limit: int = 10) -> List[Tuple[int, float]]:
        """
        Get popular articles using in-memory sorted list

        Args:
            limit: Maximum number of articles to return

        Returns:
            List of tuples containing article ID and popularity score
        """
        # Refresh data if needed
        self.refresh_all_data()

        # Return the top articles by popularity
        return self.articles_by_popularity[:limit]

    def get_top_rated_articles(self, limit: int = 10) -> List[Tuple[int, float]]:
        """
        Get top rated articles using in-memory sorted list

        Args:
            limit: Maximum number of articles to return

        Returns:
            List of tuples containing article ID and rating score
        """
        # Refresh data if needed
        self.refresh_all_data()

        # Return the top articles by rating
        return self.articles_by_rating[:limit]

    def get_collaborative_article_recommendations(self, user_id: int, limit: int = 10) -> List[Tuple[int, float]]:
        """
        Get article recommendations using collaborative filtering

        Args:
            user_id: User ID
            limit: Maximum number of recommendations to return

        Returns:
            List of tuples containing article ID and score
        """
        # Refresh data if needed
        self.refresh_all_data()

        # Get user's article scores
        user_scores = self.user_article_scores.get(user_id, {})
        user_articles = set(user_scores.keys())

        # If user has no scores, return popular articles
        if not user_scores:
            return self.get_popular_articles(limit=limit)

        # Find similar users based on article scores
        similar_users = []

        for other_user_id, other_scores in self.user_article_scores.items():
            # Skip the user itself
            if other_user_id == user_id:
                continue

            # Skip users with no scores
            if not other_scores:
                continue

            # Convert article scores to dictionaries for cosine similarity calculation
            # Skip if no common articles
            if not set(user_scores.keys()) & set(other_scores.keys()):
                continue

            # Calculate cosine similarity
            similarity = self.calculate_cosine_similarity(user_scores, other_scores)

            # Add to similar users if similarity is positive
            if similarity > 0:
                similar_users.append((other_user_id, similarity))

        # Sort by similarity and limit
        similar_users.sort(key=lambda x: x[1], reverse=True)
        similar_users = similar_users[:20]  # Limit to top 20 similar users

        # If no similar users found, return popular articles
        if not similar_users:
            return self.get_popular_articles(limit=limit)

        # Calculate scores for all articles
        article_scores = defaultdict(float)

        # For each similar user
        for similar_user_id, similarity in similar_users:
            # Get their article scores
            similar_user_scores = self.user_article_scores.get(similar_user_id, {})

            # For each article they scored
            for article_id, score in similar_user_scores.items():
                # Skip articles the user has already scored
                if article_id in user_articles:
                    continue

                # Add weighted score
                article_scores[article_id] += similarity * score

        # If no recommendations found, return popular articles
        if not article_scores:
            return self.get_popular_articles(limit=limit)

        # Sort by score and limit
        top_articles = heapq.nlargest(limit, article_scores.items(), key=lambda x: x[1])

        return top_articles

    def get_all_articles_with_collaborative(self, user_id: int, limit: int = 100) -> List[Tuple[int, float]]:
        """
        Get all articles sorted by collaborative filtering score

        Args:
            user_id: User ID
            limit: Maximum number of articles to return

        Returns:
            List of tuples containing article ID and score
        """
        # Refresh data if needed
        self.refresh_all_data()

        # Get user's article scores
        user_scores = self.user_article_scores.get(user_id, {})

        # If user has no scores, return popular articles
        if not user_scores:
            return self.get_popular_articles(limit=limit)

        # Find similar users based on article scores
        similar_users = []

        for other_user_id, other_scores in self.user_article_scores.items():
            # Skip the user itself
            if other_user_id == user_id:
                continue

            # Skip users with no scores
            if not other_scores:
                continue

            # Convert article scores to dictionaries for cosine similarity calculation
            # Skip if no common articles
            if not set(user_scores.keys()) & set(other_scores.keys()):
                continue

            # Calculate cosine similarity
            similarity = self.calculate_cosine_similarity(user_scores, other_scores)

            # Add to similar users if similarity is positive
            if similarity > 0:
                similar_users.append((other_user_id, similarity))

        # Sort by similarity and limit
        similar_users.sort(key=lambda x: x[1], reverse=True)
        similar_users = similar_users[:20]  # Limit to top 20 similar users

        # Calculate scores for all articles
        article_scores = defaultdict(float)

        # First, add user's own scores with high weight
        for article_id, score in user_scores.items():
            article_scores[article_id] = score * 2  # Higher weight for user's own scores

        # Then add scores from similar users
        for similar_user_id, similarity in similar_users:
            similar_user_scores = self.user_article_scores.get(similar_user_id, {})
            for article_id, score in similar_user_scores.items():
                article_scores[article_id] += similarity * score

        # Add a small score based on popularity for all articles
        for article_id, article in self.articles.items():
            if article_id not in article_scores:
                article_scores[article_id] = (article.popularity or 0) / 1000

        # Sort by score and limit
        top_articles = heapq.nlargest(limit, article_scores.items(), key=lambda x: x[1])

        return top_articles