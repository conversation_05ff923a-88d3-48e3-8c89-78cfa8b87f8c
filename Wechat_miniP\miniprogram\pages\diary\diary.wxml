<!--diary.wxml-->
<view class="page">
  <view class="nav-bar">
    <view class="nav-bar-title">旅行日记</view>
    <view class="nav-bar-actions">
      <view class="search-btn" bindtap="toggleSearch">
        <text class="iconfont icon-search"></text>
      </view>
      <view class="add-btn" bindtap="goToCreateDiary">
        <text class="iconfont icon-add"></text>
      </view>
    </view>
  </view>

  <!-- 搜索栏 -->
  <view class="search-container" wx:if="{{showSearch}}">
    <view class="search-box">
      <input class="search-input" placeholder="搜索日记标题或地点..." value="{{searchKeyword}}" bindinput="onSearchInput" confirm-type="search" bindconfirm="onSearch" />
      <view class="search-action" bindtap="onSearch">搜索</view>
    </view>
    <view class="filter-tabs">
      <view class="filter-tab {{filterType === 'all' ? 'active' : ''}}" data-type="all" bindtap="onFilterChange">全部</view>
      <view class="filter-tab {{filterType === 'recent' ? 'active' : ''}}" data-type="recent" bindtap="onFilterChange">最近</view>
      <view class="filter-tab {{filterType === 'popular' ? 'active' : ''}}" data-type="popular" bindtap="onFilterChange">热门</view>
    </view>
  </view>

  <view class="container">
    <!-- 加载状态 -->
    <view class="loading" wx:if="{{loading}}">
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 日记列表 -->
    <view class="diary-list" wx:if="{{!loading && diaryList.length > 0}}">
      <view class="diary-item" wx:for="{{diaryList}}" wx:key="article_id" bindtap="goToDiaryDetail" data-id="{{item.article_id}}">
        <view class="diary-header">
          <view class="diary-title">{{item.title}}</view>
          <view class="diary-date">{{item.created_at}}</view>
        </view>
        <view class="diary-content">
          <view class="diary-text">{{item.content_preview}}</view>
          <view class="diary-image" wx:if="{{item.image_url}}">
            <image src="{{item.image_url}}" mode="aspectFill" />
          </view>
        </view>
        <view class="diary-footer">
          <view class="diary-location" wx:if="{{item.location}}">
            <text class="iconfont icon-location"></text>
            <text>{{item.location}}</text>
          </view>
          <view class="diary-stats">
            <view class="stat-item">
              <text class="iconfont icon-eye"></text>
              <text>{{item.popularity || 0}}</text>
            </view>
            <view class="stat-item">
              <text class="iconfont icon-heart"></text>
              <text>{{item.like_count || 0}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!loading && diaryList.length === 0}}">
      <view class="empty-icon">📝</view>
      <view class="empty-title">还没有旅行日记</view>
      <view class="empty-desc">记录你的美好旅行时光吧</view>
      <view class="empty-action" bindtap="goToCreateDiary">写第一篇日记</view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{!loading && diaryList.length > 0 && hasMore}}">
      <view class="load-more-btn" bindtap="loadMore">加载更多</view>
    </view>
  </view>
</view>
