#!/usr/bin/env python3
"""
依赖更新脚本
用于更新项目依赖到兼容版本
"""

import subprocess
import sys
import os

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description}成功")
        if result.stdout:
            print(f"输出: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败")
        print(f"错误: {e.stderr}")
        return False

def main():
    """主函数"""
    print("🚀 依赖更新脚本")
    print("=" * 50)
    
    # 检查是否在虚拟环境中
    if not (hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)):
        print("⚠️  警告: 建议在虚拟环境中运行此脚本")
        response = input("是否继续? (y/N): ")
        if response.lower() != 'y':
            return
    
    # 升级pip
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", "升级pip"):
        return
    
    # 卸载可能冲突的包
    print("\n🗑️  卸载可能冲突的包...")
    conflicting_packages = [
        "SQLAlchemy",
        "Flask-SQLAlchemy", 
        "openai"
    ]
    
    for package in conflicting_packages:
        run_command(f"{sys.executable} -m pip uninstall {package} -y", f"卸载 {package}")
    
    # 安装核心依赖（按顺序）
    core_dependencies = [
        "SQLAlchemy==1.4.53",
        "Flask-SQLAlchemy==3.0.5",
        "PyMySQL==1.1.0",
        "Flask==2.3.2",
        "Flask-Migrate==4.0.5",
        "Flask-Cors==4.0.0",
    ]
    
    print("\n📦 安装核心依赖...")
    for dep in core_dependencies:
        if not run_command(f"{sys.executable} -m pip install {dep}", f"安装 {dep}"):
            print(f"❌ 核心依赖 {dep} 安装失败，停止安装")
            return
    
    # 安装其他依赖
    print("\n📦 安装其他依赖...")
    if not run_command(f"{sys.executable} -m pip install -r requirements.txt", "安装requirements.txt中的所有依赖"):
        print("❌ 依赖安装失败")
        return
    
    # 验证安装
    print("\n🔍 验证关键依赖...")
    key_packages = ["Flask", "SQLAlchemy", "PyMySQL", "Flask-SQLAlchemy"]
    
    for package in key_packages:
        try:
            __import__(package.lower().replace('-', '_'))
            print(f"✅ {package} 导入成功")
        except ImportError as e:
            print(f"❌ {package} 导入失败: {e}")
    
    print("\n🎉 依赖更新完成!")
    print("\n📋 接下来的步骤:")
    print("1. 重新启动应用: python app.py")
    print("2. 如果仍有问题，请检查数据库连接配置")

if __name__ == "__main__":
    main()
