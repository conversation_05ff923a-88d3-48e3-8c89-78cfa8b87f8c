from utils.database import db
import datetime

def get_utc_now():
    """Get current UTC time in a timezone-aware format"""
    return datetime.datetime.now(datetime.timezone.utc)

class ArticleImage(db.Model):
    """
    Article image model - represents an image associated with an article
    """
    __tablename__ = 'article_images'
    __table_args__ = {'extend_existing': True}

    image_id = db.Column(db.Integer, primary_key=True)
    article_id = db.Column(db.Integer, db.ForeignKey('articles.article_id'), nullable=False)
    image_url = db.Column(db.String(255), nullable=False)
    created_at = db.Column(db.DateTime, default=get_utc_now)

    # Define relationships
    article = db.relationship('Article', backref='images')

    def to_dict(self):
        """Convert article image to dictionary"""
        return {
            'image_id': self.image_id,
            'article_id': self.article_id,
            'image_url': self.image_url,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class ArticleVideo(db.Model):
    """
    Article video model - represents a video associated with an article
    """
    __tablename__ = 'article_videos'
    __table_args__ = {'extend_existing': True}

    video_id = db.Column(db.Integer, primary_key=True)
    article_id = db.Column(db.Integer, db.ForeignKey('articles.article_id'), nullable=False)
    video_url = db.Column(db.String(255), nullable=False)
    created_at = db.Column(db.DateTime, default=get_utc_now)

    # Define relationships
    article = db.relationship('Article', backref='videos')

    def to_dict(self):
        """Convert article video to dictionary"""
        return {
            'video_id': self.video_id,
            'article_id': self.article_id,
            'video_url': self.video_url,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class ArticleMedia(db.Model):
    """
    Article media model - represents media files (images/videos) associated with an article
    """
    __tablename__ = 'article_media'
    __table_args__ = {'extend_existing': True}

    id = db.Column(db.Integer, primary_key=True)
    article_id = db.Column(db.Integer, db.ForeignKey('articles.article_id'), nullable=False)
    media_type = db.Column(db.String(20), nullable=False)  # 'image' or 'video'
    file_path = db.Column(db.String(500), nullable=False)  # 本地文件路径
    file_url = db.Column(db.String(500), nullable=True)    # 访问URL
    filename = db.Column(db.String(255), nullable=False)   # 原始文件名
    file_size = db.Column(db.Integer, nullable=True)       # 文件大小（字节）
    mime_type = db.Column(db.String(100), nullable=True)   # MIME类型
    created_at = db.Column(db.DateTime, default=get_utc_now)

    # Define relationships
    article = db.relationship('Article', backref='media_files')

    def to_dict(self):
        """Convert article media to dictionary"""
        return {
            'id': self.id,
            'article_id': self.article_id,
            'media_type': self.media_type,
            'file_path': self.file_path,
            'file_url': self.file_url,
            'filename': self.filename,
            'file_size': self.file_size,
            'mime_type': self.mime_type,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
