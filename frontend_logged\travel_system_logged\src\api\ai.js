/**
 * AI生成模块API
 */
import axios from './axios'

const aiApi = {
  /**
   * 生成旅游计划
   * @param {Object} data - 请求数据
   * @param {Array} data.locations - 地点ID数组
   * @param {number} data.days - 旅游天数
   * @param {string} data.preferences - 用户偏好
   * @returns {Promise} API响应
   */
  generateTravelPlan(data) {
    return axios.post('/api/ai/generate_plan', data)
  },

  /**
   * 根据地点名称生成旅游计划
   * @param {Object} data - 请求数据
   * @param {Array} data.location_names - 地点名称数组
   * @param {number} data.days - 旅游天数
   * @param {string} data.preferences - 用户偏好
   * @returns {Promise} API响应
   */
  generateTravelPlanByName(data) {
    return axios.post('/api/ai/generate_plan_by_name', data)
  },

  /**
   * 生成文章摘要
   * @param {Object} data - 请求数据
   * @param {number} data.article_id - 文章ID
   * @param {number} data.max_length - 摘要最大长度
   * @returns {Promise} API响应
   */
  generateArticleSummary(data) {
    return axios.post('/api/ai/generate_summary', data)
  },

  /**
   * 生成地点描述
   * @param {Object} data - 请求数据
   * @param {number} data.location_id - 地点ID
   * @param {string} data.style - 描述风格
   * @param {string} data.focus - 描述重点
   * @returns {Promise} API响应
   */
  generateLocationDescription(data) {
    return axios.post('/api/ai/generate_description', data)
  },



  /**
   * 生成场景描述
   * @param {FormData} formData - 表单数据，包含prompt和可选的images
   * @returns {Promise} API响应
   */
  generateSceneDescription(formData) {
    return axios.post('/api/ai/generate', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 生成日记故事板
   * @param {Object} data - 请求数据
   * @param {number} data.article_id - 文章ID
   * @param {string} data.style - 动画风格
   * @returns {Promise} API响应
   */
  generateDiaryStoryboard(data) {
    return axios.post('/api/ai/generate_storyboard', data)
  },

  /**
   * 获取协同过滤推荐
   * @param {Object} data - 请求数据
   * @param {number} data.user_id - 用户ID
   * @param {number} data.limit - 推荐数量限制
   * @returns {Promise} API响应
   */
  getCollaborativeRecommendations(data) {
    return axios.post('/api/recommend/collaborative', data)
  },

  /**
   * 获取混合推荐
   * @param {Object} data - 请求数据
   * @param {number} data.user_id - 用户ID
   * @param {number} data.limit - 推荐数量限制
   * @param {Object} data.weights - 算法权重
   * @returns {Promise} API响应
   */
  getHybridRecommendations(data) {
    return axios.post('/api/recommend/hybrid', data)
  },

  /**
   * 获取个性化推荐
   * @param {Object} data - 请求数据
   * @param {number} data.user_id - 用户ID（可选，游客模式下不需要）
   * @param {number} data.limit - 推荐数量限制
   * @returns {Promise} API响应
   */
  getPersonalizedRecommendations(data) {
    return axios.post('/api/recommend', data)
  },

  /**
   * 获取智能餐厅推荐
   * @param {Object} data - 请求数据
   * @param {number} data.user_id - 用户ID
   * @param {number} data.limit - 推荐数量限制
   * @returns {Promise} API响应
   */
  getSmartRestaurantRecommendations(data) {
    return axios.post('/api/food/smart_recommend', data)
  },

  /**
   * 生成旅游动画
   * @param {Object} data - 请求数据
   * @param {number} data.article_id - 文章ID
   * @param {string} data.animation_style - 动画风格
   * @returns {Promise} API响应
   */
  generateTravelAnimation(data) {
    return axios.post('/api/ai/generate_travel_animation', data)
  },

  /**
   * 智能美食推荐
   * @param {Object} data - 请求数据
   * @param {Array} data.cuisine_types - 菜系类型数组
   * @param {string} data.location_preference - 地点偏好
   * @param {string} data.budget_range - 预算范围
   * @param {number} data.limit - 推荐数量限制
   * @returns {Promise} API响应
   */
  generateFoodRecommendation(data) {
    return axios.post('/api/ai/generate_food_recommendation', data)
  },

  /**
   * 生成图片
   * @param {Object} data - 请求数据
   * @param {string} data.prompt - 图片描述
   * @param {string} data.size - 图片尺寸
   * @param {string} data.style - 图片风格
   * @param {number} data.count - 生成数量
   * @returns {Promise} API响应
   */
  generateImage(data) {
    return axios.post('/api/ai/generate_image', data)
  }
}

export default aiApi
