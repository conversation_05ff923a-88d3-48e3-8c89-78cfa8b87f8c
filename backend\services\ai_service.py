"""
AI服务
封装对外部AI大模型API的调用
"""
import json
import hashlib
import requests
from datetime import datetime, timedelta
from typing import Dict, Optional
from flask import current_app
from utils.database import db

class AICache(db.Model):
    """AI请求缓存模型"""
    __tablename__ = 'ai_cache'

    id = db.Column(db.Integer, primary_key=True)
    request_hash = db.Column(db.String(64), unique=True, nullable=False)
    response = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.now)
    expires_at = db.Column(db.DateTime, nullable=False)

class AIService:
    """AI服务类，封装对外部AI大模型API的调用"""

    def __init__(self, provider=None):
        """
        初始化AI服务

        Args:
            provider: AI提供商，可选值：'deepseek', 'openai', 'baidu'，默认使用配置中的DEFAULT_PROVIDER
        """
        # 从Flask配置中获取AI配置
        self._load_config_from_app()

        self.provider = provider or self.default_provider

        # 初始化百度文心一言访问令牌（如果需要）
        self.baidu_access_token = None
        if self.provider == 'baidu' and self.baidu_api_key and self.baidu_secret_key:
            self._get_baidu_access_token()

    def _load_config_from_app(self):
        """从Flask应用配置中加载AI配置"""
        config = current_app.config

        # DeepSeek配置
        self.deepseek_api_key = config.get('DEEPSEEK_API_KEY')
        self.deepseek_model = config.get('DEEPSEEK_MODEL')
        self.deepseek_api_base = config.get('DEEPSEEK_API_BASE')
        self.deepseek_temperature = config.get('DEEPSEEK_TEMPERATURE')
        self.deepseek_max_tokens = config.get('DEEPSEEK_MAX_TOKENS')
        self.deepseek_stream = False  # 是否使用流式响应

        # OpenAI配置
        self.openai_api_key = config.get('OPENAI_API_KEY')
        self.openai_model = config.get('OPENAI_MODEL')
        self.openai_temperature = config.get('OPENAI_TEMPERATURE')
        self.openai_max_tokens = config.get('OPENAI_MAX_TOKENS')

        # 百度文心一言配置
        self.baidu_api_key = config.get('BAIDU_API_KEY')
        self.baidu_secret_key = config.get('BAIDU_SECRET_KEY')
        self.baidu_model = config.get('BAIDU_MODEL')

        # 通用配置
        self.default_provider = config.get('DEFAULT_PROVIDER')
        self.enable_cache = config.get('ENABLE_CACHE', False)
        self.cache_expiration = config.get('CACHE_EXPIRATION', 3600)

    def _get_baidu_access_token(self):
        """获取百度文心一言访问令牌"""
        url = "https://aip.baidubce.com/oauth/2.0/token"
        params = {
            "grant_type": "client_credentials",
            "client_id": self.baidu_api_key,
            "client_secret": self.baidu_secret_key
        }
        response = requests.post(url, params=params)
        if response.status_code == 200:
            result = response.json()
            self.baidu_access_token = result.get("access_token")
            return self.baidu_access_token
        return None

    def _get_cache(self, request_data: Dict) -> Optional[str]:
        """
        从缓存中获取响应

        Args:
            request_data: 请求数据

        Returns:
            缓存的响应，如果没有缓存则返回None
        """
        if not self.enable_cache:
            return None

        try:
            # 计算请求数据的哈希值
            request_hash = hashlib.md5(json.dumps(request_data, sort_keys=True).encode()).hexdigest()

            # 查询缓存
            cache = AICache.query.filter_by(request_hash=request_hash).first()

            # 如果缓存存在且未过期，返回缓存的响应
            if cache and cache.expires_at > datetime.now():
                return cache.response

            return None
        except Exception as e:
            print(f"缓存查询失败，跳过缓存: {e}")
            return None

    def _set_cache(self, request_data: Dict, response: str) -> None:
        """
        将响应存入缓存

        Args:
            request_data: 请求数据
            response: 响应数据
        """
        if not self.enable_cache:
            return

        try:
            # 计算请求数据的哈希值
            request_hash = hashlib.md5(json.dumps(request_data, sort_keys=True).encode()).hexdigest()

            # 计算过期时间 - 使用MySQL兼容的方式
            expires_at = datetime.now() + timedelta(seconds=self.cache_expiration)

            # 查询是否已存在缓存
            cache = AICache.query.filter_by(request_hash=request_hash).first()

            if cache:
                # 更新缓存
                cache.response = response
                cache.expires_at = expires_at
            else:
                # 创建新缓存
                cache = AICache(
                    request_hash=request_hash,
                    response=response,
                    expires_at=expires_at
                )
                db.session.add(cache)

            db.session.commit()
        except Exception as e:
            print(f"缓存保存失败，跳过缓存: {e}")
            # 不抛出异常，继续正常流程

    def generate_text(self, prompt: str, max_tokens: int = None, temperature: float = None) -> str:
        """
        生成文本

        Args:
            prompt: 提示词
            max_tokens: 最大生成token数
            temperature: 温度参数，控制生成文本的随机性

        Returns:
            生成的文本
        """
        # 构建请求数据
        request_data = {
            "prompt": prompt,
            "max_tokens": max_tokens or self.deepseek_max_tokens,
            "temperature": temperature or self.deepseek_temperature,
            "provider": self.provider
        }

        # 尝试从缓存获取
        cached_response = self._get_cache(request_data)
        if cached_response:
            return cached_response

        # 根据提供商调用不同的API
        if self.provider == 'deepseek':
            response = self._call_deepseek(prompt, max_tokens, temperature)
        elif self.provider == 'openai':
            response = self._call_openai(prompt, max_tokens, temperature)
        elif self.provider == 'baidu':
            response = self._call_baidu(prompt, max_tokens, temperature)
        else:
            raise ValueError(f"Unsupported AI provider: {self.provider}")

        # 缓存响应
        self._set_cache(request_data, response)

        return response

    def _call_deepseek(self, prompt: str, max_tokens: int = None, temperature: float = None) -> str:
        """调用DeepSeek API"""
        try:
            url = f"{self.deepseek_api_base}/chat/completions"

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.deepseek_api_key}"
            }

            # 根据DeepSeek API文档构建请求
            payload = {
                "model": self.deepseek_model,
                "messages": [
                    {"role": "system", "content": "You are a helpful assistant specialized in travel planning and content generation."},
                    {"role": "user", "content": prompt}
                ],
                "max_tokens": max_tokens or self.deepseek_max_tokens,
                "temperature": temperature or self.deepseek_temperature,
                "stream": False
            }

            print(f"发送DeepSeek API请求到: {url}")
            print(f"请求payload模型: {payload['model']}, max_tokens: {payload['max_tokens']}")

            # 添加重试机制
            max_retries = 2
            for attempt in range(max_retries + 1):
                try:
                    print(f"DeepSeek API调用尝试 {attempt + 1}/{max_retries + 1}")

                    # 发送请求，增加超时时间
                    response = requests.post(url, headers=headers, json=payload, timeout=45)

                    print(f"DeepSeek API响应状态码: {response.status_code}")

                    if response.status_code == 200:
                        result = response.json()
                        content = result.get("choices", [{}])[0].get("message", {}).get("content", "").strip()
                        print(f"DeepSeek API调用成功，返回内容长度: {len(content)}")
                        return content
                    else:
                        # 尝试解析错误响应
                        try:
                            error_json = response.json()
                            print(f"DeepSeek API错误响应: {error_json}")
                            # 直接返回错误JSON，让调用者处理
                            return json.dumps({"error": error_json})
                        except:
                            error_message = f"DeepSeek API错误: {response.status_code} - {response.text}"
                            print(f"DeepSeek API错误: {error_message}")
                            return json.dumps({"error": {"message": error_message}})

                except requests.exceptions.Timeout:
                    print(f"DeepSeek API超时 (尝试 {attempt + 1})")
                    if attempt < max_retries:
                        print("等待5秒后重试...")
                        import time
                        time.sleep(5)
                        continue
                    else:
                        return json.dumps({"error": {"message": "API调用超时，请稍后重试"}})

                except requests.exceptions.ConnectionError:
                    print(f"DeepSeek API连接错误 (尝试 {attempt + 1})")
                    if attempt < max_retries:
                        print("等待3秒后重试...")
                        import time
                        time.sleep(3)
                        continue
                    else:
                        return json.dumps({"error": {"message": "网络连接错误，请检查网络"}})

        except Exception as e:
            print(f"DeepSeek API调用异常: {str(e)}")
            return json.dumps({"error": {"message": f"API调用异常: {str(e)}"}})

    def _call_openai(self, prompt: str, max_tokens: int = None, temperature: float = None) -> str:
        """调用OpenAI API"""
        try:
            # 使用requests直接调用OpenAI API，避免依赖openai库
            url = "https://api.openai.com/v1/chat/completions"

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.openai_api_key}"
            }

            payload = {
                "model": self.openai_model,
                "messages": [
                    {"role": "system", "content": "You are a helpful assistant specialized in travel planning and content generation."},
                    {"role": "user", "content": prompt}
                ],
                "max_tokens": max_tokens or self.openai_max_tokens,
                "temperature": temperature or self.openai_temperature
            }

            response = requests.post(url, headers=headers, json=payload)

            if response.status_code == 200:
                result = response.json()
                return result.get("choices", [{}])[0].get("message", {}).get("content", "").strip()
            else:
                return f"OpenAI API错误: {response.status_code} - {response.text}"

        except Exception as e:
            print(f"OpenAI API error: {str(e)}")
            return f"无法生成内容，请稍后再试。错误: {str(e)}"

    def _call_baidu(self, prompt: str, max_tokens: int = None, temperature: float = None) -> str:
        """调用百度文心一言API"""
        if not self.baidu_access_token:
            self._get_baidu_access_token()
            if not self.baidu_access_token:
                return "无法获取百度API访问令牌，请检查配置。"

        url = f"https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/{self.baidu_model}?access_token={self.baidu_access_token}"

        payload = json.dumps({
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": temperature or self.openai_temperature  # 百度API使用与OpenAI相似的温度参数
        })

        headers = {
            'Content-Type': 'application/json'
        }

        try:
            response = requests.post(url, headers=headers, data=payload)
            if response.status_code == 200:
                result = response.json()
                return result.get("result", "")
            else:
                return f"百度API错误: {response.status_code} - {response.text}"
        except Exception as e:
            print(f"Baidu API error: {str(e)}")
            return f"无法生成内容，请稍后再试。错误: {str(e)}"
