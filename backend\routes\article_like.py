from flask import Blueprint, request
from services.article_like_service import ArticleLikeService
from utils.response import success, error
from models.article import Article, ArticleFavorite
from models.user import User
from models.article_like import ArticleLike
from models.article_comment import ArticleComment
from utils.database import db

article_like_bp = Blueprint('article_like', __name__)

@article_like_bp.route('', methods=['POST'])
def like_article():
    """
    Like an article
    """
    try:
        data = request.get_json()

        if not data or not data.get('user_id') or not data.get('article_id'):
            return error('Missing required fields')

        user_id = data['user_id']
        article_id = data['article_id']

        service = ArticleLikeService()
        result, message = service.like_article(user_id, article_id)

        if not result:
            return error(message)

        # Get updated like count
        like_count = service.get_article_likes_count(article_id)

        return success({'like_count': like_count}, message)

    except Exception as e:
        return error(str(e))

@article_like_bp.route('/unlike', methods=['POST'])
def unlike_article():
    """
    Unlike an article
    """
    try:
        data = request.get_json()

        if not data or not data.get('user_id') or not data.get('article_id'):
            return error('Missing required fields')

        user_id = data['user_id']
        article_id = data['article_id']

        service = ArticleLikeService()
        result, message = service.unlike_article(user_id, article_id)

        if not result:
            return error(message)

        # Get updated like count
        like_count = service.get_article_likes_count(article_id)

        return success({'like_count': like_count}, message)

    except Exception as e:
        return error(str(e))

@article_like_bp.route('/check', methods=['POST'])
def check_like():
    """
    Check if user has liked an article
    """
    try:
        data = request.get_json()

        if not data or not data.get('user_id') or not data.get('article_id'):
            return error('Missing required fields')

        user_id = data['user_id']
        article_id = data['article_id']

        service = ArticleLikeService()
        is_liked = service.check_like(user_id, article_id)

        return success({'is_liked': is_liked}, 'Like status checked')

    except Exception as e:
        return error(str(e))

@article_like_bp.route('/count/<int:article_id>', methods=['GET'])
def get_like_count(article_id):
    """
    Get the number of likes for an article
    """
    try:
        service = ArticleLikeService()
        like_count = service.get_article_likes_count(article_id)

        return success({'like_count': like_count}, 'Like count retrieved')

    except Exception as e:
        return error(str(e))

@article_like_bp.route('/user/<int:user_id>', methods=['GET'])
def get_user_liked_articles(user_id):
    """
    Get all articles liked by a user
    """
    try:
        # 检查用户是否存在
        user = User.query.get(user_id)
        if not user:
            return error('User not found')

        # 获取用户点赞的文章
        service = ArticleLikeService()
        articles = service.get_user_liked_articles(user_id)

        # 获取文章详情
        article_details = []

        for article in articles:
            # 获取文章对象
            article_obj = Article.query.get(article.article_id)
            if article_obj:
                # 获取文章作者信息
                author = User.query.get(article_obj.user_id)
                author_name = author.username if author else 'Unknown'
                author_avatar = author.avatar if author else None

                # 解压文章内容
                content = article_obj.content
                if content:
                    try:
                        # 使用Huffman编码解压
                        from services.article_service import ArticleService
                        import json
                        service = ArticleService()
                        # 将JSON字符串解析为字典
                        if article_obj.huffman_codes:
                            huffman_codes = json.loads(article_obj.huffman_codes)
                            content = service.decompress_text(content, huffman_codes)
                        elif isinstance(content, bytes):
                            # 尝试使用UTF-8解码
                            content = content.decode('utf-8')
                    except Exception as e:
                        print(f"Error decompressing article {article_obj.article_id}: {e}")
                        content = "内容解压失败"

                # 添加点赞时间
                like = db.session.query(ArticleLike).filter_by(
                    user_id=user_id,
                    article_id=article.article_id
                ).first()

                # 确保时间包含时区信息
                if like and like.created_at:
                    if like.created_at.tzinfo is None:
                        # 如果时间没有时区信息，添加UTC时区
                        import datetime
                        liked_at = like.created_at.replace(tzinfo=datetime.timezone.utc).isoformat()
                    else:
                        # 已有时区信息
                        liked_at = like.created_at.isoformat()
                else:
                    liked_at = None

                # 获取点赞、收藏和评论数量
                likes_count = ArticleLike.query.filter_by(article_id=article_obj.article_id).count()
                comments_count = ArticleComment.query.filter_by(article_id=article_obj.article_id).count()
                favorites_count = ArticleFavorite.query.filter_by(article_id=article_obj.article_id).count()

                # 收集所有图片和视频URL
                image_urls = {
                    'image_url': article_obj.image_url,
                    'image_url_2': article_obj.image_url_2,
                    'image_url_3': article_obj.image_url_3,
                    'image_url_4': article_obj.image_url_4,
                    'image_url_5': article_obj.image_url_5,
                    'image_url_6': article_obj.image_url_6
                }

                video_urls = {
                    'video_url': article_obj.video_url,
                    'video_url_2': article_obj.video_url_2,
                    'video_url_3': article_obj.video_url_3
                }

                # 构建文章信息
                article_detail = {
                    'article_id': article_obj.article_id,
                    'title': article_obj.title,
                    'content': content,
                    'user_id': article_obj.user_id,
                    'author': author_name,
                    'author_avatar': author_avatar,
                    'created_at': article_obj.created_at.isoformat() if article_obj.created_at else None,
                    'updated_at': article_obj.updated_at.isoformat() if article_obj.updated_at else None,
                    **image_urls,
                    **video_urls,
                    'location': article_obj.location,
                    'popularity': article_obj.popularity,
                    'evaluation': article_obj.evaluation,
                    'liked_at': liked_at,
                    'likes_count': likes_count,
                    'comments_count': comments_count,
                    'favorites_count': favorites_count
                }

                article_details.append(article_detail)

        return success(article_details, 'User liked articles retrieved')

    except Exception as e:
        import traceback
        traceback.print_exc()
        return error(str(e))
