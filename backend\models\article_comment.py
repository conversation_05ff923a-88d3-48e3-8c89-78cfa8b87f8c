from utils.database import db
import datetime

def get_utc_now():
    """Get current UTC time in a timezone-aware format"""
    return datetime.datetime.now(datetime.timezone.utc)

class ArticleComment(db.Model):
    """
    Article comment model - represents a comment on an article
    """
    __tablename__ = 'article_comments'
    __table_args__ = {'extend_existing': True}

    comment_id = db.Column(db.Integer, primary_key=True)
    article_id = db.Column(db.Integer, db.ForeignKey('articles.article_id'), nullable=False)
    user_id = db.Column(db.Integer, db.<PERSON>ey('users.user_id'), nullable=False)
    content = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=get_utc_now)
    updated_at = db.Column(db.DateTime, default=get_utc_now, onupdate=get_utc_now)

    # Define relationships
    user = db.relationship('User', backref='article_comments')
    article = db.relationship('Article', backref='comments')

    def to_dict(self):
        """Convert article comment to dictionary"""
        return {
            'comment_id': self.comment_id,
            'article_id': self.article_id,
            'user_id': self.user_id,
            'content': self.content,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'username': self.user.username if self.user else None
        }
