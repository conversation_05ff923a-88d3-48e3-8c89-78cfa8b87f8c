/* my-diaries.wxss */
.page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 顶部导航栏 */
.nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-left, .nav-right {
  width: 120rpx;
  display: flex;
  align-items: center;
}

.nav-left {
  justify-content: flex-start;
}

.nav-right {
  justify-content: flex-end;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
}

.create-btn {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  padding: 12rpx 20rpx;
  border-radius: 24rpx;
  font-size: 26rpx;
  font-weight: bold;
}

/* 统计信息栏 */
.stats-bar {
  display: flex;
  background: rgba(255, 255, 255, 0.95);
  margin: 16rpx;
  border-radius: 16rpx;
  padding: 24rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #667eea;
  line-height: 1;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}

/* 日记列表 */
.diary-list {
  padding: 0 16rpx;
}

.diary-item {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  padding: 24rpx;
  display: flex;
  gap: 20rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
}

.diary-item:active {
  transform: scale(0.98);
}

.diary-content {
  flex: 1;
}

.diary-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.diary-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  flex: 1;
  margin-right: 16rpx;
}

.diary-date {
  font-size: 24rpx;
  color: #999;
  white-space: nowrap;
}

.diary-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}

.diary-meta {
  display: flex;
  gap: 24rpx;
  margin-bottom: 12rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.meta-icon {
  font-size: 24rpx;
}

.meta-text {
  font-size: 24rpx;
  color: #999;
}

.diary-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.tag-item {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}

.tag-text {
  font-weight: 500;
}

.diary-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  overflow: hidden;
  flex-shrink: 0;
}

.diary-image image {
  width: 100%;
  height: 100%;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
  color: white;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
}

.empty-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  opacity: 0.8;
  margin-bottom: 40rpx;
  line-height: 1.6;
}

.empty-action {
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 48rpx;
  padding: 24rpx 48rpx;
  display: inline-block;
  transition: all 0.3s ease;
}

.empty-action:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}

.action-text {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 120rpx 40rpx;
  color: white;
}

.loading-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  animation: rotate 2s linear infinite;
}

.loading-text {
  font-size: 28rpx;
  opacity: 0.8;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
