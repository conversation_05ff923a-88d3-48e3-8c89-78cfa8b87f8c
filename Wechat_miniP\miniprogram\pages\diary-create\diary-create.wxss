/* diary-create.wxss */
.page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  position: relative;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 顶部导航栏 */
.nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-left, .nav-right {
  width: 120rpx;
  display: flex;
  align-items: center;
}

.nav-left {
  justify-content: flex-start;
}

.nav-right {
  justify-content: flex-end;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
}

.publish-btn {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.container {
  padding: 32rpx 24rpx;
  background: rgba(255, 255, 255, 0.98);
  margin: 16rpx;
  border-radius: 24rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 32rpx;
}

/* 表单项 */
.form-item {
  margin-bottom: 48rpx;
  position: relative;
}

.form-label {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: left;
}

.label-icon {
  font-size: 36rpx;
  margin-right: 12rpx;
}

.required {
  color: #ff4757;
  margin-left: 8rpx;
  font-size: 28rpx;
}

.optional {
  color: #999;
  margin-left: 8rpx;
  font-size: 24rpx;
  font-weight: normal;
}

/* 字符计数 */
.char-count {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  font-size: 24rpx;
  color: #999;
  background: rgba(255, 255, 255, 0.9);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

/* 标题输入 */
.title-input {
  width: 100%;
  height: 120rpx;
  background: white;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
  text-align: left;
  box-sizing: border-box;
  line-height: 120rpx;
}

.title-input:focus {
  border-color: #667eea;
  background: white;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.2);
}

.form-input {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: #667eea;
  background: white;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.2);
}

/* 地点选择器 */
.location-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100rpx;
  background: white;
  border-radius: 16rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.location-selector:active {
  transform: scale(0.98);
  border-color: #667eea;
  background: #f8f9ff;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.2);
}

.location-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  text-align: left;
  flex: 1;
}

.location-text.placeholder {
  color: #999;
  font-weight: normal;
}

.location-arrow {
  font-size: 24rpx;
  color: #667eea;
  transition: transform 0.3s ease;
  font-weight: bold;
}

.location-selector:active .location-arrow {
  transform: scale(1.2);
}

/* 内容输入 */
.content-textarea {
  width: 100%;
  min-height: 500rpx;
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 30rpx;
  border: 2rpx solid #e9ecef;
  line-height: 1.8;
  transition: all 0.3s ease;
  box-sizing: border-box;
  text-align: left;
}

.content-textarea:focus {
  border-color: #667eea;
  background: white;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.2);
}

/* 图片上传 */
.image-upload {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.image-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.image-item:active {
  transform: scale(0.95);
}

.image-item image {
  width: 100%;
  height: 100%;
}

.image-delete {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 48rpx;
  height: 48rpx;
  background: linear-gradient(45deg, #ff4757, #ff3742);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.3);
  transition: transform 0.3s ease;
}

.image-delete:active {
  transform: scale(0.9);
}

.image-delete .iconfont {
  font-size: 24rpx;
  color: white;
  font-weight: bold;
}

.image-add {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #667eea;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #667eea;
  background: rgba(102, 126, 234, 0.05);
  transition: all 0.3s ease;
}

.image-add:active {
  transform: scale(0.95);
  background: rgba(102, 126, 234, 0.1);
  border-color: #5a67d8;
}

.image-add .iconfont {
  font-size: 48rpx;
  margin-bottom: 8rpx;
}

.add-text {
  font-size: 24rpx;
  font-weight: 500;
}

.image-tip, .tag-tip {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;
  margin-top: 16rpx;
  padding: 12rpx 16rpx;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 12rpx;
  border-left: 4rpx solid #667eea;
}

.tip-icon {
  margin-right: 8rpx;
  font-size: 28rpx;
}

/* 标签容器 */
.tag-container {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 2rpx solid transparent;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.tag-item {
  position: relative;
  padding: 16rpx 24rpx;
  background: white;
  border-radius: 24rpx;
  font-size: 26rpx;
  color: #666;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.tag-item:active {
  transform: scale(0.95);
}

.tag-item.selected {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  border-color: #667eea;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

.tag-text {
  font-weight: 500;
}

.tag-check {
  font-size: 20rpx;
  font-weight: bold;
}

.custom-tag {
  display: flex;
  gap: 16rpx;
  align-items: center;
  margin-bottom: 16rpx;
}

.tag-input {
  flex: 1;
  height: 72rpx;
  background: white;
  border-radius: 36rpx;
  padding: 0 24rpx;
  font-size: 26rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.tag-input:focus {
  border-color: #667eea;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.2);
}

.tag-add-btn {
  width: 72rpx;
  height: 72rpx;
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
  transition: transform 0.3s ease;
}

.tag-add-btn:active {
  transform: scale(0.9);
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 24rpx;
  margin-top: 60rpx;
  padding-bottom: 40rpx;
}

.btn-secondary, .btn-primary {
  flex: 1;
  height: 96rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  border-radius: 48rpx;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #666;
  border: 2rpx solid #e9ecef;
}

.btn-secondary:active {
  transform: scale(0.95);
  background: #e9ecef;
}

.btn-primary {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.4);
}

.btn-primary:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

/* 安全区域 */
.safe-area {
  height: 40rpx;
}
