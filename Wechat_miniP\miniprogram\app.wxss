/**app.wxss**/
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
}

/* 全局导航栏样式 */
.nav-bar {
  height: 90rpx;
  background-color: #409EFF;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  color: white;
  font-size: 36rpx;
  font-weight: bold;
  box-sizing: content-box;
  padding-top: calc(env(safe-area-inset-top) + 45rpx); /* 增加额外的顶部内边距 */
  width: 100%;
  z-index: 101; /* 确保高于顶部背景色 */
  overflow: visible;
}

.nav-bar-title {
  text-align: center;
  font-weight: bold;
}

/* 页面通用样式 */
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
  --nav-bar-color: #409EFF;
}

/* 添加顶部背景色 */
.page::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(env(safe-area-inset-top) + 50rpx); /* 确保覆盖顶部安全区域和额外空间 */
  background-color: var(--nav-bar-color);
  z-index: 50;
}

.page {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 自定义导航栏图标 */
.tab-bar-icon {
  font-family: "Material Icons";
  font-size: 24px;
  display: inline-block;
  width: 1em;
  height: 1em;
  line-height: 1;
  text-align: center;
}

@font-face {
  font-family: 'Material Icons';
  font-style: normal;
  font-weight: 400;
  src: url(https://cdn.jsdelivr.net/npm/material-icons@1.13.8/iconfont/MaterialIcons-Regular.woff2) format('woff2');
}

/* iconfont 图标样式 */
.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 图标映射 */
.icon-camera::before { content: "📷"; }
.icon-arrow-right::before { content: "→"; }
.icon-search::before { content: "🔍"; }
.icon-add::before { content: "+"; }
.icon-location::before { content: "📍"; }
.icon-eye::before { content: "👁"; }
.icon-heart::before { content: "♡"; }
.icon-heart-fill::before { content: "♥"; }
.icon-star::before { content: "☆"; }
.icon-star-fill::before { content: "★"; }
.icon-share::before { content: "📤"; }
.icon-close::before { content: "×"; }
