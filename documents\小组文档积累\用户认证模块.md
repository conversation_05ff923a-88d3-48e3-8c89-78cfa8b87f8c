# 用户认证模块 API 文档

本文档详细描述了 Flask 版本后端的用户认证模块 API 接口及其在 Postman 中的测试用例。

## 目录

1. [用户注册](#用户注册)
2. [用户登录](#用户登录)

## 用户注册

### 接口信息

- **URL**: `/api/auth/register`
- **方法**: `POST`
- **描述**: 注册新用户
- **请求体**: JSON

### 请求参数

| 参数名          | 类型   | 必填 | 描述                                |
| --------------- | ------ | ---- | ----------------------------------- |
| username        | string | 是   | 用户名，长度为3-20个字符            |
| email           | string | 是   | 电子邮箱，必须是有效的邮箱格式      |
| password        | string | 是   | 密码，长度至少为6个字符             |
| confirmPassword | string | 是   | 确认密码，必须与密码一致            |
| avatar          | string | 否   | 头像URL，默认为"default_avatar.jpg" |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "User registered successfully",
    "data": {
        "user": {
            "user_id": 1,
            "username": "testuser",
            "email": "<EMAIL>",
            "avatar": "default_avatar.jpg"
        },
        "token": "user-1-1620000000"
    }
}
```

#### 错误响应

- **缺少必填字段** (400 Bad Request)

```json
{
    "code": 1,
    "message": "Missing required fields",
    "data": null
}
```

- **用户名已存在** (400 Bad Request)

```json
{
    "code": 1,
    "message": "Username already exists",
    "data": null
}
```

- **邮箱已存在** (400 Bad Request)

```json
{
    "code": 1,
    "message": "Email already exists",
    "data": null
}
```

- **密码不匹配** (400 Bad Request)

```json
{
    "code": 1,
    "message": "Passwords do not match",
    "data": null
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `POST`
4. 输入 URL: `http://localhost:5000/api/auth/register`
5. 选择 Body 选项卡，选择 raw，设置类型为 JSON
6. 输入请求体:

```json
{
    "username": "testuser1",
    "email": "<EMAIL>",
    "password": "password123",
    "confirmPassword": "password123"
}
```

7. 点击 Send 按钮发送请求

## 用户登录

### 接口信息

- **URL**: `/api/auth/login`
- **方法**: `POST`
- **描述**: 用户登录
- **请求体**: JSON

### 请求参数

| 参数名   | 类型   | 必填 | 描述   |
| -------- | ------ | ---- | ------ |
| username | string | 是   | 用户名 |
| password | string | 是   | 密码   |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Login successful",
    "data": {
        "user": {
            "user_id": 1,
            "username": "testuser",
            "email": "<EMAIL>",
            "avatar": "default_avatar.jpg"
        },
        "token": "user-1-1620000000"
    }
}
```

#### 错误响应

- **缺少用户名或密码** (400 Bad Request)

```json
{
    "code": 1,
    "message": "Missing username or password",
    "data": null
}
```

- **用户名不存在或密码错误** (401 Unauthorized)

```json
{
    "code": 1,
    "message": "Invalid username or password",
    "data": null
}
```

### 实现细节

- 登录成功后生成的token格式为 `user-{user_id}-{timestamp}`
- 实际项目中应使用JWT或其他更安全的认证机制
- 密码验证使用简单的字符串比较，生产环境应使用加密哈希

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `POST`
4. 输入 URL: `http://localhost:5000/api/auth/login`
5. 选择 Body 选项卡，选择 raw，设置类型为 JSON
6. 输入请求体:

```json
{
    "username": "testuser1",
    "password": "password123"
}
```

7. 点击 Send 按钮发送请求

## 实现细节

- 登录成功后生成的token格式为 `user-{user_id}-{timestamp}`
- 实际项目中应使用JWT或其他更安全的认证机制
- 密码验证使用简单的字符串比较，生产环境应使用加密哈希

## 完整的 Postman 测试流程

以下是一个完整的测试流程，按照顺序执行可以测试用户认证模块的所有功能：

### 1. 注册新用户

```
POST http://localhost:5000/api/auth/register
Content-Type: application/json

{
    "username": "testuser1",
    "email": "<EMAIL>",
    "password": "password123",
    "confirmPassword": "password123"
}
```

### 2. 登录

```
POST http://localhost:5000/api/auth/login
Content-Type: application/json

{
    "username": "testuser1",
    "password": "password123"
}
```

## 注意事项

- 用户注册时需要提供用户名、邮箱和密码
- 用户名和邮箱必须唯一
- 密码和确认密码必须一致
- 登录成功后会返回用户信息和token
- token可用于后续需要身份验证的API调用


