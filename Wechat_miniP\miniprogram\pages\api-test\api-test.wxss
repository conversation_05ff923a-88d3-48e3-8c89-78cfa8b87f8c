/* api-test.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: white;
  border-radius: 12rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.retry-btn {
  background-color: #409EFF;
  color: white;
  font-size: 28rpx;
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
}

.loading {
  text-align: center;
  padding: 100rpx 0;
  color: #666;
  font-size: 28rpx;
}

.results {
  background-color: white;
  border-radius: 12rpx;
  overflow: hidden;
}

.result-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.result-item:last-child {
  border-bottom: none;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.result-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.result-status {
  font-size: 32rpx;
  font-weight: bold;
}

.result-status.success {
  color: #67C23A;
}

.result-status.error {
  color: #F56C6C;
}

.result-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-bottom: 15rpx;
}

.result-time {
  font-size: 24rpx;
  color: #666;
}

.result-count {
  font-size: 24rpx;
  color: #409EFF;
}

.result-error {
  font-size: 24rpx;
  color: #F56C6C;
}

.view-btn {
  background-color: #E6F7FF;
  color: #409EFF;
  font-size: 26rpx;
  padding: 10rpx 20rpx;
  border-radius: 6rpx;
  border: 1rpx solid #409EFF;
}

.empty {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}
