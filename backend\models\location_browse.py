from utils.database import db
import datetime

def get_utc_now():
    """Get current UTC time in a timezone-aware format"""
    return datetime.datetime.now(datetime.timezone.utc)

class LocationBrowseHistory(db.Model):
    """
    Location browse history model - represents a user's browsing history of locations
    """
    __tablename__ = 'location_browse_history'
    __table_args__ = {'extend_existing': True}

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>ey('users.user_id'), nullable=False)
    location_id = db.Column(db.Integer, db.ForeignKey('locations.location_id'), nullable=False)
    browse_time = db.Column(db.DateTime, default=get_utc_now)

    # Define relationships
    user = db.relationship('User', backref='location_browse_history')
    location = db.relationship('Location', backref='browse_history')

    def to_dict(self):
        """Convert location browse history to dictionary"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'location_id': self.location_id,
            'browse_time': self.browse_time.isoformat() if self.browse_time else None
        }
