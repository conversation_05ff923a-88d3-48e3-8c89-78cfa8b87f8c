<view class="tab-bar">
  <view class="tab-bar-border"></view>
  <view wx:for="{{list}}" wx:key="index" class="tab-bar-item" data-path="{{item.pagePath}}" data-index="{{index}}" bindtap="switchTab">
    <view class="tab-bar-icon-text" style="color: {{selected === index ? selectedColor : color}}">{{item.iconText}}</view>
    <view style="color: {{selected === index ? selectedColor : color}}" class="tab-bar-text">{{item.text}}</view>
  </view>
</view>
