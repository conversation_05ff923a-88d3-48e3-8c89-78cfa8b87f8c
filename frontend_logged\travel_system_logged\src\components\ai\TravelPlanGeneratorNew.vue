<template>
  <div class="travel-plan-generator">
    <div class="generator-header">
      <h2>智能旅游计划生成</h2>
      <p>输入您想去的地点，AI将为您生成个性化的旅游计划</p>
    </div>

    <div class="form-section">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <!-- 地点输入 -->
        <el-form-item label="旅游地点" prop="locations" required>
          <el-select
            v-model="form.locations"
            multiple
            filterable
            remote
            reserve-keyword
            placeholder="请输入地点名称，支持模糊搜索"
            :remote-method="searchLocations"
            :loading="searchLoading"
            style="width: 100%"
            @change="handleLocationChange"
          >
            <el-option
              v-for="location in locationOptions"
              :key="location.location_id"
              :label="location.name"
              :value="location.name"
            >
              <span style="float: left">{{ location.name }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">
                {{ getLocationTypeText(location.type) }}
              </span>
            </el-option>
          </el-select>
          <div class="form-tip">
            支持输入地点名称进行模糊搜索，如"故宫"、"长城"等
          </div>
        </el-form-item>

        <!-- 旅游天数 -->
        <el-form-item label="旅游天数" prop="days" required>
          <el-input-number
            v-model="form.days"
            :min="1"
            :max="30"
            controls-position="right"
            style="width: 200px"
          />
          <span class="form-tip">建议根据地点数量合理安排天数</span>
        </el-form-item>

        <!-- 旅游偏好 -->
        <el-form-item label="旅游偏好" prop="preferences">
          <el-checkbox-group v-model="form.preferences">
            <el-checkbox label="文化">文化古迹</el-checkbox>
            <el-checkbox label="自然">自然风光</el-checkbox>
            <el-checkbox label="美食">美食体验</el-checkbox>
            <el-checkbox label="购物">购物娱乐</el-checkbox>
            <el-checkbox label="摄影">摄影打卡</el-checkbox>
            <el-checkbox label="休闲">休闲度假</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <!-- 生成按钮 -->
        <el-form-item>
          <el-button
            type="primary"
            @click="generatePlan"
            :loading="loading"
            size="large"
            style="width: 200px"
          >
            <i class="el-icon-magic-stick"></i>
            {{ getLoadingText() }}
          </el-button>
          <div v-if="loading" class="loading-tips">
            <p>AI正在为您生成个性化旅游计划...</p>
            <p>这可能需要30-60秒，请耐心等待</p>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <!-- 生成结果 -->
    <div v-if="generatedPlan" class="result-section">
      <div class="result-header">
        <h3>生成的旅游计划</h3>
        <el-button @click="copyToClipboard" type="text">
          <i class="el-icon-document-copy"></i>
          复制计划
        </el-button>
      </div>

      <div class="plan-content">
        <div class="plan-title">
          <h2>{{ generatedPlan.title }}</h2>
        </div>

        <div v-for="day in generatedPlan.days" :key="day.day" class="day-item">
          <div class="day-header">
            <div class="day-number">第{{ day.day }}天</div>
            <div class="day-title">{{ day.title || `第${day.day}天行程` }}</div>
          </div>
          <div class="day-schedule">
            <div
              v-for="(location, index) in day.locations"
              :key="index"
              class="schedule-item"
            >
              <div class="schedule-time">{{ location.time }}</div>
              <div class="schedule-content">
                <h4>{{ location.name }}</h4>
                <p>{{ location.description }}</p>
              </div>
            </div>
          </div>
        </div>

        <div v-if="generatedPlan.tips" class="plan-tips">
          <h3>旅游小贴士</h3>
          <ul>
            <li v-for="(tip, index) in generatedPlan.tips" :key="index">
              {{ tip }}
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 错误信息 -->
    <div v-if="error" class="error-section">
      <el-alert :title="error" type="error" show-icon />
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import aiApi from '@/api/ai'
import locationApi from '@/api/location'

export default {
  name: 'TravelPlanGeneratorNew',
  emits: ['generated'],
  setup(props, { emit }) {
    const formRef = ref(null)
    const loading = ref(false)
    const searchLoading = ref(false)
    const error = ref('')
    const generatedPlan = ref(null)
    const locationOptions = ref([])

    const form = reactive({
      locations: [],
      days: 3,
      preferences: ['文化']
    })

    const rules = {
      locations: [
        { required: true, message: '请选择至少一个旅游地点', trigger: 'change' }
      ],
      days: [
        { required: true, message: '请输入旅游天数', trigger: 'blur' }
      ]
    }

    // 获取地点类型文本
    const getLocationTypeText = (type) => {
      const typeMap = {
        0: '教育',
        1: '文化',
        2: '自然',
        3: '娱乐'
      }
      return typeMap[type] || '未知'
    }

    // 搜索地点
    const searchLocations = async (query) => {
      if (!query) {
        locationOptions.value = []
        return
      }

      searchLoading.value = true
      try {
        const response = await locationApi.fuzzySearch(query, 10)
        if (response.data.code === 0) {
          locationOptions.value = response.data.data
        }
      } catch (error) {
        console.error('搜索地点失败:', error)
      } finally {
        searchLoading.value = false
      }
    }

    // 处理地点选择变化
    const handleLocationChange = (value) => {
      // 根据选择的地点数量调整建议天数
      if (value.length > 0 && form.days < value.length) {
        form.days = Math.min(value.length, 7)
      }
    }

    // 获取加载文本
    const getLoadingText = () => {
      return loading.value ? '生成中...' : '生成旅游计划'
    }

    // 生成旅游计划
    const generatePlan = async () => {
      try {
        await formRef.value.validate()

        loading.value = true
        error.value = ''
        generatedPlan.value = null

        const requestData = {
          location_names: form.locations,
          days: form.days,
          preferences: form.preferences.join(', ')
        }

        console.log('发送AI生成请求:', requestData)
        const response = await aiApi.generateTravelPlanByName(requestData)

        console.log('AI API响应:', response.data)

        if (response.data.code === 0) {
          generatedPlan.value = response.data.data.plan
          emit('generated', generatedPlan.value)
          ElMessage.success('旅游计划生成成功！')
        } else {
          error.value = response.data.message || '生成失败'
          console.error('AI生成失败:', response.data)
          ElMessage.error(`生成失败: ${error.value}`)
        }
      } catch (error) {
        console.error('生成旅游计划失败:', error)
        console.error('错误详情:', error.response?.data)

        // 处理不同类型的错误
        let errorMessage = '生成失败，请稍后重试'

        if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
          errorMessage = 'AI生成超时，请稍后重试。如果问题持续，系统将使用备用方案生成计划。'
        } else if (error.response?.data?.message) {
          errorMessage = `生成失败: ${error.response.data.message}`
        } else if (error.message) {
          if (error.message.includes('Network Error')) {
            errorMessage = '网络连接错误，请检查网络连接后重试'
          } else {
            errorMessage = `生成失败: ${error.message}`
          }
        }

        error.value = errorMessage
        ElMessage.error(errorMessage)
      } finally {
        loading.value = false
      }
    }

    // 复制到剪贴板
    const copyToClipboard = async () => {
      try {
        const planText = formatPlanForCopy(generatedPlan.value)
        await navigator.clipboard.writeText(planText)
        ElMessage.success('计划已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
        ElMessage.error('复制失败')
      }
    }

    // 格式化计划用于复制
    const formatPlanForCopy = (plan) => {
      let text = `${plan.title}\n\n`

      plan.days.forEach(day => {
        text += `第${day.day}天 - ${day.title || `第${day.day}天行程`}\n`
        day.locations.forEach(location => {
          text += `${location.time}: ${location.name}\n`
          text += `${location.description}\n\n`
        })
        text += '\n'
      })

      if (plan.tips) {
        text += '旅游小贴士:\n'
        plan.tips.forEach(tip => {
          text += `• ${tip}\n`
        })
      }

      return text
    }

    // 初始化时加载热门地点
    onMounted(async () => {
      try {
        const response = await locationApi.fuzzySearch('', 10)
        if (response.data.code === 0) {
          locationOptions.value = response.data.data
        }
      } catch (error) {
        console.error('加载热门地点失败:', error)
      }
    })

    return {
      formRef,
      form,
      rules,
      loading,
      searchLoading,
      error,
      generatedPlan,
      locationOptions,
      getLocationTypeText,
      searchLocations,
      handleLocationChange,
      getLoadingText,
      generatePlan,
      copyToClipboard
    }
  }
}
</script>

<style scoped>
.travel-plan-generator {
  max-width: 800px;
  margin: 0 auto;
}

.generator-header {
  text-align: center;
  margin-bottom: 40px;
}

.generator-header h2 {
  color: #333;
  margin-bottom: 10px;
}

.generator-header p {
  color: #666;
  font-size: 1rem;
}

.form-section {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 15px;
  margin-bottom: 30px;
}

.form-tip {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
}

.loading-tips {
  margin-top: 15px;
  padding: 15px;
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
  color: #1e40af;
  text-align: center;
}

.loading-tips p {
  margin: 5px 0;
  font-size: 0.9rem;
}

.loading-tips p:first-child {
  font-weight: 600;
}

.result-section {
  margin-top: 30px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e9ecef;
}

.plan-content {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 15px;
}

.plan-title h2 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
  font-size: 1.8rem;
}

.day-item {
  margin-bottom: 30px;
  background: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.day-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 15px 20px;
  display: flex;
  align-items: center;
}

.day-number {
  background: rgba(255, 255, 255, 0.2);
  padding: 5px 15px;
  border-radius: 20px;
  margin-right: 15px;
  font-weight: 600;
}

.day-title {
  font-size: 1.2rem;
  font-weight: 600;
}

.day-schedule {
  padding: 20px;
}

.schedule-item {
  display: flex;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e9ecef;
}

.schedule-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.schedule-time {
  width: 120px;
  flex-shrink: 0;
  font-weight: 600;
  color: #667eea;
  font-size: 0.9rem;
}

.schedule-content {
  flex: 1;
}

.schedule-content h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1.1rem;
}

.schedule-content p {
  margin: 0;
  color: #666;
  line-height: 1.6;
}

.plan-tips {
  margin-top: 30px;
  background: white;
  padding: 20px;
  border-radius: 10px;
  border-left: 4px solid #ffd700;
}

.plan-tips h3 {
  color: #333;
  margin-bottom: 15px;
}

.plan-tips ul {
  margin: 0;
  padding-left: 20px;
}

.plan-tips li {
  margin-bottom: 8px;
  color: #666;
  line-height: 1.6;
}

.error-section {
  margin-top: 20px;
}

@media (max-width: 768px) {
  .schedule-item {
    flex-direction: column;
  }

  .schedule-time {
    width: auto;
    margin-bottom: 10px;
  }

  .result-header {
    flex-direction: column;
    gap: 15px;
  }
}
</style>
