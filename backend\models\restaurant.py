"""
Restaurant model - represents a restaurant in the system
"""
from utils.database import db

class Restaurant(db.Model):
    """
    Restaurant model - represents a restaurant in the system
    """
    __tablename__ = 'restaurant'
    __table_args__ = {'extend_existing': True}

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=True)
    cuisine_type = db.Column(db.String(255), nullable=True)
    popularity = db.Column(db.Integer, nullable=True)
    evaluation = db.Column(db.Float, nullable=True)
    number_of_view = db.Column(db.Integer, nullable=True)
    dishes_name_price = db.Column('dishes_name(price)', db.String(255), nullable=True)
    dishes_name1_price = db.Column('dishes_name1(price)', db.String(255), nullable=True)
    dishes_name2_price = db.Column('dishes_name2(price)', db.String(255), nullable=True)
    dishes_image = db.Column(db.String(255), nullable=True)
    dishes_image1 = db.Column(db.String(255), nullable=True)
    dishes_image2 = db.Column(db.String(255), nullable=True)
    average_price_perperson = db.Column(db.Float, nullable=True)
    x = db.Column(db.Integer, nullable=True)
    y = db.Column(db.Integer, nullable=True)
    image_url = db.Column(db.String(255), nullable=True)

    def to_dict(self):
        """Convert restaurant to dictionary"""
        # 如果数据库中有image_url字段，则使用它，否则使用默认的图片路径
        image_path = self.image_url if self.image_url else f'/uploads/restaurants/{self.name}.jpg'

        return {
            'id': self.id,
            'name': self.name,
            'cuisine_type': self.cuisine_type,
            'popularity': self.popularity,
            'evaluation': self.evaluation,
            'number_of_view': self.number_of_view,
            'dishes': [
                self.parse_dish(self.dishes_name_price, self.dishes_image),
                self.parse_dish(self.dishes_name1_price, self.dishes_image1),
                self.parse_dish(self.dishes_name2_price, self.dishes_image2)
            ],
            'average_price': self.average_price_perperson,
            'location': {
                'x': self.x,
                'y': self.y
            },
            'image_url': image_path
        }

    def parse_dish(self, dish_string, dish_image=None):
        """Parse dish string to get name and price"""
        if not dish_string:
            return None

        try:
            # 解析格式如 "烤鸭（158）" 的字符串
            name_part = dish_string.split('（')[0]
            price_part = dish_string.split('（')[1].replace('）', '')

            dish_data = {
                'name': name_part,
                'price': float(price_part)
            }

            # 添加菜品图片
            if dish_image:
                dish_data['image_url'] = dish_image

            return dish_data
        except (IndexError, ValueError):
            dish_data = {
                'name': dish_string,
                'price': 0
            }

            # 添加菜品图片
            if dish_image:
                dish_data['image_url'] = dish_image

            return dish_data
