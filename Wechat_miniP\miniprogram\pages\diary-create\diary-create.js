// diary-create.js
Page({
  data: {
    title: '',
    location: '',
    content: '',
    imageList: [],
    customTag: '',
    tagOptions: [
      { id: '1', name: '美食', selected: false },
      { id: '2', name: '风景', selected: false },
      { id: '3', name: '文化', selected: false },
      { id: '4', name: '历史', selected: false },
      { id: '5', name: '购物', selected: false },
      { id: '6', name: '娱乐', selected: false },
      { id: '7', name: '住宿', selected: false },
      { id: '8', name: '交通', selected: false }
    ],
    userId: 1 // 临时用户ID
  },
  
  // 页面加载
  onLoad: function(options) {
    console.log('写日记页面加载');
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  },

  // 标题输入
  onTitleInput: function(e) {
    this.setData({
      title: e.detail.value
    });
  },
    
    // 内容输入
    onContentInput: function(e) {
      this.setData({
        content: e.detail.value
      });
    },
    
    // 选择地点
    selectLocation: function() {
      const that = this;
      console.log('选择地点被点击');

      const locations = [
        '北京市', '上海市', '广州市', '深圳市',
        '杭州市', '成都市', '西安市', '南京市',
        '苏州市', '武汉市', '重庆市', '天津市',
        '青岛市', '大连市', '厦门市', '长沙市'
      ];

      wx.showActionSheet({
        itemList: locations,
        success: function(res) {
          console.log('选择了地点:', locations[res.tapIndex]);
          that.setData({
            location: locations[res.tapIndex]
          });

          wx.showToast({
            title: '地点已选择',
            icon: 'success',
            duration: 1500
          });
        },
        fail: function(err) {
          console.error('地点选择失败:', err);
        }
      });
    },
    
    // 选择图片
    chooseImage: function() {
      const that = this;
      const remainCount = 6 - this.data.imageList.length;
      wx.chooseMedia({
        count: remainCount,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        success: function(res) {
          const tempFiles = res.tempFiles.map(file => file.tempFilePath);
          that.setData({
            imageList: that.data.imageList.concat(tempFiles)
          });
        }
      });
    },
    
    // 预览图片
    previewImage: function(e) {
      const index = e.currentTarget.dataset.index;
      wx.previewImage({
        current: this.data.imageList[index],
        urls: this.data.imageList
      });
    },
    
    // 删除图片
    deleteImage: function(e) {
      const index = e.currentTarget.dataset.index;
      const imageList = this.data.imageList.slice();
      imageList.splice(index, 1);
      this.setData({ imageList: imageList });
    },
    
    // 切换标签
    toggleTag: function(e) {
      const tagId = e.currentTarget.dataset.id;
      const tagOptions = this.data.tagOptions.map(tag => {
        if (tag.id === tagId) {
          return Object.assign({}, tag, { selected: !tag.selected });
        }
        return tag;
      });
      this.setData({ tagOptions: tagOptions });
    },
    
    // 自定义标签输入
    onCustomTagInput: function(e) {
      this.setData({
        customTag: e.detail.value
      });
    },
    
    // 添加自定义标签
    addCustomTag: function() {
      const customTag = this.data.customTag.trim();
      if (!customTag) {
        wx.showToast({
          title: '请输入标签内容',
          icon: 'none'
        });
        return;
      }
      
      // 检查是否已存在
      const exists = this.data.tagOptions.some(tag => tag.name === customTag);
      if (exists) {
        wx.showToast({
          title: '标签已存在',
          icon: 'none'
        });
        return;
      }
      
      const newTag = {
        id: Date.now().toString(),
        name: customTag,
        selected: true
      };
      
      this.setData({
        tagOptions: this.data.tagOptions.concat([newTag]),
        customTag: ''
      });
    },
    
    // 保存草稿
    saveDraft: function() {
      wx.showToast({
        title: '草稿已保存',
        icon: 'success'
      });
    },
    
    // 发布日记
    publishDiary: function() {
      const that = this;
      
      // 验证必填字段
      if (!this.data.title.trim()) {
        wx.showToast({
          title: '请输入标题',
          icon: 'none'
        });
        return;
      }
      
      if (!this.data.content.trim()) {
        wx.showToast({
          title: '请输入内容',
          icon: 'none'
        });
        return;
      }
      
      wx.showLoading({
        title: '发布中...'
      });
      
      // 获取选中的标签
      const selectedTags = this.data.tagOptions
        .filter(tag => tag.selected)
        .map(tag => tag.name);
      
      // 构建请求数据
      const articleData = {
        user_id: this.data.userId,
        title: this.data.title,
        content: this.data.content,
        location: this.data.location,
        tags: selectedTags,
        image_url: this.data.imageList[0] || null,
        image_url_2: this.data.imageList[1] || null,
        image_url_3: this.data.imageList[2] || null,
        image_url_4: this.data.imageList[3] || null,
        image_url_5: this.data.imageList[4] || null,
        image_url_6: this.data.imageList[5] || null
      };
      
      wx.request({
        url: 'http://localhost:5000/api/articles',
        method: 'POST',
        data: articleData,
        header: {
          'Content-Type': 'application/json'
        },
        success: function(res) {
          wx.hideLoading();

          // 修复响应格式判断：后端返回 code: 0 表示成功
          if (res.data.code === 0) {
            wx.showToast({
              title: '发布成功',
              icon: 'success'
            });

            setTimeout(function() {
              wx.navigateBack();
            }, 1500);
          } else {
            console.error('发布失败:', res.data);
            wx.showToast({
              title: res.data.message || '发布失败',
              icon: 'none'
            });
          }
        },
        fail: function(error) {
          wx.hideLoading();
          console.error('发布日记失败:', error);
          wx.showToast({
            title: '网络错误',
            icon: 'none'
          });
        }
      });
    }
});
