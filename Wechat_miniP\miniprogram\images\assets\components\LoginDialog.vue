<template>
  <el-dialog
    v-model="dialogVisible"
    title="登录/注册"
    width="400px"
    :close-on-click-modal="false"
    :show-close="true"
  >
    <el-tabs v-model="activeTab">
      <el-tab-pane label="登录" name="login">
        <div class="login-form-container">
          <el-form 
            :model="loginForm" 
            :rules="loginRules"
            ref="loginFormRef"
            label-width="80px"
          >
            <el-form-item label="邮箱" prop="email">
              <el-input 
                v-model="loginForm.email" 
                placeholder="请输入邮箱"
                autocomplete="off"
                @keydown.enter.prevent="handleLoginAttempt"
              ></el-input>
            </el-form-item>
            <el-form-item label="密码" prop="password">
              <el-input 
                v-model="loginForm.password" 
                type="password" 
                placeholder="请输入密码"
                autocomplete="new-password"
                @keydown.enter.prevent="handleLoginAttempt"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button 
                type="primary" 
                @click="handleLoginAttempt"
                style="width: 100%"
                :disabled="!isLoginFormValid"
              >登录</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>
      <el-tab-pane label="注册" name="register">
        <div class="register-form-container">
          <el-form 
            :model="registerForm" 
            :rules="registerRules"
            ref="registerFormRef"
            label-width="80px"
          >
            <el-form-item label="用户名" prop="username">
              <el-input v-model="registerForm.username" placeholder="请输入用户名"></el-input>
            </el-form-item>
            <el-form-item label="密码" prop="password">
              <el-input v-model="registerForm.password" type="password" placeholder="请输入密码"></el-input>
            </el-form-item>
            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input v-model="registerForm.confirmPassword" type="password" placeholder="请再次输入密码"></el-input>
            </el-form-item>
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="registerForm.email" placeholder="请输入邮箱"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button 
                type="primary" 
                @click="handleRegisterAttempt" 
                style="width: 100%"
                :disabled="!isRegisterFormValid"
              >注册</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, defineEmits, onMounted, nextTick, defineExpose } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { login, register, testConnection } from '@/api/auth'

const router = useRouter()
const dialogVisible = ref(false)
const activeTab = ref('login')
const emit = defineEmits(['login-success'])

const loginFormRef = ref(null)
const registerFormRef = ref(null)

// 登录表单数据
const loginForm = ref({
  email: '',
  password: ''
})

// 注册表单数据
const registerForm = ref({
  username: '',
  password: '',
  confirmPassword: '',
  email: ''
})

// 登录表单验证规则
const loginRules = {
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度应在6-20个字符之间', trigger: 'blur' }
  ]
}

// 注册表单验证规则
const registerRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度应在3-20个字符之间', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度应在6-20个字符之间', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入密码', trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (value !== registerForm.value.password) {
          callback(new Error('两次输入的密码不一致'));
        } else {
          callback();
        }
      }, 
      trigger: 'blur' 
    }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

// 计算属性：登录表单是否有效
const isLoginFormValid = computed(() => {
  return loginForm.value.email && loginForm.value.password;
})

// 计算属性：注册表单是否有效
const isRegisterFormValid = computed(() => {
  return registerForm.value.username && 
         registerForm.value.password && 
         registerForm.value.confirmPassword && 
         registerForm.value.email &&
         registerForm.value.password === registerForm.value.confirmPassword;
})

// 监听确认密码变化，实时验证
watch(() => registerForm.value.password, () => {
  if (registerFormRef.value) {
    registerFormRef.value.validateField('confirmPassword');
  }
})

// 监听确认密码变化，实时验证
watch(() => registerForm.value.confirmPassword, () => {
  if (registerFormRef.value) {
    registerFormRef.value.validateField('confirmPassword');
  }
})

// 新的登录处理函数
const handleLoginAttempt = async (e) => {
  e.preventDefault() 
  if (!loginFormRef.value) {
    return false;
  }

  try {
    // 先进行表单验证
    const valid = await loginFormRef.value.validate();
    if (!valid) {
      return false;
    }

    // 发送登录请求
    const response = await login({
      email: loginForm.value.email,
      password: loginForm.value.password
    });

    // 处理登录成功
    if (response && response.user) {
      const userData = {
        id: response.user.id,
        username: response.user.username,
        email: response.user.email
      };

      // 保存用户数据
      localStorage.setItem('currentUser', JSON.stringify(userData));
      localStorage.setItem('userType', 'user');
      localStorage.setItem('authToken', response.token);

      // 提示成功并关闭对话框
      ElMessage.success('登录成功');
      emit('login-success', response.user);
      dialogVisible.value = false;

      // 等待状态更新后再跳转
      await nextTick();
      await router.replace('/home');
      return true;
    }
  } catch (error) {
    console.error('登录失败:', error);
    ElMessage.error(error.response?.data?.error || '登录失败');
  }

  return false;
};

// 新的注册处理函数
const handleRegisterAttempt = async () => {
  if (!registerFormRef.value) {
    return false;
  }

  try {
    const valid = await registerFormRef.value.validate();
    if (!valid) {
      return false;
    }

    const response = await register({
      username: registerForm.value.username,
      password: registerForm.value.password,
      confirmPassword: registerForm.value.confirmPassword,
      email: registerForm.value.email
    });

    if (response && response.user) {
      const userData = {
        id: response.user.id,
        username: response.user.username,
        email: response.user.email
      };

      localStorage.setItem('currentUser', JSON.stringify(userData));
      localStorage.setItem('userType', 'user');
      localStorage.setItem('authToken', response.token);

      ElMessage.success('注册成功');
      emit('login-success', response.user);
      dialogVisible.value = false;

      await nextTick();
      await router.replace('/home');
      return true;
    }
  } catch (error) {
    console.error('注册失败:', error);
    ElMessage.error(error.response?.data?.error || '注册失败');
  }

  return false;
};

// 组件方法导出
defineExpose({
  show: () => {
    dialogVisible.value = true;
  },
  activeTab
});

// 组件加载时的连接测试
onMounted(async () => {
  try {
    const result = await testConnection();
    console.log('后端连接测试结果:', result);
  } catch (error) {
    console.error('后端连接测试失败:', error);
  }
});
</script>

<style scoped>
.el-dialog {
  border-radius: 8px;
}

.el-form-item {
  margin-bottom: 20px;
}

.login-form-container,
.register-form-container {
  width: 100%;
}

/* 防止表单默认行为 */
:deep(.el-form) {
  pointer-events: auto;
}

:deep(.el-input__wrapper) {
  pointer-events: auto;
}

:deep(.el-button) {
  pointer-events: auto;
}
</style> 