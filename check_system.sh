#!/bin/bash

echo "========================================"
echo "鸿雁智游系统状态检查"
echo "========================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查函数
check_service() {
    local service_name=$1
    local port=$2
    local url=$3
    
    echo -n "检查 $service_name (端口 $port): "
    
    if lsof -i :$port > /dev/null 2>&1; then
        echo -e "${GREEN}✓ 运行中${NC}"
        
        if [ ! -z "$url" ]; then
            echo -n "  - 服务响应测试: "
            if curl -s "$url" > /dev/null 2>&1; then
                echo -e "${GREEN}✓ 正常${NC}"
            else
                echo -e "${RED}✗ 无响应${NC}"
            fi
        fi
    else
        echo -e "${RED}✗ 未运行${NC}"
    fi
}

# 1. 检查基础环境
echo ""
echo "=== 基础环境检查 ==="

echo -n "Python版本: "
if command -v python > /dev/null; then
    python --version
else
    echo -e "${RED}未安装${NC}"
fi

echo -n "Node.js版本: "
if command -v node > /dev/null; then
    node --version
else
    echo -e "${RED}未安装${NC}"
fi

echo -n "MySQL服务: "
if pgrep mysql > /dev/null || pgrep mysqld > /dev/null; then
    echo -e "${GREEN}✓ 运行中${NC}"
else
    echo -e "${RED}✗ 未运行${NC}"
fi

# 2. 检查项目依赖
echo ""
echo "=== 项目依赖检查 ==="

echo -n "Python虚拟环境: "
if [ -d "backend/venv" ]; then
    echo -e "${GREEN}✓ 存在${NC}"
else
    echo -e "${RED}✗ 不存在${NC}"
fi

echo -n "Node.js依赖 (根目录): "
if [ -d "node_modules" ]; then
    echo -e "${GREEN}✓ 已安装${NC}"
else
    echo -e "${RED}✗ 未安装${NC}"
fi

echo -n "前端依赖: "
if [ -d "frontend_logged/travel_system_logged/node_modules" ]; then
    echo -e "${GREEN}✓ 已安装${NC}"
else
    echo -e "${RED}✗ 未安装${NC}"
fi

# 3. 检查配置文件
echo ""
echo "=== 配置文件检查 ==="

echo -n "后端配置文件: "
if [ -f "backend/config.py" ]; then
    echo -e "${GREEN}✓ 存在${NC}"
else
    echo -e "${RED}✗ 不存在${NC}"
fi

echo -n "环境变量文件: "
if [ -f "backend/.env" ]; then
    echo -e "${GREEN}✓ 存在${NC}"
else
    echo -e "${YELLOW}! 不存在 (可选)${NC}"
fi

echo -n "代理服务器配置: "
if [ -f "proxy-server.js" ]; then
    echo -e "${GREEN}✓ 存在${NC}"
else
    echo -e "${RED}✗ 不存在${NC}"
fi

# 4. 检查服务状态
echo ""
echo "=== 服务状态检查 ==="

check_service "后端服务" "5000" "http://localhost:5000/api/health"
check_service "代理服务器" "3000" "http://localhost:3000/health"
check_service "前端服务" "8080" "http://localhost:8080"

# 5. 检查数据库连接
echo ""
echo "=== 数据库连接检查 ==="

if [ -f "backend/test_db_connection.py" ]; then
    echo -n "数据库连接测试: "
    cd backend
    if [ -d "venv" ]; then
        source venv/bin/activate
        if python test_db_connection.py > /dev/null 2>&1; then
            echo -e "${GREEN}✓ 连接正常${NC}"
        else
            echo -e "${RED}✗ 连接失败${NC}"
        fi
    else
        echo -e "${YELLOW}! 虚拟环境不存在，跳过测试${NC}"
    fi
    cd ..
else
    echo -e "${YELLOW}! 数据库测试脚本不存在${NC}"
fi

# 6. 检查日志文件
echo ""
echo "=== 日志文件检查 ==="

echo -n "后端日志目录: "
if [ -d "backend/logs" ]; then
    echo -e "${GREEN}✓ 存在${NC}"
    LOG_COUNT=$(ls backend/logs/*.log 2>/dev/null | wc -l)
    echo "  - 日志文件数量: $LOG_COUNT"
else
    echo -e "${YELLOW}! 不存在${NC}"
fi

# 7. 检查上传目录
echo ""
echo "=== 上传目录检查 ==="

echo -n "上传目录: "
if [ -d "backend/uploads" ]; then
    echo -e "${GREEN}✓ 存在${NC}"
    UPLOAD_SIZE=$(du -sh backend/uploads 2>/dev/null | cut -f1)
    echo "  - 目录大小: $UPLOAD_SIZE"
else
    echo -e "${YELLOW}! 不存在${NC}"
fi

# 8. 系统资源检查
echo ""
echo "=== 系统资源检查 ==="

echo -n "磁盘空间: "
DISK_USAGE=$(df . | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -lt 90 ]; then
    echo -e "${GREEN}✓ 充足 (已使用 $DISK_USAGE%)${NC}"
else
    echo -e "${RED}✗ 不足 (已使用 $DISK_USAGE%)${NC}"
fi

echo -n "内存使用: "
if command -v free > /dev/null; then
    MEM_USAGE=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    echo "${MEM_USAGE}%"
elif command -v vm_stat > /dev/null; then
    # macOS
    echo "macOS系统 (请使用活动监视器查看)"
else
    echo "无法检测"
fi

# 9. 网络连接检查
echo ""
echo "=== 网络连接检查 ==="

echo -n "外网连接: "
if ping -c 1 8.8.8.8 > /dev/null 2>&1; then
    echo -e "${GREEN}✓ 正常${NC}"
else
    echo -e "${RED}✗ 异常${NC}"
fi

echo -n "API服务连接: "
if curl -s --connect-timeout 5 "https://api.deepseek.com" > /dev/null 2>&1; then
    echo -e "${GREEN}✓ DeepSeek API可达${NC}"
else
    echo -e "${YELLOW}! DeepSeek API不可达${NC}"
fi

# 10. 总结
echo ""
echo "========================================"
echo "检查完成"
echo "========================================"

# 检查是否有服务在运行
RUNNING_SERVICES=0
if lsof -i :5000 > /dev/null 2>&1; then ((RUNNING_SERVICES++)); fi
if lsof -i :3000 > /dev/null 2>&1; then ((RUNNING_SERVICES++)); fi
if lsof -i :8080 > /dev/null 2>&1; then ((RUNNING_SERVICES++)); fi

if [ $RUNNING_SERVICES -eq 3 ]; then
    echo -e "${GREEN}✓ 系统完全运行中 (3/3 服务)${NC}"
    echo "访问地址: http://localhost:8080"
elif [ $RUNNING_SERVICES -gt 0 ]; then
    echo -e "${YELLOW}! 系统部分运行中 ($RUNNING_SERVICES/3 服务)${NC}"
    echo "请检查未启动的服务"
else
    echo -e "${RED}✗ 系统未运行${NC}"
    echo "请运行 ./start_system.sh 启动系统"
fi

echo ""
