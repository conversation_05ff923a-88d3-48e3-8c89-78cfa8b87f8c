"""
基于日记内容的旅游动画生成服务

该模块利用用户的旅游日记（文章）中的图片、视频、文本内容来生成个性化的旅游动画
"""

import os
import json
import uuid
import requests
from typing import List, Dict, Optional, Tuple
from datetime import datetime
from PIL import Image, ImageDraw, ImageFont
import cv2
import numpy as np
from moviepy.editor import VideoFileClip, ImageClip, CompositeVideoClip, TextClip, concatenate_videoclips
from models.article import Article
from models.user import User
from services.ai_service import AIService
from utils.database import db

class DiaryAnimationService:
    """基于日记内容的旅游动画生成服务"""
    
    def __init__(self):
        """初始化服务"""
        self.ai_service = AIService()
        self.output_dir = os.path.join('static', 'generated_animations')
        self.temp_dir = os.path.join('temp', 'animation_processing')
        
        # 确保目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.temp_dir, exist_ok=True)
        
        # 动画配置
        self.config = {
            'video_duration': 30,  # 总时长30秒
            'fps': 24,
            'resolution': (1920, 1080),
            'transition_duration': 1.0,  # 转场时长
            'text_duration': 3.0,  # 文字显示时长
            'image_duration': 4.0,  # 图片显示时长
        }
    
    def generate_diary_animation(self, article_id: int, style: str = 'cinematic') -> Optional[str]:
        """
        基于日记内容生成旅游动画
        
        Args:
            article_id: 文章ID
            style: 动画风格 ('cinematic', 'slideshow', 'storytelling')
            
        Returns:
            生成的动画文件URL，失败则返回None
        """
        try:
            # 获取文章数据
            article = Article.query.get(article_id)
            if not article:
                print(f"Article {article_id} not found")
                return None
            
            # 提取文章内容
            content_data = self._extract_article_content(article)
            if not content_data:
                print("Failed to extract article content")
                return None
            
            # 生成动画脚本
            animation_script = self._generate_animation_script(content_data, style)
            
            # 根据风格生成动画
            if style == 'cinematic':
                animation_path = self._create_cinematic_animation(content_data, animation_script)
            elif style == 'slideshow':
                animation_path = self._create_slideshow_animation(content_data, animation_script)
            elif style == 'storytelling':
                animation_path = self._create_storytelling_animation(content_data, animation_script)
            else:
                animation_path = self._create_default_animation(content_data, animation_script)
            
            if animation_path:
                # 返回相对URL
                return animation_path.replace('static/', '/static/')
            
            return None
            
        except Exception as e:
            print(f"Error generating diary animation: {e}")
            return None
    
    def _extract_article_content(self, article: Article) -> Optional[Dict]:
        """提取文章内容"""
        try:
            # 解压文章内容
            content = article.content
            if article.huffman_codes:
                from services.article_service import ArticleService
                service = ArticleService()
                huffman_codes = json.loads(article.huffman_codes)
                content = service.decompress_text(article.content, huffman_codes)
            
            if isinstance(content, bytes):
                content = content.decode('utf-8')
            
            # 收集所有图片URL
            images = []
            for i in range(1, 7):  # image_url 到 image_url_6
                if i == 1:
                    url = article.image_url
                else:
                    url = getattr(article, f'image_url_{i}', None)
                if url:
                    images.append(url)
            
            # 收集所有视频URL
            videos = []
            for i in range(1, 4):  # video_url 到 video_url_3
                if i == 1:
                    url = article.video_url
                else:
                    url = getattr(article, f'video_url_{i}', None)
                if url:
                    videos.append(url)
            
            return {
                'article_id': article.article_id,
                'title': article.title,
                'content': content,
                'location': article.location,
                'images': images,
                'videos': videos,
                'created_at': article.created_at,
                'user_id': article.user_id
            }
            
        except Exception as e:
            print(f"Error extracting article content: {e}")
            return None
    
    def _generate_animation_script(self, content_data: Dict, style: str) -> Dict:
        """使用AI生成动画脚本"""
        try:
            # 构建AI提示词
            prompt = f"""
            请为以下旅游日记生成一个{style}风格的动画脚本：
            
            标题: {content_data['title']}
            地点: {content_data['location']}
            内容: {content_data['content'][:1000]}...
            图片数量: {len(content_data['images'])}
            视频数量: {len(content_data['videos'])}
            
            请生成一个包含以下元素的动画脚本：
            1. 开场文字（标题和地点）
            2. 图片展示顺序和配文
            3. 视频片段使用建议
            4. 转场效果建议
            5. 背景音乐风格建议
            6. 结尾文字
            
            请以JSON格式返回，包含scenes数组，每个scene包含：
            - type: 'text', 'image', 'video'
            - content: 显示内容
            - duration: 持续时间（秒）
            - transition: 转场效果
            - position: 在动画中的位置
            """
            
            ai_response = self.ai_service.generate_text(prompt, max_tokens=1000)
            
            try:
                script = json.loads(ai_response)
                return script
            except json.JSONDecodeError:
                # 如果AI返回的不是有效JSON，使用默认脚本
                return self._generate_default_script(content_data, style)
                
        except Exception as e:
            print(f"Error generating animation script: {e}")
            return self._generate_default_script(content_data, style)
    
    def _generate_default_script(self, content_data: Dict, style: str) -> Dict:
        """生成默认动画脚本"""
        scenes = []
        
        # 开场
        scenes.append({
            'type': 'text',
            'content': content_data['title'],
            'duration': 3.0,
            'transition': 'fade_in',
            'position': 'center'
        })
        
        if content_data['location']:
            scenes.append({
                'type': 'text',
                'content': f"📍 {content_data['location']}",
                'duration': 2.0,
                'transition': 'slide_up',
                'position': 'bottom'
            })
        
        # 图片展示
        for i, image_url in enumerate(content_data['images']):
            scenes.append({
                'type': 'image',
                'content': image_url,
                'duration': 4.0,
                'transition': 'crossfade' if i > 0 else 'fade_in',
                'position': 'center'
            })
        
        # 视频展示
        for video_url in content_data['videos']:
            scenes.append({
                'type': 'video',
                'content': video_url,
                'duration': 5.0,
                'transition': 'cut',
                'position': 'center'
            })
        
        # 结尾
        scenes.append({
            'type': 'text',
            'content': '感谢观看 ✨',
            'duration': 2.0,
            'transition': 'fade_in',
            'position': 'center'
        })
        
        return {'scenes': scenes, 'style': style}
    
    def _create_cinematic_animation(self, content_data: Dict, script: Dict) -> Optional[str]:
        """创建电影风格动画"""
        try:
            clips = []
            
            for scene in script.get('scenes', []):
                clip = self._create_scene_clip(scene, content_data, 'cinematic')
                if clip:
                    clips.append(clip)
            
            if not clips:
                return None
            
            # 合成最终视频
            final_video = concatenate_videoclips(clips, method="compose")
            
            # 生成输出文件名
            output_filename = f"diary_animation_{content_data['article_id']}_{uuid.uuid4().hex[:8]}.mp4"
            output_path = os.path.join(self.output_dir, output_filename)
            
            # 导出视频
            final_video.write_videofile(
                output_path,
                fps=self.config['fps'],
                codec='libx264',
                audio_codec='aac'
            )
            
            # 清理资源
            final_video.close()
            for clip in clips:
                clip.close()
            
            return output_path
            
        except Exception as e:
            print(f"Error creating cinematic animation: {e}")
            return None
    
    def _create_slideshow_animation(self, content_data: Dict, script: Dict) -> Optional[str]:
        """创建幻灯片风格动画"""
        # 实现幻灯片风格的动画生成
        # 这里可以实现更简单的图片轮播效果
        return self._create_cinematic_animation(content_data, script)
    
    def _create_storytelling_animation(self, content_data: Dict, script: Dict) -> Optional[str]:
        """创建故事叙述风格动画"""
        # 实现故事叙述风格的动画生成
        # 可以添加更多文字说明和过渡效果
        return self._create_cinematic_animation(content_data, script)
    
    def _create_default_animation(self, content_data: Dict, script: Dict) -> Optional[str]:
        """创建默认风格动画"""
        return self._create_cinematic_animation(content_data, script)
    
    def _create_scene_clip(self, scene: Dict, content_data: Dict, style: str) -> Optional:
        """创建单个场景片段"""
        try:
            scene_type = scene.get('type')
            content = scene.get('content')
            duration = scene.get('duration', 3.0)
            
            if scene_type == 'text':
                return self._create_text_clip(content, duration, style)
            elif scene_type == 'image':
                return self._create_image_clip(content, duration, style)
            elif scene_type == 'video':
                return self._create_video_clip(content, duration, style)
            
            return None
            
        except Exception as e:
            print(f"Error creating scene clip: {e}")
            return None
    
    def _create_text_clip(self, text: str, duration: float, style: str) -> Optional:
        """创建文字片段"""
        try:
            return TextClip(
                text,
                fontsize=60,
                color='white',
                font='Arial-Bold'
            ).set_duration(duration).set_position('center')
            
        except Exception as e:
            print(f"Error creating text clip: {e}")
            return None
    
    def _create_image_clip(self, image_url: str, duration: float, style: str) -> Optional:
        """创建图片片段"""
        try:
            # 下载图片
            image_path = self._download_media(image_url, 'image')
            if not image_path:
                return None
            
            return ImageClip(image_path).set_duration(duration).resize(self.config['resolution'])
            
        except Exception as e:
            print(f"Error creating image clip: {e}")
            return None
    
    def _create_video_clip(self, video_url: str, duration: float, style: str) -> Optional:
        """创建视频片段"""
        try:
            # 下载视频
            video_path = self._download_media(video_url, 'video')
            if not video_path:
                return None
            
            clip = VideoFileClip(video_path)
            if clip.duration > duration:
                clip = clip.subclip(0, duration)
            
            return clip.resize(self.config['resolution'])
            
        except Exception as e:
            print(f"Error creating video clip: {e}")
            return None
    
    def _download_media(self, url: str, media_type: str) -> Optional[str]:
        """下载媒体文件"""
        try:
            # 如果是相对路径，转换为绝对路径
            if url.startswith('/uploads/'):
                return os.path.join('static', url[1:])
            elif url.startswith('http'):
                # 下载远程文件
                response = requests.get(url)
                if response.status_code == 200:
                    extension = url.split('.')[-1] if '.' in url else 'jpg'
                    filename = f"{media_type}_{uuid.uuid4().hex[:8]}.{extension}"
                    file_path = os.path.join(self.temp_dir, filename)
                    
                    with open(file_path, 'wb') as f:
                        f.write(response.content)
                    
                    return file_path
            
            return None
            
        except Exception as e:
            print(f"Error downloading media: {e}")
            return None
