#!/usr/bin/env python3
"""
简单轮播视频生成服务
按照用户要求的4个步骤：
1. 获取用户发表的日记的内容、图片、视频
2. 根据内容进行文生图
3. 将文生图后的图片、用户日记中的图片、视频合成一个视频
4. 为该视频配上背景音乐
"""

import os
import time
import requests
import subprocess
import json
from typing import Dict, List, Any, Optional
from models import Article
from services.doubao_service import create_doubao_service


class SimpleSlideshowService:
    """简单轮播视频生成服务"""

    def __init__(self):
        """初始化服务"""
        print("🎬 简单轮播视频服务已初始化")
        self.doubao_service = None
        try:
            self.doubao_service = create_doubao_service()
            print("✅ 豆包文生图服务已连接")
        except Exception as e:
            print(f"⚠️ 豆包服务初始化失败: {e}")

    def generate_slideshow_video(self, article_id: int, options: Dict[str, Any]) -> Dict[str, Any]:
        """
        按照4个步骤生成轮播视频
        """
        try:
            print(f"🎬 开始生成文章 {article_id} 的轮播视频...")

            # 步骤1: 获取用户发表的日记的内容、图片、视频
            print("📝 步骤1: 获取日记内容、图片、视频...")
            diary_data = self._get_diary_data(article_id)
            if not diary_data['success']:
                return diary_data

            # 步骤2: 根据内容进行文生图
            print("🎨 步骤2: 根据日记内容进行文生图...")
            ai_images = self._generate_images_from_content(diary_data['content'], options)

            # 步骤3: 将文生图后的图片、用户日记中的图片、视频合成一个视频
            print("🎬 步骤3: 合成轮播视频...")
            video_result = self._create_combined_video(ai_images, diary_data['user_media'], options)

            # 步骤4: 为该视频配上背景音乐
            print("🎵 步骤4: 添加背景音乐...")
            final_video = self._add_background_music(video_result, options)

            if final_video.get('success'):
                return {
                    'success': True,
                    'video_result': final_video,
                    'ai_images': ai_images,
                    'user_media': diary_data['user_media'],
                    'content_analysis': diary_data['content'],
                    'creation_info': {
                        'created_at': time.time(),
                        'article_id': article_id,
                        'options': options,
                        'steps_completed': ['获取日记数据', '文生图', '视频合成', '添加音乐']
                    }
                }
            else:
                return final_video

        except Exception as e:
            print(f"❌ 轮播视频生成失败: {e}")
            import traceback
            traceback.print_exc()
            return self._error_response(f'轮播视频生成异常: {str(e)}')

    def _get_diary_data(self, article_id: int) -> Dict[str, Any]:
        """步骤1: 获取用户发表的日记的内容、图片、视频"""
        try:
            # 获取文章
            article = Article.query.get(article_id)
            if not article:
                return {'success': False, 'error': f'文章 {article_id} 不存在'}

            print(f"📖 文章标题: {article.title}")
            print(f"📍 文章地点: {article.location}")
            print(f"📝 文章内容长度: {len(article.content)} 字符")

            # 直接从数据库获取该文章的图片和视频URL
            user_images = []
            user_videos = []

            # 获取文章中的所有图片URL
            image_urls = [
                article.image_url,
                article.image_url_2,
                article.image_url_3,
                article.image_url_4,
                article.image_url_5,
                article.image_url_6
            ]

            for i, image_url in enumerate(image_urls):
                if image_url and image_url.strip():
                    # 构建完整的文件路径
                    if image_url.startswith('/uploads/'):
                        file_path = image_url[1:]  # 去掉开头的 /
                    elif image_url.startswith('uploads/'):
                        file_path = image_url
                    else:
                        file_path = os.path.join('uploads', 'images', image_url)

                    if os.path.exists(file_path):
                        user_images.append({
                            'id': i + 1,
                            'filename': os.path.basename(file_path),
                            'path': file_path,
                            'url': image_url,
                            'type': 'user_uploaded'
                        })
                        print(f"📷 找到图片 {i+1}: {file_path}")
                    else:
                        print(f"⚠️ 图片文件不存在: {file_path}")

            # 获取文章中的所有视频URL
            video_urls = [
                article.video_url,
                article.video_url_2,
                article.video_url_3
            ]

            for i, video_url in enumerate(video_urls):
                if video_url and video_url.strip():
                    # 构建完整的文件路径
                    if video_url.startswith('/uploads/'):
                        file_path = video_url[1:]  # 去掉开头的 /
                    elif video_url.startswith('uploads/'):
                        file_path = video_url
                    else:
                        file_path = os.path.join('uploads', 'videos', video_url)

                    if os.path.exists(file_path):
                        user_videos.append({
                            'id': i + 1,
                            'filename': os.path.basename(file_path),
                            'path': file_path,
                            'url': video_url,
                            'type': 'user_uploaded'
                        })
                        print(f"🎬 找到视频 {i+1}: {file_path}")
                    else:
                        print(f"⚠️ 视频文件不存在: {file_path}")

            print(f"📁 该文章的用户图片: {len(user_images)} 张")
            print(f"🎬 该文章的用户视频: {len(user_videos)} 个")

            # 解压缩文章内容
            content_text = self._decompress_content(article.content, article.huffman_codes)

            return {
                'success': True,
                'content': {
                    'title': article.title,
                    'location': article.location or '未知地点',
                    'content': content_text,
                    'keywords': self._extract_keywords(article.title + ' ' + (article.location or ''))
                },
                'user_media': {
                    'images': user_images,
                    'videos': user_videos
                }
            }

        except Exception as e:
            print(f"❌ 获取日记数据失败: {e}")
            import traceback
            traceback.print_exc()
            return {'success': False, 'error': f'获取日记数据失败: {str(e)}'}

    def _decompress_content(self, compressed_content: bytes, huffman_codes: str) -> str:
        """解压缩文章内容"""
        try:
            print(f"🔍 开始解压缩内容，压缩数据长度: {len(compressed_content) if compressed_content else 0}")
            print(f"🔍 Huffman编码长度: {len(huffman_codes) if huffman_codes else 0}")

            if huffman_codes:
                # 如果有Huffman编码，使用Huffman解压
                import json
                import zlib

                # 先尝试zlib解压
                try:
                    decompressed = zlib.decompress(compressed_content)
                    content = decompressed.decode('utf-8')
                    print(f"✅ zlib解压成功，内容长度: {len(content)}")
                    print(f"📝 解压后内容预览: {content[:200]}..." if len(content) > 200 else f"📝 解压后内容: {content}")
                    return content
                except Exception as zlib_error:
                    print(f"⚠️ zlib解压失败: {zlib_error}")

                # 如果zlib失败，尝试实现简单的Huffman解码
                try:
                    codes = json.loads(huffman_codes)
                    print(f"🔍 Huffman编码表: {list(codes.keys())[:10]}...")  # 显示前10个编码

                    # 实现简单的Huffman解码
                    content = self._huffman_decode(compressed_content, codes)
                    if content:
                        print(f"✅ Huffman解压成功，内容长度: {len(content)}")
                        print(f"📝 解压后内容预览: {content[:200]}..." if len(content) > 200 else f"📝 解压后内容: {content}")
                        return content
                except Exception as huffman_error:
                    print(f"⚠️ Huffman解压失败: {huffman_error}")

            # 如果都失败了，尝试直接解码
            try:
                if isinstance(compressed_content, bytes):
                    content = compressed_content.decode('utf-8', errors='ignore')
                    print(f"⚠️ 使用直接解码，内容: {content[:100]}...")
                    return content
                else:
                    content = str(compressed_content)
                    print(f"⚠️ 转换为字符串，内容: {content[:100]}...")
                    return content
            except Exception as direct_error:
                print(f"❌ 直接解码也失败: {direct_error}")
                return "无法解压缩的文章内容"

        except Exception as e:
            print(f"❌ 内容解压缩异常: {e}")
            return "解压缩异常的文章内容"

    def _huffman_decode(self, compressed_data: bytes, codes: dict) -> str:
        """简单的Huffman解码实现"""
        try:
            # 反转编码表：从编码到字符
            decode_table = {v: k for k, v in codes.items()}

            # 将字节转换为二进制字符串
            binary_str = ''.join(format(byte, '08b') for byte in compressed_data)

            # 解码
            result = []
            i = 0
            while i < len(binary_str):
                for length in range(1, 20):  # 假设最长编码不超过20位
                    if i + length <= len(binary_str):
                        code = binary_str[i:i+length]
                        if code in decode_table:
                            result.append(decode_table[code])
                            i += length
                            break
                else:
                    # 如果没有找到匹配的编码，跳过这一位
                    i += 1

            return ''.join(result)
        except Exception as e:
            print(f"❌ Huffman解码失败: {e}")
            return None

    def _extract_keywords(self, content: str) -> List[str]:
        """从内容中提取关键词"""
        # 简单的关键词提取
        keywords = ['旅游', '风景', '美食', '文化', '建筑', '自然', '故宫', '博物院']
        content_lower = content.lower()

        found_keywords = []
        for keyword in keywords:
            if keyword in content_lower:
                found_keywords.append(keyword)

        return found_keywords if found_keywords else ['旅游', '风景']

    def _generate_images_from_content(self, content: Dict[str, Any], options: Dict[str, Any]) -> List[Dict[str, Any]]:
        """步骤2: 根据日记内容进行文生图"""
        try:
            ai_images = []
            image_count = options.get('image_count', 6)  # 增加到6张图片

            if not self.doubao_service:
                print("⚠️ 豆包服务不可用，跳过AI图片生成")
                return ai_images

            # 构建基于完整日记内容的提示词
            diary_content = content['content']
            title = content['title']
            location = content['location']
            keywords = content['keywords']
            style = options.get('animation_style', '温馨')

            print(f"📝 基于日记内容生成 {image_count} 张图片:")
            print(f"   标题: {title}")
            print(f"   地点: {location}")
            print(f"   内容: {diary_content[:100]}..." if len(diary_content) > 100 else f"   内容: {diary_content}")
            print(f"   关键词: {', '.join(keywords)}")

            # 生成更多不同角度的提示词
            prompts = [
                f"{title}，{location}，{diary_content}，{style}风格，高质量摄影，4K分辨率，无文字，无水印",
                f"{location}的{style}风格风景，体现{title}的氛围，{', '.join(keywords)}，高质量摄影，4K分辨率，无水印",
                f"基于'{diary_content}'的{style}风格插画，{location}背景，{', '.join(keywords)}元素，艺术摄影，4K分辨率，无水印",
                f"{location}建筑特色，{style}风格，{title}主题，{', '.join(keywords)}，专业摄影，4K分辨率，无水印",
                f"{diary_content}场景重现，{location}实景，{style}风格渲染，高质量摄影，4K分辨率，无水印",
                f"{title}纪念照风格，{location}标志性景观，{style}氛围，{', '.join(keywords)}元素，4K分辨率，无水印"
            ]

            # 如果需要更多图片，重复使用提示词并添加变化
            while len(prompts) < image_count:
                base_prompt = prompts[len(prompts) % len(prompts)]
                variation_words = ['唯美', '梦幻', '写实', '艺术', '经典', '现代']
                variation = variation_words[len(prompts) % len(variation_words)]
                new_prompt = base_prompt.replace(style, f"{variation}{style}")
                prompts.append(new_prompt)

            for i in range(image_count):
                prompt = prompts[i]
                print(f"🎨 生成第 {i+1} 张图片")
                print(f"   提示词: {prompt[:150]}..." if len(prompt) > 150 else f"   提示词: {prompt}")

                try:
                    # 调用豆包文生图
                    images = self.doubao_service.generate_image(prompt, style='realistic')
                    if images and len(images) > 0:
                        image_url = images[0]['url']
                        # 下载并保存图片
                        local_path = self._download_image(image_url, f'ai_image_{i+1}')
                        if local_path:
                            ai_images.append({
                                'id': i + 1,
                                'url': image_url,
                                'local_path': local_path,
                                'prompt': prompt,
                                'platform': 'doubao'
                            })
                            print(f"✅ 第 {i+1} 张图片生成成功")
                        else:
                            print(f"❌ 第 {i+1} 张图片下载失败")
                    else:
                        print(f"❌ 第 {i+1} 张图片生成失败: 豆包API返回空结果")

                except Exception as e:
                    print(f"❌ 第 {i+1} 张图片生成异常: {e}")
                    continue

            print(f"🎨 AI图片生成完成，成功 {len(ai_images)} 张")
            return ai_images

        except Exception as e:
            print(f"❌ AI图片生成失败: {e}")
            return []

    def _download_image(self, image_url: str, filename_prefix: str) -> Optional[str]:
        """下载图片到本地"""
        try:
            # 创建保存目录
            save_dir = os.path.join('uploads', 'animations')
            os.makedirs(save_dir, exist_ok=True)

            # 生成文件名
            timestamp = str(int(time.time()))
            filename = f"{filename_prefix}_{timestamp}.jpg"
            local_path = os.path.join(save_dir, filename)

            # 下载图片
            response = requests.get(image_url, timeout=30)
            if response.status_code == 200:
                with open(local_path, 'wb') as f:
                    f.write(response.content)
                return local_path
            else:
                print(f"❌ 下载图片失败: HTTP {response.status_code}")
                return None

        except Exception as e:
            print(f"❌ 下载图片异常: {e}")
            return None

    def _create_combined_video(self, ai_images: List[Dict[str, Any]], user_media: Dict[str, Any], options: Dict[str, Any]) -> Dict[str, Any]:
        """步骤3: 将文生图后的图片、用户日记中的图片、视频合成一个视频"""
        try:
            print("🎬 开始合成轮播视频...")

            # 准备所有媒体文件 - 新的轮播顺序
            all_media = []

            print("📋 轮播视频内容安排:")

            # 1. 首先添加AI生成的图片（每张4秒，增加展示时间）
            ai_count = 0
            for ai_image in ai_images:
                if ai_image.get('local_path') and os.path.exists(ai_image['local_path']):
                    all_media.append({
                        'type': 'image',
                        'path': ai_image['local_path'],
                        'duration': 4,  # 增加到4秒
                        'source': 'ai_generated',
                        'description': f'AI生成图片 {ai_count + 1}'
                    })
                    ai_count += 1
                    print(f"   📸 AI图片 {ai_count}: {os.path.basename(ai_image['local_path'])} (4秒)")

            # 2. 然后添加用户上传的视频（保持原时长，但最多12秒）
            video_count = 0
            for user_video in user_media.get('videos', []):
                if os.path.exists(user_video['path']):
                    video_duration = min(self._get_video_duration(user_video['path']), 12)  # 增加到12秒
                    all_media.append({
                        'type': 'video',
                        'path': user_video['path'],
                        'duration': video_duration,
                        'source': 'user_uploaded',
                        'description': f'用户视频 {video_count + 1}'
                    })
                    video_count += 1
                    print(f"   🎬 用户视频 {video_count}: {os.path.basename(user_video['path'])} ({video_duration:.1f}秒)")

            # 3. 最后添加用户上传的图片（每张5秒，作为压轴展示）
            image_count = 0
            for user_image in user_media.get('images', []):
                if os.path.exists(user_image['path']):
                    all_media.append({
                        'type': 'image',
                        'path': user_image['path'],
                        'duration': 5,  # 增加到5秒，突出用户图片
                        'source': 'user_uploaded',
                        'description': f'用户图片 {image_count + 1}'
                    })
                    image_count += 1
                    print(f"   🖼️ 用户图片 {image_count}: {os.path.basename(user_image['path'])} (5秒)")

            print(f"📊 轮播视频统计: AI图片 {ai_count} 张，用户视频 {video_count} 个，用户图片 {image_count} 张")

            if not all_media:
                return {
                    'success': False,
                    'error': '没有可用的媒体文件'
                }

            print(f"📋 准备合成 {len(all_media)} 个媒体文件")

            # 生成视频
            output_path = self._generate_video_with_ffmpeg(all_media)

            if output_path and os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                total_duration = sum(media['duration'] for media in all_media)

                # 构建正确的视频URL
                video_filename = os.path.basename(output_path)
                video_url = f'http://localhost:5000/uploads/animations/{video_filename}'

                print(f"📺 视频文件: {output_path}")
                print(f"🌐 视频URL: {video_url}")

                return {
                    'success': True,
                    'video_path': output_path,
                    'video_url': video_url,
                    'duration': total_duration,
                    'file_size': file_size,
                    'media_count': len(all_media),
                    'ai_images_count': len(ai_images),
                    'user_images_count': len(user_media.get('images', [])),
                    'user_videos_count': len(user_media.get('videos', []))
                }
            else:
                return {
                    'success': False,
                    'error': 'FFmpeg视频生成失败'
                }

        except Exception as e:
            print(f"❌ 视频合成失败: {e}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'error': f'视频合成异常: {str(e)}'
            }

    def _get_video_duration(self, video_path: str) -> float:
        """获取视频时长"""
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', video_path
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                data = json.loads(result.stdout)
                duration = float(data['format']['duration'])
                return duration
            else:
                return 5.0  # 默认5秒
        except Exception as e:
            print(f"❌ 获取视频时长失败: {e}")
            return 5.0  # 默认5秒

    def _generate_video_with_ffmpeg(self, media_list: List[Dict[str, Any]]) -> Optional[str]:
        """使用FFmpeg生成轮播视频（无音频版本）"""
        try:
            print("🎬 使用FFmpeg生成轮播视频...")

            # 生成输出文件名
            timestamp = str(int(time.time()))
            output_filename = f"slideshow_video_{timestamp}.mp4"
            output_path = os.path.join('uploads', 'animations', output_filename)

            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # 创建临时文件列表
            temp_dir = os.path.join('uploads', 'temp')
            os.makedirs(temp_dir, exist_ok=True)

            # 生成FFmpeg输入文件列表
            input_list_file = os.path.join(temp_dir, f'input_list_{timestamp}.txt')

            with open(input_list_file, 'w', encoding='utf-8') as f:
                for i, media in enumerate(media_list):
                    if media['type'] == 'image':
                        # 图片：显示指定时长
                        abs_path = os.path.abspath(media['path']).replace('\\', '/')
                        f.write(f"file '{abs_path}'\n")
                        f.write(f"duration {media['duration']}\n")

                        # 如果是最后一张图片，需要额外处理
                        if i == len(media_list) - 1:
                            f.write(f"file '{abs_path}'\n")  # 重复最后一张图片

                    elif media['type'] == 'video':
                        # 视频：直接添加
                        abs_path = os.path.abspath(media['path']).replace('\\', '/')
                        f.write(f"file '{abs_path}'\n")

            print(f"📋 生成的输入文件列表: {input_list_file}")

            # 显示生成的文件列表内容
            try:
                with open(input_list_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    print(f"📄 输入文件列表内容:\n{content}")
            except Exception as e:
                print(f"⚠️ 无法读取输入文件列表: {e}")

            # 构建FFmpeg命令（简化版本，避免参数冲突）
            cmd = [
                'ffmpeg', '-y',  # -y 覆盖输出文件
                '-f', 'concat', '-safe', '0', '-i', input_list_file,
                '-c:v', 'libx264',  # 视频编码器
                '-pix_fmt', 'yuv420p',  # 像素格式
                '-vf', 'scale=1920:1080:force_original_aspect_ratio=decrease,pad=1920:1080:(ow-iw)/2:(oh-ih)/2',  # 缩放和填充
                output_path
            ]

            print(f"🎬 执行FFmpeg命令: {' '.join(cmd)}")

            # 执行FFmpeg命令
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

            print(f"📊 FFmpeg返回码: {result.returncode}")
            if result.stderr:
                print(f"📤 FFmpeg信息: {result.stderr[-500:]}")  # 只显示最后500字符

            if result.returncode == 0:
                # 检查文件是否真的生成了
                if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                    print(f"✅ 轮播视频生成成功: {output_path}")
                    print(f"📁 文件大小: {os.path.getsize(output_path)} 字节")

                    # 清理临时文件
                    try:
                        os.remove(input_list_file)
                    except:
                        pass

                    return output_path
                else:
                    print(f"❌ FFmpeg执行成功但文件未生成或为空: {output_path}")
                    return None
            else:
                print(f"❌ FFmpeg执行失败，返回码: {result.returncode}")
                print(f"错误信息: {result.stderr}")
                return None

        except Exception as e:
            print(f"❌ FFmpeg生成轮播视频异常: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _add_background_music(self, video_result: Dict[str, Any], options: Dict[str, Any]) -> Dict[str, Any]:
        """步骤4: 为该视频配上背景音乐"""
        try:
            if not video_result.get('success'):
                print("⚠️ 视频生成失败，跳过音乐添加")
                return video_result

            background_music = options.get('background_music')
            if not background_music or background_music == 'none':
                print("ℹ️ 未指定背景音乐，返回无音频视频")
                return video_result

            music_path = os.path.join('uploads', 'music', background_music)
            if not os.path.exists(music_path):
                print(f"⚠️ 背景音乐文件不存在: {music_path}")
                print(f"   可用音乐文件: {self._list_available_music()}")
                return video_result

            print(f"🎵 开始添加背景音乐: {background_music}")
            print(f"   音乐文件路径: {music_path}")
            print(f"   视频文件路径: {video_result['video_path']}")
            print(f"   视频时长: {video_result.get('duration', 0)} 秒")

            # 生成带音乐的视频文件名
            original_path = video_result['video_path']
            timestamp = str(int(time.time()))
            music_video_filename = f"slideshow_with_music_{timestamp}.mp4"
            music_video_path = os.path.join('uploads', 'animations', music_video_filename)

            # 获取视频时长，用于音乐循环
            video_duration = video_result.get('duration', 30)

            # 使用FFmpeg添加背景音乐（改进版本，确保音乐循环）
            cmd = [
                'ffmpeg', '-y',
                '-stream_loop', '-1',  # 无限循环音频
                '-i', music_path,      # 输入音乐（先放音频）
                '-i', original_path,   # 输入视频
                '-c:v', 'copy',        # 复制视频流
                '-c:a', 'aac',         # 音频编码
                '-map', '1:v:0',       # 映射视频流（来自第二个输入）
                '-map', '0:a:0',       # 映射音频流（来自第一个输入）
                '-t', str(video_duration),  # 设置输出时长
                '-shortest',           # 以最短的流为准
                music_video_path
            ]

            print(f"🎵 执行FFmpeg音乐合成命令: {' '.join(cmd)}")
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)  # 减少超时时间
                print(f"🎵 FFmpeg音乐合成返回码: {result.returncode}")
                if result.stderr:
                    print(f"🎵 FFmpeg音乐合成信息: {result.stderr[-300:]}")
            except subprocess.TimeoutExpired:
                print("⚠️ FFmpeg音乐合成超时，返回无音频视频")
                return video_result

            if result.returncode == 0 and os.path.exists(music_video_path):
                print(f"✅ 背景音乐添加成功: {music_video_path}")

                # 删除原始无音频视频
                try:
                    os.remove(original_path)
                except:
                    pass

                # 更新结果
                video_filename = os.path.basename(music_video_path)
                video_url = f'http://localhost:5000/uploads/animations/{video_filename}'

                print(f"📺 带音乐的视频文件: {music_video_path}")
                print(f"🌐 带音乐的视频URL: {video_url}")

                video_result['video_path'] = music_video_path
                video_result['video_url'] = video_url
                video_result['file_size'] = os.path.getsize(music_video_path)
                video_result['has_music'] = True

                return video_result
            else:
                print(f"❌ 背景音乐添加失败，返回码: {result.returncode}")
                print(f"错误信息: {result.stderr}")
                return video_result

        except Exception as e:
            print(f"❌ 添加背景音乐异常: {e}")
            return video_result

    def _list_available_music(self) -> List[str]:
        """列出可用的音乐文件"""
        try:
            music_dir = os.path.join('uploads', 'music')
            if os.path.exists(music_dir):
                music_files = [f for f in os.listdir(music_dir)
                             if f.lower().endswith(('.mp3', '.wav', '.m4a', '.aac'))]
                return music_files
            return []
        except Exception as e:
            print(f"❌ 获取音乐文件列表失败: {e}")
            return []

    def _error_response(self, message: str) -> Dict[str, Any]:
        """生成错误响应"""
        return {
            'success': False,
            'error': message
        }
