# 文章评分模块 API 文档

本文档详细描述了 Flask 版本后端的文章评分模块 API 接口及其在 Postman 中的测试用例。

## 目录

1. [设置文章评分](#设置文章评分)
2. [获取文章平均评分](#获取文章平均评分)
3. [获取用户对文章的评分](#获取用户对文章的评分)
4. [获取文章的所有评分](#获取文章的所有评分)

## 设置文章评分

### 接口信息

- **URL**: `/api/article_score`
- **方法**: `POST`
- **描述**: 设置用户对文章的评分
- **请求体**: JSON

### 请求参数

| 参数名     | 类型    | 必填 | 描述           |
| ---------- | ------- | ---- | -------------- |
| user_id    | integer | 是   | 用户 ID        |
| article_id | integer | 是   | 文章 ID        |
| score      | float   | 是   | 评分，范围 0-5 |

### 响应

#### 成功响应 (200 OK)

```json
{
    "message": "Article score set successfully"
}
```

#### 错误响应

- **参数缺失** (400 Bad Request)

```json
{
    "error": "Missing required parameters"
}
```

- **用户不存在** (404 Not Found)

```json
{
    "error": "User not found"
}
```

- **文章不存在** (404 Not Found)

```json
{
    "error": "Article not found"
}
```

- **评分范围错误** (400 Bad Request)

```json
{
    "error": "Score must be between 0 and 5"
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `POST`
4. 输入 URL: `http://localhost:5000/api/article_score`
5. 选择 Body 选项卡，选择 raw，设置类型为 JSON
6. 输入请求体:

```json
{
    "user_id": 1,
    "article_id": 1,
    "score": 4.5
}
```

7. 点击 Send 按钮发送请求

## 获取文章平均评分

### 接口信息

- **URL**: `/api/article_score/get_average_score/{article_id}`
- **方法**: `GET`
- **描述**: 获取文章的平均评分
- **URL 参数**: article_id - 文章 ID

### 响应

#### 成功响应 (200 OK)

```json
{
    "average_score": 4.5
}
```

#### 错误响应

- **文章不存在** (404 Not Found)

```json
{
    "error": "Article not found"
}
```

- **文章没有评分** (404 Not Found)

```json
{
    "error": "No scores found for this article"
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `GET`
4. 输入 URL: `http://localhost:5000/api/article_score/get_average_score/1` (替换 `1` 为实际的文章 ID)
5. 点击 Send 按钮发送请求

## 获取用户对文章的评分

### 接口信息

- **URL**: `/api/article_score/get_user_score`
- **方法**: `POST`
- **描述**: 获取特定用户对特定文章的评分
- **请求体**: JSON

### 请求参数

| 参数名     | 类型    | 必填 | 描述    |
| ---------- | ------- | ---- | ------- |
| user_id    | integer | 是   | 用户 ID |
| article_id | integer | 是   | 文章 ID |

### 响应

#### 成功响应 (200 OK)

```json
{
    "score": 4.5
}
```

#### 错误响应

- **参数缺失** (400 Bad Request)

```json
{
    "error": "Missing required parameters"
}
```

- **用户不存在** (404 Not Found)

```json
{
    "error": "User not found"
}
```

- **文章不存在** (404 Not Found)

```json
{
    "error": "Article not found"
}
```

- **评分不存在** (404 Not Found)

```json
{
    "error": "No score found for this user and article"
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `POST`
4. 输入 URL: `http://localhost:5000/api/article_score/get_user_score`
5. 选择 Body 选项卡，选择 raw，设置类型为 JSON
6. 输入请求体:

```json
{
    "user_id": 1,
    "article_id": 1
}
```

7. 点击 Send 按钮发送请求

## 获取文章的所有评分

### 接口信息

- **URL**: `/api/article_score/get_all_scores/{article_id}`
- **方法**: `GET`
- **描述**: 获取文章的所有评分
- **URL 参数**: article_id - 文章 ID

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Scores retrieved successfully",
    "data": {
        "scores": [
            {
                "user_id": 1,
                "username": "testuser1",
                "score": 4.5,
                "created_at": "2023-05-01T12:00:00Z"
            },
            {
                "user_id": 2,
                "username": "testuser2",
                "score": 5.0,
                "created_at": "2023-05-02T14:30:00Z"
            }
        ]
    }
}
```

#### 错误响应

- **文章不存在** (404 Not Found)

```json
{
    "code": 1,
    "message": "Article not found",
    "data": null
}
```

- **文章没有评分** (404 Not Found)

```json
{
    "code": 1,
    "message": "No scores found for this article",
    "data": null
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `GET`
4. 输入 URL: `http://localhost:5000/api/article_score/get_all_scores/1` (替换 `1` 为实际的文章 ID)
5. 点击 Send 按钮发送请求

## 文章评分模型

文章评分模型包含以下字段：

| 字段名     | 类型     | 描述           |
| ---------- | -------- | -------------- |
| id         | integer  | 评分记录 ID    |
| user_id    | integer  | 用户 ID        |
| article_id | integer  | 文章 ID        |
| score      | float    | 评分，范围 0-5 |
| created_at | datetime | 评分创建时间   |
| updated_at | datetime | 评分更新时间   |

## 评分计算规则

1. **文章平均评分**：文章的平均评分是所有用户对该文章评分的算术平均值。
2. **评分范围**：评分范围为 0-5，支持小数点后一位。
3. **评分更新**：如果用户已经对文章进行过评分，再次评分会更新原有评分，而不是创建新的评分记录。
4. **文章评分影响**：文章的平均评分会影响文章在搜索结果和推荐列表中的排序。

## 完整的 Postman 测试流程

以下是一个完整的测试流程，按照顺序执行可以测试文章评分模块的所有功能：

### 1. 设置文章评分

```
POST http://localhost:5000/api/article_score
Content-Type: application/json

{
    "user_id": 1,
    "article_id": 1,
    "score": 4.5
}
```

### 2. 获取文章平均评分

```
GET http://localhost:5000/api/article_score/get_average_score/1
```

### 3. 获取用户对文章的评分

```
POST http://localhost:5000/api/article_score/get_user_score
Content-Type: application/json

{
    "user_id": 1,
    "article_id": 1
}
```

### 4. 获取文章的所有评分

```
GET http://localhost:5000/api/article_score/get_all_scores/1
```

### 5. 更新文章评分

```
POST http://localhost:5000/api/article_score
Content-Type: application/json

{
    "user_id": 1,
    "article_id": 1,
    "score": 5.0
}
```

## 代码实现

文章评分模块的核心代码位于 `backend/routes/article_score.py` 文件中。以下是关键部分的代码实现：

### 设置文章评分

```python
@article_score_bp.route('', methods=['POST'])
def set_article_score():
    """
    Set article score
    """
    try:
        data = request.get_json()

        # Validate required fields
        if not data or 'user_id' not in data or 'article_id' not in data or 'score' not in data:
            return jsonify({"error": "Missing required parameters"}), 400

        user_id = data['user_id']
        article_id = data['article_id']
        score = data['score']

        # Validate score range
        if not 0 <= score <= 5:
            return jsonify({"error": "Score must be between 0 and 5"}), 400

        # Check if user exists
        user = User.query.get(user_id)
        if not user:
            return jsonify({"error": "User not found"}), 404

        # Check if article exists
        article = Article.query.get(article_id)
        if not article:
            return jsonify({"error": "Article not found"}), 404

        # Check if score already exists
        article_score = ArticleScore.query.filter_by(user_id=user_id, article_id=article_id).first()

        if article_score:
            # Update existing score
            article_score.score = score
        else:
            # Create new score
            article_score = ArticleScore(user_id=user_id, article_id=article_id, score=score)
            db.session.add(article_score)

        db.session.commit()

        # Update article evaluation
        update_article_evaluation(article_id)

        return jsonify({"message": "Article score set successfully"}), 200

    except Exception as e:
        return jsonify({"error": str(e)}), 500
```

### 更新文章评分

```python
def update_article_evaluation(article_id):
    """
    Update article evaluation based on scores
    """
    try:
        # Get all scores for the article
        scores = ArticleScore.query.filter_by(article_id=article_id).all()

        if not scores:
            return

        # Calculate average score
        total_score = sum(score.score for score in scores)
        average_score = total_score / len(scores)

        # Update article evaluation
        article = Article.query.get(article_id)
        if article:
            article.evaluation = average_score
            db.session.commit()

    except Exception as e:
        print(f"Error updating article evaluation: {e}")
```

## 注意事项

1. 评分范围为 0-5，超出范围的评分将被拒绝。
2. 用户只能对每篇文章评分一次，再次评分会更新原有评分。
3. 文章的平均评分会自动更新，无需手动计算。
4. 文章的平均评分会影响文章在搜索结果和推荐列表中的排序。
5. 评分记录包含时间戳，可用于分析用户评分行为的时间趋势。