# 鸿雁智游个性化旅游系统 - 环境配置说明

## 系统概述

鸿雁智游是一个基于大模型的个性化旅游系统，采用前后端分离架构，支持Web端和微信小程序端。系统具备旅游推荐、路线规划、场所查询、旅游日记管理等核心功能，并集成了AIGC动画生成等先进功能。

## 技术架构

- **后端**: Python Flask + MySQL + SQLAlchemy
- **Web前端**: Vue.js 3 + Element Plus + 高德地图API
- **小程序端**: 微信小程序原生开发 + TypeScript
- **代理服务**: Node.js Express (解决小程序域名校验)
- **AI服务**: DeepSeek API + 豆包AIGC API

## 环境要求

### 基础环境
- **操作系统**: Windows 10/11, macOS, Linux
- **Python**: 3.8+ (推荐 3.9-3.12)
- **Node.js**: 16.0+ (推荐 18.0+)
- **MySQL**: 8.0+ (推荐 8.0.30+)
- **Git**: 最新版本

### 开发工具
- **后端开发**: PyCharm, VS Code, Sublime Text
- **前端开发**: VS Code, WebStorm
- **小程序开发**: 微信开发者工具
- **数据库管理**: Navicat, MySQL Workbench, phpMyAdmin

## 环境配置步骤

### 1. 克隆项目
```bash
git clone <repository-url>
cd personalized-travel-system
```

### 2. Python环境配置

#### 2.1 创建虚拟环境
```bash
# 进入后端目录
cd backend

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate
```

#### 2.2 安装Python依赖
```bash
# 安装所有依赖
pip install -r requirements.txt

# 或安装最小依赖集
pip install -r requirements_minimal.txt

# 如果遇到网络问题，使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

#### 2.3 主要Python依赖说明
- **Flask 2.3.2**: Web框架
- **SQLAlchemy 1.4.53**: ORM框架
- **PyMySQL 1.1.0**: MySQL数据库连接器
- **Flask-CORS 4.0.0**: 跨域资源共享
- **requests 2.31.0**: HTTP请求库
- **numpy 1.26.4**: 数值计算
- **scikit-learn 1.3.2**: 机器学习算法
- **opencv-python **********: 图像处理
- **moviepy 1.0.3**: 视频处理

### 3. Node.js环境配置

#### 3.1 安装Node.js依赖 (代理服务器)
```bash
# 在项目根目录
npm install

# 或使用yarn
yarn install
```

#### 3.2 Web前端依赖
```bash
# 进入前端目录
cd frontend_logged/travel_system_logged

# 安装依赖
npm install

# 或使用yarn
yarn install
```

#### 3.3 微信小程序依赖
```bash
# 进入小程序目录
cd Wechat_miniP

# 安装依赖
npm install
```

### 4. 数据库配置

#### 4.1 MySQL安装与配置
1. 下载并安装MySQL 8.0+
2. 创建数据库用户和数据库
```sql
-- 创建数据库
CREATE DATABASE study_tour_system CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 创建用户（可选，也可使用root）
CREATE USER 'travel_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON study_tour_system.* TO 'travel_user'@'localhost';
FLUSH PRIVILEGES;
```

#### 4.2 导入数据库结构和数据
```bash
# 使用MySQL命令行导入
mysql -u root -p study_tour_system < backend/database/study_tour_system.sql

# 或使用Navicat等图形化工具导入
```

### 5. 配置文件设置

#### 5.1 后端配置
编辑 `backend/config.py` 文件：
```python
# 数据库连接配置
SQLALCHEMY_DATABASE_URI = 'mysql+pymysql://用户名:密码@localhost/study_tour_system'

# DeepSeek API配置
DEEPSEEK_API_KEY = 'your_deepseek_api_key'

# 豆包AIGC配置
DOUBAO_API_KEY = 'your_doubao_api_key'
DOUBAO_ACCESS_KEY_ID = 'your_access_key_id'
DOUBAO_SECRET_ACCESS_KEY = 'your_secret_access_key'
```

#### 5.2 创建环境变量文件
在 `backend/` 目录下创建 `.env` 文件：
```env
# 数据库配置
DATABASE_URL=mysql+pymysql://root:your_password@localhost/study_tour_system

# API密钥
DEEPSEEK_API_KEY=your_deepseek_api_key
DOUBAO_API_KEY=your_doubao_api_key
DOUBAO_ACCESS_KEY_ID=your_access_key_id
DOUBAO_SECRET_ACCESS_KEY=your_secret_access_key

# Flask配置
SECRET_KEY=your_secret_key_here
```

### 6. API密钥申请

#### 6.1 DeepSeek API
1. 访问 [DeepSeek官网](https://platform.deepseek.com/)
2. 注册账号并实名认证
3. 在控制台创建API密钥
4. 充值账户余额（按使用量计费）

#### 6.2 豆包AIGC API
1. 访问 [火山引擎控制台](https://console.volcengine.com/)
2. 开通豆包大模型服务
3. 获取Access Key ID和Secret Access Key
4. 开通图像生成和视频生成服务

#### 6.3 高德地图API
1. 访问 [高德开放平台](https://lbs.amap.com/)
2. 注册开发者账号
3. 创建应用获取API Key
4. 在前端配置文件中设置API Key

### 7. 网络配置

#### 7.1 代理服务器配置
编辑 `proxy-server.js` 文件，修改目标IP地址：
```javascript
const proxyOptions = {
  target: 'http://你的IP地址:5000', // 修改为实际IP
  // ...其他配置
};
```

#### 7.2 获取本机IP地址
```bash
# Windows
ipconfig

# macOS/Linux
ifconfig
# 或
ip addr show
```

### 8. 微信小程序配置

#### 8.1 微信开发者工具
1. 下载并安装微信开发者工具
2. 使用微信扫码登录
3. 导入项目（选择 `Wechat_miniP` 目录）

#### 8.2 小程序配置
在 `Wechat_miniP/project.config.json` 中配置：
```json
{
  "appid": "你的小程序AppID",
  "projectname": "鸿雁智游",
  // ...其他配置
}
```

## 常见问题解决

### 1. Python依赖安装失败
```bash
# 升级pip
python -m pip install --upgrade pip

# 使用国内镜像源
pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/

# 单独安装问题包
pip install 包名 --no-cache-dir
```

### 2. MySQL连接失败
- 检查MySQL服务是否启动
- 确认用户名密码正确
- 检查防火墙设置
- 验证数据库是否存在

### 3. Node.js依赖安装慢
```bash
# 使用淘宝镜像
npm config set registry https://registry.npmmirror.com

# 或使用cnpm
npm install -g cnpm --registry=https://registry.npmmirror.com
cnpm install
```

### 4. 端口占用问题
```bash
# 查看端口占用
netstat -ano | findstr :5000
netstat -ano | findstr :3000

# 杀死占用进程
taskkill /PID 进程ID /F
```

## 下一步
配置完成后，请参考《运行指南.md》启动系统各个组件。
