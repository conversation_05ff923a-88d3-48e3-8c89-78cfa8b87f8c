@echo off
chcp 65001 >nul
echo ========================================
echo 鸿雁智游个性化旅游系统启动脚本
echo ========================================

echo.
echo [1/4] 检查MySQL服务...
net start mysql >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ MySQL服务已启动
) else (
    echo ✗ MySQL服务启动失败，请手动启动MySQL
    echo 请确保MySQL已安装并配置正确
    pause
    exit /b 1
)

echo.
echo [2/4] 启动后端服务...
if not exist "backend\venv" (
    echo ✗ 虚拟环境不存在，请先运行环境配置
    echo 请执行: cd backend && python -m venv venv && venv\Scripts\activate && pip install -r requirements.txt
    pause
    exit /b 1
)

cd backend
echo 激活Python虚拟环境...
call venv\Scripts\activate
echo 启动Flask后端服务...
start "鸿雁智游-后端服务" cmd /k "python app.py"
cd ..
timeout /t 3 >nul

echo.
echo [3/4] 启动代理服务器...
if not exist "node_modules" (
    echo ✗ Node.js依赖未安装，请先运行: npm install
    pause
    exit /b 1
)
echo 启动Node.js代理服务器...
start "鸿雁智游-代理服务器" cmd /k "node proxy-server.js"
timeout /t 3 >nul

echo.
echo [4/4] 启动前端服务...
if not exist "frontend_logged\travel_system_logged\node_modules" (
    echo ✗ 前端依赖未安装，请先在frontend_logged\travel_system_logged目录运行: npm install
    pause
    exit /b 1
)
cd frontend_logged\travel_system_logged
echo 启动Vue.js前端服务...
start "鸿雁智游-前端服务" cmd /k "npm run serve"
cd ..\..
timeout /t 5 >nul

echo.
echo ========================================
echo 系统启动完成！
echo ========================================
echo 后端API服务: http://localhost:5000
echo 代理服务器:   http://localhost:3000  
echo Web前端:     http://localhost:8080
echo ========================================
echo.
echo 请等待各服务完全启动后再访问前端页面
echo 如需停止服务，请关闭对应的命令行窗口
echo.
echo 按任意键打开前端页面...
pause >nul

start http://localhost:8080
