# 文件上传模块 API 文档

本文档详细描述了 Flask 版本后端的文件上传模块 API 接口及其在 Postman 中的测试用例。

## 目录

1. [上传头像](#上传头像)
2. [上传文章图片](#上传文章图片)
3. [上传文章视频](#上传文章视频)

## 上传头像

### 接口信息

- **URL**: `/api/upload/avatar`
- **方法**: `POST`
- **描述**: 上传用户头像
- **请求体**: form-data

### 请求参数

| 参数名  | 类型   | 必填 | 描述                                          |
| ------- | ------ | ---- | --------------------------------------------- |
| file    | file   | 是   | 头像文件，支持 png, jpg, jpeg, gif, webp 格式 |
| user_id | string | 是   | 用户 ID                                       |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Avatar uploaded successfully",
    "data": {
        "url": "/uploads/avatars/avatar_1_abc123.jpg"
    }
}
```

#### 错误响应

- **文件缺失** (400 Bad Request)

```json
{
    "code": 1,
    "message": "No file part",
    "data": null
}
```

- **用户不存在** (404 Not Found)

```json
{
    "code": 1,
    "message": "User not found",
    "data": null
}
```

- **文件类型不支持** (400 Bad Request)

```json
{
    "code": 1,
    "message": "File type not allowed. Allowed types: png, jpg, jpeg, gif, webp",
    "data": null
}
```

- **文件过大** (400 Bad Request)

```json
{
    "code": 1,
    "message": "File too large. Maximum size: 50MB",
    "data": null
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `POST`
4. 输入 URL: `http://localhost:5000/api/upload/avatar`
5. 选择 Body 选项卡，选择 form-data
6. 添加键值对:
   - Key: `file`, Value: [选择文件], Type: File
   - Key: `user_id`, Value: `1` (替换为实际的用户 ID), Type: Text
7. 点击 Send 按钮发送请求

## 上传文章图片

### 接口信息

- **URL**: `/api/upload/image`
- **方法**: `POST`
- **描述**: 上传文章图片
- **请求体**: form-data

### 请求参数

| 参数名     | 类型   | 必填 | 描述                                          |
| ---------- | ------ | ---- | --------------------------------------------- |
| file       | file   | 是   | 图片文件，支持 png, jpg, jpeg, gif, webp 格式 |
| article_id | string | 否   | 文章 ID，如果提供，则会自动更新文章的图片 URL |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Image uploaded successfully",
    "data": {
        "url": "/uploads/images/image_abc123.jpg"
    }
}
```

#### 错误响应

- **文件缺失** (400 Bad Request)

```json
{
    "code": 1,
    "message": "No file part",
    "data": null
}
```

- **文章不存在** (404 Not Found)

```json
{
    "code": 1,
    "message": "Article not found",
    "data": null
}
```

- **文件类型不支持** (400 Bad Request)

```json
{
    "code": 1,
    "message": "File type not allowed. Allowed types: png, jpg, jpeg, gif, webp",
    "data": null
}
```

- **文件过大** (400 Bad Request)

```json
{
    "code": 1,
    "message": "File too large. Maximum size: 50MB",
    "data": null
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `POST`
4. 输入 URL: `http://localhost:5000/api/upload/image`
5. 选择 Body 选项卡，选择 form-data
6. 添加键值对:
   - Key: `file`, Value: [选择文件], Type: File
   - Key: `article_id`, Value: `1` (可选，替换为实际的文章 ID), Type: Text
7. 点击 Send 按钮发送请求

## 上传文章视频

### 接口信息

- **URL**: `/api/upload/video`
- **方法**: `POST`
- **描述**: 上传文章视频
- **请求体**: form-data

### 请求参数

| 参数名     | 类型   | 必填 | 描述                                          |
| ---------- | ------ | ---- | --------------------------------------------- |
| file       | file   | 是   | 视频文件，支持 mp4, webm, ogg, mov 格式       |
| article_id | string | 否   | 文章 ID，如果提供，则会自动更新文章的视频 URL |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Video uploaded successfully",
    "data": {
        "url": "/uploads/videos/video_abc123.mp4"
    }
}
```

#### 错误响应

- **文件缺失** (400 Bad Request)

```json
{
    "code": 1,
    "message": "No file part",
    "data": null
}
```

- **文章不存在** (404 Not Found)

```json
{
    "code": 1,
    "message": "Article not found",
    "data": null
}
```

- **文件类型不支持** (400 Bad Request)

```json
{
    "code": 1,
    "message": "File type not allowed. Allowed types: mp4, webm, ogg, mov",
    "data": null
}
```

- **文件过大** (400 Bad Request)

```json
{
    "code": 1,
    "message": "File too large. Maximum size: 50MB",
    "data": null
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `POST`
4. 输入 URL: `http://localhost:5000/api/upload/video`
5. 选择 Body 选项卡，选择 form-data
6. 添加键值对:
   - Key: `file`, Value: [选择文件], Type: File
   - Key: `article_id`, Value: `1` (可选，替换为实际的文章 ID), Type: Text
7. 点击 Send 按钮发送请求

## 文件上传实现细节

### 文件存储结构

文件上传模块将文件存储在以下目录结构中：

```
backend/uploads/
├── avatars/     # 用户头像
├── images/      # 文章图片
└── videos/      # 文章视频
```

### 文件命名规则

- 头像文件：`avatar_{user_id}_{uuid}.{extension}`
- 图片文件：`image_{article_id}_{uuid}.{extension}` 或 `image_{uuid}.{extension}`
- 视频文件：`video_{article_id}_{uuid}.{extension}` 或 `video_{uuid}.{extension}`

其中，`{uuid}` 是一个随机生成的唯一标识符，用于避免文件名冲突。

### 文件大小限制

所有上传的文件大小不得超过 50MB。

### 支持的文件类型

- 图片文件：png, jpg, jpeg, gif, webp
- 视频文件：mp4, webm, ogg, mov

## 完整的 Postman 测试流程

以下是一个完整的测试流程，按照顺序执行可以测试文件上传模块的所有功能：

### 1. 上传头像

```
POST http://localhost:5000/api/upload/avatar
Content-Type: multipart/form-data

file: [选择文件]
user_id: 1
```

### 2. 上传文章图片

```
POST http://localhost:5000/api/upload/image
Content-Type: multipart/form-data

file: [选择文件]
article_id: 1
```

### 3. 上传文章视频

```
POST http://localhost:5000/api/upload/video
Content-Type: multipart/form-data

file: [选择文件]
article_id: 1
```

## 代码实现

文件上传模块的核心代码位于 `backend/routes/upload.py` 文件中。以下是关键部分的代码实现：

### 文件类型验证

```python
# 允许的文件类型
ALLOWED_IMAGE_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
ALLOWED_VIDEO_EXTENSIONS = {'mp4', 'webm', 'ogg', 'mov'}

def allowed_image(filename):
    """检查是否是允许的图片文件类型"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_IMAGE_EXTENSIONS

def allowed_video(filename):
    """检查是否是允许的视频文件类型"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_VIDEO_EXTENSIONS
```

### 文件大小限制

```python
# 最大文件大小 (50MB)
MAX_FILE_SIZE = 50 * 1024 * 1024
```

### 文件名生成

```python
def get_unique_filename(filename):
    """生成唯一的文件名"""
    ext = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''
    return f"{uuid.uuid4().hex}.{ext}"
```

## 注意事项

1. 确保上传目录存在且具有写入权限。
2. 上传大文件时可能需要较长时间，请耐心等待。
3. 如果上传失败，请检查文件类型和大小是否符合要求。
4. 上传成功后，文件 URL 将返回相对路径，需要与服务器地址拼接才能访问。