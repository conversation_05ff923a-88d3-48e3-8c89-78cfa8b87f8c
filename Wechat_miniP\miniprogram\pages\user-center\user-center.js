// user-center.js
Component({
  data: {
    isLoggedIn: false,
    userInfo: {
      nickname: '未登录',
      avatar: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzIiIGZpbGw9IiNGNUY1RjUiLz4KPGNpcmNsZSBjeD0iMzIiIGN5PSIyNCIgcj0iMTAiIGZpbGw9IiNEREREREQiLz4KPHBhdGggZD0iTTEyIDUyQzEyIDQzLjE2MzQgMTkuMTYzNCAzNiAyOCAzNkg0NEM1Mi44MzY2IDM2IDYwIDQzLjE2MzQgNjAgNTJWNjRIMTJWNTJaIiBmaWxsPSIjREREREREIi8+Cjwvc3ZnPgo='
    },
    userStats: {
      diaryCount: 0,
      favoriteCount: 0,
      routeCount: 0
    },
    quickActions: [
      {
        id: 1,
        icon: '✍️',
        title: '写日记',
        url: '/pages/diary-create/diary-create'
      },
      {
        id: 2,
        icon: '🗺️',
        title: '路线规划',
        url: '/pages/route-plan/route-plan'
      },
      {
        id: 3,
        icon: '🔍',
        title: '找景点',
        url: '/pages/place-search/place-search'
      },
      {
        id: 4,
        icon: '🌟',
        title: '推荐',
        url: '/pages/recommend/recommend'
      }
    ],
    menuGroups: [
      {
        title: '我的内容',
        items: [
          {
            id: 1,
            icon: '📖',
            title: '我的日记',
            desc: '查看我发布的旅行日记',
            url: '/pages/diary/diary'
          },
          {
            id: 2,
            icon: '❤️',
            title: '我的收藏',
            desc: '收藏的景点和日记',
            url: '/pages/favorites/favorites'
          },
          {
            id: 3,
            icon: '🗺️',
            title: '历史路线',
            desc: '查看历史规划路线',
            url: '/pages/route-history/route-history'
          }
        ]
      },
      {
        title: '设置与帮助',
        items: [
          {
            id: 4,
            icon: '⚙️',
            title: '设置',
            desc: '个人设置和偏好',
            url: '/pages/settings/settings'
          },
          {
            id: 5,
            icon: '📞',
            title: '联系我们',
            desc: '获取帮助与客户支持',
            url: '/pages/contact/contact'
          },
          {
            id: 6,
            icon: '📋',
            title: '关于我们',
            desc: '了解鸿雁智游',
            url: '/pages/about/about'
          }
        ]
      }
    ]
  },

  onLoad() {
    this.checkLoginStatus();
    this.loadUserStats();
  },

  onShow() {
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 4
      });
    }
    this.checkLoginStatus();
    this.loadUserStats();
  },

  pageLifetimes: {
    show() {
      if (typeof this.getTabBar === 'function' && this.getTabBar()) {
        this.getTabBar().setData({
          selected: 4
        });
      }
      this.checkLoginStatus();
      this.loadUserStats();
    }
  },

  methods: {
    // 获取用户信息
    getUserProfile() {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          this.setData({
            'userInfo.nickname': res.userInfo.nickName,
            'userInfo.avatar': res.userInfo.avatarUrl
          });

          // 保存用户信息到本地存储
          wx.setStorageSync('userInfo', {
            nickname: res.userInfo.nickName,
            avatar: res.userInfo.avatarUrl
          });
        },
        fail: () => {
          wx.showToast({
            title: '获取用户信息失败',
            icon: 'none'
          });
        }
      });
    },

    // 头像加载错误处理
    onAvatarError() {
      console.log('头像加载失败，使用默认头像');
      this.setData({
        'userInfo.avatar': 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzIiIGZpbGw9IiNGNUY1RjUiLz4KPGNpcmNsZSBjeD0iMzIiIGN5PSIyNCIgcj0iMTAiIGZpbGw9IiNEREREREQiLz4KPHBhdGggZD0iTTEyIDUyQzEyIDQzLjE2MzQgMTkuMTYzNCAzNiAyOCAzNkg0NEM1Mi44MzY2IDM2IDYwIDQzLjE2MzQgNjAgNTJWNjRIMTJWNTJaIiBmaWxsPSIjREREREREIi8+Cjwvc3ZnPgo='
      });
    },

    // 加载用户统计数据
    loadUserStats() {
      // 检查登录状态已经在checkLoginStatus中处理了用户信息
      // 这里只需要加载统计数据

      // 模拟加载用户统计数据
      // 实际应该调用后端API
      wx.request({
        url: 'http://localhost:5000/api/user/stats',
        method: 'GET',
        data: {
          user_id: 1 // 临时用户ID
        },
        success: (res) => {
          if (res.data.success) {
            this.setData({
              userStats: res.data.data
            });
          }
        },
        fail: () => {
          // 使用模拟数据
          this.setData({
            userStats: {
              diaryCount: 5,
              favoriteCount: 12,
              routeCount: 3
            }
          });
        }
      });
    },

    // 通用跳转方法
    navigateToPage(url) {
      if (!url) return;

      // 定义tabbar页面列表
      const tabbarPages = [
        '/pages/index/index',
        '/pages/place-search/place-search',
        '/pages/route-plan/route-plan',
        '/pages/diary/diary',
        '/pages/user-center/user-center'
      ];

      // 检查是否为tabbar页面
      const isTabbarPage = tabbarPages.includes(url);

      if (isTabbarPage) {
        wx.switchTab({ url: url });
      } else {
        wx.navigateTo({ url: url });
      }
    },

    // 快捷功能点击
    onQuickActionTap(e) {
      const url = e.currentTarget.dataset.url;
      this.navigateToPage(url);
    },

    // 菜单项点击
    onMenuItemTap(e) {
      const url = e.currentTarget.dataset.url;
      if (url) {
        if (url.startsWith('/pages/diary/') || url.startsWith('/pages/contact/') || url.startsWith('/pages/settings/')) {
          // 已存在的页面
          this.navigateToPage(url);
        } else {
          // 未实现的页面
          wx.showToast({
            title: '功能开发中',
            icon: 'none'
          });
        }
      }
    },

    // 跳转到日记列表
    goToDiaryList() {
      this.navigateToPage('/pages/diary/diary');
    },

    // 跳转到收藏页面
    goToFavorites() {
      wx.showToast({
        title: '收藏功能开发中',
        icon: 'none'
      });
    },

    // 跳转到路线历史
    goToRouteHistory() {
      wx.showToast({
        title: '路线历史功能开发中',
        icon: 'none'
      });
    },

    // 检查登录状态
    checkLoginStatus() {
      const userInfo = wx.getStorageSync('userInfo');
      const token = wx.getStorageSync('token');

      if (!userInfo || !token) {
        // 未登录状态
        this.setData({
          isLoggedIn: false,
          userInfo: {
            nickname: '未登录',
            avatar: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzIiIGZpbGw9IiNGNUY1RjUiLz4KPGNpcmNsZSBjeD0iMzIiIGN5PSIyNCIgcj0iMTAiIGZpbGw9IiNEREREREQiLz4KPHBhdGggZD0iTTEyIDUyQzEyIDQzLjE2MzQgMTkuMTYzNCAzNiAyOCAzNkg0NEM1Mi44MzY2IDM2IDYwIDQzLjE2MzQgNjAgNTJWNjRIMTJWNTJaIiBmaWxsPSIjREREREREIi8+Cjwvc3ZnPgo='
          }
        });
        return false;
      }

      // 已登录状态 - 处理不同的用户信息格式
      const displayUserInfo = {
        nickname: userInfo.nickname || userInfo.username || '用户',
        avatar: userInfo.avatar || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzIiIGZpbGw9IiNGNUY1RjUiLz4KPGNpcmNsZSBjeD0iMzIiIGN5PSIyNCIgcj0iMTAiIGZpbGw9IiNEREREREQiLz4KPHBhdGggZD0iTTEyIDUyQzEyIDQzLjE2MzQgMTkuMTYzNCAzNiAyOCAzNkg0NEM1Mi44MzY2IDM2IDYwIDQzLjE2MzQgNjAgNTJWNjRIMTJWNTJaIiBmaWxsPSIjREREREREIi8+Cjwvc3ZnPgo='
      };

      this.setData({
        isLoggedIn: true,
        userInfo: displayUserInfo
      });
      return true;
    },

    // 登录按钮点击
    onLoginTap() {
      console.log('登录按钮被点击');
      wx.navigateTo({
        url: '/pages/login/login'
      });
    },

    // 登出按钮点击
    onLogoutTap() {
      const that = this;
      wx.showModal({
        title: '确认登出',
        content: '确定要退出登录吗？',
        success: function(res) {
          if (res.confirm) {
            // 清除本地存储
            wx.removeStorageSync('userInfo');
            wx.removeStorageSync('token');
            wx.removeStorageSync('userId');

            // 更新页面状态
            that.setData({
              isLoggedIn: false,
              userInfo: {
                nickname: '未登录',
                avatar: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzIiIGZpbGw9IiNGNUY1RjUiLz4KPGNpcmNsZSBjeD0iMzIiIGN5PSIyNCIgcj0iMTAiIGZpbGw9IiNEREREREQiLz4KPHBhdGggZD0iTTEyIDUyQzEyIDQzLjE2MzQgMTkuMTYzNCAzNiAyOCAzNkg0NEM1Mi44MzY2IDM2IDYwIDQzLjE2MzQgNjAgNTJWNjRIMTJWNTJaIiBmaWxsPSIjREREREREIi8+Cjwvc3ZnPgo='
              },
              userStats: {
                diaryCount: 0,
                favoriteCount: 0,
                routeCount: 0
              }
            });

            wx.showToast({
              title: '已退出登录',
              icon: 'success'
            });
          }
        }
      });
    }
  }
})
