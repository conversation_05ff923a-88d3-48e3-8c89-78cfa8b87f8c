// user-center.js
Page({
  data: {
    userInfo: {
      nickname: '旅行者',
      avatar: '/images/icon/user.png'
    },
    userStats: {
      diaryCount: 0,
      favoriteCount: 0,
      routeCount: 0
    },
    isLoggedIn: false,
    quickActions: [
      {
        id: 1,
        icon: '✍️',
        title: '写日记',
        url: '/pages/diary-create/diary-create'
      },
      {
        id: 2,
        icon: '🗺️',
        title: '路线规划',
        url: '/pages/route-plan/route-plan'
      },
      {
        id: 3,
        icon: '🔍',
        title: '找景点',
        url: '/pages/place-search/place-search'
      },
      {
        id: 4,
        icon: '🌟',
        title: '推荐',
        url: '/pages/recommend/recommend'
      }
    ],
    menuGroups: [
      {
        title: '我的内容',
        items: [
          {
            id: 1,
            icon: '📖',
            title: '我的日记',
            desc: '查看我发布的旅行日记',
            url: '/pages/my-diaries/my-diaries'
          },
          {
            id: 2,
            icon: '❤️',
            title: '我的收藏',
            desc: '收藏的景点和日记',
            url: '/pages/favorites/favorites'
          },
          {
            id: 3,
            icon: '🗺️',
            title: '历史路线',
            desc: '查看历史规划路线',
            url: '/pages/route-history/route-history'
          }
        ]
      },
      {
        title: '设置与帮助',
        items: [
          {
            id: 4,
            icon: '⚙️',
            title: '设置',
            desc: '个人设置和偏好',
            url: '/pages/settings/settings'
          },
          {
            id: 5,
            icon: '📞',
            title: '联系我们',
            desc: '获取帮助与客户支持',
            url: '/pages/contact/contact'
          },
          {
            id: 6,
            icon: '📋',
            title: '关于我们',
            desc: '了解鸿雁智游',
            url: '/pages/about/about'
          }
        ]
      }
    ]
  },

  onLoad: function() {
    console.log('个人中心页面加载');
    this.checkLoginStatus();
    this.loadUserStats();
  },

  onShow: function() {
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 4
      });
    }
    this.checkLoginStatus();
    this.loadUserStats();
  },

  // 获取用户信息
  getUserProfile: function() {
      const that = this;
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: function(res) {
          that.setData({
            'userInfo.nickname': res.userInfo.nickName,
            'userInfo.avatar': res.userInfo.avatarUrl
          });

          // 保存用户信息到本地存储
          wx.setStorageSync('userInfo', {
            nickname: res.userInfo.nickName,
            avatar: res.userInfo.avatarUrl
          });
        },
        fail: function() {
          wx.showToast({
            title: '获取用户信息失败',
            icon: 'none'
          });
        }
      });
  },

  // 加载用户统计数据
  loadUserStats: function() {
      const that = this;

      // 检查登录状态
      if (!this.data.isLoggedIn) {
        return;
      }

      // 尝试从本地存储获取用户信息
      const savedUserInfo = wx.getStorageSync('userInfo');
      if (savedUserInfo) {
        // 确保用户信息格式正确
        const userInfo = {
          nickname: savedUserInfo.username || savedUserInfo.nickname || '旅行者',
          avatar: savedUserInfo.avatar || '/images/icon/user.png'
        };
        this.setData({
          userInfo: userInfo
        });
      }

      // 获取用户详细信息
      const userId = this.getCurrentUserId();
      if (userId && userId !== 1) {
        wx.request({
          url: `http://localhost:5000/api/user/${userId}`,
          method: 'GET',
          success: function(res) {
            console.log('用户详细信息API响应:', res);
            if (res.data.code === 0 && res.data.data && res.data.data.user) {
              const user = res.data.data.user;
              that.setData({
                'userInfo.nickname': user.username,
                'userInfo.avatar': user.avatar ? `http://localhost:5000/uploads/avatars/${user.avatar}` : '/images/icon/user.png'
              });
            }
          },
          fail: function(error) {
            console.error('获取用户详细信息失败:', error);
          }
        });
      }

      // 加载用户统计数据
      wx.request({
        url: 'http://localhost:5000/api/user/stats',
        method: 'GET',
        data: {
          user_id: userId
        },
        success: function(res) {
          console.log('用户统计API响应:', res);
          if (res.data.code === 0 && res.data.data) {
            that.setData({
              userStats: res.data.data
            });
          }
        },
        fail: function(error) {
          console.error('获取用户统计数据失败:', error);
          // 使用模拟数据
          that.setData({
            userStats: {
              diaryCount: 5,
              favoriteCount: 12,
              routeCount: 3
            }
          });
        }
      });
  },

  // 快捷功能点击
  onQuickActionTap: function(e) {
      const url = e.currentTarget.dataset.url;
      if (url) {
        wx.navigateTo({
          url: url
        });
      }
  },

  // 菜单项点击
  onMenuItemTap: function(e) {
      const url = e.currentTarget.dataset.url;
      if (url) {
        if (url.includes('/my-diaries/') || url.includes('/diary-create/') || url.includes('/contact/') || url.includes('/settings/')) {
          // 已存在的页面
          wx.navigateTo({
            url: url
          });
        } else {
          // 未实现的页面
          wx.showToast({
            title: '功能开发中',
            icon: 'none'
          });
        }
      }
  },

  // 跳转到日记列表
  goToDiaryList: function() {
      wx.navigateTo({
        url: '/pages/my-diaries/my-diaries'
      });
  },

  // 跳转到收藏页面
  goToFavorites: function() {
      wx.showToast({
        title: '收藏功能开发中',
        icon: 'none'
      });
  },

  // 跳转到路线历史
  goToRouteHistory: function() {
      wx.showToast({
        title: '路线历史功能开发中',
        icon: 'none'
      });
  },

  // 检查登录状态
  checkLoginStatus: function() {
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');

    if (!userInfo || !token) {
      // 未登录状态
      this.setData({
        isLoggedIn: false,
        userInfo: {
          nickname: '未登录',
          avatar: '/images/icon/user.png'
        }
      });
      return false;
    }

    // 已登录状态
    this.setData({
      isLoggedIn: true
    });
    return true;
  },

  // 登录按钮点击
  onLoginTap: function() {
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },

  // 登出按钮点击
  onLogoutTap: function() {
    const that = this;
    wx.showModal({
      title: '确认登出',
      content: '确定要退出登录吗？',
      success: function(res) {
        if (res.confirm) {
          // 清除本地存储
          wx.removeStorageSync('userInfo');
          wx.removeStorageSync('token');
          wx.removeStorageSync('userId');

          // 更新页面状态
          that.setData({
            isLoggedIn: false,
            userInfo: {
              nickname: '未登录',
              avatar: '/images/icon/user.png'
            },
            userStats: {
              diaryCount: 0,
              favoriteCount: 0,
              routeCount: 0
            }
          });

          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
        }
      }
    });
  },

  // 获取当前用户ID
  getCurrentUserId: function() {
    const userInfo = wx.getStorageSync('userInfo');
    const userId = wx.getStorageSync('userId');

    if (userInfo && userInfo.user_id) {
      return userInfo.user_id;
    } else if (userId) {
      return userId;
    } else {
      return 1; // 默认用户ID，用于游客模式
    }
  }
});
