try:
    from elasticsearch import Elasticsearch
    ELASTICSEARCH_AVAILABLE = True
except ImportError:
    ELASTICSEARCH_AVAILABLE = False
    print("Elasticsearch not available, using fallback search")

class SearchEngine:
    def __init__(self):
        self.index_name = "travel_journals"
        if ELASTICSEARCH_AVAILABLE:
            try:
                self.es = Elasticsearch(['http://localhost:9200'])
                # 测试连接
                if not self.es.ping():
                    print("Elasticsearch server not available, using fallback search")
                    self.es = None
            except Exception as e:
                print(f"Error connecting to Elasticsearch: {e}")
                self.es = None
        else:
            self.es = None

    def create_index(self):
        if not self.es:
            print("Elasticsearch not available, skipping index creation")
            return

        body = {
            "mappings": {
                "properties": {
                    "title": {"type": "text", "analyzer": "ik_max_word"},
                    "content": {"type": "text", "analyzer": "ik_max_word"},
                    "location": {"type": "keyword"}
                }
            }
        }
        try:
            self.es.indices.create(index=self.index_name, body=body)
        except Exception as e:
            print(f"Error creating index: {e}")

    def index_journal(self, journal):
        if not self.es:
            print("Elasticsearch not available, skipping indexing")
            return

        doc = {
            "title": journal.title,
            "content": journal.content,
            "location": journal.location,
            "views": journal.views
        }
        try:
            self.es.index(index=self.index_name, id=journal.id, body=doc)
        except Exception as e:
            print(f"Error indexing document: {e}")

    def search(self, query):
        if not self.es:
            print("Elasticsearch not available, using fallback search")
            # 使用简单的内存搜索作为备用
            from models.journal import TravelJournal
            journals = TravelJournal.query.filter(
                TravelJournal.title.ilike(f'%{query}%') |
                TravelJournal.content.ilike(f'%{query}%')
            ).all()

            return [{
                'id': j.id,
                'title': j.title,
                'content': j.content[:100] + '...' if j.content else ''
            } for j in journals]

        try:
            result = self.es.search(index=self.index_name, body={
                "query": {
                    "multi_match": {
                        "query": query,
                        "fields": ["title^3", "content"]
                    }
                },
                "highlight": {
                    "fields": {
                        "content": {}
                    }
                }
            })
            return [{
                'id': hit['_id'],
                'title': hit['_source'].get('title', ''),
                'content': hit['_source'].get('content', ''),
                'highlight': hit.get('highlight', {})
            } for hit in result['hits']['hits']]
        except Exception as e:
            print(f"Error searching: {e}")
            return []