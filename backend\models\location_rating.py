from utils.database import db
from datetime import datetime

class LocationRating(db.Model):
    """
    Location Rating model - represents a user's rating for a location
    """
    __tablename__ = 'location_ratings'
    __table_args__ = {'extend_existing': True}

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.<PERSON><PERSON>, db.<PERSON>ey('users.user_id'), nullable=False)
    location_id = db.Column(db.Integer, db.<PERSON>ey('locations.location_id'), nullable=False)
    rating = db.Column(db.Integer, nullable=False)  # 1-5 stars
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Define unique constraint to ensure a user can only rate a location once
    __table_args__ = (
        db.UniqueConstraint('user_id', 'location_id', name='uix_user_location_rating'),
        {'extend_existing': True}
    )

    def to_dict(self):
        """Convert rating to dictionary"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'location_id': self.location_id,
            'rating': self.rating,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
