import axios from './axios';
import service from '@/utils/request';

export const register = async (userData) => {
  try {
    console.log('发送注册请求:', userData);
    const response = await axios.post('/auth/register', userData);
    console.log('注册响应:', response.data);

    if (response.data.status === 'success') {
      console.log("响应了")
      return {
        status: 'success',
        user: response.data.user,
        token: response.data.token
      };
    } else {
      throw new Error(response.data.error || '注册失败');
    }
  } catch (error) {
    console.error('注册错误:', error);
    throw error;
  }
};

// 登录
export const login = async (credentials) => {
  try {
    console.log('发送登录请求:', credentials);
    const response = await axios.post('/auth/login', credentials);
    console.log('登录响应:', response.data);

    // 适配Flask后端的响应格式
    if (response.data.code === 0) {
      return {
        status: 'success',
        user: response.data.data.user,
        token: response.data.data.token
      };
    } else {
      throw new Error(response.data.message || '登录失败');
    }
  } catch (error) {
    console.error('登录错误:', error);
    if (error.response && error.response.data) {
      throw new Error(error.response.data.message || '登录失败');
    }
    throw error;
  }
};

















export const getCurrentUser = async () => {
  console.log('开始获取用户信息');
  try {
    const token = localStorage.getItem('authToken');
    const userId = localStorage.getItem('userId'); // <-- 从 localStorage 获取 userId
    console.log('获取到的token:', token);
    console.log('获取到的userId:', userId);

    if (!token || !userId) { // <-- 检查 token 和 userId 是否都存在
      console.log('Token 或 UserId 不存在，重定向到登录');
      // 清理可能存在的无效项
      localStorage.removeItem('authToken');
      localStorage.removeItem('userId');
      localStorage.removeItem('currentUser'); // 可能也需要清理
      // window.location.href = '/login'; // 暂时注释掉重定向，方便调试
      return { status: 'error', message: '用户未登录或用户信息不完整' };
    }

    // 尝试从localStorage获取用户信息
    const localUserStr = localStorage.getItem('currentUser');
    if (localUserStr) {
      try {
        const localUser = JSON.parse(localUserStr);
        console.log('从localStorage获取到的用户信息:', localUser);

        // 确保用户ID一致
        if (localUser && (localUser.id == userId || localUser.user_id == userId)) {
          // 确保user_id字段存在
          if (!localUser.user_id && localUser.id) {
            localUser.user_id = localUser.id;
          }

          console.log('使用localStorage中的用户信息');
          return {
            status: 'success',
            user: localUser
          };
        }
      } catch (e) {
        console.error('解析localStorage中的用户信息失败:', e);
      }
    }

    // 如果localStorage中没有有效的用户信息，则从API获取
    console.log('从API获取用户信息');
    const response = await service.get(`/api/user/${userId}`); // <-- 使用 service 和正确的路径

    console.log('获取用户信息响应 (经过拦截器处理):', response);

    if (response && response.data && response.data.user) {
      const user = response.data.user;

      // 确保user_id字段存在
      if (!user.user_id && user.id) {
        user.user_id = user.id;
      }

      // 保存到localStorage
      localStorage.setItem('currentUser', JSON.stringify(user));

      return {
        status: 'success',
        user: user // <-- 从 response.data.user 获取用户对象
      };
    } else {
      // 如果响应格式不符合预期
      console.error('获取用户信息响应格式不正确:', response);
      return { status: 'error', message: '获取用户信息失败：响应格式错误' };
    }

  } catch (error) {
    console.error('获取用户信息失败:', error);
    if (error.message?.includes('401')) { // 检查是否是认证错误
        // 可能已经在 request.js 中处理了跳转
        console.log('认证失败，可能已在拦截器中处理跳转');
    }
    // 返回一个表示失败的状态或特定错误对象，避免调用者拿到 undefined
    return { status: 'error', message: error.message || '获取用户信息失败' };
  }
};

export const logout = async () => {
  try {
    await axios.post('/auth/logout');
    localStorage.removeItem('authToken');
    localStorage.clear();
  } catch (error) {
    console.error('登出时发生错误:', error);
  }
};


// 刷新token——————————————————————————————————————————————————————新
export const refreshToken = async () => {
  try {
    const refreshToken = localStorage.getItem('refreshToken');
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }
    const response = await axios.post('/auth/refresh', {}, {
      headers: {
        'Authorization': `Bearer ${refreshToken}`
      }
    });
    if (response.data.status === 'success') {
      localStorage.setItem('authToken', response.data.access_token);
      return response.data.access_token;
    }
  } catch (error) {
    throw new Error(error.response?.data?.message || '刷新token失败');
  }
};