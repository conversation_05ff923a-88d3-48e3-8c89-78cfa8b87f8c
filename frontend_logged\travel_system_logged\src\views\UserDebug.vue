<template>
  <div class="user-debug">
    <h1>用户信息调试页面</h1>
    
    <div class="debug-section">
      <h2>localStorage 内容</h2>
      <div class="storage-item" v-for="(value, key) in localStorageData" :key="key">
        <strong>{{ key }}:</strong> {{ value }}
      </div>
    </div>

    <div class="debug-section">
      <h2>用户工具函数测试</h2>
      <p><strong>getCurrentUserId():</strong> {{ currentUserId }}</p>
      <p><strong>getCurrentUser():</strong> {{ JSON.stringify(currentUser) }}</p>
      <p><strong>isUserLoggedIn():</strong> {{ isLoggedIn }}</p>
      <p><strong>getAuthToken():</strong> {{ authToken }}</p>
    </div>

    <div class="debug-section">
      <h2>操作</h2>
      <el-button @click="refreshData">刷新数据</el-button>
      <el-button @click="clearAllData" type="danger">清除所有数据</el-button>
      <el-button @click="setTestUser" type="primary">设置测试用户</el-button>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { getCurrentUserId, getCurrentUser, isUserLoggedIn, getAuthToken, clearUserData, saveUserData } from '@/utils/userUtils'

export default {
  name: 'UserDebug',
  setup() {
    const localStorageData = ref({})
    const currentUserId = ref(null)
    const currentUser = ref(null)
    const isLoggedIn = ref(false)
    const authToken = ref(null)

    const loadLocalStorageData = () => {
      const data = {}
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        const value = localStorage.getItem(key)
        try {
          // 尝试解析JSON
          data[key] = JSON.parse(value)
        } catch (e) {
          // 如果不是JSON，直接显示字符串
          data[key] = value
        }
      }
      localStorageData.value = data
    }

    const loadUserData = () => {
      currentUserId.value = getCurrentUserId()
      currentUser.value = getCurrentUser()
      isLoggedIn.value = isUserLoggedIn()
      authToken.value = getAuthToken()
    }

    const refreshData = () => {
      loadLocalStorageData()
      loadUserData()
    }

    const clearAllData = () => {
      clearUserData()
      refreshData()
    }

    const setTestUser = () => {
      const testUser = {
        id: 1,
        user_id: 1,
        username: 'testuser',
        email: '<EMAIL>'
      }
      const testToken = 'test-token-123'
      
      saveUserData(testUser, testToken)
      refreshData()
    }

    onMounted(() => {
      refreshData()
    })

    return {
      localStorageData,
      currentUserId,
      currentUser,
      isLoggedIn,
      authToken,
      refreshData,
      clearAllData,
      setTestUser
    }
  }
}
</script>

<style scoped>
.user-debug {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.debug-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f9f9f9;
}

.debug-section h2 {
  margin-top: 0;
  color: #333;
}

.storage-item {
  margin-bottom: 10px;
  padding: 10px;
  background: white;
  border-radius: 4px;
  word-break: break-all;
}

.storage-item strong {
  color: #666;
}
</style>
