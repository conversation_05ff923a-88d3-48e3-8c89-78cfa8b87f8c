"""
餐馆推荐算法模块

该模块实现了基于协同过滤的餐馆推荐算法，包括：
1. 基于用户的协同过滤（User-Based Collaborative Filtering）
2. 基于内容的推荐（Content-Based Recommendation）
3. 混合推荐（Hybrid Recommendation）

所有算法都使用纯Python实现，不依赖外部机器学习库。
"""

from collections import defaultdict
import math
from typing import List, Dict, Tuple, Any

from models.restaurant import Restaurant
from models.restaurant_favorite import RestaurantFavorite
from models.restaurant_comment import RestaurantComment
from models.user import User


class RestaurantRecommender:
    """餐馆推荐算法类"""

    @staticmethod
    def get_user_favorites(user_id: int) -> List[int]:
        """
        获取用户收藏的餐馆ID列表

        Args:
            user_id: 用户ID

        Returns:
            用户收藏的餐馆ID列表
        """
        favorites = RestaurantFavorite.query.filter_by(user_id=user_id).all()
        return [favorite.restaurant_id for favorite in favorites]

    @staticmethod
    def get_user_ratings(user_id: int) -> Dict[int, float]:
        """
        获取用户对餐馆的评分

        Args:
            user_id: 用户ID

        Returns:
            用户对餐馆的评分字典 {restaurant_id: rating}
        """
        comments = RestaurantComment.query.filter_by(user_id=user_id).all()
        return {comment.restaurant_id: comment.rating for comment in comments}

    @staticmethod
    def get_user_cuisine_preferences(user_id: int) -> Dict[str, float]:
        """
        获取用户对菜系的偏好

        Args:
            user_id: 用户ID

        Returns:
            用户对菜系的偏好字典 {cuisine_type: preference_score}
        """
        # 获取用户收藏的餐馆
        favorite_restaurant_ids = RestaurantRecommender.get_user_favorites(user_id)
        favorite_restaurants = Restaurant.query.filter(Restaurant.id.in_(favorite_restaurant_ids)).all()

        # 获取用户评分的餐馆
        user_ratings = RestaurantRecommender.get_user_ratings(user_id)
        rated_restaurants = Restaurant.query.filter(Restaurant.id.in_(list(user_ratings.keys()))).all()

        # 统计菜系偏好
        cuisine_preferences = defaultdict(float)

        # 收藏的餐馆权重为3.0（增加权重，更重视用户收藏的菜系）
        for restaurant in favorite_restaurants:
            if restaurant.cuisine_type:
                cuisine_preferences[restaurant.cuisine_type] += 3.0
                print(f"用户 {user_id} 收藏了 {restaurant.cuisine_type} 菜系的餐馆 {restaurant.name}")

        # 评分的餐馆权重为评分/5.0 * 2.0（增加权重，更重视用户评分的菜系）
        for restaurant in rated_restaurants:
            if restaurant.cuisine_type:
                cuisine_preferences[restaurant.cuisine_type] += (user_ratings[restaurant.id] / 5.0) * 2.0
                print(f"用户 {user_id} 给 {restaurant.cuisine_type} 菜系的餐馆 {restaurant.name} 评分 {user_ratings[restaurant.id]}")

        return dict(cuisine_preferences)

    @staticmethod
    def calculate_cosine_similarity(vec1: Dict[Any, float], vec2: Dict[Any, float]) -> float:
        """
        计算两个向量的余弦相似度

        Args:
            vec1: 第一个向量 {key: value}
            vec2: 第二个向量 {key: value}

        Returns:
            余弦相似度 (0-1)
        """
        # 找到共同的键
        common_keys = set(vec1.keys()) & set(vec2.keys())

        # 如果没有共同的键，相似度为0
        if not common_keys:
            return 0.0

        # 计算点积
        dot_product = sum(vec1[key] * vec2[key] for key in common_keys)

        # 计算向量模长
        norm1 = math.sqrt(sum(value * value for value in vec1.values()))
        norm2 = math.sqrt(sum(value * value for value in vec2.values()))

        # 避免除以0
        if norm1 == 0 or norm2 == 0:
            return 0.0

        # 计算余弦相似度
        return dot_product / (norm1 * norm2)

    @staticmethod
    def find_similar_users(user_id: int, limit: int = 10) -> List[Tuple[int, float]]:
        """
        找到与指定用户相似的用户

        Args:
            user_id: 用户ID
            limit: 返回结果数量限制

        Returns:
            相似用户列表 [(user_id, similarity)]
        """
        # 获取当前用户的菜系偏好
        user_cuisine_preferences = RestaurantRecommender.get_user_cuisine_preferences(user_id)

        # 如果用户没有偏好，返回空列表
        if not user_cuisine_preferences:
            return []

        # 获取所有用户
        all_users = User.query.all()

        # 计算与每个用户的相似度
        similarities = []
        for other_user in all_users:
            # 跳过自己
            if other_user.user_id == user_id:
                continue

            # 获取其他用户的菜系偏好
            other_user_cuisine_preferences = RestaurantRecommender.get_user_cuisine_preferences(other_user.user_id)

            # 如果其他用户没有偏好，跳过
            if not other_user_cuisine_preferences:
                continue

            # 计算余弦相似度
            similarity = RestaurantRecommender.calculate_cosine_similarity(
                user_cuisine_preferences, other_user_cuisine_preferences
            )

            # 如果相似度大于0，添加到结果
            if similarity > 0:
                similarities.append((other_user.user_id, similarity))

        # 按相似度降序排序
        similarities.sort(key=lambda x: x[1], reverse=True)

        # 限制结果数量
        return similarities[:limit]

    @staticmethod
    def collaborative_filtering_recommend(user_id: int, limit: int = 10) -> List[Tuple[int, float]]:
        """
        基于协同过滤的餐馆推荐

        Args:
            user_id: 用户ID
            limit: 返回结果数量限制

        Returns:
            推荐的餐馆ID列表及其得分 [(restaurant_id, score)]
        """
        # 获取用户已收藏和评分的餐馆ID
        user_favorite_ids = set(RestaurantRecommender.get_user_favorites(user_id))
        user_rating_ids = set(RestaurantRecommender.get_user_ratings(user_id).keys())
        user_interacted_ids = user_favorite_ids | user_rating_ids

        # 找到相似用户
        similar_users = RestaurantRecommender.find_similar_users(user_id, limit=20)

        # 如果没有相似用户，返回空列表
        if not similar_users:
            return []

        # 计算每个餐馆的推荐得分
        restaurant_scores = defaultdict(float)

        # 对于每个相似用户
        for similar_user_id, similarity in similar_users:
            # 获取该用户收藏的餐馆
            similar_user_favorites = RestaurantRecommender.get_user_favorites(similar_user_id)

            # 获取该用户评分的餐馆
            similar_user_ratings = RestaurantRecommender.get_user_ratings(similar_user_id)

            # 对于该用户收藏的每个餐馆
            for restaurant_id in similar_user_favorites:
                # 跳过用户已交互的餐馆
                if restaurant_id in user_interacted_ids:
                    continue

                # 添加加权得分 (收藏权重为1.0)
                restaurant_scores[restaurant_id] += similarity * 1.0

            # 对于该用户评分的每个餐馆
            for restaurant_id, rating in similar_user_ratings.items():
                # 跳过用户已交互的餐馆
                if restaurant_id in user_interacted_ids:
                    continue

                # 添加加权得分 (评分权重为rating/5.0)
                restaurant_scores[restaurant_id] += similarity * (rating / 5.0)

        # 如果没有推荐结果，返回空列表
        if not restaurant_scores:
            return []

        # 使用自实现的堆排序获取得分最高的餐馆
        top_restaurants = RestaurantRecommender._custom_heap_top_k(
            list(restaurant_scores.items()), limit, key_func=lambda x: x[1], reverse=True
        )

        return top_restaurants

    @staticmethod
    def _custom_heap_top_k(items: List, k: int, key_func, reverse: bool = False) -> List:
        """
        自实现的堆排序Top-K算法
        不使用Python内置的heapq模块

        Args:
            items: 待排序的元素列表
            k: 需要的前K个元素
            key_func: 排序键函数
            reverse: 是否降序排序

        Returns:
            排序后的前K个元素
        """
        if not items or k <= 0:
            return []

        if k >= len(items):
            # 如果k大于等于总数，使用完整排序
            return RestaurantRecommender._quick_sort(items, key_func, reverse)

        # 构建最小堆（用于降序）或最大堆（用于升序）
        heap = []

        for item in items:
            key_value = key_func(item)

            if len(heap) < k:
                # 堆未满，直接添加
                heap.append((key_value, item))
                RestaurantRecommender._heapify_up(heap, len(heap) - 1, reverse)
            else:
                # 堆已满，比较并可能替换堆顶
                if reverse:  # 降序，使用最小堆
                    if key_value > heap[0][0]:
                        heap[0] = (key_value, item)
                        RestaurantRecommender._heapify_down(heap, 0, reverse)
                else:  # 升序，使用最大堆
                    if key_value < heap[0][0]:
                        heap[0] = (key_value, item)
                        RestaurantRecommender._heapify_down(heap, 0, reverse)

        # 提取结果并排序
        result = [item for _, item in heap]
        return RestaurantRecommender._quick_sort(result, key_func, reverse)

    @staticmethod
    def _quick_sort(items: List, key_func, reverse: bool = False) -> List:
        """简单的快速排序实现"""
        if len(items) <= 1:
            return items.copy()

        pivot = items[len(items) // 2]
        pivot_key = key_func(pivot)

        if reverse:
            left = [x for x in items if key_func(x) > pivot_key]
            middle = [x for x in items if key_func(x) == pivot_key]
            right = [x for x in items if key_func(x) < pivot_key]
        else:
            left = [x for x in items if key_func(x) < pivot_key]
            middle = [x for x in items if key_func(x) == pivot_key]
            right = [x for x in items if key_func(x) > pivot_key]

        return RestaurantRecommender._quick_sort(left, key_func, reverse) + middle + RestaurantRecommender._quick_sort(right, key_func, reverse)

    @staticmethod
    def _heapify_up(heap: List, index: int, reverse: bool):
        """堆的上浮操作"""
        while index > 0:
            parent = (index - 1) // 2
            if reverse:  # 最小堆
                if heap[index][0] >= heap[parent][0]:
                    break
            else:  # 最大堆
                if heap[index][0] <= heap[parent][0]:
                    break

            heap[index], heap[parent] = heap[parent], heap[index]
            index = parent

    @staticmethod
    def _heapify_down(heap: List, index: int, reverse: bool):
        """堆的下沉操作"""
        size = len(heap)
        while True:
            target = index
            left = 2 * index + 1
            right = 2 * index + 2

            if reverse:  # 最小堆
                if left < size and heap[left][0] < heap[target][0]:
                    target = left
                if right < size and heap[right][0] < heap[target][0]:
                    target = right
            else:  # 最大堆
                if left < size and heap[left][0] > heap[target][0]:
                    target = left
                if right < size and heap[right][0] > heap[target][0]:
                    target = right

            if target == index:
                break

            heap[index], heap[target] = heap[target], heap[index]
            index = target
