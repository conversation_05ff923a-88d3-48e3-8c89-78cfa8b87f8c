<template>
  <div class="smart-travel-plan-generator">
    <div class="generator-form">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <!-- 旅游地点输入 -->
        <el-form-item label="旅游地点" prop="locationInput" required>
          <el-select
            v-model="form.locationInput"
            filterable
            remote
            reserve-keyword
            allow-create
            default-first-option
            placeholder="请输入地点名称，支持搜索数据库中的景点"
            :remote-method="searchLocations"
            :loading="searchLoading"
            style="width: 100%;"
            @change="handleLocationChange"
          >
            <el-option
              v-for="location in locationOptions"
              :key="location.location_id"
              :label="location.location_name"
              :value="location.location_name"
            >
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <div style="flex: 1;">
                  <div style="font-weight: 500; color: #333;">{{ location.location_name }}</div>
                  <div style="color: #8492a6; font-size: 12px; margin-top: 2px;">
                    <span>{{ getLocationTypeText(location.type) }}</span>
                    <span v-if="location.address" style="margin-left: 10px;">{{ location.address }}</span>
                  </div>
                </div>
              </div>
            </el-option>
          </el-select>
          <div class="form-tip">
            <div v-if="searchLoading" style="color: #409eff;">
              <i class="el-icon-loading"></i> 正在搜索景点数据库...
            </div>
            <div v-else-if="locationOptions.length > 0" style="color: #67c23a;">
              找到 {{ locationOptions.length }} 个相关景点，您也可以直接输入其他地点名称
            </div>
            <div v-else style="color: #909399;">
              支持从景点数据库搜索或直接输入任意地点名称
            </div>
          </div>
        </el-form-item>

        <!-- 旅游天数 -->
        <el-form-item label="旅游天数" prop="days" required>
          <el-input-number
            v-model="form.days"
            :min="1"
            :max="30"
            controls-position="right"
            style="width: 200px;"
          />
          <span style="margin-left: 10px; color: #666;">天</span>
        </el-form-item>

        <!-- 旅游偏好 -->
        <el-form-item label="旅游偏好" prop="preferences">
          <el-checkbox-group v-model="form.preferences">
            <el-checkbox label="自然风光">自然风光</el-checkbox>
            <el-checkbox label="历史文化">历史文化</el-checkbox>
            <el-checkbox label="美食体验">美食体验</el-checkbox>
            <el-checkbox label="休闲娱乐">休闲娱乐</el-checkbox>
            <el-checkbox label="购物">购物</el-checkbox>
            <el-checkbox label="摄影">摄影</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <!-- 生成按钮 -->
        <el-form-item>
          <el-button
            type="primary"
            @click="generatePlan"
            :loading="loading"
            size="large"
            style="width: 200px;"
          >
            <i class="el-icon-magic-stick"></i>
            {{ loading ? '生成中...' : '生成旅游计划' }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 生成结果 -->
    <div v-if="generatedPlan" class="result-section">
      <div class="result-header">
        <h3>🎯 生成的旅游计划</h3>
        <el-button @click="copyToClipboard" type="text">
          <i class="el-icon-document-copy"></i>
          复制计划
        </el-button>
      </div>

      <div class="plan-content">
        <div class="plan-title">
          <h2>{{ generatedPlan.title }}</h2>
          <div class="plan-meta">
            <span><i class="el-icon-calendar"></i> {{ form.days }}天行程</span>
            <span><i class="el-icon-location"></i> {{ form.locationInput }}</span>
          </div>
        </div>

        <div class="days-list">
          <div
            v-for="day in generatedPlan.days"
            :key="day.day"
            class="day-item"
          >
            <div class="day-header">
              <h3>第{{ day.day }}天</h3>
              <span class="location-count">{{ day.locations?.length || 0 }}个景点</span>
            </div>

            <div class="day-locations">
              <div
                v-for="location in day.locations"
                :key="location.location_id || location.name"
                class="location-item"
              >
                <div class="location-info">
                  <h4>{{ location.name }}</h4>
                  <p class="location-time">{{ location.time }}</p>
                  <p class="location-description">{{ location.description }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误信息 -->
    <div v-if="error" class="error-section">
      <el-alert :title="error" type="error" show-icon />
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'

export default {
  name: 'SmartTravelPlanGenerator',
  setup() {
    const formRef = ref(null)
    const loading = ref(false)
    const searchLoading = ref(false)
    const error = ref('')
    const generatedPlan = ref(null)
    const locationOptions = ref([])

    const form = reactive({
      locationInput: '',
      days: 3,
      preferences: ['自然风光', '历史文化']
    })

    const rules = {
      locationInput: [
        { required: true, message: '请输入旅游地点', trigger: 'blur' }
      ],
      days: [
        { required: true, message: '请输入旅游天数', trigger: 'blur' }
      ]
    }

    // 获取地点类型文本
    const getLocationTypeText = (type) => {
      const typeMap = {
        0: '教育',
        1: '文化',
        2: '自然',
        3: '娱乐'
      }
      return typeMap[type] || '未知'
    }

    // 搜索地点
    const searchLocations = async (query) => {
      if (!query || query.length < 2) {
        locationOptions.value = []
        return
      }

      searchLoading.value = true
      try {
        console.log('搜索地点:', query)
        const response = await axios.get(`http://localhost:5000/api/locations/fuzzy_search`, {
          params: { query, limit: 10 }
        })

        console.log('搜索响应:', response.data)

        if (response.data && response.data.code === 0) {
          // 转换数据格式，确保字段名正确
          locationOptions.value = (response.data.data || []).map(location => ({
            location_id: location.location_id,
            location_name: location.name || location.location_name,
            type: location.type,
            address: location.address,
            keyword: location.keyword
          }))
          console.log('处理后的地点选项:', locationOptions.value)
        } else {
          locationOptions.value = []
        }
      } catch (error) {
        console.error('搜索地点失败:', error)
        locationOptions.value = []
      } finally {
        searchLoading.value = false
      }
    }

    // 处理地点变化
    const handleLocationChange = (value) => {
      console.log('地点选择变化:', value)
      // 清空搜索结果，避免下次搜索时显示旧结果
      if (value) {
        locationOptions.value = []
      }
    }

    // 生成旅游计划
    const generatePlan = async () => {
      try {
        await formRef.value.validate()

        if (!form.locationInput || !form.locationInput.trim()) {
          ElMessage.error('请输入旅游地点')
          return
        }

        loading.value = true
        error.value = ''
        generatedPlan.value = null

        const requestData = {
          location_names: [form.locationInput.trim()],
          days: form.days,
          preferences: form.preferences.join(', ')
        }

        const response = await axios.post('http://localhost:5000/api/ai/generate_plan_by_name', requestData)

        if (response.data.code === 0) {
          generatedPlan.value = response.data.data.plan
          ElMessage.success('旅游计划生成成功！')
        } else {
          error.value = response.data.message || '生成失败'
          ElMessage.error(error.value)
        }
      } catch (error) {
        console.error('生成旅游计划失败:', error)
        error.value = '生成失败，请稍后重试'
        ElMessage.error('生成失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }

    // 复制到剪贴板
    const copyToClipboard = async () => {
      try {
        let planText = `${generatedPlan.value.title}\n\n`
        generatedPlan.value.days.forEach(day => {
          planText += `第${day.day}天：\n`
          day.locations.forEach(location => {
            planText += `- ${location.name} (${location.time})\n  ${location.description}\n`
          })
          planText += '\n'
        })

        await navigator.clipboard.writeText(planText)
        ElMessage.success('旅游计划已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
        ElMessage.error('复制失败')
      }
    }

    return {
      formRef,
      form,
      rules,
      loading,
      searchLoading,
      error,
      generatedPlan,
      locationOptions,
      getLocationTypeText,
      searchLocations,
      handleLocationChange,
      generatePlan,
      copyToClipboard
    }
  }
}
</script>

<style scoped>
.smart-travel-plan-generator {
  max-width: 1000px;
  margin: 0 auto;
}

.generator-form {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 15px;
  margin-bottom: 30px;
}

.form-tip {
  margin-top: 8px;
  font-size: 12px;
}

.result-section {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.result-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-header h3 {
  margin: 0;
  font-size: 1.4rem;
}

.plan-content {
  padding: 30px;
}

.plan-title h2 {
  color: #333;
  margin-bottom: 10px;
}

.plan-meta {
  display: flex;
  gap: 20px;
  color: #666;
  margin-bottom: 30px;
}

.plan-meta span {
  display: flex;
  align-items: center;
  gap: 5px;
}

.day-item {
  margin-bottom: 30px;
  border: 1px solid #e9ecef;
  border-radius: 10px;
  overflow: hidden;
}

.day-header {
  background: #f8f9fa;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e9ecef;
}

.day-header h3 {
  margin: 0;
  color: #333;
}

.location-count {
  color: #666;
  font-size: 0.9rem;
}

.day-locations {
  padding: 20px;
}

.location-item {
  padding: 15px;
  border-left: 4px solid #667eea;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 15px;
}

.location-item:last-child {
  margin-bottom: 0;
}

.location-info h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1.1rem;
}

.location-time {
  color: #667eea;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.location-description {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.error-section {
  margin-top: 20px;
}

@media (max-width: 768px) {
  .plan-meta {
    flex-direction: column;
    gap: 10px;
  }

  .day-header {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
}
</style>
