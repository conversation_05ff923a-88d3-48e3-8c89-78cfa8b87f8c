#!/bin/bash

echo "========================================"
echo "鸿雁智游个性化旅游系统启动脚本"
echo "========================================"

# 检查MySQL服务
echo ""
echo "[1/4] 检查MySQL服务..."
if pgrep mysql > /dev/null || pgrep mysqld > /dev/null; then
    echo "✓ MySQL服务已运行"
else
    echo "启动MySQL服务..."
    # macOS
    if [[ "$OSTYPE" == "darwin"* ]]; then
        if command -v brew > /dev/null; then
            brew services start mysql
        else
            sudo /usr/local/mysql/support-files/mysql.server start
        fi
    # Linux
    else
        if command -v systemctl > /dev/null; then
            sudo systemctl start mysql
        else
            sudo service mysql start
        fi
    fi
    
    sleep 3
    if pgrep mysql > /dev/null || pgrep mysqld > /dev/null; then
        echo "✓ MySQL服务启动成功"
    else
        echo "✗ MySQL服务启动失败，请手动启动"
        exit 1
    fi
fi

# 检查虚拟环境
echo ""
echo "[2/4] 启动后端服务..."
if [ ! -d "backend/venv" ]; then
    echo "✗ Python虚拟环境不存在"
    echo "请先运行: cd backend && python -m venv venv && source venv/bin/activate && pip install -r requirements.txt"
    exit 1
fi

cd backend
echo "激活Python虚拟环境..."
source venv/bin/activate
echo "启动Flask后端服务..."
python app.py &
BACKEND_PID=$!
echo "✓ 后端服务已启动 (PID: $BACKEND_PID)"
cd ..

# 检查Node.js依赖
echo ""
echo "[3/4] 启动代理服务器..."
if [ ! -d "node_modules" ]; then
    echo "✗ Node.js依赖未安装，请先运行: npm install"
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

echo "启动Node.js代理服务器..."
node proxy-server.js &
PROXY_PID=$!
echo "✓ 代理服务器已启动 (PID: $PROXY_PID)"

# 检查前端依赖
echo ""
echo "[4/4] 启动前端服务..."
if [ ! -d "frontend_logged/travel_system_logged/node_modules" ]; then
    echo "✗ 前端依赖未安装"
    echo "请先在frontend_logged/travel_system_logged目录运行: npm install"
    kill $BACKEND_PID $PROXY_PID 2>/dev/null
    exit 1
fi

cd frontend_logged/travel_system_logged
echo "启动Vue.js前端服务..."
npm run serve &
FRONTEND_PID=$!
echo "✓ 前端服务已启动 (PID: $FRONTEND_PID)"
cd ../..

echo ""
echo "========================================"
echo "系统启动完成！"
echo "========================================"
echo "后端API服务: http://localhost:5000"
echo "代理服务器:   http://localhost:3000"
echo "Web前端:     http://localhost:8080"
echo "========================================"
echo ""
echo "进程ID记录:"
echo "后端: $BACKEND_PID"
echo "代理: $PROXY_PID" 
echo "前端: $FRONTEND_PID"
echo ""

# 保存PID到文件
echo $BACKEND_PID > .backend.pid
echo $PROXY_PID > .proxy.pid
echo $FRONTEND_PID > .frontend.pid

echo "要停止所有服务，请运行: ./stop_system.sh"
echo "或按 Ctrl+C 停止此脚本"
echo ""

# 等待用户中断
trap 'echo ""; echo "正在停止所有服务..."; kill $BACKEND_PID $PROXY_PID $FRONTEND_PID 2>/dev/null; rm -f .*.pid; echo "所有服务已停止"; exit 0' INT

echo "系统正在运行中... (按 Ctrl+C 停止)"
wait
