#!/usr/bin/env python3
"""
Flask-SQLAlchemy数据库连接测试
"""

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
import sys
import os

def test_flask_sqlalchemy():
    """测试Flask-SQLAlchemy连接"""
    print("=" * 50)
    print("🔧 Flask-SQLAlchemy连接测试")
    print("=" * 50)
    
    # 创建Flask应用
    app = Flask(__name__)
    
    # 测试不同的连接字符串
    connection_strings = [
        'mysql+pymysql://root:AN20050225@localhost/study_tour_system',
        'mysql+pymysql://root:AN20050225@localhost/study_tour_system?charset=utf8mb4',
        'mysql+pymysql://root:AN20050225@127.0.0.1/study_tour_system',
    ]
    
    for i, conn_str in enumerate(connection_strings, 1):
        print(f"\n{i}️⃣ 测试连接字符串: {conn_str}")
        print("-" * 50)
        
        try:
            # 配置数据库
            app.config['SQLALCHEMY_DATABASE_URI'] = conn_str
            app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
            
            # 创建SQLAlchemy实例
            db = SQLAlchemy()
            db.init_app(app)
            
            # 测试连接
            with app.app_context():
                # 尝试执行一个简单的查询 - 使用新版SQLAlchemy语法
                with db.engine.connect() as connection:
                    result = connection.execute(db.text("SELECT VERSION()"))
                    version = result.fetchone()[0]
                    print(f"✅ 连接成功! MySQL版本: {version}")

                    # 测试数据库操作
                    result = connection.execute(db.text("SELECT DATABASE()"))
                    current_db = result.fetchone()[0]
                    print(f"✅ 当前数据库: {current_db}")

                    # 测试表查询
                    result = connection.execute(db.text("SHOW TABLES"))
                    tables = result.fetchall()
                    print(f"✅ 数据库中的表数量: {len(tables)}")
                    if tables:
                        print("   表列表:")
                        for table in tables[:5]:  # 只显示前5个表
                            print(f"     - {table[0]}")
                        if len(tables) > 5:
                            print(f"     ... 还有 {len(tables) - 5} 个表")
                
                print(f"🎉 连接字符串 {i} 测试成功!")
                return conn_str
                
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            continue
    
    print("\n❌ 所有连接字符串都失败了")
    return None

def test_create_tables():
    """测试创建表"""
    print("\n" + "=" * 50)
    print("🔧 测试创建表")
    print("=" * 50)
    
    try:
        # 导入配置
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from config import Config
        
        app = Flask(__name__)
        app.config.from_object(Config)
        
        db = SQLAlchemy()
        db.init_app(app)
        
        with app.app_context():
            print("🔍 尝试创建所有表...")
            db.create_all()
            print("✅ 表创建成功!")
            
    except Exception as e:
        print(f"❌ 创建表失败: {e}")
        print(f"   错误类型: {type(e).__name__}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 测试连接
    success_conn = test_flask_sqlalchemy()
    
    if success_conn:
        print(f"\n✅ 推荐使用的连接字符串: {success_conn}")
        
        # 测试创建表
        test_create_tables()
    else:
        print("\n❌ 无法建立Flask-SQLAlchemy连接")
