# 高级推荐模块 API 文档

本文档详细描述了 Flask 版本后端的高级推荐模块 API 接口及其在 Postman 中的测试用例。高级推荐模块使用高效的查找和排序算法，相比于基于数据库查询的推荐模块具有更高的性能和灵活性。

> **重要更新**：
> 1. 所有推荐模块的路由已经重构为使用基于数据结构和算法的高效实现。原有的 API 接口路径保持不变，以确保向后兼容性。

## 目录

1. [刷新缓存](#刷新缓存)
2. [基于高效算法的内容推荐](#基于高效算法的内容推荐)
3. [基于高效算法的协同过滤推荐](#基于高效算法的协同过滤推荐)
4. [基于热度的推荐](#基于热度的推荐)
5. [基于评分的推荐](#基于评分的推荐)
6. [基于高效算法的混合推荐](#基于高效算法的混合推荐)
7. [获取相似用户](#获取相似用户)
8. [性能比较](#性能比较)
9. [高级推荐算法说明](#高级推荐算法说明)

## 刷新缓存

### 接口信息

- **URL**: `/api/advanced-recommend/refresh-cache`
- **方法**: `POST`
- **描述**: 刷新推荐系统的内存缓存，提高后续请求的响应速度
- **请求体**: JSON

### 请求参数

| 参数名 | 类型    | 必填 | 描述                           |
| ------ | ------- | ---- | ------------------------------ |
| force  | boolean | 否   | 是否强制刷新缓存，默认为 false |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Cache refreshed successfully",
    "data": {
        "message": "Cache refreshed successfully",
        "elapsed_time": "0.25 seconds"
    }
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `POST`
4. 输入 URL: `http://localhost:5000/api/advanced-recommend/refresh-cache`
5. 选择 Body 选项卡，选择 raw，设置类型为 JSON
6. 输入请求体:

```json
{
    "force": true
}
```

7. 点击 Send 按钮发送请求

## 基于高效算法的协同过滤推荐

### 接口信息

- **URL**: `/api/advanced-recommend/collaborative`
- **方法**: `POST`
- **描述**: 使用高效算法实现的协同过滤推荐，比数据库查询方式更快
- **请求体**: JSON

### 请求参数

| 参数名  | 类型    | 必填 | 描述                        |
| ------- | ------- | ---- | --------------------------- |
| user_id | integer | 是   | 用户 ID                     |
| limit   | integer | 否   | 返回结果数量限制，默认为 10 |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Collaborative recommendations retrieved successfully",
    "data": {
        "recommendations": [
            {
                "location_id": 3,
                "name": "浙江大学",
                "type": 0,
                "keyword": "综合",
                "popularity": 34,
                "evaluation": 28,
                "collaborative_score": 0.85
            },
            {
                "location_id": 7,
                "name": "中国科学技术大学",
                "type": 0,
                "keyword": "理工",
                "popularity": 12,
                "evaluation": 46,
                "collaborative_score": 0.72
            }
        ],
        "elapsed_time": "0.05 seconds"
    }
}
```

#### 错误响应

- **用户不存在** (404 Not Found)

```json
{
    "code": 1,
    "message": "User not found",
    "data": null
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `POST`
4. 输入 URL: `http://localhost:5000/api/advanced-recommend/collaborative`
5. 选择 Body 选项卡，选择 raw，设置类型为 JSON
6. 输入请求体:

```json
{
    "user_id": 1,
    "limit": 5
}
```

7. 点击 Send 按钮发送请求

## 基于高效算法的内容推荐

### 接口信息

- **URL**: `/api/advanced-recommend/content`
- **方法**: `POST`
- **描述**: 使用高效算法实现的内容推荐，比数据库查询方式更快
- **请求体**: JSON

### 请求参数

| 参数名  | 类型    | 必填 | 描述                        |
| ------- | ------- | ---- | --------------------------- |
| user_id | integer | 是   | 用户 ID                     |
| limit   | integer | 否   | 返回结果数量限制，默认为 10 |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Content-based recommendations retrieved successfully",
    "data": {
        "recommendations": [
            {
                "location_id": 13,
                "name": "哈尔滨工业大学",
                "type": 0,
                "keyword": "理工",
                "popularity": 12,
                "evaluation": 33,
                "content_score": 0.85
            },
            {
                "location_id": 16,
                "name": "北京理工大学",
                "type": 0,
                "keyword": "理工",
                "popularity": 88,
                "evaluation": 66,
                "content_score": 0.78
            }
        ],
        "elapsed_time": "0.03 seconds"
    }
}
```

#### 错误响应

- **用户不存在** (404 Not Found)

```json
{
    "code": 1,
    "message": "User not found",
    "data": null
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `POST`
4. 输入 URL: `http://localhost:5000/api/advanced-recommend/content`
5. 选择 Body 选项卡，选择 raw，设置类型为 JSON
6. 输入请求体:

```json
{
    "user_id": 1,
    "limit": 5
}
```

7. 点击 Send 按钮发送请求

## 基于热度的推荐

### 接口信息

- **URL**: `/api/advanced-recommend/popularity`
- **方法**: `GET`
- **描述**: 使用高效算法获取热门地点推荐
- **请求参数**: 查询字符串

### 请求参数

| 参数名 | 类型    | 必填 | 描述                               |
| ------ | ------- | ---- | ---------------------------------- |
| limit  | integer | 否   | 返回结果数量限制，默认为 10        |
| type   | integer | 否   | 地点类型筛选，不提供则返回所有类型 |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Popular locations retrieved successfully",
    "data": {
        "recommendations": [
            {
                "location_id": 1,
                "name": "北京天安门",
                "type": 1,
                "keyword": "文化,历史,广场",
                "popularity": 250,
                "evaluation": 4.7,
                "popularity_score": 250
            },
            {
                "location_id": 3,
                "name": "故宫博物院",
                "type": 1,
                "keyword": "文化,历史,博物馆",
                "popularity": 200,
                "evaluation": 4.9,
                "popularity_score": 200
            }
        ],
        "elapsed_time": "0.01 seconds"
    }
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `GET`
4. 输入 URL: `http://localhost:5000/api/advanced-recommend/popularity?limit=5&type=1`
5. 点击 Send 按钮发送请求

## 基于评分的推荐

### 接口信息

- **URL**: `/api/advanced-recommend/rating`
- **方法**: `GET`
- **描述**: 使用高效算法获取评分最高的地点推荐
- **请求参数**: 查询字符串

### 请求参数

| 参数名 | 类型    | 必填 | 描述                               |
| ------ | ------- | ---- | ---------------------------------- |
| limit  | integer | 否   | 返回结果数量限制，默认为 10        |
| type   | integer | 否   | 地点类型筛选，不提供则返回所有类型 |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Top rated locations retrieved successfully",
    "data": {
        "recommendations": [
            {
                "location_id": 3,
                "name": "故宫博物院",
                "type": 1,
                "keyword": "文化,历史,博物馆",
                "popularity": 200,
                "evaluation": 4.9,
                "rating_score": 4.9
            },
            {
                "location_id": 8,
                "name": "颐和园",
                "type": 2,
                "keyword": "文化,历史,公园,湖泊",
                "popularity": 150,
                "evaluation": 4.8,
                "rating_score": 4.8
            }
        ],
        "elapsed_time": "0.01 seconds"
    }
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `GET`
4. 输入 URL: `http://localhost:5000/api/advanced-recommend/rating?limit=5&type=1`
5. 点击 Send 按钮发送请求

## 基于高效算法的混合推荐

### 接口信息

- **URL**: `/api/advanced-recommend/hybrid`
- **方法**: `POST`
- **描述**: 使用高效算法实现的混合推荐，结合内容推荐、协同过滤和热度推荐
- **请求体**: JSON

### 请求参数

| 参数名  | 类型    | 必填 | 描述                           |
| ------- | ------- | ---- | ------------------------------ |
| user_id | integer | 是   | 用户 ID                        |
| limit   | integer | 否   | 返回结果数量限制，默认为 10    |
| weights | object  | 否   | 各推荐算法的权重，默认均为 1.0 |

### 权重对象

| 参数名        | 类型  | 必填 | 描述                         |
| ------------- | ----- | ---- | ---------------------------- |
| content       | float | 否   | 内容推荐权重，默认为 1.0     |
| collaborative | float | 否   | 协同过滤推荐权重，默认为 1.0 |
| popularity    | float | 否   | 热度推荐权重，默认为 0.5     |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Hybrid recommendations retrieved successfully",
    "data": {
        "recommendations": [
            {
                "location_id": 3,
                "name": "浙江大学",
                "type": 0,
                "keyword": "综合",
                "popularity": 34,
                "evaluation": 28,
                "score": 0.85
            },
            {
                "location_id": 7,
                "name": "中国科学技术大学",
                "type": 0,
                "keyword": "理工",
                "popularity": 12,
                "evaluation": 46,
                "score": 0.78
            }
        ],
        "elapsed_time": "0.08 seconds"
    }
}
```

#### 错误响应

- **用户不存在** (404 Not Found)

```json
{
    "code": 1,
    "message": "User not found",
    "data": null
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `POST`
4. 输入 URL: `http://localhost:5000/api/advanced-recommend/hybrid`
5. 选择 Body 选项卡，选择 raw，设置类型为 JSON
6. 输入请求体:

```json
{
    "user_id": 1,
    "limit": 5,
    "weights": {
        "content": 1.0,
        "collaborative": 1.0,
        "popularity": 0.5
    }
}
```

7. 点击 Send 按钮发送请求

## 获取相似用户

### 接口信息

- **URL**: `/api/advanced-recommend/similar_users`
- **方法**: `POST`
- **描述**: 使用高效算法查找与指定用户相似的用户
- **请求体**: JSON

### 请求参数

| 参数名  | 类型    | 必填 | 描述                        |
| ------- | ------- | ---- | --------------------------- |
| user_id | integer | 是   | 用户 ID                     |
| limit   | integer | 否   | 返回结果数量限制，默认为 10 |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Similar users retrieved successfully",
    "data": {
        "similar_users": [
            {
                "user_id": 3,
                "username": "user_3",
                "similarity": 0.85
            },
            {
                "user_id": 7,
                "username": "user_7",
                "similarity": 0.72
            }
        ],
        "elapsed_time": "0.04 seconds"
    }
}
```

#### 错误响应

- **用户不存在** (404 Not Found)

```json
{
    "code": 1,
    "message": "User not found",
    "data": null
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `POST`
4. 输入 URL: `http://localhost:5000/api/advanced-recommend/similar_users`
5. 选择 Body 选项卡，选择 raw，设置类型为 JSON
6. 输入请求体:

```json
{
    "user_id": 1,
    "limit": 5
}
```

7. 点击 Send 按钮发送请求

## 性能比较

### 接口信息

- **URL**: `/api/advanced-recommend/performance-comparison`
- **方法**: `POST`
- **描述**: 比较数据库查询方式和高效算法方式的性能差异
- **请求体**: JSON

### 请求参数

| 参数名  | 类型    | 必填 | 描述    |
| ------- | ------- | ---- | ------- |
| user_id | integer | 是   | 用户 ID |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Performance comparison completed",
    "data": {
        "database_based": {
            "elapsed_time": "0.25 seconds",
            "recommendation_count": 10
        },
        "algorithm_based": {
            "elapsed_time": "0.05 seconds",
            "recommendation_count": 10
        },
        "speedup": "5.00x"
    }
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `POST`
4. 输入 URL: `http://localhost:5000/api/advanced-recommend/performance-comparison`
5. 选择 Body 选项卡，选择 raw，设置类型为 JSON
6. 输入请求体:

```json
{
    "user_id": 1
}
```

7. 点击 Send 按钮发送请求

## 高级推荐算法说明

高级推荐模块使用了以下高效算法和数据结构：

### 1. 数据结构优化

- **哈希表**：用于O(1)时间复杂度的查找操作，快速访问用户和地点数据
- **堆数据结构**：用于O(n log k)时间复杂度获取Top-k结果，比全排序O(n log n)更高效
- **内存缓存**：减少重复计算和数据库查询，提高响应速度
- **增量更新机制**：支持单个景点热度或评分变化时的高效更新，避免全量重排序

### 2. 用户相似度计算

使用余弦相似度计算用户之间的相似度：

```
similarity = (A·B) / (||A|| × ||B||)
```

其中，A和B是两个用户的浏览历史向量，考虑了浏览次数等强度信息。

余弦相似度相比于之前使用的Jaccard相似度有以下优势：
- 考虑浏览次数等强度信息，而不仅仅是是否浏览
- 能够捕捉用户偏好的方向性模式
- 在高维稀疏数据中表现更好

### 3. 地点相似度计算

地点相似度基于以下因素计算：

- **类型匹配**：如果两个地点类型相同，贡献30%的相似度
- **关键词匹配**：使用Jaccard相似度计算关键词匹配度，贡献70%的相似度

### 4. 协同过滤算法

1. 使用余弦相似度找到与目标用户相似的用户
2. 对于每个相似用户浏览过但目标用户未浏览过的地点，计算加权分数（考虑相似度和浏览次数）
3. 使用堆排序获取分数最高的N个地点

协同过滤算法的优化：
- 使用余弦相似度代替Jaccard相似度，能够更好地考虑用户浏览次数等强度信息
- 相似用户的浏览次数作为权重因子，浏览次数越多权重越高
- 使用缓存机制避免重复计算相似度

### 5. 基于内容的过滤算法

1. 分析用户浏览历史中的地点特征（类型和关键词）
2. 找到具有相似特征但用户未浏览过的地点
3. 计算每个候选地点的相似度分数
4. 使用堆排序获取分数最高的N个地点

### 6. 混合推荐算法

1. 获取协同过滤和基于内容的推荐结果
2. 归一化各个推荐方法的分数
3. 根据指定的权重计算加权分数
4. 使用堆排序获取分数最高的N个地点

## 完整的 Postman 测试流程

以下是一个完整的测试流程，按照顺序执行可以测试高级推荐模块的所有功能：

### 1. 刷新缓存

```
POST http://localhost:5000/api/advanced-recommend/refresh-cache
Content-Type: application/json

{
    "force": true
}
```

### 2. 基于热度的推荐

```
GET http://localhost:5000/api/advanced-recommend/popularity?limit=5&type=1
```

### 3. 基于评分的推荐

```
GET http://localhost:5000/api/advanced-recommend/rating?limit=5&type=1
```

### 4. 基于高效算法的协同过滤推荐

```
POST http://localhost:5000/api/advanced-recommend/collaborative
Content-Type: application/json

{
    "user_id": 1,
    "limit": 5
}
```

### 5. 基于高效算法的内容推荐

```
POST http://localhost:5000/api/advanced-recommend/content
Content-Type: application/json

{
    "user_id": 1,
    "limit": 5
}
```

### 6. 基于高效算法的混合推荐

```
POST http://localhost:5000/api/advanced-recommend/hybrid
Content-Type: application/json

{
    "user_id": 1,
    "limit": 5,
    "weights": {
        "content": 1.0,
        "collaborative": 1.0,
        "popularity": 0.5
    }
}
```

### 7. 获取相似用户

```
POST http://localhost:5000/api/advanced-recommend/similar_users
Content-Type: application/json

{
    "user_id": 1,
    "limit": 5
}
```

### 8. 性能比较

```
POST http://localhost:5000/api/advanced-recommend/performance-comparison
Content-Type: application/json

{
    "user_id": 1
}
```

## 与数据库查询方法的比较

高级推荐系统相比于纯数据库查询方法有以下优势：

1. **更高的性能**：
   - 通常比数据库查询快5-10倍，可以通过 `/api/advanced-recommend/performance-comparison` 接口进行性能比较
   - 使用堆数据结构的优化算法将获取Top-K景点的时间复杂度从O(n log n)降低到O(n log k)
   - 增量更新机制避免了全量重排序，大幅提高了数据更新效率
   - 对于约200个景点的系统，响应时间通常在10ms以内

2. **更灵活的算法**：可以实现更复杂的推荐逻辑，如基于内容的相似度计算和混合推荐

3. **更低的数据库负载**：
   - 减轻数据库服务器的压力，所有数据都缓存在内存中
   - 只在缓存过期或强制刷新时才查询数据库

4. **更好的可扩展性**：
   - 可以轻松添加新的推荐方法，不需要修改数据库结构
   - 优化算法可以适应景点数量的增长，性能下降缓慢

5. **更详细的性能指标**：提供每个请求的响应时间，方便性能优化

## 注意事项

1. 在应用启动时调用 `/api/advanced-recommend/refresh-cache` 端点预热缓存，提高后续请求的响应速度
2. 对于实时推荐，使用 `/api/advanced-recommend/hybrid` 端点获取综合推荐结果
3. 根据应用场景调整混合推荐的权重，优化推荐效果
4. 定期刷新缓存以反映数据库的最新变化
5. 对于大规模应用，考虑使用分布式缓存（如Redis）进一步提高性能
6. 所有基础推荐模块的路由 (`/api/recommend/*`) 现在都重定向到高级推荐模块，但为了代码清晰度，建议直接使用高级推荐模块的路由
7. 使用 `/api/advanced-recommend/performance-comparison` 接口可以比较基础推荐和高级推荐的性能差异
8. 高级推荐模块使用工厂模式创建单例实例，确保内存使用效率
9. **优化算法**：系统使用堆数据结构和增量更新机制，为景点数量约200个的系统提供了高效的推荐功能
10. **热度和评分变化**：当景点热度或评分发生变化时，系统会自动更新排序列表，无需全量重排序
11. **内存效率**：系统只缓存必要的数据和预排序结果，对于中小规模应用（<1000个景点）内存占用可控