"""
Path planning service
"""
from typing import List, Dict, Any, Tuple
import heapq
import math
from models.path_planning import Vertex, Edge

class PathPlanningService:
    """Path planning service class"""

    def __init__(self):
        """Initialize the service"""
        self.graph = None
        self.build_graph()

    def build_graph(self):
        """Build the graph from the database"""
        self.graph = {}

        # Get all vertices
        vertices = Vertex.query.all()

        # Initialize graph with empty adjacency lists
        for vertex in vertices:
            self.graph[vertex.vertex_id] = []

        # Get all edges and populate adjacency lists
        edges = Edge.query.all()

        # 打印边的数量，用于调试
        print(f"Total edges in database: {len(edges)}")

        for edge in edges:
            # 确保 src_id 和 dest_id 存在于图中
            if edge.src_id not in self.graph:
                print(f"Warning: Edge source {edge.src_id} not found in vertices. Adding it.")
                self.graph[edge.src_id] = []

            if edge.dest_id not in self.graph:
                print(f"Warning: Edge destination {edge.dest_id} not found in vertices. Adding it.")
                self.graph[edge.dest_id] = []

            # 添加边
            self.graph[edge.src_id].append({
                'dest_id': edge.dest_id,
                'weight': edge.weight,
                'crowding': edge.crowding if edge.crowding is not None else 1.0,
                'is_rideable': edge.is_rideable if edge.is_rideable is not None else False,
                'edge_id': edge.edge_id
            })

            # 如果边是双向的，也添加反向边
            # 注意：这是一个假设，如果边本来就是有向的，这可能会导致问题
            self.graph[edge.dest_id].append({
                'dest_id': edge.src_id,
                'weight': edge.weight,
                'crowding': edge.crowding if edge.crowding is not None else 1.0,
                'is_rideable': edge.is_rideable if edge.is_rideable is not None else False,
                'edge_id': edge.edge_id
            })

    def dijkstra(self, start_id: int, end_id: int, strategy: int = 0) -> Dict[str, Any]:
        """
        Dijkstra's algorithm for finding shortest path

        Args:
            start_id: Starting vertex ID
            end_id: Destination vertex ID
            strategy: 0 for shortest distance, 1 for shortest time, 2 for rideable

        Returns:
            Dictionary with distance and path
        """
        # 如果起点和终点相同，直接返回
        if start_id == end_id:
            return {
                'distance': 0,
                'path': [start_id]
            }

        # 确保图已经构建
        if not self.graph:
            self.build_graph()

        # 打印图的大小，用于调试
        print(f"Graph size: {len(self.graph)} vertices")
        for vertex_id, edges in self.graph.items():
            print(f"Vertex {vertex_id} has {len(edges)} edges")
            if len(edges) > 0:
                print(f"  First edge: {edges[0]}")

        # 检查起点和终点是否在图中
        if start_id not in self.graph:
            print(f"Error: Start vertex {start_id} not in graph")
            return {'error': f'Invalid start vertex ID: {start_id}'}

        if end_id not in self.graph:
            print(f"Error: End vertex {end_id} not in graph")
            return {'error': f'Invalid end vertex ID: {end_id}'}

        # Initialize distances with infinity
        distances = {vertex_id: float('infinity') for vertex_id in self.graph}
        distances[start_id] = 0

        # Initialize previous vertices for path reconstruction
        previous = {vertex_id: None for vertex_id in self.graph}

        # Priority queue for Dijkstra's algorithm
        priority_queue = [(0, start_id)]

        # 用于调试的集合，记录已经访问过的顶点
        visited = set()

        while priority_queue:
            current_distance, current_vertex = heapq.heappop(priority_queue)

            # 记录已访问的顶点
            visited.add(current_vertex)

            # If we reached the destination, we're done
            if current_vertex == end_id:
                print(f"Found path from {start_id} to {end_id} with distance {current_distance}")
                break

            # If we've already found a better path, skip
            if current_distance > distances[current_vertex]:
                continue

            # Check all neighbors
            for edge in self.graph[current_vertex]:
                neighbor = edge['dest_id']

                # Calculate weight based on strategy (using double precision)
                if strategy == 0:  # Shortest distance
                    weight = float(edge['weight'])
                elif strategy == 1:  # Shortest time
                    # 确保 crowding 不为 None 且不为 0
                    crowding = edge['crowding'] if edge['crowding'] is not None and edge['crowding'] > 0 else 1.0
                    # 最短时间策略：时间 = 距离 / (步行速度 * 拥挤度)
                    # 步行速度 = 4 km/h，拥挤度影响实际速度
                    weight = edge['weight'] / crowding  # 保持double精度
                elif strategy == 2:  # Rideable
                    # 确保 crowding 不为 None 且不为 0
                    crowding = edge['crowding'] if edge['crowding'] is not None and edge['crowding'] > 0 else 1.0
                    # 确保 is_rideable 不为 None
                    is_rideable = edge['is_rideable'] if edge['is_rideable'] is not None else False
                    if is_rideable:
                        # 可骑行路段：使用骑行时间权重，权重降低以优先选择
                        actual_speed = 12.0 * crowding  # 骑行速度 12 km/h
                        time_weight = edge['weight'] / (actual_speed * 1000.0 / 60.0)  # 转换为分钟
                        weight = time_weight / 3.0  # 可骑行路段权重降低
                    else:
                        # 不可骑行路段：使用步行时间权重
                        actual_speed = 4.0 * crowding  # 步行速度 4 km/h
                        time_weight = edge['weight'] / (actual_speed * 1000.0 / 60.0)  # 转换为分钟
                        weight = time_weight  # 正常的时间权重
                elif strategy == 3:  # Smart travel (intelligent combination of cycling and walking)
                    # 确保 crowding 不为 None 且不为 0
                    crowding = edge['crowding'] if edge['crowding'] is not None and edge['crowding'] > 0 else 1.0
                    # 确保 is_rideable 不为 None
                    is_rideable = edge['is_rideable'] if edge['is_rideable'] is not None else False

                    # 智能出行策略：优先选择可骑行路段，但也考虑时间效率
                    if is_rideable:
                        # 可骑行路段：使用骑行时间权重
                        actual_speed = 12.0 * crowding  # 骑行速度 12 km/h
                        time_weight = edge['weight'] / (actual_speed * 1000.0 / 60.0)  # 转换为分钟
                        weight = time_weight / 2.0  # 可骑行路段权重更低（更优先）
                    else:
                        # 不可骑行路段：使用步行时间权重
                        actual_speed = 4.0 * crowding  # 步行速度 4 km/h
                        time_weight = edge['weight'] / (actual_speed * 1000.0 / 60.0)  # 转换为分钟
                        weight = time_weight  # 正常的时间权重
                elif strategy == 4:  # Cycling shortest distance
                    # 确保 is_rideable 不为 None
                    is_rideable = edge['is_rideable'] if edge['is_rideable'] is not None else False
                    if is_rideable:
                        # 可骑行路段使用距离权重
                        weight = float(edge['weight'])
                    else:
                        # 不可骑行路段权重大幅增加，强烈避免
                        weight = float(edge['weight']) * 10.0
                elif strategy == 5:  # Cycling shortest time (considering crowding)
                    # 确保 crowding 不为 None 且不为 0
                    crowding = edge['crowding'] if edge['crowding'] is not None and edge['crowding'] > 0 else 1.0
                    # 确保 is_rideable 不为 None
                    is_rideable = edge['is_rideable'] if edge['is_rideable'] is not None else False

                    if is_rideable:
                        # 可骑行路段：考虑拥挤度的骑行时间权重
                        # 骑行速度受拥挤度影响：速度 = 12 * crowding
                        actual_speed = 12.0 * crowding  # km/h
                        ride_time_weight = edge['weight'] / (actual_speed * 1000.0 / 60.0)  # 转换为分钟
                        weight = ride_time_weight  # 保持double精度
                    else:
                        # 不可骑行路段：考虑拥挤度的步行时间权重，并增加惩罚
                        actual_speed = 4.0 * crowding  # km/h
                        walk_time_weight = edge['weight'] / (actual_speed * 1000.0 / 60.0)  # 转换为分钟
                        weight = walk_time_weight * 2.0  # 步行权重加倍作为惩罚
                else:
                    weight = float(edge['weight'])

                # Calculate distance to neighbor through current vertex
                distance = current_distance + weight

                # If we found a better path, update
                if distance < distances[neighbor]:
                    distances[neighbor] = distance
                    previous[neighbor] = current_vertex
                    heapq.heappush(priority_queue, (distance, neighbor))

        # 打印已访问的顶点数量，用于调试
        print(f"Visited {len(visited)} vertices out of {len(self.graph)}")

        # 打印终点的距离，用于调试
        print(f"Distance to end vertex {end_id}: {distances[end_id]}")

        # Reconstruct path
        if distances[end_id] == float('infinity'):
            print(f"No path found from {start_id} to {end_id}")
            return {
                'distance': float('infinity'),
                'path': []
            }

        path = []
        current = end_id
        while current is not None:
            path.append(current)
            current = previous[current]

        # Reverse path to get start to end
        path.reverse()

        # 打印路径，用于调试
        print(f"Path from {start_id} to {end_id}: {path}")

        return {
            'distance': distances[end_id],
            'path': path
        }

    def get_shortest_path(self, start_id: int, end_id: int, strategy: int = 0) -> Dict[str, Any]:
        """
        Get shortest path between two vertices

        Args:
            start_id: Starting vertex ID
            end_id: Destination vertex ID
            strategy: 0 for shortest distance, 1 for shortest time, 2 for rideable, 3 for smart travel,
                     4 for cycling shortest distance, 5 for cycling shortest time

        Returns:
            Dictionary with path details
        """
        # 打印请求参数，用于调试
        print(f"get_shortest_path: start_id={start_id}, end_id={end_id}, strategy={strategy}")

        # 如果起点和终点相同，直接返回
        if start_id == end_id:
            vertex = Vertex.query.get(start_id)
            if not vertex:
                return {'error': f'Vertex with ID {start_id} not found'}

            return {
                'path': [start_id],
                'path_details': [],
                'total_distance': 0,
                'strategy': strategy
            }

        # 使用 Dijkstra 算法找出最短路径
        result = self.dijkstra(start_id, end_id, strategy)

        # 打印 Dijkstra 算法的结果，用于调试
        print(f"Dijkstra result: {result}")

        # 如果找不到路径，返回错误
        if 'error' in result:
            print(f"Error in Dijkstra: {result['error']}")
            return {'error': result['error']}

        if result['distance'] == float('infinity'):
            print(f"No path found from {start_id} to {end_id}")
            return {'error': 'No path found between the specified vertices'}

        path = result['path']
        path_details = []

        # 打印路径，用于调试
        print(f"Path: {path}")

        # 获取路径中的所有顶点
        try:
            vertices = {v.vertex_id: v for v in Vertex.query.filter(Vertex.vertex_id.in_(path)).all()}
            print(f"Found {len(vertices)} vertices out of {len(path)} in path")
        except Exception as e:
            print(f"Error querying vertices: {str(e)}")
            vertices = {}

        # Build path details
        for i in range(len(path) - 1):
            source_id = path[i]
            dest_id = path[i + 1]

            # 打印当前处理的边，用于调试
            print(f"Processing edge from {source_id} to {dest_id}")

            try:
                # Find the edge between these vertices
                edge = Edge.query.filter_by(src_id=source_id, dest_id=dest_id).first()

                # 如果找不到正向边，尝试查找反向边
                if not edge:
                    edge = Edge.query.filter_by(src_id=dest_id, dest_id=source_id).first()
                    print(f"Trying reverse edge from {dest_id} to {source_id}: {'Found' if edge else 'Not found'}")

                if edge:
                    # 获取顶点标签，如果顶点不在字典中，使用顶点ID
                    from_label = vertices[source_id].label if source_id in vertices else f"Vertex {source_id}"
                    to_label = vertices[dest_id].label if dest_id in vertices else f"Vertex {dest_id}"

                    path_details.append({
                        'from': from_label,
                        'to': to_label,
                        'distance': edge.weight,
                        'crowding': edge.crowding if edge.crowding is not None else 1.0,
                        'is_rideable': edge.is_rideable if edge.is_rideable is not None else False
                    })
                else:
                    # 如果找不到直接连接的边，创建一个默认的路径详情
                    print(f"Warning: No direct edge found from {source_id} to {dest_id}. Creating default path detail.")

                    # 获取顶点标签，如果顶点不在字典中，使用顶点ID
                    from_label = vertices[source_id].label if source_id in vertices else f"Vertex {source_id}"
                    to_label = vertices[dest_id].label if dest_id in vertices else f"Vertex {dest_id}"

                    # 使用 Dijkstra 算法计算的距离
                    if i == 0:
                        # 对于第一段路径，使用总距离减去后续路径的距离
                        remaining_distance = 0
                        for j in range(i + 1, len(path) - 1):
                            sub_result = self.dijkstra(path[j], path[j + 1], strategy)
                            if 'error' not in sub_result and sub_result['distance'] != float('infinity'):
                                remaining_distance += sub_result['distance']
                        distance = result['distance'] - remaining_distance
                    else:
                        # 对于其他路径段，直接计算两点之间的距离
                        sub_result = self.dijkstra(source_id, dest_id, strategy)
                        if 'error' not in sub_result and sub_result['distance'] != float('infinity'):
                            distance = sub_result['distance']
                        else:
                            distance = 100  # 使用一个合理的默认值

                    path_details.append({
                        'from': from_label,
                        'to': to_label,
                        'distance': distance,
                        'crowding': 1.0,  # 默认拥挤度
                        'is_rideable': True  # 默认可骑行
                    })
            except Exception as e:
                print(f"Error processing edge from {source_id} to {dest_id}: {str(e)}")
                # 创建一个默认的路径详情
                path_details.append({
                    'from': f"Vertex {source_id}",
                    'to': f"Vertex {dest_id}",
                    'distance': 100,  # 使用一个合理的默认值
                    'crowding': 1.0,  # 默认拥挤度
                    'is_rideable': True  # 默认可骑行
                })

        return {
            'path': path,
            'path_details': path_details,
            'total_distance': result['distance'],
            'strategy': strategy
        }

    def get_multi_destination_path(self, start_id: int, dest_ids: List[int], strategy: int = 0, algorithm: str = 'auto') -> Dict[str, Any]:
        """
        Get path through multiple destinations

        Args:
            start_id: Starting vertex ID
            dest_ids: List of destination vertex IDs
            strategy: 0 for shortest distance, 1 for shortest time, 2 for rideable, 3 for smart travel,
                     4 for cycling shortest distance, 5 for cycling shortest time
            algorithm: 'auto', 'held_karp', 'simulated_annealing', or 'simple'

        Returns:
            Dictionary with path details
        """
        if not dest_ids:
            return {'error': 'No destinations specified'}

        # If only one destination, use simple shortest path
        if len(dest_ids) == 1:
            return self.get_shortest_path(start_id, dest_ids[0], strategy)

        # For multiple destinations, use advanced algorithms
        # First, create a list of all vertices including start and destinations
        all_vertices = [start_id] + dest_ids
        n = len(all_vertices)

        # Create a distance matrix
        distance_matrix = [[0 for _ in range(n)] for _ in range(n)]
        for i in range(n):
            for j in range(n):
                if i != j:
                    result = self.dijkstra(all_vertices[i], all_vertices[j], strategy)
                    if 'error' in result:
                        # 如果找不到路径，使用一个非常大的值
                        distance_matrix[i][j] = float('inf')
                        print(f"Warning: Cannot find path from {all_vertices[i]} to {all_vertices[j]}")
                    else:
                        distance_matrix[i][j] = result['distance']

        # 检查是否有太多不可达的顶点
        unreachable_count = 0
        for i in range(n):
            for j in range(n):
                if i != j and distance_matrix[i][j] == float('inf'):
                    unreachable_count += 1

        # 根据算法参数和顶点数量选择算法
        if algorithm == 'held_karp' or (algorithm == 'auto' and n <= 10):
            # 使用 Held-Karp 算法
            from utils.held_karp import held_karp
            print(f"Using Held-Karp algorithm for {n} vertices")
            best_path_indices = held_karp(distance_matrix)
            algorithm_used = 'held_karp'
        elif algorithm == 'simulated_annealing' or (algorithm == 'auto' and n > 10):
            # 使用模拟退火算法
            from utils.simulated_annealing import simulated_annealing
            print(f"Using Simulated Annealing algorithm for {n} vertices")
            best_path_indices = simulated_annealing(distance_matrix)
            algorithm_used = 'simulated_annealing'
        else:
            # 使用简单的顺序路径
            print(f"Using simple path for {n} vertices with algorithm={algorithm}")
            best_path_indices = list(range(n))
            algorithm_used = 'simple'

        # 确保路径不是循环的，即不包含从最后一个顶点回到第一个顶点的路径
        if len(best_path_indices) > 0 and best_path_indices[0] == best_path_indices[-1]:
            best_path_indices = best_path_indices[:-1]

        # 如果算法返回的路径不可用，使用简单的顺序路径
        if not best_path_indices:
            print(f"Warning: Algorithm returned empty path. Using simple path.")
            best_path_indices = list(range(n))
            algorithm_used = 'simple'

        # 不再根据不可达顶点数量切换到简单路径，而是尽量使用算法

        # Convert indices to vertex IDs
        best_path = [all_vertices[i] for i in best_path_indices]

        # Ensure the path starts with the start_id
        if best_path[0] != start_id:
            # Find the index of start_id and rotate the path
            start_index = best_path.index(start_id)
            best_path = best_path[start_index:] + best_path[:start_index]

        # 构建完整路径，包含中间顶点
        complete_path = []
        total_distance = 0

        # 对于每对相邻的顶点，找出它们之间的最短路径
        for i in range(len(best_path) - 1):
            current = best_path[i]
            next_vertex = best_path[i + 1]

            # 使用 Dijkstra 算法找出两点之间的最短路径
            result = self.dijkstra(current, next_vertex, strategy)

            # 检查是否找到了路径
            if 'error' not in result and result['distance'] != float('infinity') and result['path']:
                # 找到了有效路径
                if i == 0:
                    # 对于第一段路径，包含起点
                    complete_path.extend(result['path'])
                else:
                    # 对于后续路径，跳过起点（因为它已经在路径中了）
                    complete_path.extend(result['path'][1:])

                # 累加距离
                total_distance += result['distance']
                print(f"Found path from {current} to {next_vertex} with distance {result['distance']}")
            else:
                # 如果找不到路径，直接添加两个顶点，并使用一个合理的默认距离
                print(f"Warning: Cannot find path from {current} to {next_vertex}. Using direct connection.")
                if i == 0:
                    complete_path.append(current)
                complete_path.append(next_vertex)

                # 使用欧几里得距离作为默认距离
                try:
                    # 获取顶点坐标
                    current_vertex = Vertex.query.get(current)
                    next_vertex_obj = Vertex.query.get(next_vertex)

                    if current_vertex and next_vertex_obj:
                        # 计算欧几里得距离
                        distance = int(((current_vertex.x - next_vertex_obj.x) ** 2 +
                                      (current_vertex.y - next_vertex_obj.y) ** 2) ** 0.5)
                    else:
                        distance = 100  # 默认距离
                except Exception as e:
                    print(f"Error calculating distance: {str(e)}")
                    distance = 100  # 默认距离

                total_distance += distance
                print(f"Using direct connection with distance {distance}")

        # Build path details
        path_details = []
        vertices = {v.vertex_id: v for v in Vertex.query.filter(Vertex.vertex_id.in_(complete_path)).all()}

        for i in range(len(complete_path) - 1):
            source_id = complete_path[i]
            dest_id = complete_path[i + 1]

            # 打印当前处理的边，用于调试
            print(f"Processing edge from {source_id} to {dest_id}")

            try:
                # 获取顶点标签，如果顶点不在字典中，使用顶点ID
                from_label = vertices[source_id].label if source_id in vertices else f"Vertex {source_id}"
                to_label = vertices[dest_id].label if dest_id in vertices else f"Vertex {dest_id}"

                # 查找边以获取距离、拥挤度和可骑行信息
                edge = Edge.query.filter_by(src_id=source_id, dest_id=dest_id).first()
                if not edge:
                    edge = Edge.query.filter_by(src_id=dest_id, dest_id=source_id).first()

                if edge:
                    # 使用边的权重作为距离
                    distance = edge.weight
                    crowding = edge.crowding if edge.crowding is not None else 1.0
                    is_rideable = edge.is_rideable if edge.is_rideable is not None else True
                else:
                    # 如果找不到边，使用欧几里得距离
                    current_vertex = Vertex.query.get(source_id)
                    next_vertex_obj = Vertex.query.get(dest_id)

                    if current_vertex and next_vertex_obj:
                        # 计算欧几里得距离
                        distance = int(((current_vertex.x - next_vertex_obj.x) ** 2 +
                                      (current_vertex.y - next_vertex_obj.y) ** 2) ** 0.5)
                    else:
                        distance = 100  # 默认距离

                    crowding = 1.0  # 默认拥挤度
                    is_rideable = True  # 默认可骑行

                path_details.append({
                    'from': from_label,
                    'to': to_label,
                    'distance': distance,
                    'crowding': crowding,
                    'is_rideable': is_rideable
                })
            except Exception as e:
                print(f"Error processing edge from {source_id} to {dest_id}: {str(e)}")
                # 创建一个默认的路径详情
                path_details.append({
                    'from': f"Vertex {source_id}",
                    'to': f"Vertex {dest_id}",
                    'distance': 100,  # 使用一个合理的默认值
                    'crowding': 1.0,  # 默认拥挤度
                    'is_rideable': True  # 默认可骑行
                })

        # 构建与 Java 版本一致的响应格式
        return {
            'path': complete_path,
            'path_details': path_details,
            'total_distance': total_distance,  # 使用累加的总距离
            'algorithm': algorithm_used,  # 使用实际使用的算法
            'strategy': strategy
        }