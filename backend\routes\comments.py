"""
评论路由模块

该模块提供文章评论的API接口，包括：
1. 添加评论
2. 获取评论
3. 删除评论
"""

from flask import Blueprint, request, jsonify
from models.article_comment import ArticleComment
from models.article import Article
from models.user import User
from utils.database import db
from utils.response import success, error
from datetime import datetime

comments_bp = Blueprint('comments', __name__)

@comments_bp.route('', methods=['POST'])
def add_comment():
    """添加评论"""
    try:
        data = request.get_json()

        if not data or not data.get('user_id') or not data.get('article_id') or not data.get('content'):
            return error('Missing required fields')

        user_id = data['user_id']
        article_id = data['article_id']
        content = data['content']

        # 检查用户是否存在
        user = User.query.get(user_id)
        if not user:
            return error('User not found')

        # 检查文章是否存在
        article = Article.query.get(article_id)
        if not article:
            return error('Article not found')

        # 创建评论
        comment = ArticleComment(
            article_id=article_id,
            user_id=user_id,
            content=content,
            created_at=datetime.utcnow()
        )

        db.session.add(comment)
        db.session.commit()

        return success({
            'comment_id': comment.comment_id,
            'username': user.username,
            'avatar': user.avatar,
            'content': comment.content,
            'created_at': comment.created_at.isoformat()
        }, 'Comment added successfully')

    except Exception as e:
        db.session.rollback()
        return error(f'Failed to add comment: {str(e)}')

@comments_bp.route('/article/<int:article_id>', methods=['GET'])
def get_article_comments(article_id):
    """获取文章的所有评论"""
    try:
        # 检查文章是否存在
        article = Article.query.get(article_id)
        if not article:
            return error('Article not found')

        # 获取评论
        comments = ArticleComment.query.filter_by(article_id=article_id).order_by(ArticleComment.created_at.desc()).all()

        # 格式化评论
        formatted_comments = []
        for comment in comments:
            user = User.query.get(comment.user_id)
            formatted_comments.append({
                'id': comment.comment_id,
                'username': user.username if user else 'Unknown User',
                'avatar': user.avatar if user else None,
                'content': comment.content,
                'createTime': comment.created_at.isoformat()
            })
        
        return success({'comments': formatted_comments}, 'Comments retrieved successfully')

    except Exception as e:
        return error(f'Failed to get comments: {str(e)}')

@comments_bp.route('/<int:comment_id>', methods=['DELETE'])
def delete_comment(comment_id):
    """删除评论"""
    try:
        # 检查评论是否存在
        comment = ArticleComment.query.get(comment_id)
        if not comment:
            return error('Comment not found')

        # 删除评论
        db.session.delete(comment)
        db.session.commit()

        return success({}, 'Comment deleted successfully')

    except Exception as e:
        db.session.rollback()
        return error(f'Failed to delete comment: {str(e)}')

@comments_bp.route('/count/<int:article_id>', methods=['GET'])
def get_comment_count(article_id):
    """获取文章的评论数量"""
    try:
        # 检查文章是否存在
        article = Article.query.get(article_id)
        if not article:
            return error('Article not found')

        # 获取评论数量
        count = ArticleComment.query.filter_by(article_id=article_id).count()

        return success({'count': count}, 'Comment count retrieved successfully')

    except Exception as e:
        return error(f'Failed to get comment count: {str(e)}')
