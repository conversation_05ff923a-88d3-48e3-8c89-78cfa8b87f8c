// route-plan.ts
import { request, planRoute, formatDistance, formatDuration } from '../../utils/util'
import { testApiConnection, detectAvailableApi } from '../../config/api'

interface Location {
  vertex_id: number
  label: string
  x: number  // 使用x, y坐标，与后端数据结构一致
  y: number
  type: number
}

interface Waypoint {
  id: number
  name: string
  x: number
  y: number
}

interface RouteResult {
  totalDistance: string
  cyclingDistance?: string
  walkingDistance?: string
  totalTime: string
  pathDetails: any[]
}

Component({
  data: {
    // 地图相关
    useWebView: false, // 暂时使用原生地图，避免web-view的限制
    mapUrl: '', // 高德地图HTML页面URL
    mapReady: true, // 地图是否已准备就绪
    mapCenter: {
      longitude: 116.3588,
      latitude: 39.9615
    },
    mapScale: 16,
    markers: [] as any[],
    polylines: [] as any[],

    // 地点数据
    locations: [] as Location[],
    locationNames: [] as string[],
    availableLocations: [] as Location[],

    // 选择的地点
    startLocationIndex: -1,
    endLocationIndex: -1,
    startLocationName: '',
    endLocationName: '',
    selectedStartId: null as number | null,
    selectedEndId: null as number | null,

    // 搜索相关
    startSearchText: '',
    endSearchText: '',
    filteredStartLocations: [] as Location[],
    filteredEndLocations: [] as Location[],
    showStartSuggestions: false,
    showEndSuggestions: false,

    // 途径点
    waypoints: [] as Waypoint[],
    showWaypointModal: false,
    waypointSearchText: '',
    filteredWaypointLocations: [] as Location[],

    // 策略选择
    selectedTransportMode: 'walking',
    selectedStrategy: 0,

    // 路线规划
    isLoading: false,
    routeResult: null as RouteResult | null,

    // 计算属性
    canPlan: false
  },

  pageLifetimes: {
    async show() {
      if (typeof this.getTabBar === 'function' && this.getTabBar()) {
        this.getTabBar().setData({
          selected: 2
        });
      }
      this.initMapUrl()

      // 先测试API连接
      await this.checkApiConnection()

      // 然后加载地点数据
      this.loadLocations()
    }
  },

  methods: {
    // 检查API连接（简化版，避免误报）
    async checkApiConnection() {
      try {
        console.log('开始检查API连接...')

        // 直接尝试加载数据，如果成功就说明连接正常
        // 不再显示加载提示，避免用户困惑
        const isConnected = await testApiConnection()

        if (isConnected) {
          console.log('API连接检查完成 - 连接正常')
          return true
        } else {
          console.log('API连接测试失败，但可能是测试函数问题，继续尝试加载数据')
          return true // 继续执行，让实际的数据加载来验证连接
        }
      } catch (error) {
        console.error('API连接检查异常:', error)
        return true // 即使检查失败也继续，让实际加载来验证
      }
    },

    // 初始化地图URL
    initMapUrl() {
      // 获取当前页面路径，构建HTML文件的绝对路径
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      const route = currentPage.route

      // 构建HTML文件URL（需要部署到服务器）
      // 在开发环境中，可以使用本地服务器
      const baseUrl = 'https://your-domain.com' // 替换为实际的服务器地址
      const mapUrl = `${baseUrl}/miniprogram/pages/route-plan/amap.html`

      this.setData({ mapUrl })
    },

    // 处理web-view消息
    onMapMessage(e: any) {
      const messages = e.detail.data
      if (messages && messages.length > 0) {
        const message = messages[messages.length - 1] // 获取最新消息
        this.handleMapMessage(message)
      }
    },

    // 处理地图消息
    handleMapMessage(message: any) {
      console.log('收到地图消息:', message)

      switch (message.type) {
        case 'mapReady':
          this.setData({ mapReady: true })
          this.syncMarkersToMap()
          break

        case 'mapClick':
          this.onMapClick(message.data)
          break

        case 'markerClick':
          this.onMarkerClick(message.data)
          break

        case 'routeResult':
          this.handleRouteResult(message.data)
          break
      }
    },

    // 向地图发送消息
    sendMessageToMap(message: any) {
      if (!this.data.mapReady) {
        console.warn('地图未准备就绪，无法发送消息')
        return
      }

      // 通过web-view的postMessage发送消息
      // 注意：这需要在HTML页面中监听message事件
      const webview = this.selectComponent('#map-webview')
      if (webview) {
        webview.postMessage(message)
      }
    },

    // 同步标记到地图
    syncMarkersToMap() {
      if (!this.data.mapReady) return

      const markers = this.data.locations.map(location => {
        const coordinates = this.calculateCoordinates(location.x, location.y)
        return {
          id: location.vertex_id,
          longitude: coordinates.lng,
          latitude: coordinates.lat,
          title: location.label,
          label: '●',
          bgColor: '#409EFF'
        }
      })

      this.sendMessageToMap({
        type: 'addMarkers',
        markers: markers
      })
    },

    // 返回上一页
    goBack() {
      wx.navigateBack()
    },

    // 更新web-view地图标记
    updateWebViewMarkers(type: string, location?: Location) {
      const markers = this.data.locations.map(loc => {
        const coordinates = this.calculateCoordinates(loc.x, loc.y)

        let markerInfo = {
          id: loc.vertex_id,
          longitude: coordinates.lng,
          latitude: coordinates.lat,
          title: loc.label,
          label: '●',
          bgColor: '#409EFF'
        }

        // 根据类型设置不同的标记样式
        if (location && loc.vertex_id === location.vertex_id) {
          switch (type) {
            case 'start':
              markerInfo.label = '起'
              markerInfo.bgColor = '#00C851'
              break
            case 'end':
              markerInfo.label = '终'
              markerInfo.bgColor = '#FF4444'
              break
            case 'waypoint':
              markerInfo.label = '途'
              markerInfo.bgColor = '#409EFF'
              break
          }
        }

        // 检查是否是起点
        if (this.data.selectedStartId === loc.vertex_id) {
          markerInfo.label = '起'
          markerInfo.bgColor = '#00C851'
        }

        // 检查是否是终点
        if (this.data.selectedEndId === loc.vertex_id) {
          markerInfo.label = '终'
          markerInfo.bgColor = '#FF4444'
        }

        // 检查是否是途径点
        const waypoint = this.data.waypoints.find(wp => wp.id === loc.vertex_id)
        if (waypoint) {
          markerInfo.label = '途'
          markerInfo.bgColor = '#409EFF'
        }

        return markerInfo
      })

      this.sendMessageToMap({
        type: 'addMarkers',
        markers: markers
      })
    },

    // 处理地图点击事件
    onMapClick(data: any) {
      console.log('地图点击:', data)
      // 可以在这里处理地图点击逻辑
    },

    // 处理标记点击事件
    onMarkerClick(data: any) {
      console.log('标记点击:', data)
      // 可以在这里处理标记点击逻辑
    },

    // 处理路线规划结果
    handleRouteResult(data: any) {
      if (data.status === 'success') {
        console.log('路线规划成功:', data.result)
        // 处理成功结果
      } else {
        console.error('路线规划失败:', data.error)
        wx.showToast({
          title: '路线规划失败',
          icon: 'error'
        })
      }
    },

    // 加载地点数据
    async loadLocations() {
      try {
        wx.showLoading({ title: '加载地点数据...' })
        console.log('开始加载地点数据...')

        // 使用与网页端相同的API接口
        const response: any = await request({
          url: '/path/vertices',
          method: 'GET'
        })

        console.log('API响应:', response)

        if (response && Array.isArray(response)) {
          const locations = response
          const locationNames = locations.map((loc: Location) => loc.label)

          this.setData({
            locations,
            locationNames,
            availableLocations: locations
          })

          console.log('获取到地点数据:', locations.length, '个地点')

          if (locations.length === 0) {
            wx.showModal({
              title: '提示',
              content: '暂无地点数据，请联系管理员',
              showCancel: false
            })
            return
          }

          // 不在地图上显示所有地点，只在需要时显示起点终点标记
          // this.showAllLocationsOnMap(locations)

          // 如果使用web-view，同步标记到地图
          if (this.data.useWebView && this.data.mapReady) {
            this.syncMarkersToMap()
          }

          wx.showToast({
            title: `已加载${locations.length}个地点`,
            icon: 'success'
          })
        } else {
          console.error('API返回数据格式错误:', response)
          wx.showModal({
            title: '数据加载失败',
            content: '服务器返回的数据格式不正确',
            showCancel: false
          })
        }
      } catch (error) {
        console.error('加载地点数据失败:', error)
        wx.showModal({
          title: '加载失败',
          content: `地点数据加载失败：${error.message || '未知错误'}\n\n请检查网络连接和服务器状态`,
          showCancel: false
        })
      } finally {
        wx.hideLoading()
      }
    },

    // 坐标转换函数（与网页端完全一致）
    calculateCoordinates(x: number, y: number) {
      // 使用与网页端相同的精确坐标转换公式
      // 经度 = 116.35526546085191 + 0.000010265146964114 * X坐标
      // 纬度 = 39.95804755710804 + 0.0000091118507731447 * Y坐标
      const longitude = 116.35526546085191 + 0.000010265146964114 * x
      const latitude = 39.95804755710804 + 0.0000091118507731447 * y

      return {
        lng: longitude,
        lat: latitude
      }
    },

    // 格式化距离显示
    formatDistance(distance: number): string {
      if (distance < 1000) {
        return `${Math.round(distance)} 米`
      } else {
        return `${(distance / 1000).toFixed(2)} 公里`
      }
    },

    // 格式化时间显示
    formatDuration(minutes: number): string {
      if (minutes < 60) {
        return `${Math.round(minutes)} 分钟`
      } else {
        const hours = Math.floor(minutes / 60)
        const remainingMinutes = Math.round(minutes % 60)
        if (remainingMinutes === 0) {
          return `${hours} 小时`
        } else {
          return `${hours} 小时 ${remainingMinutes} 分钟`
        }
      }
    },

    // 在地图上显示所有地点
    showAllLocationsOnMap(locations: Location[]) {
      const markers = locations.map((location, index) => {
        // 使用坐标转换公式计算经纬度
        const coordinates = this.calculateCoordinates(location.x, location.y)

        return {
          id: location.vertex_id,
          latitude: coordinates.lat,
          longitude: coordinates.lng,
          title: location.label,
          width: 30,
          height: 30,
          callout: {
            content: location.label,
            fontSize: 12,
            borderRadius: 4,
            bgColor: '#ffffff',
            padding: 4,
            display: 'BYCLICK'
          }
        }
      })

      this.setData({ markers })
    },

    // 起点搜索输入
    onStartSearchInput(e: any) {
      const value = e.detail.value
      this.setData({ startSearchText: value })
      this.filterStartLocations(value)
    },

    // 起点搜索获得焦点
    onStartSearchFocus() {
      this.setData({ showStartSuggestions: true })
      if (this.data.startSearchText) {
        this.filterStartLocations(this.data.startSearchText)
      }
    },

    // 起点搜索失去焦点
    onStartSearchBlur() {
      // 延迟关闭建议列表，以便点击建议项时能够触发点击事件
      setTimeout(() => {
        this.setData({ showStartSuggestions: false })
      }, 200)
    },

    // 过滤起点位置
    filterStartLocations(searchText: string) {
      if (!searchText.trim()) {
        this.setData({ filteredStartLocations: [] })
        return
      }

      const searchLower = searchText.toLowerCase().trim()
      const filtered = this.data.locations.filter(location =>
        location.label && location.label.toLowerCase().includes(searchLower)
      ).slice(0, 10) // 限制最多显示10个结果

      this.setData({ filteredStartLocations: filtered })
    },

    // 选择起点
    selectStartLocation(e: any) {
      const location = e.currentTarget.dataset.location

      this.setData({
        selectedStartId: location.vertex_id,
        startSearchText: location.label,
        startLocationName: location.label,
        showStartSuggestions: false
      })

      this.updateStartMarker(location)
      this.updateCanPlan()
    },

    // 终点搜索输入
    onEndSearchInput(e: any) {
      const value = e.detail.value
      this.setData({ endSearchText: value })
      this.filterEndLocations(value)
    },

    // 终点搜索获得焦点
    onEndSearchFocus() {
      this.setData({ showEndSuggestions: true })
      if (this.data.endSearchText) {
        this.filterEndLocations(this.data.endSearchText)
      }
    },

    // 终点搜索失去焦点
    onEndSearchBlur() {
      // 延迟关闭建议列表，以便点击建议项时能够触发点击事件
      setTimeout(() => {
        this.setData({ showEndSuggestions: false })
      }, 200)
    },

    // 过滤终点位置
    filterEndLocations(searchText: string) {
      if (!searchText.trim()) {
        this.setData({ filteredEndLocations: [] })
        return
      }

      const searchLower = searchText.toLowerCase().trim()
      const filtered = this.data.locations.filter(location =>
        location.label && location.label.toLowerCase().includes(searchLower)
      ).slice(0, 10) // 限制最多显示10个结果

      this.setData({ filteredEndLocations: filtered })
    },

    // 选择终点
    selectEndLocation(e: any) {
      const location = e.currentTarget.dataset.location

      this.setData({
        selectedEndId: location.vertex_id,
        endSearchText: location.label,
        endLocationName: location.label,
        showEndSuggestions: false
      })

      this.updateEndMarker(location)
      this.updateCanPlan()
    },

    // 起点选择（保留兼容性）
    onStartLocationChange(e: any) {
      const index = e.detail.value
      const location = this.data.locations[index]

      this.setData({
        startLocationIndex: index,
        startLocationName: location.label,
        selectedStartId: location.vertex_id,
        startSearchText: location.label
      })

      this.updateStartMarker(location)
      this.updateCanPlan()
    },

    // 终点选择（保留兼容性）
    onEndLocationChange(e: any) {
      const index = e.detail.value
      const location = this.data.locations[index]

      this.setData({
        endLocationIndex: index,
        endLocationName: location.label,
        selectedEndId: location.vertex_id,
        endSearchText: location.label
      })

      this.updateEndMarker(location)
      this.updateCanPlan()
    },

    // 更新起点标记
    updateStartMarker(location: Location) {
      console.log('更新起点标记:', location.label)

      if (this.data.useWebView) {
        // 使用高德地图
        this.updateWebViewMarkers('start', location)
      } else {
        // 使用原生地图 - 只显示起点和终点标记
        const markers = []

        // 添加起点标记
        const startCoords = this.calculateCoordinates(location.x, location.y)
        markers.push({
          id: `start_${location.vertex_id}`,
          latitude: startCoords.lat,
          longitude: startCoords.lng,
          title: '起点',
          width: 40,
          height: 40,
          label: {
            content: '起',
            fontSize: 14,
            color: '#ffffff',
            bgColor: '#00C851',
            borderRadius: 20,
            padding: 4
          },
          callout: {
            content: `起点: ${location.label}`,
            fontSize: 12,
            borderRadius: 4,
            bgColor: '#00C851',
            color: '#ffffff',
            padding: 4,
            display: 'ALWAYS'
          }
        })

        // 如果有终点，也添加终点标记
        if (this.data.selectedEndId) {
          const endLocation = this.data.locations.find(loc => loc.vertex_id === this.data.selectedEndId)
          if (endLocation) {
            const endCoords = this.calculateCoordinates(endLocation.x, endLocation.y)
            markers.push({
              id: `end_${endLocation.vertex_id}`,
              latitude: endCoords.lat,
              longitude: endCoords.lng,
              title: '终点',
              width: 40,
              height: 40,
              label: {
                content: '终',
                fontSize: 14,
                color: '#ffffff',
                bgColor: '#FF4444',
                borderRadius: 20,
                padding: 4
              },
              callout: {
                content: `终点: ${endLocation.label}`,
                fontSize: 12,
                borderRadius: 4,
                bgColor: '#FF4444',
                color: '#ffffff',
                padding: 4,
                display: 'ALWAYS'
              }
            })
          }
        }

        this.setData({ markers })
        console.log('起点标记已更新')
      }
    },

    // 更新终点标记
    updateEndMarker(location: Location) {
      if (this.data.useWebView) {
        // 使用高德地图
        this.updateWebViewMarkers('end', location)
      } else {
        // 使用原生地图 - 只显示起点和终点标记
        const markers = []

        // 如果有起点，添加起点标记
        if (this.data.selectedStartId) {
          const startLocation = this.data.locations.find(loc => loc.vertex_id === this.data.selectedStartId)
          if (startLocation) {
            const startCoords = this.calculateCoordinates(startLocation.x, startLocation.y)
            markers.push({
              id: `start_${startLocation.vertex_id}`,
              latitude: startCoords.lat,
              longitude: startCoords.lng,
              title: '起点',
              width: 40,
              height: 40,
              label: {
                content: '起',
                fontSize: 14,
                color: '#ffffff',
                bgColor: '#00C851',
                borderRadius: 20,
                padding: 4
              },
              callout: {
                content: `起点: ${startLocation.label}`,
                fontSize: 12,
                borderRadius: 4,
                bgColor: '#00C851',
                color: '#ffffff',
                padding: 4,
                display: 'ALWAYS'
              }
            })
          }
        }

        // 添加终点标记
        const endCoords = this.calculateCoordinates(location.x, location.y)
        markers.push({
          id: `end_${location.vertex_id}`,
          latitude: endCoords.lat,
          longitude: endCoords.lng,
          title: '终点',
          width: 40,
          height: 40,
          label: {
            content: '终',
            fontSize: 14,
            color: '#ffffff',
            bgColor: '#FF4444',
            borderRadius: 20,
            padding: 4
          },
          callout: {
            content: `终点: ${location.label}`,
            fontSize: 12,
            borderRadius: 4,
            bgColor: '#FF4444',
            color: '#ffffff',
            padding: 4,
            display: 'ALWAYS'
          }
        })

        this.setData({ markers })
        console.log('终点标记已更新')
      }
    },

    // 更新是否可以规划路线
    updateCanPlan() {
      const canPlan = this.data.selectedStartId !== null && this.data.selectedEndId !== null
      this.setData({ canPlan })
    },

    // 显示途径点选择器
    showWaypointPicker() {
      // 过滤掉已选择的起点、终点和途径点
      const usedIds = [
        this.data.selectedStartId,
        this.data.selectedEndId,
        ...this.data.waypoints.map(wp => wp.id)
      ].filter(id => id !== null)

      const availableLocations = this.data.locations.filter(
        location => !usedIds.includes(location.vertex_id)
      )

      this.setData({
        availableLocations,
        showWaypointModal: true,
        waypointSearchText: '',
        filteredWaypointLocations: availableLocations.slice(0, 10) // 默认显示前10个
      })
    },

    // 隐藏途径点选择器
    hideWaypointPicker() {
      this.setData({
        showWaypointModal: false,
        waypointSearchText: '',
        filteredWaypointLocations: []
      })
    },

    // 途径点搜索输入
    onWaypointSearchInput(e: any) {
      const value = e.detail.value
      this.setData({ waypointSearchText: value })
      this.filterWaypointLocations(value)
    },

    // 途径点搜索获得焦点
    onWaypointSearchFocus() {
      if (this.data.waypointSearchText) {
        this.filterWaypointLocations(this.data.waypointSearchText)
      } else {
        // 显示所有可用位置
        this.setData({
          filteredWaypointLocations: this.data.availableLocations.slice(0, 10)
        })
      }
    },

    // 过滤途径点位置
    filterWaypointLocations(searchText: string) {
      if (!searchText.trim()) {
        this.setData({
          filteredWaypointLocations: this.data.availableLocations.slice(0, 10)
        })
        return
      }

      const searchLower = searchText.toLowerCase().trim()
      const filtered = this.data.availableLocations.filter(location =>
        location.label && location.label.toLowerCase().includes(searchLower)
      ).slice(0, 10) // 限制最多显示10个结果

      this.setData({ filteredWaypointLocations: filtered })
    },

    // 选择途径点
    selectWaypoint(e: any) {
      const location = e.currentTarget.dataset.location
      const waypoint: Waypoint = {
        id: location.vertex_id,
        name: location.label,
        x: location.x,
        y: location.y
      }

      const waypoints = [...this.data.waypoints, waypoint]
      this.setData({ waypoints })

      this.updateWaypointMarkers()
      this.hideWaypointPicker()
    },

    // 删除途径点
    removeWaypoint(e: any) {
      const index = e.currentTarget.dataset.index
      const waypoints = this.data.waypoints.filter((_, i) => i !== index)
      this.setData({ waypoints })

      this.updateWaypointMarkers()
    },

    // 更新途径点标记
    updateWaypointMarkers() {
      if (this.data.useWebView) {
        // 使用高德地图
        this.updateWebViewMarkers('waypoint')
      } else {
        // 使用原生地图
        const markers = this.data.markers.map(marker => {
          const waypoint = this.data.waypoints.find(wp => wp.id === marker.id)
          if (waypoint) {
            return {
              ...marker,
              width: 35,
              height: 35,
              label: {
                content: '途',
                fontSize: 12,
                color: '#ffffff',
                bgColor: '#409EFF',
                borderRadius: 15,
                padding: 3
              },
              callout: {
                ...marker.callout,
                content: `途径点: ${waypoint.name}`
              }
            }
          }
          return marker
        })

        this.setData({ markers })
      }
    },

    // 选择交通方式
    selectTransportMode(e: any) {
      const mode = e.currentTarget.dataset.mode
      this.setData({ selectedTransportMode: mode })

      // 如果选择智能出行策略，自动设置交通方式为"不限"
      if (this.data.selectedStrategy === 3 && mode !== 'driving') {
        this.setData({ selectedStrategy: 0 })
      }
    },

    // 选择策略
    selectStrategy(e: any) {
      const strategy = parseInt(e.currentTarget.dataset.strategy)
      this.setData({ selectedStrategy: strategy })

      // 如果选择智能出行策略，自动设置交通方式为"不限"
      if (strategy === 3) {
        this.setData({ selectedTransportMode: 'driving' })
      }
    },

    // 路径规划（与网页端逻辑完全一致）
    async planRoute() {
      console.log('开始计算后端路线...')
      console.log('起点ID:', this.data.selectedStartId)
      console.log('终点ID:', this.data.selectedEndId)
      console.log('策略:', this.data.selectedStrategy)
      console.log('交通方式:', this.data.selectedTransportMode)

      if (!this.data.selectedStartId || !this.data.selectedEndId) {
        wx.showToast({
          title: '请先选择起点和终点',
          icon: 'error'
        })
        return
      }

      try {
        this.setData({ isLoading: true })
        wx.showLoading({ title: '规划路线中...' })

        // 获取有效的途径点ID
        const waypointIds = this.data.waypoints
          .filter(wp => wp.id)
          .map(wp => parseInt(wp.id.toString()))

        console.log('途径点IDs:', waypointIds)

        // 根据交通方式和策略选择实际策略（与网页端逻辑一致）
        let actualStrategy = this.data.selectedStrategy

        // 如果选择了骑行模式，根据用户选择的策略调整
        if (this.data.selectedTransportMode === 'riding') {
          if (this.data.selectedStrategy === 0) {
            actualStrategy = 4  // 骑行最短距离策略
            console.log('使用骑行最短距离策略')
          } else if (this.data.selectedStrategy === 1) {
            actualStrategy = 5  // 骑行最短时间策略
            console.log('使用骑行最短时间策略')
          } else {
            actualStrategy = 2  // 默认可骑行策略
            console.log('使用默认自行车路线规划策略')
          }
        }
        // 如果选择了智能出行策略，使用策略3
        else if (this.data.selectedStrategy === 3) {
          actualStrategy = 3
          console.log('使用智能出行策略')
        }
        // 如果选择了最短时间/最短距离策略且出行方式为"不限"，使用智能出行策略
        else if ((this.data.selectedStrategy === 0 || this.data.selectedStrategy === 1) && this.data.selectedTransportMode === 'driving') {
          actualStrategy = 3
          console.log(`用户选择${this.data.selectedStrategy === 0 ? '最短距离' : '最短时间'}策略且出行方式为"不限"，使用智能出行策略`)
        }

        let requestData: any

        if (waypointIds.length > 0) {
          // 使用多目的地路径规划API
          requestData = {
            start_id: parseInt(this.data.selectedStartId.toString()),
            destinations: waypointIds.concat([parseInt(this.data.selectedEndId.toString())]), // 将终点添加到途径点列表末尾
            strategy: actualStrategy,
            algorithm: 'simple' // 使用简单顺序路径算法，按照用户指定的顺序访问途径点
          }
        } else {
          // 使用单目的地路径规划API
          requestData = {
            start_id: parseInt(this.data.selectedStartId.toString()),
            end_id: parseInt(this.data.selectedEndId.toString()),
            strategy: actualStrategy
          }
        }

        console.log('发送请求数据:', requestData)

        // 调用后端路径规划API
        const response: any = await request({
          url: '/path/plan',
          method: 'POST',
          data: requestData
        })

        if (response && response.path) {
          console.log('后端路径规划结果:', response)
          this.processBackendRouteResult(response)
        } else {
          throw new Error('路径规划失败')
        }

      } catch (error) {
        console.error('路径规划失败:', error)
        wx.showToast({
          title: '路径规划失败',
          icon: 'error'
        })
      } finally {
        this.setData({ isLoading: false })
        wx.hideLoading()
      }
    },

    // 处理后端路径规划结果（与网页端逻辑完全一致）
    processBackendRouteResult(response: any) {
      // 处理路径数据
      if (response.path && response.vertexes) {
        // 更新距离信息
        const distance = response.total_distance || 0

        // 骑行模式或智能出行模式下，分别显示骑行和步行距离
        const shouldShowDetailedDistance = (this.data.selectedTransportMode === 'riding' || this.data.selectedStrategy === 3 ||
          ((this.data.selectedStrategy === 0 || this.data.selectedStrategy === 1) && this.data.selectedTransportMode === 'driving'))

        let routeResult: RouteResult = {
          totalDistance: '',
          totalTime: '',
          pathDetails: []
        }

        if (shouldShowDetailedDistance && response.path_details && response.path_details.length > 0) {
          // 计算可骑行和不可骑行的距离
          let rideableDistance = 0
          let nonRideableDistance = 0

          response.path_details.forEach((segment: any) => {
            const segmentDistance = segment.distance || 0
            if (segment.is_rideable) {
              rideableDistance += segmentDistance
            } else {
              nonRideableDistance += segmentDistance
            }
          })

          // 格式化距离显示
          const totalDistance = rideableDistance + nonRideableDistance

          let distanceText = `总计: ${this.formatDistance(totalDistance)}`

          // 添加骑行和步行距离详情
          if (rideableDistance > 0 && nonRideableDistance > 0) {
            routeResult.cyclingDistance = this.formatDistance(rideableDistance)
            routeResult.walkingDistance = this.formatDistance(nonRideableDistance)
          } else if (rideableDistance > 0) {
            distanceText += `\n全程骑行`
          } else if (nonRideableDistance > 0) {
            distanceText += `\n全程步行`
          }

          routeResult.totalDistance = distanceText
        } else {
          // 其他模式下直接显示总距离
          routeResult.totalDistance = this.formatDistance(distance)
        }

        // 根据交通方式估算时间
        let timeInMinutes = 0

        // 骑行模式或智能出行模式下，需要考虑每段路径的可骑行性
        const shouldShowDetailedTime = (this.data.selectedTransportMode === 'riding' || this.data.selectedStrategy === 3 ||
          ((this.data.selectedStrategy === 0 || this.data.selectedStrategy === 1) && this.data.selectedTransportMode === 'driving'))

        if (shouldShowDetailedTime && response.path_details && response.path_details.length > 0) {
          // 计算骑行部分的时间（分钟）- 考虑拥挤度
          let rideTime = 0
          response.path_details.forEach((segment: any) => {
            if (segment.is_rideable) {
              const segmentDistance = segment.distance || 0
              const crowding = segment.crowding || 1.0
              const rideSpeed = 12 * crowding // 骑行速度 = 12公里/小时 * 拥挤度
              rideTime += (segmentDistance / 1000) / rideSpeed * 60
            }
          })

          // 计算步行部分的时间（分钟）- 考虑拥挤度
          let walkTime = 0
          response.path_details.forEach((segment: any) => {
            if (!segment.is_rideable) {
              const segmentDistance = segment.distance || 0
              const crowding = segment.crowding || 1.0
              const walkSpeed = 4 * crowding // 步行速度 = 4公里/小时 * 拥挤度
              walkTime += (segmentDistance / 1000) / walkSpeed * 60
            }
          })

          // 总时间 = 骑行时间 + 步行时间
          timeInMinutes = Math.ceil(rideTime + walkTime)

          console.log(`总距离: ${distance}米`)
          console.log(`骑行时间: ${rideTime.toFixed(2)}分钟 (考虑拥挤度)`)
          console.log(`步行时间: ${walkTime.toFixed(2)}分钟 (考虑拥挤度)`)
          console.log('骑行模式/智能出行 - 修正后的总时间:', timeInMinutes, '分钟')
        } else {
          // 其他模式下使用平均速度
          let averageSpeed = 4 // 默认步行速度 4km/h

          // 根据交通方式调整平均速度
          if (this.data.selectedTransportMode === 'riding') {
            averageSpeed = 12 // 自行车平均速度 12km/h
          } else if (this.data.selectedTransportMode === 'driving') {
            averageSpeed = 30 // 驾车平均速度 30km/h
          } else if (this.data.selectedTransportMode === 'walking') {
            // 步行模式 - 考虑拥挤度的平均值
            if (response.path_details && response.path_details.length > 0) {
              const avgCrowding = response.path_details.reduce((sum: number, segment: any) =>
                sum + (segment.crowding || 1.0), 0) / response.path_details.length
              averageSpeed = 4 * avgCrowding
            } else {
              averageSpeed = 4 // 没有详细信息时使用默认值
            }
          }

          // 计算时间（分钟）- 使用向上取整，确保时间估计不会偏小
          timeInMinutes = Math.ceil((distance / 1000) / averageSpeed * 60)
        }

        routeResult.totalTime = this.formatDuration(timeInMinutes)

        // 生成路径步骤说明 - 简化版本，只显示地点名称
        const steps: string[] = []
        const vertices = response.vertexes
        const waypointIds = this.data.waypoints.map(wp => wp.id)

        // 添加起点
        const startVertex = vertices.find((v: any) => v.vertex_id === response.path[0])
        if (startVertex) {
          steps.push(`从 ${startVertex.label || '起点'} 出发`)
        }

        // 添加中间路径点
        for (let i = 1; i < response.path.length; i++) {
          const vertex = vertices.find((v: any) => v.vertex_id === response.path[i])
          if (vertex) {
            // 检查是否是途径点
            const isWaypoint = waypointIds.includes(vertex.vertex_id)
            const isEndpoint = vertex.vertex_id === parseInt(this.data.selectedEndId.toString())

            if (isWaypoint) {
              steps.push(`途径 ${vertex.label || '途径点'}`)
            } else if (isEndpoint) {
              steps.push(`到达终点 ${vertex.label || '终点'}`)
            } else {
              steps.push(`前往 ${vertex.label || `地点${i}`}`)
            }
          }
        }

        // 如果没有步骤，添加一个默认步骤
        if (steps.length === 0) {
          steps.push('按照地图上的路线行驶')
        }

        // 格式化路径详情
        routeResult.pathDetails = this.formatPathDetails(response.path_details || [], vertices, response.path)

        this.setData({ routeResult })

        // 在地图上绘制路径
        this.drawPathOnMap(vertices, response.path, response.path_details || [])

        // 添加起点和终点标记
        this.addStartEndMarkers(vertices, response.path)

        wx.showToast({
          title: '路线规划成功',
          icon: 'success'
        })
      } else {
        wx.showToast({
          title: '无法获取详细路线信息',
          icon: 'error'
        })
      }
    },

    // 在地图上绘制路径（与网页端逻辑一致）
    drawPathOnMap(vertices: any[], path: number[], pathDetails: any[] = []) {
      console.log('绘制路径 - 顶点数:', vertices.length, '路径点数:', path.length, '路径详情数:', pathDetails.length)

      try {
        // 构建路径点数组
        const pathPoints: any[] = []
        for (const vertexId of path) {
          const vertex = vertices.find((v: any) => v.vertex_id === vertexId)
          if (vertex) {
            // 使用坐标转换公式计算经纬度
            const coordinates = this.calculateCoordinates(vertex.x, vertex.y)
            pathPoints.push({
              longitude: coordinates.lng,
              latitude: coordinates.lat
            })
          }
        }

        if (pathPoints.length < 2) {
          console.error('路径点不足，无法绘制路线')
          return
        }

        // 检查是否是骑行模式或智能出行模式
        const isRidingMode = this.data.selectedTransportMode === 'riding'
        const isSmartTravelMode = this.data.selectedStrategy === 3 ||
          ((this.data.selectedStrategy === 0 || this.data.selectedStrategy === 1) && this.data.selectedTransportMode === 'driving')

        try {
          if ((isRidingMode || isSmartTravelMode) && pathDetails.length > 0 && pathDetails.length + 1 >= pathPoints.length) {
            // 骑行模式或智能出行模式下，使用不同颜色标识可骑行和不可骑行路段
            // 创建多个折线，每段路径使用不同颜色
            const polylines: any[] = []

            for (let i = 0; i < pathDetails.length; i++) {
              if (i + 1 >= pathPoints.length) continue

              const segmentPath = [pathPoints[i], pathPoints[i + 1]]
              const isRideable = pathDetails[i].is_rideable
              const crowding = pathDetails[i].crowding || 1.0

              // 确定线条颜色和样式
              let strokeColor: string, strokeStyle: string

              if (isSmartTravelMode || isRidingMode) {
                // 智能出行模式和骑行模式：骑行路段根据拥挤度确定颜色，步行路段统一使用蓝色
                if (isRideable) {
                  strokeColor = this.getCrowdingColor(crowding)  // 骑行路段根据拥挤度变色
                  strokeStyle = 'solid'  // 骑行实线
                } else {
                  strokeColor = '#3498db'  // 步行路段统一使用蓝色
                  strokeStyle = 'dashed'  // 步行虚线
                }
              } else {
                // 其他模式：使用默认颜色
                strokeColor = '#3498db'
                strokeStyle = 'solid'
              }

              // 创建折线段
              polylines.push({
                points: segmentPath,
                color: strokeColor,
                width: 6,
                dottedLine: strokeStyle === 'dashed',
                arrowLine: true
              })
            }

            this.setData({ polylines })
            console.log(`已绘制${isSmartTravelMode ? '智能出行' : isRidingMode ? '骑行' : '混合'}路线，共`, polylines.length, '段')
          } else {
            // 非骑行模式或没有详细路径信息，使用单一颜色
            const polylines = [{
              points: pathPoints,
              color: '#3498db', // 线条颜色
              width: 6,        // 线条宽度
              dottedLine: false,   // 线条样式
              arrowLine: true           // 显示方向箭头
            }]

            this.setData({ polylines })
            console.log('已绘制普通路线，路径点数:', pathPoints.length)
          }
        } catch (error) {
          console.error('绘制路线错误:', error)
          // 出错时使用简单路线作为备选
          const polylines = [{
            points: pathPoints,
            color: '#3498db',
            width: 6,
            dottedLine: false,
            arrowLine: true
          }]

          this.setData({ polylines })
          console.log('使用备选方式绘制路线')
        }

        // 调整地图视野以包含整个路线
        this.fitMapToRoute(pathPoints)
      } catch (error) {
        console.error('绘制路径错误:', error)
      }
    },

    // 根据拥挤度获取颜色（与网页端一致）
    getCrowdingColor(crowding: number) {
      if (crowding >= 0.9) {
        return '#27ae60'  // 绿色 - 拥挤度高，通行顺畅
      } else if (crowding >= 0.5) {
        return '#f39c12'  // 黄色 - 拥挤度中等
      } else {
        return '#e74c3c'  // 红色 - 拥挤度低，比较拥挤
      }
    },

    // 格式化路径详情
    formatPathDetails(pathDetails: any[], vertices: any[], path: number[]) {
      if (!pathDetails || pathDetails.length === 0 || !vertices || !path) {
        return []
      }

      const details = []

      for (let i = 0; i < pathDetails.length && i < path.length - 1; i++) {
        const fromVertexId = path[i]
        const toVertexId = path[i + 1]

        const fromVertex = vertices.find((v: any) => v.vertex_id === fromVertexId)
        const toVertex = vertices.find((v: any) => v.vertex_id === toVertexId)

        if (fromVertex && toVertex) {
          const detail = pathDetails[i]
          const isRideable = detail.is_rideable
          const crowding = detail.crowding || 1.0
          const distance = detail.distance || 0
          const travelMode = isRideable ? '骑行' : '步行'

          // 计算这一段的预计时间
          let segmentTime = 0
          if (isRideable) {
            const rideSpeed = 12 * crowding // 骑行速度 = 12公里/小时 * 拥挤度
            segmentTime = (distance / 1000) / rideSpeed * 60 // 转换为分钟
          } else {
            const walkSpeed = 4 * crowding // 步行速度 = 4公里/小时 * 拥挤度
            segmentTime = (distance / 1000) / walkSpeed * 60 // 转换为分钟
          }

          details.push({
            from_name: fromVertex.label || `地点${i + 1}`,
            to_name: toVertex.label || `地点${i + 2}`,
            distance: this.formatDistance(distance),
            time: this.formatDuration(Math.ceil(segmentTime)),
            mode: travelMode,
            crowding: crowding,
            crowdingText: this.getCrowdingText(crowding),
            color: isRideable ? this.getCrowdingColor(crowding) : '#3498db'
          })
        }
      }

      return details
    },

    // 获取拥挤度文本描述
    getCrowdingText(crowding: number) {
      if (crowding >= 0.9) {
        return '通畅'
      } else if (crowding >= 0.5) {
        return '一般'
      } else {
        return '拥挤'
      }
    },

    // 调整地图视野以包含整个路线（温和方式，避免地图背景丢失）
    fitMapToRoute(pathPoints: any[]) {
      if (pathPoints.length === 0) return

      try {
        // 使用更长的延迟执行，避免与地图渲染冲突
        setTimeout(() => {
          const mapContext = wx.createMapContext('map', this)

          // 使用includePoints方法，这是最温和的调整方式
          mapContext.includePoints({
            points: pathPoints,
            padding: [100, 100, 100, 100] // 增加边距，确保路线完全可见
          })

          console.log('地图视野已调整以包含路线')
        }, 1000) // 延迟1000ms执行，确保地图渲染完成
      } catch (error) {
        console.error('调整地图视野失败:', error)
        // 如果调整视野失败，不影响路线显示
        console.log('路线已绘制完成，但视野调整失败')
      }
    },

    // 添加起点和终点标记
    addStartEndMarkers(vertices: any[], path: number[]) {
      if (!path || path.length < 2) return

      const startVertexId = path[0]
      const endVertexId = path[path.length - 1]

      const startVertex = vertices.find((v: any) => v.vertex_id === startVertexId)
      const endVertex = vertices.find((v: any) => v.vertex_id === endVertexId)

      if (!startVertex || !endVertex) return

      const markers = []

      // 起点标记
      const startCoords = this.calculateCoordinates(startVertex.x, startVertex.y)
      markers.push({
        id: `start_${startVertexId}`,
        latitude: startCoords.lat,
        longitude: startCoords.lng,
        title: '起点',
        width: 40,
        height: 40,
        label: {
          content: '起',
          fontSize: 14,
          color: '#ffffff',
          bgColor: '#00C851',
          borderRadius: 20,
          padding: 4
        },
        callout: {
          content: `起点: ${startVertex.label}`,
          fontSize: 12,
          borderRadius: 4,
          bgColor: '#00C851',
          color: '#ffffff',
          padding: 4,
          display: 'ALWAYS'
        }
      })

      // 终点标记
      const endCoords = this.calculateCoordinates(endVertex.x, endVertex.y)
      markers.push({
        id: `end_${endVertexId}`,
        latitude: endCoords.lat,
        longitude: endCoords.lng,
        title: '终点',
        width: 40,
        height: 40,
        label: {
          content: '终',
          fontSize: 14,
          color: '#ffffff',
          bgColor: '#FF4444',
          borderRadius: 20,
          padding: 4
        },
        callout: {
          content: `终点: ${endVertex.label}`,
          fontSize: 12,
          borderRadius: 4,
          bgColor: '#FF4444',
          color: '#ffffff',
          padding: 4,
          display: 'ALWAYS'
        }
      })

      // 添加途径点标记
      this.data.waypoints.forEach((waypoint, index) => {
        const waypointVertex = vertices.find((v: any) => v.vertex_id === waypoint.id)
        if (waypointVertex) {
          const waypointCoords = this.calculateCoordinates(waypointVertex.x, waypointVertex.y)
          markers.push({
            id: `waypoint_${waypoint.id}`,
            latitude: waypointCoords.lat,
            longitude: waypointCoords.lng,
            title: `途径点${index + 1}`,
            width: 35,
            height: 35,
            label: {
              content: '途',
              fontSize: 12,
              color: '#ffffff',
              bgColor: '#409EFF',
              borderRadius: 15,
              padding: 3
            },
            callout: {
              content: `途径点: ${waypoint.name}`,
              fontSize: 12,
              borderRadius: 4,
              bgColor: '#409EFF',
              color: '#ffffff',
              padding: 4,
              display: 'BYCLICK'
            }
          })
        }
      })

      this.setData({ markers })
      console.log('已添加起点和终点标记，共', markers.length, '个标记')
    },

    // 地图初始化完成
    onMapReady(e: any) {
      console.log('地图初始化完成:', e.detail)
      this.setData({ mapReady: true })

      // 延迟执行地图设置，确保地图完全加载
      setTimeout(() => {
        try {
          const mapContext = wx.createMapContext('map', this)

          // 设置地图中心点和缩放级别（温和方式）
          mapContext.moveToLocation({
            longitude: this.data.mapCenter.longitude,
            latitude: this.data.mapCenter.latitude
          })

          // 地图初始化时不显示所有地点，保持地图清洁
          // if (this.data.locations.length > 0) {
          //   this.showAllLocationsOnMap(this.data.locations)
          // }

          console.log('地图设置完成')
        } catch (error) {
          console.error('地图设置失败:', error)
        }
      }, 1000)
    },

    // 地图点击事件
    onMapTap(e: any) {
      console.log('地图点击:', e.detail)
    },

    // 标记点击事件
    onMarkerTap(e: any) {
      console.log('标记点击:', e.detail)
    }
  }
})
