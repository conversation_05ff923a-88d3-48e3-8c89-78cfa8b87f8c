#!/usr/bin/env python3
"""
数据库连接测试脚本
用于测试不同的数据库连接参数
"""

import pymysql
import sys

def test_connection(host, user, password, database=None):
    """测试数据库连接"""
    try:
        print(f"🔍 测试连接: {user}@{host}")
        if database:
            print(f"   数据库: {database}")
        else:
            print("   不指定数据库")
        
        connection = pymysql.connect(
            host=host,
            user=user,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        print("✅ 连接成功!")
        
        # 测试查询
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"   MySQL版本: {version[0]}")
            
            if database:
                cursor.execute("SELECT DATABASE()")
                current_db = cursor.fetchone()
                print(f"   当前数据库: {current_db[0]}")
            else:
                cursor.execute("SHOW DATABASES")
                databases = cursor.fetchall()
                print("   可用数据库:")
                for db in databases:
                    print(f"     - {db[0]}")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("🔧 数据库连接测试工具")
    print("=" * 50)

    # 测试参数
    hosts = ['localhost', '127.0.0.1']  # 两个可能的主机
    users = ['root', 'mysql', 'admin']  # 可能的用户名
    passwords = ['AN20050225', 'An20050225', '']  # 可能的密码（包括空密码）
    database = 'study_tour_system'
    
    print("\n1️⃣ 测试基本连接（不指定数据库）")
    print("-" * 30)

    success_config = None

    # 尝试所有组合
    for host in hosts:
        for user in users:
            for password in passwords:
                password_display = password if password else "(空密码)"
                print(f"\n尝试: {user}@{host} 密码: {password_display}")
                if test_connection(host, user, password):
                    success_config = (host, user, password)
                    break
            if success_config:
                break
        if success_config:
            break

    if not success_config:
        print("\n❌ 所有组合都无法连接，请检查:")
        print("   1. MySQL服务是否启动")
        print("   2. 用户名和密码是否正确")
        print("   3. MySQL是否允许本地连接")
        return

    host, user, password = success_config
    print(f"\n✅ 找到正确配置: {user}@{host} 密码: {password}")

    print("\n2️⃣ 测试指定数据库连接")
    print("-" * 30)

    if test_connection(host, user, password, database):
        print(f"\n🎉 数据库 '{database}' 连接成功!")
        print(f"   正确的连接字符串: mysql+pymysql://{user}:{password}@{host}/{database}")
    else:
        print(f"\n⚠️  数据库 '{database}' 不存在或无法访问")
        print("   您可能需要先创建数据库:")
        print(f"   CREATE DATABASE {database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;")

if __name__ == "__main__":
    main()
