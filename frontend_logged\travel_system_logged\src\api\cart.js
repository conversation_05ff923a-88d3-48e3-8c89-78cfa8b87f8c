import request from '@/utils/request';

/**
 * 添加菜品到购物车
 * @param {Object} data - 购物车数据
 * @param {number} data.user_id - 用户ID
 * @param {string} data.dish_name - 菜品名称
 * @param {number} data.dish_price - 菜品价格
 * @param {string} [data.dish_image] - 菜品图片URL
 * @param {number} data.restaurant_id - 餐馆ID
 * @returns {Promise} - 返回添加结果的Promise
 */
export function addToCart(data) {
  return request({
    url: '/api/cart/add',
    method: 'post',
    data
  });
}

/**
 * 获取用户购物车
 * @param {number} userId - 用户ID
 * @returns {Promise} - 返回用户购物车的Promise
 */
export function getUserCart(userId) {
  return request({
    url: `/api/cart/user/${userId}`,
    method: 'get'
  });
}

/**
 * 更新购物车中菜品数量
 * @param {number} cartId - 购物车项ID
 * @param {number} quantity - 新数量
 * @returns {Promise} - 返回更新结果的Promise
 */
export function updateCartItem(cartId, quantity) {
  return request({
    url: `/api/cart/update/${cartId}`,
    method: 'put',
    data: { quantity }
  });
}

/**
 * 从购物车中移除菜品
 * @param {number} cartId - 购物车项ID
 * @returns {Promise} - 返回移除结果的Promise
 */
export function removeFromCart(cartId) {
  return request({
    url: `/api/cart/remove/${cartId}`,
    method: 'delete'
  });
}

/**
 * 清空购物车
 * @param {number} userId - 用户ID
 * @returns {Promise} - 返回清空结果的Promise
 */
export function clearCart(userId) {
  return request({
    url: `/api/cart/clear/${userId}`,
    method: 'delete'
  });
}

/**
 * 提交订单
 * @param {Object} data - 订单数据
 * @param {number} data.user_id - 用户ID
 * @returns {Promise} - 返回提交结果的Promise
 */
export function checkout(data) {
  return request({
    url: '/api/cart/checkout',
    method: 'post',
    data
  });
}
