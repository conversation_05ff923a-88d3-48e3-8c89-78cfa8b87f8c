"""
景点推荐算法模块

该模块实现了各种景点排序和推荐算法，包括：
1. 基于热度的排序
2. 基于评分的排序
3. 基于协同过滤的推荐
4. 基于内容的推荐
5. 混合推荐

所有算法都使用纯Python实现，不依赖数据库查询。
"""

from collections import defaultdict, Counter
from typing import List, Dict, Tuple, Optional
import math


class RecommendationAlgorithms:
    """景点推荐算法类"""

    @staticmethod
    def sort_by_popularity(locations: List[Dict], limit: Optional[int] = None) -> List[Dict]:
        """
        按热度排序景点 - 使用优化的基数排序算法

        Args:
            locations: 景点列表
            limit: 返回结果数量限制

        Returns:
            按热度排序的景点列表
        """
        print(f"开始按热度排序 {len(locations)} 个景点")

        # 检查是否包含北京邮电大学
        bupt_locations = [loc for loc in locations if loc.get('name') == '北京邮电大学']
        print(f"排序前包含 {len(bupt_locations)} 个北京邮电大学")
        if bupt_locations:
            print(f"北京邮电大学的热度: {bupt_locations[0].get('popularity')}")

        # 如果只需要前K个结果，使用堆排序优化
        if limit and limit < len(locations) * 0.1:  # 当limit小于总数的10%时使用堆排序
            result = RecommendationAlgorithms._heap_sort_top_k(
                locations,
                key_func=lambda x: x.get('popularity', 0) or 0,
                k=limit,
                reverse=True
            )
        else:
            # 使用优化的快速排序算法
            result = RecommendationAlgorithms._optimized_quick_sort(
                locations,
                key_func=lambda x: x.get('popularity', 0) or 0,
                reverse=True
            )

        print(f"排序后，前5个景点的热度: {[(loc.get('name'), loc.get('popularity')) for loc in result[:5]]}")

        # 检查排序后是否包含北京邮电大学
        bupt_locations_after = [loc for loc in result if loc.get('name') == '北京邮电大学']
        print(f"排序后包含 {len(bupt_locations_after)} 个北京邮电大学")
        if bupt_locations_after:
            bupt_index = result.index(bupt_locations_after[0])
            print(f"北京邮电大学在排序后的位置: {bupt_index}")

        # 应用限制（如果还没有应用）
        if limit and len(result) > limit:
            result = result[:limit]
            print(f"应用限制 {limit}，返回 {len(result)} 个景点")

            # 检查限制后是否包含北京邮电大学
            bupt_locations_limit = [loc for loc in result if loc.get('name') == '北京邮电大学']
            print(f"限制后包含 {len(bupt_locations_limit)} 个北京邮电大学")

        return result

    @staticmethod
    def sort_by_rating(locations: List[Dict], limit: Optional[int] = None) -> List[Dict]:
        """
        按评分排序景点 - 使用优化的归并排序算法

        Args:
            locations: 景点列表
            limit: 返回结果数量限制

        Returns:
            按评分排序的景点列表
        """
        # 如果只需要前K个结果，使用堆排序优化
        if limit and limit < len(locations) * 0.1:  # 当limit小于总数的10%时使用堆排序
            result = RecommendationAlgorithms._heap_sort_top_k(
                locations,
                key_func=lambda x: x.get('evaluation', 0),
                k=limit,
                reverse=True
            )
        else:
            # 使用优化的归并排序算法（稳定排序）
            result = RecommendationAlgorithms._optimized_merge_sort(
                locations,
                key_func=lambda x: x.get('evaluation', 0),
                reverse=True
            )

        # 应用限制（如果还没有应用）
        if limit and len(result) > limit:
            result = result[:limit]

        return result

    @staticmethod
    def filter_by_type(locations: List[Dict], location_type: int) -> List[Dict]:
        """
        按类型过滤景点

        Args:
            locations: 景点列表
            location_type: 景点类型

        Returns:
            过滤后的景点列表
        """
        return [loc for loc in locations if loc.get('type') == location_type]

    @staticmethod
    def collaborative_filtering(locations: List[Dict], user_history: Dict[int, int],
                               all_users_history: Dict[int, Dict[int, int]],
                               limit: Optional[int] = None) -> List[Dict]:
        """
        基于协同过滤的景点推荐

        Args:
            locations: 景点列表
            user_history: 用户浏览历史 {location_id: count}
            all_users_history: 所有用户的浏览历史 {user_id: {location_id: count}}
            limit: 返回结果数量限制

        Returns:
            推荐的景点列表
        """
        # 如果用户没有浏览历史，返回热门景点
        if not user_history:
            return RecommendationAlgorithms.sort_by_popularity(locations, limit)

        # 用户已浏览的景点ID集合
        user_locations = set(user_history.keys())

        # 计算用户间的相似度
        user_id = -1  # 当前用户的ID，这里假设为-1
        similar_users = RecommendationAlgorithms._find_similar_users(
            user_id, user_history, all_users_history, limit=20
        )

        # 如果没有相似用户，返回热门景点
        if not similar_users:
            return RecommendationAlgorithms.sort_by_popularity(locations, limit)

        # 计算所有景点的得分
        location_scores = defaultdict(float)

        # 对于每个相似用户
        for similar_user_id, similarity in similar_users:
            # 获取他们的浏览历史
            similar_user_history = all_users_history.get(similar_user_id, {})

            # 对于他们浏览的每个景点
            for location_id, count in similar_user_history.items():
                # 跳过用户已浏览的景点
                if location_id in user_locations:
                    continue

                # 添加加权得分
                location_scores[location_id] += similarity * count

        # 如果没有推荐结果，返回热门景点
        if not location_scores:
            return RecommendationAlgorithms.sort_by_popularity(locations, limit)

        # 创建location_id到location的映射
        location_map = {loc.get('location_id'): loc for loc in locations}

        # 使用自实现的堆排序获取得分最高的景点
        top_location_ids = RecommendationAlgorithms._custom_heap_top_k(
            list(location_scores.items()),
            limit if limit else len(location_scores),
            key_func=lambda x: x[1],
            reverse=True
        )

        # 转换为景点列表
        result = []
        for location_id, score in top_location_ids:
            location = location_map.get(location_id)
            if location:
                location_copy = location.copy()
                location_copy['collaborative_score'] = score
                result.append(location_copy)

        return result

    @staticmethod
    def _find_similar_users(user_id: int, user_history: Dict[int, int],
                           all_users_history: Dict[int, Dict[int, int]],
                           limit: int = 10) -> List[Tuple[int, float]]:
        """
        找到与指定用户相似的用户

        Args:
            user_id: 用户ID
            user_history: 用户浏览历史 {location_id: count}
            all_users_history: 所有用户的浏览历史 {user_id: {location_id: count}}
            limit: 返回结果数量限制

        Returns:
            相似用户列表 [(user_id, similarity)]
        """
        similarities = []

        # 对于每个用户
        for other_id, other_history in all_users_history.items():
            # 跳过自己
            if other_id == user_id:
                continue

            # 计算余弦相似度
            similarity = RecommendationAlgorithms._cosine_similarity(user_history, other_history)

            # 添加到结果
            if similarity > 0:
                similarities.append((other_id, similarity))

        # 按相似度排序
        similarities.sort(key=lambda x: x[1], reverse=True)

        # 应用限制
        if limit:
            similarities = similarities[:limit]

        return similarities

    @staticmethod
    def _cosine_similarity(vec1: Dict[int, int], vec2: Dict[int, int]) -> float:
        """
        计算两个向量的余弦相似度

        Args:
            vec1: 第一个向量 {id: value}
            vec2: 第二个向量 {id: value}

        Returns:
            余弦相似度
        """
        # 找到共同的键
        common_keys = set(vec1.keys()) & set(vec2.keys())

        # 如果没有共同的键，相似度为0
        if not common_keys:
            return 0.0

        # 计算点积
        dot_product = sum(vec1[k] * vec2[k] for k in common_keys)

        # 计算向量模长
        norm1 = math.sqrt(sum(v * v for v in vec1.values()))
        norm2 = math.sqrt(sum(v * v for v in vec2.values()))

        # 避免除以0
        if norm1 == 0 or norm2 == 0:
            return 0.0

        # 计算余弦相似度
        return dot_product / (norm1 * norm2)

    @staticmethod
    def content_based_filtering(locations: List[Dict], user_history: Dict[int, int],
                               limit: Optional[int] = None) -> List[Dict]:
        """
        基于内容的景点推荐

        Args:
            locations: 景点列表
            user_history: 用户浏览历史 {location_id: count}
            limit: 返回结果数量限制

        Returns:
            推荐的景点列表
        """
        # 如果用户没有浏览历史，返回热门景点
        if not user_history:
            return RecommendationAlgorithms.sort_by_popularity(locations, limit)

        # 用户已浏览的景点ID集合
        user_locations = set(user_history.keys())

        # 创建location_id到location的映射
        location_map = {loc.get('location_id'): loc for loc in locations}

        # 分析用户偏好
        location_types = Counter()
        keywords = Counter()

        for location_id, count in user_history.items():
            location = location_map.get(location_id)
            if location:
                # 统计景点类型
                location_types[location.get('type')] += count

                # 统计关键词
                if location.get('keyword'):
                    for keyword in location.get('keyword', '').split(','):
                        keywords[keyword.strip()] += count

        # 获取最常见的景点类型和关键词
        common_types = [t for t, _ in location_types.most_common()]
        common_keywords = [k for k, _ in keywords.most_common(10)]  # 限制为前10个关键词

        # 计算所有景点的得分
        location_scores = defaultdict(float)

        for location in locations:
            location_id = location.get('location_id')

            # 跳过用户已浏览的景点
            if location_id in user_locations:
                continue

            score = 0.0

            # 类型匹配
            if location.get('type') in common_types:
                # 更常见的类型得分更高
                type_rank = common_types.index(location.get('type'))
                type_score = 1.0 / (type_rank + 1)  # 最常见的为1.0，第二常见的为0.5，依此类推
                score += type_score

            # 关键词匹配
            if location.get('keyword'):
                location_keywords = [k.strip() for k in location.get('keyword', '').split(',')]
                for keyword in location_keywords:
                    if keyword in common_keywords:
                        # 更常见的关键词得分更高
                        keyword_rank = common_keywords.index(keyword)
                        keyword_score = 1.0 / (keyword_rank + 1)
                        score += keyword_score

            # 添加热度因素（小权重）
            score += (location.get('popularity', 0) or 0) / 1000

            # 如果得分为正，添加到结果
            if score > 0:
                location_scores[location_id] = score

        # 如果没有推荐结果，返回热门景点
        if not location_scores:
            return RecommendationAlgorithms.sort_by_popularity(locations, limit)

        # 使用自实现的堆排序获取得分最高的景点
        top_location_ids = RecommendationAlgorithms._custom_heap_top_k(
            list(location_scores.items()),
            limit if limit else len(location_scores),
            key_func=lambda x: x[1],
            reverse=True
        )

        # 转换为景点列表
        result = []
        for location_id, score in top_location_ids:
            location = location_map.get(location_id)
            if location:
                location_copy = location.copy()
                location_copy['content_score'] = score
                result.append(location_copy)

        return result

    @staticmethod
    def hybrid_recommendation(locations: List[Dict], user_history: Dict[int, int],
                             all_users_history: Dict[int, Dict[int, int]],
                             weights: Dict[str, float] = None,
                             limit: Optional[int] = None) -> List[Dict]:
        """
        混合推荐算法

        Args:
            locations: 景点列表
            user_history: 用户浏览历史 {location_id: count}
            all_users_history: 所有用户的浏览历史 {user_id: {location_id: count}}
            weights: 各个推荐方法的权重 {'collaborative': w1, 'content': w2, 'popularity': w3}
            limit: 返回结果数量限制

        Returns:
            推荐的景点列表
        """
        # 设置默认权重
        if weights is None:
            weights = {
                'collaborative': 1.0,
                'content': 1.0,
                'popularity': 0.5
            }

        # 如果用户没有浏览历史，返回热门景点
        if not user_history:
            return RecommendationAlgorithms.sort_by_popularity(locations, limit)

        # 用户已浏览的景点ID集合
        user_locations = set(user_history.keys())

        # 创建location_id到location的映射
        location_map = {loc.get('location_id'): loc for loc in locations}

        # 获取协同过滤推荐结果
        collaborative_recs = RecommendationAlgorithms.collaborative_filtering(
            locations, user_history, all_users_history, limit=None
        )
        collaborative_scores = {
            loc.get('location_id'): loc.get('collaborative_score', 0)
            for loc in collaborative_recs
        }

        # 获取基于内容的推荐结果
        content_recs = RecommendationAlgorithms.content_based_filtering(
            locations, user_history, limit=None
        )
        content_scores = {
            loc.get('location_id'): loc.get('content_score', 0)
            for loc in content_recs
        }

        # 获取热门景点
        popular_recs = RecommendationAlgorithms.sort_by_popularity(locations, limit=None)
        # 归一化热度得分
        max_popularity = max((loc.get('popularity', 0) or 0) for loc in popular_recs) if popular_recs else 1
        popular_scores = {
            loc.get('location_id'): (loc.get('popularity', 0) or 0) / max_popularity
            for loc in popular_recs
        }

        # 计算混合得分
        hybrid_scores = defaultdict(float)

        # 获取所有景点ID
        all_location_ids = set()
        all_location_ids.update(collaborative_scores.keys())
        all_location_ids.update(content_scores.keys())
        all_location_ids.update(popular_scores.keys())

        # 跳过用户已浏览的景点
        all_location_ids = all_location_ids - user_locations

        for location_id in all_location_ids:
            # 获取各个方法的得分（默认为0）
            collaborative_score = collaborative_scores.get(location_id, 0) * weights['collaborative']
            content_score = content_scores.get(location_id, 0) * weights['content']
            popularity_score = popular_scores.get(location_id, 0) * weights['popularity']

            # 计算加权和
            total_weight = weights['collaborative'] + weights['content'] + weights['popularity']
            hybrid_score = (collaborative_score + content_score + popularity_score) / total_weight

            hybrid_scores[location_id] = hybrid_score

        # 使用自实现的堆排序获取得分最高的景点
        top_location_ids = RecommendationAlgorithms._custom_heap_top_k(
            list(hybrid_scores.items()),
            limit if limit else len(hybrid_scores),
            key_func=lambda x: x[1],
            reverse=True
        )

        # 转换为景点列表
        result = []
        for location_id, score in top_location_ids:
            location = location_map.get(location_id)
            if location:
                location_copy = location.copy()
                location_copy['hybrid_score'] = score
                result.append(location_copy)

        return result

    @staticmethod
    def enhanced_hybrid_recommendation(locations: List[Dict], user_history: Dict[int, int],
                                     user_favorites: List[int], all_users_history: Dict[int, Dict[int, int]],
                                     all_users_favorites: Dict[int, List[int]],
                                     weights: Dict[str, float] = None,
                                     limit: Optional[int] = None) -> List[Dict]:
        """
        增强的混合推荐算法（考虑收藏数据）

        Args:
            locations: 景点列表
            user_history: 用户浏览历史 {location_id: count}
            user_favorites: 用户收藏的景点ID列表
            all_users_history: 所有用户的浏览历史 {user_id: {location_id: count}}
            all_users_favorites: 所有用户的收藏数据 {user_id: [location_id1, location_id2, ...]}
            weights: 各个推荐方法的权重
            limit: 返回结果数量限制

        Returns:
            推荐的景点列表
        """
        # 设置默认权重
        if weights is None:
            weights = {
                'collaborative': 1.0,
                'content': 1.0,
                'popularity': 0.5,
                'favorites': 1.5  # 收藏权重更高
            }

        # 如果用户没有任何行为数据，返回热门景点
        if not user_history and not user_favorites:
            return RecommendationAlgorithms.sort_by_popularity(locations, limit)

        # 用户已浏览和收藏的景点ID集合
        user_locations = set(user_history.keys()) | set(user_favorites)

        # 构建景点映射
        location_map = {loc['location_id']: loc for loc in locations}

        # 1. 协同过滤推荐（基于浏览历史）
        collaborative_scores = {}
        if user_history:
            collaborative_scores = RecommendationAlgorithms._collaborative_filtering_scores(
                user_history, all_users_history, location_map
            )

        # 2. 基于收藏的协同过滤推荐
        favorites_collaborative_scores = {}
        if user_favorites:
            favorites_collaborative_scores = RecommendationAlgorithms._favorites_collaborative_filtering_scores(
                user_favorites, all_users_favorites, location_map
            )

        # 3. 内容过滤推荐
        content_scores = {}
        if user_history or user_favorites:
            # 合并用户偏好数据
            combined_preferences = user_history.copy()
            for fav_id in user_favorites:
                combined_preferences[fav_id] = combined_preferences.get(fav_id, 0) + 3  # 收藏权重为3

            content_scores = RecommendationAlgorithms._content_filtering_scores(
                combined_preferences, location_map
            )

        # 4. 热度推荐
        popular_scores = RecommendationAlgorithms._popularity_scores(location_map)

        # 计算混合得分
        hybrid_scores = defaultdict(float)

        # 获取所有景点ID
        all_location_ids = set()
        all_location_ids.update(collaborative_scores.keys())
        all_location_ids.update(favorites_collaborative_scores.keys())
        all_location_ids.update(content_scores.keys())
        all_location_ids.update(popular_scores.keys())

        # 跳过用户已浏览和收藏的景点
        all_location_ids = all_location_ids - user_locations

        for location_id in all_location_ids:
            # 获取各个方法的得分（默认为0）
            collaborative_score = collaborative_scores.get(location_id, 0) * weights['collaborative']
            favorites_score = favorites_collaborative_scores.get(location_id, 0) * weights['favorites']
            content_score = content_scores.get(location_id, 0) * weights['content']
            popularity_score = popular_scores.get(location_id, 0) * weights['popularity']

            # 计算加权和
            total_weight = sum(weights.values())
            hybrid_score = (collaborative_score + favorites_score + content_score + popularity_score) / total_weight

            hybrid_scores[location_id] = hybrid_score

        # 使用自实现的堆排序获取得分最高的景点
        top_location_ids = RecommendationAlgorithms._custom_heap_top_k(
            list(hybrid_scores.items()),
            limit if limit else len(hybrid_scores),
            key_func=lambda x: x[1],
            reverse=True
        )

        # 转换为景点列表
        result = []
        for location_id, score in top_location_ids:
            location = location_map.get(location_id)
            if location:
                location_copy = location.copy()
                location_copy['enhanced_hybrid_score'] = score
                result.append(location_copy)

        return result

    @staticmethod
    def _favorites_collaborative_filtering_scores(user_favorites: List[int],
                                                all_users_favorites: Dict[int, List[int]],
                                                location_map: Dict[int, Dict]) -> Dict[int, float]:
        """
        基于收藏数据的协同过滤评分

        Args:
            user_favorites: 用户收藏的景点ID列表
            all_users_favorites: 所有用户的收藏数据
            location_map: 景点映射

        Returns:
            景点ID到评分的映射
        """
        if not user_favorites or not all_users_favorites:
            return {}

        user_favorites_set = set(user_favorites)
        scores = defaultdict(float)

        # 计算与其他用户的相似度
        for other_user_id, other_favorites in all_users_favorites.items():
            if not other_favorites:
                continue

            other_favorites_set = set(other_favorites)

            # 计算Jaccard相似度
            intersection = user_favorites_set & other_favorites_set
            union = user_favorites_set | other_favorites_set

            if len(union) == 0:
                continue

            similarity = len(intersection) / len(union)

            # 如果相似度太低，跳过
            if similarity < 0.1:
                continue

            # 为相似用户收藏但当前用户未收藏的景点加分
            for location_id in other_favorites:
                if location_id not in user_favorites_set and location_id in location_map:
                    scores[location_id] += similarity

        # 归一化得分
        if scores:
            max_score = max(scores.values())
            if max_score > 0:
                for location_id in scores:
                    scores[location_id] /= max_score

        return dict(scores)

    @staticmethod
    def _content_filtering_scores(user_preferences: Dict[int, int], location_map: Dict[int, Dict]) -> Dict[int, float]:
        """
        基于内容的过滤评分

        Args:
            user_preferences: 用户偏好数据 {location_id: weight}
            location_map: 景点映射

        Returns:
            景点ID到评分的映射
        """
        if not user_preferences:
            return {}

        # 分析用户偏好
        from collections import Counter
        location_types = Counter()
        keywords = Counter()

        for location_id, weight in user_preferences.items():
            location = location_map.get(location_id)
            if location:
                # 统计景点类型偏好
                location_type = location.get('type')
                if location_type is not None:
                    location_types[location_type] += weight

                # 统计关键词偏好
                if location.get('keyword'):
                    for keyword in location.get('keyword', '').split(','):
                        keywords[keyword.strip()] += weight

        # 获取最常见的类型和关键词
        common_types = [t for t, _ in location_types.most_common()]
        common_keywords = [k for k, _ in keywords.most_common(10)]

        # 计算所有景点的内容匹配得分
        scores = {}
        user_location_ids = set(user_preferences.keys())

        for location_id, location in location_map.items():
            # 跳过用户已交互的景点
            if location_id in user_location_ids:
                continue

            score = 0.0

            # 类型匹配得分
            location_type = location.get('type')
            if location_type in common_types:
                type_rank = common_types.index(location_type)
                type_score = 1.0 / (type_rank + 1)  # 排名越高得分越高
                score += type_score * 0.4  # 类型权重40%

            # 关键词匹配得分
            if location.get('keyword'):
                location_keywords = [k.strip() for k in location.get('keyword', '').split(',')]
                keyword_score = 0.0
                for keyword in location_keywords:
                    if keyword in common_keywords:
                        keyword_rank = common_keywords.index(keyword)
                        keyword_score += 1.0 / (keyword_rank + 1)
                score += keyword_score * 0.6  # 关键词权重60%

            # 添加少量热度因子
            score += (location.get('popularity', 0) or 0) / 10000

            if score > 0:
                scores[location_id] = score

        # 归一化得分
        if scores:
            max_score = max(scores.values())
            if max_score > 0:
                for location_id in scores:
                    scores[location_id] /= max_score

        return scores

    @staticmethod
    def _collaborative_filtering_scores(user_history: Dict[int, int],
                                      all_users_history: Dict[int, Dict[int, int]],
                                      location_map: Dict[int, Dict]) -> Dict[int, float]:
        """
        协同过滤评分

        Args:
            user_history: 用户浏览历史
            all_users_history: 所有用户浏览历史
            location_map: 景点映射

        Returns:
            景点ID到评分的映射
        """
        if not user_history or not all_users_history:
            return {}

        user_locations = set(user_history.keys())
        scores = defaultdict(float)

        # 计算与其他用户的相似度
        for other_user_id, other_history in all_users_history.items():
            if not other_history:
                continue

            # 计算余弦相似度
            similarity = RecommendationAlgorithms._calculate_cosine_similarity(user_history, other_history)

            # 如果相似度太低，跳过
            if similarity < 0.1:
                continue

            # 为相似用户浏览但当前用户未浏览的景点加分
            for location_id, count in other_history.items():
                if location_id not in user_locations and location_id in location_map:
                    scores[location_id] += similarity * count

        # 归一化得分
        if scores:
            max_score = max(scores.values())
            if max_score > 0:
                for location_id in scores:
                    scores[location_id] /= max_score

        return dict(scores)

    @staticmethod
    def _popularity_scores(location_map: Dict[int, Dict]) -> Dict[int, float]:
        """
        热度评分

        Args:
            location_map: 景点映射

        Returns:
            景点ID到评分的映射
        """
        scores = {}
        max_popularity = max((loc.get('popularity', 0) or 0) for loc in location_map.values()) if location_map else 1

        for location_id, location in location_map.items():
            popularity = location.get('popularity', 0) or 0
            scores[location_id] = popularity / max_popularity if max_popularity > 0 else 0

        return scores

    @staticmethod
    def _calculate_cosine_similarity(history1: Dict[int, int], history2: Dict[int, int]) -> float:
        """
        计算两个用户浏览历史的余弦相似度

        Args:
            history1: 用户1的浏览历史
            history2: 用户2的浏览历史

        Returns:
            余弦相似度值 (0-1)
        """
        # 获取共同浏览的景点
        common_locations = set(history1.keys()) & set(history2.keys())

        if not common_locations:
            return 0.0

        # 计算向量的点积
        dot_product = sum(history1[loc] * history2[loc] for loc in common_locations)

        # 计算向量的模长
        norm1 = sum(count ** 2 for count in history1.values()) ** 0.5
        norm2 = sum(count ** 2 for count in history2.values()) ** 0.5

        if norm1 == 0 or norm2 == 0:
            return 0.0

        return dot_product / (norm1 * norm2)

    # ===== 高效排序算法实现 =====

    @staticmethod
    def _custom_heap_top_k(items: List, k: int, key_func, reverse: bool = False) -> List:
        """
        自实现的堆排序Top-K算法
        不使用Python内置的heapq模块

        Args:
            items: 待排序的元素列表
            k: 需要的前K个元素
            key_func: 排序键函数
            reverse: 是否降序排序

        Returns:
            排序后的前K个元素
        """
        if not items or k <= 0:
            return []

        if k >= len(items):
            return RecommendationAlgorithms._optimized_quick_sort(items, key_func, reverse)

        # 构建最小堆（用于降序）或最大堆（用于升序）
        heap = []

        for item in items:
            key_value = key_func(item)

            if len(heap) < k:
                # 堆未满，直接添加
                heap.append((key_value, item))
                RecommendationAlgorithms._heapify_up(heap, len(heap) - 1, reverse)
            else:
                # 堆已满，比较并可能替换堆顶
                if reverse:  # 降序，使用最小堆
                    if key_value > heap[0][0]:
                        heap[0] = (key_value, item)
                        RecommendationAlgorithms._heapify_down(heap, 0, reverse)
                else:  # 升序，使用最大堆
                    if key_value < heap[0][0]:
                        heap[0] = (key_value, item)
                        RecommendationAlgorithms._heapify_down(heap, 0, reverse)

        # 提取结果并排序
        result = [item for _, item in heap]
        return RecommendationAlgorithms._optimized_quick_sort(result, key_func, reverse)

    @staticmethod
    def _heapify_up(heap: List, index: int, reverse: bool):
        """堆的上浮操作"""
        while index > 0:
            parent = (index - 1) // 2
            if reverse:  # 最小堆
                if heap[index][0] >= heap[parent][0]:
                    break
            else:  # 最大堆
                if heap[index][0] <= heap[parent][0]:
                    break

            heap[index], heap[parent] = heap[parent], heap[index]
            index = parent

    @staticmethod
    def _heapify_down(heap: List, index: int, reverse: bool):
        """堆的下沉操作"""
        size = len(heap)
        while True:
            target = index
            left = 2 * index + 1
            right = 2 * index + 2

            if reverse:  # 最小堆
                if left < size and heap[left][0] < heap[target][0]:
                    target = left
                if right < size and heap[right][0] < heap[target][0]:
                    target = right
            else:  # 最大堆
                if left < size and heap[left][0] > heap[target][0]:
                    target = left
                if right < size and heap[right][0] > heap[target][0]:
                    target = right

            if target == index:
                break

            heap[index], heap[target] = heap[target], heap[index]
            index = target

    @staticmethod
    def _heap_sort_top_k(items: List[Dict], key_func, k: int, reverse: bool = False) -> List[Dict]:
        """
        使用堆排序获取Top-K元素
        时间复杂度: O(n log k)，空间复杂度: O(k)

        Args:
            items: 待排序的元素列表
            key_func: 排序键函数
            k: 需要的前K个元素
            reverse: 是否降序排序

        Returns:
            排序后的前K个元素
        """
        if not items or k <= 0:
            return []

        if k >= len(items):
            # 如果k大于等于总数，使用完整排序
            return RecommendationAlgorithms._optimized_quick_sort(items, key_func, reverse)

        # 使用自实现的堆排序
        return RecommendationAlgorithms._custom_heap_top_k(items, k, key_func, reverse)

    @staticmethod
    def _optimized_quick_sort(items: List[Dict], key_func, reverse: bool = False) -> List[Dict]:
        """
        优化的快速排序算法
        使用三路快排和随机pivot选择
        平均时间复杂度: O(n log n)，最坏时间复杂度: O(n²)

        Args:
            items: 待排序的元素列表
            key_func: 排序键函数
            reverse: 是否降序排序

        Returns:
            排序后的元素列表
        """
        if len(items) <= 1:
            return items.copy()

        # 复制列表避免修改原列表
        arr = items.copy()
        RecommendationAlgorithms._quick_sort_recursive(arr, 0, len(arr) - 1, key_func, reverse)
        return arr

    @staticmethod
    def _quick_sort_recursive(arr: List[Dict], low: int, high: int, key_func, reverse: bool):
        """快速排序递归实现"""
        if low < high:
            # 使用三路快排处理重复元素
            lt, gt = RecommendationAlgorithms._three_way_partition(arr, low, high, key_func, reverse)
            RecommendationAlgorithms._quick_sort_recursive(arr, low, lt - 1, key_func, reverse)
            RecommendationAlgorithms._quick_sort_recursive(arr, gt + 1, high, key_func, reverse)

    @staticmethod
    def _three_way_partition(arr: List[Dict], low: int, high: int, key_func, reverse: bool) -> tuple:
        """
        三路快排分区算法
        将数组分为三部分：小于pivot、等于pivot、大于pivot
        """
        import random

        # 随机选择pivot避免最坏情况
        pivot_idx = random.randint(low, high)
        arr[pivot_idx], arr[high] = arr[high], arr[pivot_idx]

        pivot_value = key_func(arr[high])

        lt = low  # 小于pivot的区域的右边界
        gt = high  # 大于pivot的区域的左边界
        i = low

        while i <= gt:
            current_value = key_func(arr[i])

            if reverse:  # 降序排序
                if current_value > pivot_value:
                    arr[i], arr[lt] = arr[lt], arr[i]
                    lt += 1
                    i += 1
                elif current_value < pivot_value:
                    arr[i], arr[gt] = arr[gt], arr[i]
                    gt -= 1
                else:
                    i += 1
            else:  # 升序排序
                if current_value < pivot_value:
                    arr[i], arr[lt] = arr[lt], arr[i]
                    lt += 1
                    i += 1
                elif current_value > pivot_value:
                    arr[i], arr[gt] = arr[gt], arr[i]
                    gt -= 1
                else:
                    i += 1

        return lt, gt

    @staticmethod
    def _optimized_merge_sort(items: List[Dict], key_func, reverse: bool = False) -> List[Dict]:
        """
        优化的归并排序算法（稳定排序）
        时间复杂度: O(n log n)，空间复杂度: O(n)

        Args:
            items: 待排序的元素列表
            key_func: 排序键函数
            reverse: 是否降序排序

        Returns:
            排序后的元素列表
        """
        if len(items) <= 1:
            return items.copy()

        # 复制列表避免修改原列表
        arr = items.copy()
        temp = [None] * len(arr)
        RecommendationAlgorithms._merge_sort_recursive(arr, temp, 0, len(arr) - 1, key_func, reverse)
        return arr

    @staticmethod
    def _merge_sort_recursive(arr: List[Dict], temp: List, low: int, high: int, key_func, reverse: bool):
        """归并排序递归实现"""
        if low < high:
            mid = (low + high) // 2
            RecommendationAlgorithms._merge_sort_recursive(arr, temp, low, mid, key_func, reverse)
            RecommendationAlgorithms._merge_sort_recursive(arr, temp, mid + 1, high, key_func, reverse)
            RecommendationAlgorithms._merge(arr, temp, low, mid, high, key_func, reverse)

    @staticmethod
    def _merge(arr: List[Dict], temp: List, low: int, mid: int, high: int, key_func, reverse: bool):
        """归并操作"""
        # 复制到临时数组
        for i in range(low, high + 1):
            temp[i] = arr[i]

        i, j, k = low, mid + 1, low

        # 归并两个有序子数组
        while i <= mid and j <= high:
            left_value = key_func(temp[i])
            right_value = key_func(temp[j])

            if reverse:  # 降序
                if left_value >= right_value:
                    arr[k] = temp[i]
                    i += 1
                else:
                    arr[k] = temp[j]
                    j += 1
            else:  # 升序
                if left_value <= right_value:
                    arr[k] = temp[i]
                    i += 1
                else:
                    arr[k] = temp[j]
                    j += 1
            k += 1

        # 复制剩余元素
        while i <= mid:
            arr[k] = temp[i]
            i += 1
            k += 1

        while j <= high:
            arr[k] = temp[j]
            j += 1
            k += 1