"""
推荐路由模块

该模块提供景点推荐的API接口，包括：
1. 为您推荐
2. 热门景点
3. 高评分景点
4. 全部景点
"""

from flask import Blueprint, request
from utils.response import success, error

recommend_bp = Blueprint('recommend', __name__)

@recommend_bp.route('', methods=['POST'])
def get_recommendations():
    """
    获取推荐地点，根据用户登录状态返回不同的推荐结果
    - 游客模式：按照热度返回前30个景点
    - 登录模式：进行基于内容的协同过滤
    """
    # 重定向到高级推荐系统
    from routes.advanced_recommend import get_for_you_recommendations
    return get_for_you_recommendations()

@recommend_bp.route('/popular', methods=['GET'])
@recommend_bp.route('/popularity', methods=['GET'])
def get_popular_locations():
    """
    获取热门地点推荐
    """
    # 重定向到高级推荐系统
    from routes.advanced_recommend import get_popular_locations
    return get_popular_locations()

@recommend_bp.route('/rating', methods=['GET'])
def get_top_rated_locations():
    """
    获取评分最高的地点推荐
    """
    # 重定向到高级推荐系统
    from routes.advanced_recommend import get_top_rated_locations
    return get_top_rated_locations()

@recommend_bp.route('/collaborative', methods=['POST'])
def get_collaborative_recommendations():
    """
    基于协同过滤的地点推荐
    """
    # 重定向到高级推荐系统
    from routes.advanced_recommend import get_collaborative_recommendations
    return get_collaborative_recommendations()

@recommend_bp.route('/all-locations', methods=['POST', 'GET'])
def get_all_locations_with_collaborative():
    """
    获取所有地点，按照热度排序
    """
    # 重定向到高级推荐系统
    from routes.advanced_recommend import get_all_locations_by_popularity
    return get_all_locations_by_popularity()

@recommend_bp.route('/all-locations-by-popularity', methods=['GET'])
def get_all_locations_by_popularity():
    """
    获取所有地点，按照热度排序
    """
    # 重定向到高级推荐系统
    from routes.advanced_recommend import get_all_locations_by_popularity
    return get_all_locations_by_popularity()

@recommend_bp.route('/content', methods=['POST'])
def get_content_recommendations():
    """
    基于内容的地点推荐
    """
    # 重定向到高级推荐系统
    from routes.advanced_recommend import get_content_recommendations
    return get_content_recommendations()

@recommend_bp.route('/hybrid', methods=['POST'])
def get_hybrid_recommendations():
    """
    混合推荐
    """
    # 重定向到高级推荐系统
    from routes.advanced_recommend import get_hybrid_recommendations
    return get_hybrid_recommendations()
