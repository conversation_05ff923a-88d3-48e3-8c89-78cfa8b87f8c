import axios from 'axios';

// 创建axios实例
const instance = axios.create({
  baseURL: 'http://localhost:5000',
  timeout: 60000, // 增加到60秒，AI生成可能需要更长时间
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器
instance.interceptors.request.use(
  config => {
    // 从localStorage获取token
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;

    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器
instance.interceptors.response.use(
  response => {
    // 直接返回响应数据，而不是整个响应对象
    return response.data;
  },
  error => {
    if (error.response) {
      switch (error.response.status) {
        case 401: // 未授权
          // 清除token并跳转到登录页
          localStorage.removeItem('authToken');
          console.log("问题在这里")
         // window.location.href = '/login';
          break;
        case 403: // 权限不足
          console.error('权限不足');
          break;
        case 404: // 请求不存在
          console.error('请求的资源不存在');
          break;
        case 500: // 服务器错误
          console.error('服务器错误');
          break;
        default:
          console.error('其他错误');
      }
    }
    return Promise.reject(error);
  }
);

export default instance;