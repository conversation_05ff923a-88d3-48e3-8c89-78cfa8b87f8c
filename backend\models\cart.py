from utils.database import db
from datetime import datetime

class UserCart(db.Model):
    """用户购物车模型"""
    __tablename__ = 'user_cart'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON><PERSON>('users.user_id'), nullable=False)
    dish_name = db.Column(db.String(255), nullable=False)
    dish_price = db.Column(db.Float, nullable=False)
    dish_image = db.Column(db.String(255), nullable=True)
    restaurant_id = db.Column(db.Integer, db.<PERSON>ey('restaurant.id'), nullable=False)
    restaurant_name = db.Column(db.String(255), nullable=False)
    quantity = db.Column(db.Integer, default=1, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.now)

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'dish_name': self.dish_name,
            'dish_price': self.dish_price,
            'dish_image': self.dish_image,
            'restaurant_id': self.restaurant_id,
            'restaurant_name': self.restaurant_name,
            'quantity': self.quantity,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'total_price': round(self.dish_price * self.quantity, 2)
        }
