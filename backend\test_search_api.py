#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门测试搜索API的脚本
"""

import requests
import json
import time

# API基础URL
BASE_URL = "http://localhost:5000/api"

def test_api_endpoint(url, description):
    """测试单个API端点"""
    print(f"\n{'='*50}")
    print(f"测试: {description}")
    print(f"URL: {url}")
    print(f"{'='*50}")
    
    try:
        start_time = time.time()
        response = requests.get(url, timeout=10)
        end_time = time.time()
        
        print(f"状态码: {response.status_code}")
        print(f"响应时间: {(end_time - start_time)*1000:.2f}ms")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"响应类型: {type(data)}")
                
                if isinstance(data, list):
                    print(f"数组长度: {len(data)}")
                    if len(data) > 0:
                        print(f"第一个元素: {json.dumps(data[0], ensure_ascii=False, indent=2)}")
                        if len(data) > 1:
                            print(f"第二个元素: {json.dumps(data[1], ensure_ascii=False, indent=2)}")
                elif isinstance(data, dict):
                    print(f"对象键: {list(data.keys())}")
                    print(f"完整响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
                else:
                    print(f"响应内容: {data}")
                    
            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}")
                print(f"原始响应: {response.text[:500]}")
        else:
            print(f"错误响应: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败 - 请确保后端服务正在运行")
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_database_content():
    """测试数据库内容"""
    print(f"\n{'='*50}")
    print("测试数据库内容")
    print(f"{'='*50}")
    
    try:
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from app import app
        from models.location import Location
        from models.path_planning import Vertex
        
        with app.app_context():
            # 检查Location表
            print("\n📊 Location表统计:")
            total_locations = Location.query.count()
            attractions = Location.query.filter_by(type=1).count()
            schools = Location.query.filter_by(type=0).count()
            print(f"  总数: {total_locations}")
            print(f"  景点: {attractions}")
            print(f"  学校: {schools}")
            
            # 查找热门景点
            popular_locations = Location.query.order_by(Location.popularity.desc()).limit(10).all()
            print(f"\n🔥 前10个热门景点:")
            for i, loc in enumerate(popular_locations, 1):
                print(f"  {i}. {loc.name} (热度: {loc.popularity}, 评分: {loc.evaluation})")
            
            # 查找包含"故宫"的记录
            gugong_locations = Location.query.filter(Location.name.ilike('%故宫%')).all()
            print(f"\n🏛️ 包含'故宫'的Location记录 ({len(gugong_locations)}个):")
            for loc in gugong_locations:
                print(f"  - {loc.name} (ID: {loc.location_id}, 热度: {loc.popularity})")
            
            # 查找包含"天安门"的记录
            tiananmen_locations = Location.query.filter(Location.name.ilike('%天安门%')).all()
            print(f"\n🏛️ 包含'天安门'的Location记录 ({len(tiananmen_locations)}个):")
            for loc in tiananmen_locations:
                print(f"  - {loc.name} (ID: {loc.location_id}, 热度: {loc.popularity})")
            
            # 检查Vertex表
            print(f"\n📊 Vertex表统计:")
            total_vertices = Vertex.query.count()
            print(f"  总数: {total_vertices}")
            
            # 查找包含"故宫"的顶点
            gugong_vertices = Vertex.query.filter(Vertex.label.ilike('%故宫%')).all()
            print(f"\n🏛️ 包含'故宫'的Vertex记录 ({len(gugong_vertices)}个):")
            for vertex in gugong_vertices:
                print(f"  - {vertex.label} (ID: {vertex.vertex_id}, 类型: {vertex.type})")
                
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")

def main():
    print("🚀 开始测试搜索API功能...")
    
    # 测试数据库内容
    test_database_content()
    
    # 测试API端点
    test_cases = [
        # 热门景点API
        (f"{BASE_URL}/path/popular-locations?limit=10", "获取热门景点"),
        (f"{BASE_URL}/path/popular-locations?limit=5&type=1", "获取热门景点(仅景点类型)"),
        
        # 搜索景点API
        (f"{BASE_URL}/path/search-locations?name=故宫", "搜索故宫"),
        (f"{BASE_URL}/path/search-locations?name=天安门", "搜索天安门"),
        (f"{BASE_URL}/path/search-locations?name=北京&limit=5", "搜索北京(限制5个)"),
        (f"{BASE_URL}/path/search-locations?name=大学&type=0", "搜索大学(学校类型)"),
        
        # 原有搜索API (对比)
        (f"{BASE_URL}/path/search-by-name?name=故宫", "原有API搜索故宫"),
        (f"{BASE_URL}/path/search-by-name?name=天安门", "原有API搜索天安门"),
        
        # 地点建议API
        (f"{BASE_URL}/path/location-suggestions?q=北京&limit=5", "地点建议"),
        
        # 健康检查
        (f"{BASE_URL}/health", "健康检查"),
    ]
    
    for url, description in test_cases:
        test_api_endpoint(url, description)
        time.sleep(0.5)  # 避免请求过快
    
    print(f"\n{'='*50}")
    print("✅ 测试完成")
    print(f"{'='*50}")
    
    # 总结
    print("\n📋 问题排查建议:")
    print("1. 如果连接失败，请确保后端服务正在运行: python app.py")
    print("2. 如果数据为空，请检查数据库中是否有景点数据")
    print("3. 如果搜索无结果，请检查数据库中景点名称是否正确")
    print("4. 如果小程序无法访问，请检查IP地址和网络配置")
    print("5. 查看后端控制台日志获取更多信息")

if __name__ == "__main__":
    main()
