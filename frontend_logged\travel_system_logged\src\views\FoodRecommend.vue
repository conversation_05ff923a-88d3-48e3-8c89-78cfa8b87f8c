<template>
  <div class="food-recommend-container">
    <div class="banner">
      <h1>美食推荐</h1>
      <p>发现当地特色美食，品尝舌尖上的旅行</p>
    </div>

    <div class="filter-container">
      <el-tabs v-model="recommendationType" @tab-click="handleRecommendationTypeChange">
        <el-tab-pane name="smart">
          <template #label>
            <el-tooltip content="基于协同过滤算法，根据您的收藏和评分为您推荐相似用户喜欢的餐馆" placement="top">
              <span>个性化推荐</span>
            </el-tooltip>
          </template>
        </el-tab-pane>
        <el-tab-pane label="热门美食" name="popularity"></el-tab-pane>
        <el-tab-pane label="高分美食" name="rating"></el-tab-pane>
        <el-tab-pane label="特色美食" name="special">
          <template #label>
            <el-tooltip content="评分4.0以上的高品质餐馆" placement="top">
              <span>特色美食</span>
            </el-tooltip>
          </template>
        </el-tab-pane>
        <el-tab-pane label="附近美食" name="nearby"></el-tab-pane>
        <el-tab-pane label="我收藏的餐馆" name="favorites"></el-tab-pane>
        <el-tab-pane label="我的购物车" name="cart">
          <template #label>
            <el-badge :value="cartItemCount" :hidden="cartItemCount === 0">
              <span>我的购物车</span>
            </el-badge>
          </template>
        </el-tab-pane>
      </el-tabs>

      <div class="search-box">
        <el-autocomplete
          v-model="searchKeyword"
          :fetch-suggestions="querySearchAsync"
          placeholder="搜索美食名称、菜系或菜品"
          prefix-icon="Search"
          clearable
          @select="handleSelect"
          @keyup.enter="handleSearch"
          :trigger-on-focus="false"
          :highlight-first-item="true"
        >
          <template #default="{ item }">
            <div class="suggestion-item">
              <div class="suggestion-img" v-if="item.image_url">
                <img :src="`http://localhost:5000${item.image_url}`" alt="" />
              </div>
              <div class="suggestion-info">
                <div class="suggestion-name">{{ item.name }}</div>
                <div class="suggestion-type">{{ item.cuisine_type }}</div>
              </div>
            </div>
          </template>
        </el-autocomplete>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
      </div>

      <div class="sort-options">
        <span class="filter-label">排序方式:</span>
        <el-radio-group v-model="sortBy" @change="handleSortChange">
          <el-radio-button label="popularity">人气</el-radio-button>
          <el-radio-button label="rating">评分</el-radio-button>
          <el-radio-button label="price">价格</el-radio-button>
          <el-radio-button label="distance" v-if="recommendationType === 'nearby'">距离</el-radio-button>
        </el-radio-group>
      </div>

      <div class="filter-tags">
        <span class="filter-label">菜系筛选:</span>
        <el-check-tag
          v-for="tag in cuisineTypes"
          :key="tag"
          :checked="selectedCuisineType === tag"
          @change="(checked) => handleCuisineTypeChange(tag, checked)"
        >
          {{ tag }}
        </el-check-tag>
      </div>

      <div v-if="recommendationType === 'nearby'" class="location-filter">
        <span class="filter-label">当前位置:</span>

        <div class="location-inputs">
          <el-tooltip content="横向位置坐标" placement="top">
            <el-input-number
              v-model="locationX"
              :min="-10000"
              :max="10000"
              placeholder="X坐标"
              @change="handleLocationChange"
            >
              <template #prepend>X坐标</template>
            </el-input-number>
          </el-tooltip>
          <el-tooltip content="纵向位置坐标" placement="top">
            <el-input-number
              v-model="locationY"
              :min="-10000"
              :max="10000"
              placeholder="Y坐标"
              @change="handleLocationChange"
            >
              <template #prepend>Y坐标</template>
            </el-input-number>
          </el-tooltip>
          <el-tooltip content="搜索范围（单位：米）" placement="top">
            <el-input-number
              v-model="maxDistance"
              :min="0"
              :max="10000"
              placeholder="最大距离"
              @change="handleLocationChange"
            >
              <template #prepend>最大距离</template>
            </el-input-number>
          </el-tooltip>
        </div>
      </div>
    </div>

    <!-- 购物车标签页 -->
    <div v-if="recommendationType === 'cart'" class="cart-container">
      <ShoppingCart :userId="userId" @update-count="fetchCartItemCount" />
    </div>

    <!-- 美食列表 -->
    <div v-else v-loading="loading" class="food-list">
      <template v-if="filteredFoods.length > 0">
        <div class="food-card" v-for="food in filteredFoods" :key="food.food_id" @click="goToFoodDetail(food.food_id)">
          <div class="food-image">
            <img :src="food.image_url || defaultFoodImage" :alt="food.name" />
            <div v-if="food.is_favorite" class="favorite-badge">
              <i class="el-icon-star-on"></i> 已收藏
            </div>
          </div>
          <div class="food-info">
            <div class="food-title">
              <h3>{{ food.name }}</h3>
              <div v-if="food.is_favorite" class="favorite-icon">
                <i class="el-icon-star-on"></i>
              </div>
            </div>
            <div class="food-tags">
              <el-tag size="small" type="success">{{ food.cuisine_type }}</el-tag>
              <el-tag size="small" type="info">{{ food.region }}</el-tag>
              <el-tag v-if="food.recommendation_type === 'collaborative'" size="small" type="danger">协同推荐</el-tag>
            </div>
            <div class="food-rating">
              <el-rate v-model="food.rating" disabled text-color="#ff9900" />
              <span>{{ food.rating.toFixed(1) }}</span>
            </div>
            <div class="food-stats">
              <el-tag size="small" type="info" effect="plain">
                <i class="el-icon-view"></i> {{ food.number_of_view || 0 }}
              </el-tag>
            </div>
            <p class="food-description">{{ food.description }}</p>

            <!-- 添加菜品展示 -->
            <div class="food-dishes" v-if="food.dishes && food.dishes.length > 0">
              <h4>招牌菜品:</h4>
              <div class="dish-tags">
                <el-tag
                  v-for="(dish, index) in food.dishes.filter(d => d && d.name)"
                  :key="index"
                  size="small"
                  effect="plain"
                  :type="index === 0 ? 'danger' : index === 1 ? 'warning' : 'success'"
                  class="dish-tag"
                >
                  {{ dish.name }} (¥{{ dish.price }})
                </el-tag>
              </div>
            </div>
          </div>
        </div>
      </template>
      <el-empty v-else description="暂无符合条件的美食" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import {
  getFoodRecommendations,
  getPopularFood,
  getSpecialFood,
  searchFood,
  getNearbyFood,
  getCuisineTypes,
  getUserFavoriteRestaurants,
  getRestaurantSuggestions,
  getCollaborativeRecommendations
} from '@/api/food';
import { getUserCart } from '@/api/cart';
import ShoppingCart from '@/components/ShoppingCart.vue';
import defaultFoodImage from '@/assets/forbidden_city.jpg';

const router = useRouter();
const loading = ref(false);
const allFoods = ref([]);
const filteredFoods = ref([]);
const recommendationType = ref('smart');
const searchKeyword = ref('');
const sortBy = ref('popularity');
// 默认使用降序排序，不再提供给用户选择
const sortOrder = ref('desc');
const selectedCuisineType = ref('');
const locationX = ref(500);  // 默认位置X坐标
const locationY = ref(500);  // 默认位置Y坐标
const maxDistance = ref(5000);  // 默认最大距离
const cartItemCount = ref(0);  // 购物车商品数量
const userId = ref(localStorage.getItem('userId') || '');  // 用户ID
const cartRefreshInterval = ref(null);  // 购物车刷新定时器

// 菜系类型选项
const cuisineTypes = ref([]);

// 获取菜系类型
const fetchCuisineTypes = async () => {
  try {
    const response = await getCuisineTypes();
    if (response.code === 0 && response.data) {
      cuisineTypes.value = response.data;
    }
  } catch (error) {
    console.error('获取菜系类型出错:', error);
  }
};

// 处理推荐类型变化
const handleRecommendationTypeChange = () => {
  // 重置筛选条件
  selectedCuisineType.value = '';
  sortBy.value = 'popularity';

  if (recommendationType.value === 'cart') {
    // 如果切换到购物车标签页，刷新购物车数据
    fetchCartItemCount();
  } else {
    // 其他标签页，获取美食数据
    fetchFoods();
  }
};

// 处理菜系类型筛选变化
const handleCuisineTypeChange = (value, checked) => {
  if (checked) {
    selectedCuisineType.value = value;
  } else {
    selectedCuisineType.value = '';
  }

  // 对于个性化推荐和收藏的餐馆，我们需要特殊处理
  if (recommendationType.value === 'smart' || recommendationType.value === 'favorites') {
    // 先获取所有数据，然后在前端进行筛选
    if (allFoods.value.length > 0) {
      applyFiltersLocally();
    } else {
      fetchFoods();
    }
  } else {
    // 其他标签页正常获取数据
    fetchFoods();
  }
};

// 处理排序变化
const handleSortChange = () => {
  // 如果是价格排序，使用升序；其他排序方式使用降序
  if (sortBy.value === 'price') {
    sortOrder.value = 'asc';
  } else {
    sortOrder.value = 'desc';
  }

  // 对于个性化推荐和收藏的餐馆，我们需要特殊处理
  if (recommendationType.value === 'smart' || recommendationType.value === 'favorites') {
    // 先获取所有数据，然后在前端进行排序
    if (allFoods.value.length > 0) {
      applyFiltersLocally();
    } else {
      fetchFoods();
    }
  } else {
    // 其他标签页正常获取数据
    fetchFoods();
  }
};

// 在前端本地应用筛选和排序
const applyFiltersLocally = () => {
  // 先应用菜系筛选
  let filtered = [...allFoods.value];

  if (selectedCuisineType.value) {
    filtered = filtered.filter(item =>
      item.cuisine_type === selectedCuisineType.value
    );
  }

  // 再应用排序
  if (sortBy.value) {
    filtered.sort((a, b) => {
      let valueA, valueB;

      switch (sortBy.value) {
        case 'popularity':
          valueA = a.number_of_view || 0;
          valueB = b.number_of_view || 0;
          break;
        case 'rating':
          valueA = a.rating || 0;
          valueB = b.rating || 0;
          break;
        case 'price':
          valueA = a.average_price || 0;
          valueB = b.average_price || 0;
          break;
        default:
          valueA = a.number_of_view || 0;
          valueB = b.number_of_view || 0;
      }

      // 根据排序顺序返回比较结果
      return sortOrder.value === 'asc' ? valueA - valueB : valueB - valueA;
    });
  }

  // 更新过滤后的餐馆列表
  filteredFoods.value = filtered;
};

// 处理位置变化
const handleLocationChange = () => {
  if (recommendationType.value === 'nearby') {
    fetchFoods();
  }
};

// 处理搜索建议
const querySearchAsync = async (queryString, callback) => {
  if (queryString.length < 1) {
    callback([]);
    return;
  }

  try {
    const response = await getRestaurantSuggestions(queryString);
    if (response.code === 0 && response.data) {
      callback(response.data);
    } else {
      callback([]);
    }
  } catch (error) {
    console.error('获取搜索建议出错:', error);
    callback([]);
  }
};

// 处理选择搜索建议
const handleSelect = (item) => {
  if (item && item.id) {
    goToFoodDetail(item.id);
  }
};

// 处理搜索
const handleSearch = () => {
  if (searchKeyword.value.trim()) {
    searchFoods();
  } else {
    fetchFoods();
  }
};

// 获取美食数据
const fetchFoods = async () => {
  loading.value = true;
  try {
    let response;
    const params = {
      limit: 100, // 增加到100，确保返回所有60家餐馆
      cuisine_type: selectedCuisineType.value,
      sort_by: sortBy.value,
      order: sortOrder.value
    };

    // 获取用户ID
    const userId = localStorage.getItem('userId');

    switch (recommendationType.value) {
      case 'smart':
        // 智能推荐：根据用户登录状态选择不同的推荐方式
        if (userId) {
          // 用户已登录，使用协同过滤推荐
          try {
            // 先尝试使用协同过滤推荐，但保留排序和筛选参数
            // 注意：协同过滤API可能不支持所有排序参数，但我们仍然传递它们

            // 传递排序和筛选参数给协同过滤API
            const options = {
              cuisine_type: params.cuisine_type,
              sort_by: params.sort_by,
              order: params.order
            };
            response = await getCollaborativeRecommendations(userId, params.limit, options);

            // 如果协同过滤推荐没有结果，使用个性化推荐
            if (response.code === 0 && (!response.data || response.data.length === 0)) {
              ElMessage.info('暂无个性化推荐，显示热门餐馆');

              // 使用热门餐馆API，但保留用户的排序和筛选选择
              response = await getPopularFood(params);
            } else if (response.code === 0 && response.data && response.data.length > 0) {
              // 如果协同过滤推荐有结果，但用户选择了特定的排序方式或菜系筛选
              // 我们在前端对结果进行排序和筛选
              let filteredData = [...response.data];

              // 应用菜系筛选
              if (params.cuisine_type) {
                filteredData = filteredData.filter(item =>
                  item.cuisine_type === params.cuisine_type
                );
              }

              // 应用排序
              if (params.sort_by) {
                filteredData.sort((a, b) => {
                  let valueA, valueB;

                  switch (params.sort_by) {
                    case 'popularity':
                      valueA = a.number_of_view || 0;
                      valueB = b.number_of_view || 0;
                      break;
                    case 'rating':
                      valueA = a.evaluation || 0;
                      valueB = b.evaluation || 0;
                      break;
                    case 'price':
                      valueA = a.average_price || 0;
                      valueB = b.average_price || 0;
                      break;
                    default:
                      valueA = a.number_of_view || 0;
                      valueB = b.number_of_view || 0;
                  }

                  // 根据排序顺序返回比较结果
                  return params.order === 'asc' ? valueA - valueB : valueB - valueA;
                });
              }

              // 更新响应数据
              response.data = filteredData;
            }
          } catch (error) {
            console.error('协同过滤推荐出错:', error);
            // 如果协同过滤推荐出错，使用个性化推荐
            response = await getFoodRecommendations({
              ...params,
              user_id: userId
            });
          }
        } else {
          // 用户未登录，使用普通推荐
          response = await getPopularFood(params);
        }
        break;
      case 'popularity':
        response = await getPopularFood(params);
        break;
      case 'rating':
        response = await getFoodRecommendations({
          ...params,
          sort_by: 'rating'
        });
        break;
      case 'special':
        response = await getSpecialFood(params);
        break;
      case 'nearby':
        // 附近美食：使用位置信息
        response = await getNearbyFood({
          ...params,
          location_x: locationX.value,
          location_y: locationY.value,
          max_distance: maxDistance.value
        });
        break;
      case 'favorites':
        // 我收藏的餐馆：获取用户收藏的餐馆
        if (userId) {
          response = await getUserFavoriteRestaurants(userId);

          // 如果获取成功，应用排序和筛选
          if (response.code === 0 && response.data && response.data.length > 0) {
            let filteredData = [...response.data];

            // 应用菜系筛选
            if (params.cuisine_type) {
              filteredData = filteredData.filter(item =>
                item.cuisine_type === params.cuisine_type
              );
            }

            // 应用排序
            if (params.sort_by) {
              filteredData.sort((a, b) => {
                let valueA, valueB;

                switch (params.sort_by) {
                  case 'popularity':
                    valueA = a.number_of_view || 0;
                    valueB = b.number_of_view || 0;
                    break;
                  case 'rating':
                    valueA = a.evaluation || 0;
                    valueB = b.evaluation || 0;
                    break;
                  case 'price':
                    valueA = a.average_price || 0;
                    valueB = b.average_price || 0;
                    break;
                  default:
                    valueA = a.number_of_view || 0;
                    valueB = b.number_of_view || 0;
                }

                // 根据排序顺序返回比较结果
                return params.order === 'asc' ? valueA - valueB : valueB - valueA;
              });
            }

            // 更新响应数据
            response.data = filteredData;
          }
        } else {
          ElMessage.warning('请先登录后再查看收藏的餐馆');
          recommendationType.value = 'smart'; // 切换回智能推荐
          return fetchFoods(); // 重新获取数据
        }
        break;
      default:
        response = await getPopularFood(params);
    }

    if (response.code === 0 && response.data) {
      // 转换餐馆数据
      allFoods.value = response.data.map(item => ({
        ...item,
        food_id: item.id, // 确保food_id字段存在，用于路由跳转
        rating: item.evaluation || 0, // 使用evaluation字段作为评分
        image_url: item.image_url ? `http://localhost:5000${item.image_url}` : defaultFoodImage,
        description: item.description || `${item.name}是一家提供${item.cuisine_type}的餐厅，平均价格${item.average_price}元/人。`,
        is_favorite: false // 默认未收藏
      }));

      filteredFoods.value = [...allFoods.value];

      // 如果用户已登录，检查每个餐馆是否已收藏
      const userId = localStorage.getItem('userId');
      if (userId) {
        checkFavoriteStatus(userId);
      }
    } else {
      ElMessage.error('获取美食数据失败');
    }
  } catch (error) {
    console.error('获取美食数据出错:', error);
    ElMessage.error('获取美食数据出错');
  } finally {
    loading.value = false;
  }
};

// 搜索美食
const searchFoods = async () => {
  loading.value = true;
  try {
    const params = {
      keyword: searchKeyword.value,
      cuisine_type: selectedCuisineType.value,
      sort_by: sortBy.value,
      order: sortOrder.value,
      limit: 100 // 增加到100，确保返回所有60家餐馆
    };

    // 如果是按距离排序，添加位置信息
    if (sortBy.value === 'distance') {
      params.location_x = locationX.value;
      params.location_y = locationY.value;
    }

    const response = await searchFood(params);

    if (response.code === 0 && response.data) {
      // 转换餐馆数据
      allFoods.value = response.data.map(item => ({
        ...item,
        food_id: item.id, // 确保food_id字段存在，用于路由跳转
        rating: item.evaluation || 0, // 使用evaluation字段作为评分
        image_url: item.image_url ? `http://localhost:5000${item.image_url}` : defaultFoodImage,
        description: item.description || `${item.name}是一家提供${item.cuisine_type}的餐厅，平均价格${item.average_price}元/人。`,
        is_favorite: false // 默认未收藏
      }));

      filteredFoods.value = [...allFoods.value];

      // 如果用户已登录，检查每个餐馆是否已收藏
      const userId = localStorage.getItem('userId');
      if (userId) {
        checkFavoriteStatus(userId);
      }
    } else {
      ElMessage.error('搜索美食失败');
    }
  } catch (error) {
    console.error('搜索美食出错:', error);
    ElMessage.error('搜索美食出错');
  } finally {
    loading.value = false;
  }
};

// 跳转到美食详情页
const goToFoodDetail = (foodId) => {
  router.push(`/food/detail/${foodId}`);
};

// 添加位置筛选相关的样式
const addLocationFilterStyles = () => {
  const style = document.createElement('style');
  style.textContent = `
    .location-filter {
      margin-top: 15px;
      display: flex;
      flex-direction: column;
      gap: 10px;
      background-color: #f9f9f9;
      padding: 15px;
      border-radius: 8px;
      border-left: 4px solid #ff7e5f;
    }

    .location-description {
      margin-bottom: 10px;
    }

    .location-description p {
      margin: 0;
      font-size: 0.9rem;
      color: #666;
      line-height: 1.4;
    }

    .location-inputs {
      display: flex;
      gap: 15px;
      flex-wrap: wrap;
    }

    .location-inputs .el-input-number {
      width: 180px;
    }

    .sort-options {
      margin-top: 15px;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      gap: 10px;
    }
  `;
  document.head.appendChild(style);
};

// 检查餐馆收藏状态
const checkFavoriteStatus = async (userId) => {
  try {
    // 获取用户收藏的餐馆列表
    const response = await getUserFavoriteRestaurants(userId);
    if (response.code === 0 && response.data) {
      // 创建收藏餐馆ID集合
      const favoriteIds = new Set(response.data.map(item => item.id));

      // 更新餐馆收藏状态
      allFoods.value.forEach(food => {
        food.is_favorite = favoriteIds.has(food.food_id);
      });

      // 更新过滤后的餐馆列表
      filteredFoods.value = [...allFoods.value];
    }
  } catch (error) {
    console.error('检查餐馆收藏状态出错:', error);
  }
};

// 获取购物车商品数量
const fetchCartItemCount = async () => {
  if (!userId.value) return;

  try {
    const response = await getUserCart(userId.value);
    if (response && response.code === 0 && response.data) {
      cartItemCount.value = response.data.item_count || 0;
    }
  } catch (error) {
    console.error('获取购物车商品数量出错:', error);
  }
};

// 设置购物车刷新定时器
const setupCartRefreshInterval = () => {
  // 清除已有的定时器
  if (cartRefreshInterval.value) {
    clearInterval(cartRefreshInterval.value);
  }

  // 每30秒刷新一次购物车数量
  cartRefreshInterval.value = setInterval(() => {
    fetchCartItemCount();
  }, 30000);
};

// 页面加载时获取数据
onMounted(() => {
  fetchCuisineTypes();
  fetchFoods();
  addLocationFilterStyles();

  // 获取购物车商品数量
  fetchCartItemCount();
  setupCartRefreshInterval();

  // 监听allFoods变化，当数据加载完成后应用本地筛选
  watch(allFoods, (newValue) => {
    if (newValue.length > 0 && (recommendationType.value === 'smart' || recommendationType.value === 'favorites')) {
      // 如果有筛选条件或排序条件，应用本地筛选
      if (selectedCuisineType.value || sortBy.value !== 'popularity') {
        applyFiltersLocally();
      }
    }
  });
});

// 组件卸载前清除定时器
onBeforeUnmount(() => {
  if (cartRefreshInterval.value) {
    clearInterval(cartRefreshInterval.value);
  }
});
</script>

<style scoped>
.food-recommend-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.banner {
  text-align: center;
  margin-bottom: 40px;
  padding: 80px 0;
  background: linear-gradient(135deg, #ff7e5f, #feb47b);
  color: white;
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
  transition: all 0.5s ease;
}

.banner:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

.banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('https://img.freepik.com/free-photo/top-view-table-full-delicious-food-composition_23-2149141352.jpg') center/cover no-repeat;
  opacity: 0.2;
  z-index: 0;
  transition: opacity 0.5s ease;
}

.banner:hover::before {
  opacity: 0.3;
}

.banner::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
  z-index: 1;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.banner:hover::after {
  opacity: 1;
}

.banner h1 {
  font-size: 3.5rem;
  margin-bottom: 20px;
  position: relative;
  z-index: 2;
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.4);
  font-weight: 800;
  letter-spacing: 1px;
  transition: transform 0.3s ease;
}

.banner:hover h1 {
  transform: scale(1.05);
}

.banner p {
  font-size: 1.3rem;
  position: relative;
  z-index: 2;
  text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.4);
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
  transition: transform 0.3s ease;
}

.banner:hover p {
  transform: scale(1.05);
}

.filter-container {
  margin-bottom: 40px;
  padding: 30px;
  background-color: #ffffff;
  border-radius: 16px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.06);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  border-bottom: 4px solid transparent;
  border-image: linear-gradient(to right, #ff7e5f, #feb47b);
  border-image-slice: 1;
}

.filter-container:hover {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-5px);
}

.filter-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to right, #ff7e5f, #feb47b);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.filter-container:hover::after {
  opacity: 1;
}

.search-box {
  display: flex;
  margin-bottom: 25px;
  position: relative;
}

.search-box .el-autocomplete {
  flex: 1;
  margin-right: 15px;
}

.search-box .el-button {
  transition: all 0.3s ease;
  font-weight: 600;
  letter-spacing: 0.5px;
  padding: 12px 24px;
  border-radius: 8px;
}

.search-box .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 126, 95, 0.3);
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
  transition: all 0.2s ease;
  border-radius: 8px;
}

.suggestion-item:hover {
  background-color: #f9f9f9;
  transform: translateX(5px);
}

.suggestion-img {
  width: 50px;
  height: 50px;
  margin-right: 15px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.suggestion-item:hover .suggestion-img {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.suggestion-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.suggestion-item:hover .suggestion-img img {
  transform: scale(1.1);
}

.suggestion-info {
  flex: 1;
  transition: all 0.2s ease;
}

.suggestion-name {
  font-weight: 600;
  color: #333;
  font-size: 1rem;
  margin-bottom: 3px;
  transition: color 0.2s ease;
}

.suggestion-item:hover .suggestion-name {
  color: #ff7e5f;
}

.suggestion-type {
  font-size: 0.85rem;
  color: #777;
  transition: color 0.2s ease;
}

.suggestion-item:hover .suggestion-type {
  color: #555;
}

.filter-tags {
  margin-top: 25px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 12px;
  position: relative;
  transition: all 0.3s ease;
}

.filter-tags:hover {
  background-color: #f5f5f5;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.filter-label {
  margin-right: 15px;
  font-weight: 600;
  color: #444;
  font-size: 1.05rem;
  position: relative;
  padding-left: 10px;
}

.filter-label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 18px;
  background: linear-gradient(to bottom, #ff7e5f, #feb47b);
  border-radius: 2px;
}

.el-check-tag {
  margin: 5px 8px 5px 0;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 1px solid #dcdfe6;
}

.el-check-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}

.el-check-tag.is-checked {
  background-color: #ff7e5f;
  color: white;
  border-color: #ff7e5f;
}

.food-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.food-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
  background-color: white;
  position: relative;
}

.food-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.food-card::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, #ff7e5f, #feb47b);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.food-card:hover::after {
  transform: scaleX(1);
}

.food-image {
  height: 220px;
  overflow: hidden;
  position: relative;
}

.food-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0,0,0,0) 70%, rgba(0,0,0,0.5) 100%);
  z-index: 1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.food-card:hover .food-image::before {
  opacity: 1;
}

.food-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.food-card:hover .food-image img {
  transform: scale(1.08);
}

.favorite-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background: linear-gradient(135deg, #ff7e5f, #feb47b);
  color: white;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  z-index: 2;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  gap: 5px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 126, 95, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 126, 95, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 126, 95, 0);
  }
}

.favorite-badge i {
  color: #fff;
  font-size: 1rem;
}

.food-info {
  padding: 20px;
  position: relative;
}

.food-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.food-title h3 {
  margin: 0;
  flex: 1;
}

.favorite-icon {
  color: #ff7e5f;
  font-size: 1.2rem;
  margin-left: 10px;
}

.food-info h3 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 1.3rem;
  color: #333;
  font-weight: 600;
  transition: color 0.3s ease;
}

.food-card:hover .food-info h3 {
  color: #ff7e5f;
}

.food-tags {
  margin-bottom: 12px;
  display: flex;
  flex-wrap: wrap;
}

.food-tags .el-tag {
  margin-right: 8px;
  margin-bottom: 5px;
  transition: all 0.3s ease;
}

.food-card:hover .food-tags .el-tag {
  transform: translateY(-2px);
}

.food-rating {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.food-rating span {
  margin-left: 5px;
  color: #ff9900;
  font-weight: bold;
}

.food-stats {
  display: flex;
  gap: 5px;
  margin-bottom: 10px;
}

.food-description {
  color: #666;
  font-size: 0.95rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5;
  margin-top: 5px;
  transition: color 0.3s ease;
}

.food-card:hover .food-description {
  color: #444;
}

.food-dishes {
  margin-top: 12px;
}

.food-dishes h4 {
  margin: 0 0 8px;
  font-size: 0.95rem;
  color: #333;
  font-weight: 600;
}

.dish-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.dish-tags .el-tag {
  margin-right: 0;
  transition: all 0.3s ease;
}

.dish-tags .el-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.dish-tag {
  margin-bottom: 5px;
  font-weight: 500;
  border-radius: 12px;
  padding: 0 10px;
  height: 24px;
  line-height: 22px;
}

.cart-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  margin-top: 20px;
  min-height: 400px;
}
</style>