"""
餐馆查找工具类

提供高效的餐馆查找算法，包括：
1. 精准查找
2. 模糊查询
3. 基于内容的查找
"""
from typing import List, Dict, Any, Set, Tuple
import re
from models.restaurant import Restaurant


class RestaurantFinder:
    """餐馆查找工具类"""

    @staticmethod
    def hash_based_exact_match(restaurants: List[Restaurant], field: str, value: Any) -> List[Restaurant]:
        """
        基于哈希表的精准查找餐馆（更高效的查找算法）
        时间复杂度: O(1)平均，O(n)最坏

        Args:
            restaurants: 餐馆列表
            field: 查找字段，例如 'name', 'cuisine_type'
            value: 查找值

        Returns:
            匹配的餐馆列表
        """
        # 构建哈希表
        hash_map = {}
        for restaurant in restaurants:
            if hasattr(restaurant, field):
                field_value = getattr(restaurant, field)
                if field_value not in hash_map:
                    hash_map[field_value] = []
                hash_map[field_value].append(restaurant)

        # O(1)查找
        return hash_map.get(value, [])

    @staticmethod
    def exact_match(restaurants: List[Restaurant], field: str, value: Any) -> List[Restaurant]:
        """
        精准查找餐馆（保留原方法作为备用）

        Args:
            restaurants: 餐馆列表
            field: 查找字段，例如 'name', 'cuisine_type'
            value: 查找值

        Returns:
            匹配的餐馆列表
        """
        result = []
        for restaurant in restaurants:
            if hasattr(restaurant, field) and getattr(restaurant, field) == value:
                result.append(restaurant)
        return result

    @staticmethod
    def fuzzy_search(restaurants: List[Restaurant], keyword: str) -> List[Restaurant]:
        """
        模糊查询餐馆

        Args:
            restaurants: 餐馆列表
            keyword: 查询关键字

        Returns:
            匹配的餐馆列表
        """
        if not keyword:
            return restaurants

        # 将关键字转换为小写，用于不区分大小写的匹配
        keyword_lower = keyword.lower()

        # 分词，以便更精确地匹配
        keywords = re.findall(r'\w+', keyword_lower)

        # 计算每个餐馆的匹配分数
        restaurant_scores = []
        for restaurant in restaurants:
            score = 0
            matched = False

            # 检查餐馆名称（完全匹配得分最高）
            restaurant_name = restaurant.name.lower() if restaurant.name else ""
            if keyword_lower == restaurant_name:
                score += 100  # 完全匹配餐馆名称，得分最高
                matched = True
            elif any(kw == restaurant_name for kw in keywords):
                score += 90  # 关键词完全匹配餐馆名称
                matched = True
            elif any(kw in restaurant_name.split() for kw in keywords):
                score += 80  # 关键词是餐馆名称的一个完整单词
                matched = True
            elif any(restaurant_name.startswith(kw) for kw in keywords):
                score += 70  # 关键词是餐馆名称的前缀
                matched = True
            elif any(kw in restaurant_name for kw in keywords):
                score += 50  # 关键词是餐馆名称的一部分
                matched = True

            # 检查菜系类型
            cuisine_type = restaurant.cuisine_type.lower() if restaurant.cuisine_type else ""
            if keyword_lower == cuisine_type:
                score += 80  # 完全匹配菜系类型
                matched = True
            elif any(kw == cuisine_type for kw in keywords):
                score += 70  # 关键词完全匹配菜系类型
                matched = True
            elif any(cuisine_type.startswith(kw) for kw in keywords):
                score += 60  # 关键词是菜系类型的前缀
                matched = True
            elif any(kw in cuisine_type for kw in keywords):
                score += 40  # 关键词是菜系类型的一部分
                matched = True

            # 检查菜品名称
            dishes = [
                restaurant.dishes_name_price,
                restaurant.dishes_name1_price,
                restaurant.dishes_name2_price
            ]

            for dish in dishes:
                dish_lower = dish.lower() if dish else ""
                if keyword_lower in dish_lower:
                    score += 30  # 关键词是菜品名称的一部分
                    matched = True
                    break

            # 只添加有匹配的餐馆
            if matched:
                restaurant_scores.append((restaurant, score))

        # 按分数降序排序
        restaurant_scores.sort(key=lambda x: x[1], reverse=True)

        # 返回排序后的餐馆列表
        return [r[0] for r in restaurant_scores]

    @staticmethod
    def boyer_moore_search(text: str, pattern: str) -> bool:
        """
        使用Boyer-Moore算法进行字符串匹配

        Args:
            text: 待搜索的文本
            pattern: 搜索模式

        Returns:
            是否匹配成功
        """
        if not pattern:
            return True

        if not text:
            return False

        # 将文本和模式转换为小写，用于不区分大小写的匹配
        text = text.lower()
        pattern = pattern.lower()

        n, m = len(text), len(pattern)

        if m > n:
            return False

        # 构建坏字符规则
        bad_char = {}
        for i in range(m - 1):
            bad_char[pattern[i]] = m - 1 - i

        # 模式串中最后一个字符的偏移量
        skip = bad_char.get(pattern[m - 1], m)
        bad_char[pattern[m - 1]] = skip

        # 搜索
        i = m - 1
        while i < n:
            j = m - 1
            k = i
            while j >= 0 and text[k] == pattern[j]:
                j -= 1
                k -= 1

            if j == -1:
                return True

            i += bad_char.get(text[i], m)

        return False

    @staticmethod
    def content_based_search(restaurants: List[Restaurant], keyword: str) -> List[Tuple[Restaurant, float]]:
        """
        基于内容的餐馆查找，返回餐馆及其相关度得分

        Args:
            restaurants: 餐馆列表
            keyword: 查询关键字

        Returns:
            匹配的餐馆列表及其相关度得分
        """
        if not keyword:
            return [(restaurant, 1.0) for restaurant in restaurants]

        # 将关键字转换为小写，用于不区分大小写的匹配
        keyword_lower = keyword.lower()

        # 分词
        keywords = re.findall(r'\w+', keyword_lower)

        result = []
        for restaurant in restaurants:
            score = 0.0
            max_score = len(keywords)  # 最大可能得分

            # 检查餐馆名称
            restaurant_name = restaurant.name.lower() if restaurant.name else ""
            for kw in keywords:
                if RestaurantFinder.boyer_moore_search(restaurant_name, kw):
                    score += 1.0

            # 检查菜系类型
            cuisine_type = restaurant.cuisine_type.lower() if restaurant.cuisine_type else ""
            for kw in keywords:
                if RestaurantFinder.boyer_moore_search(cuisine_type, kw):
                    score += 0.8  # 菜系匹配的权重略低于名称匹配

            # 检查菜品名称
            dishes = [
                restaurant.dishes_name_price,
                restaurant.dishes_name1_price,
                restaurant.dishes_name2_price
            ]

            for dish in dishes:
                dish_lower = dish.lower() if dish else ""
                for kw in keywords:
                    if RestaurantFinder.boyer_moore_search(dish_lower, kw):
                        score += 0.6  # 菜品匹配的权重更低

            # 计算相关度得分（归一化）
            relevance = min(score / max_score, 1.0) if max_score > 0 else 0.0

            # 只返回相关度大于0的结果
            if relevance > 0:
                result.append((restaurant, relevance))

        # 按相关度降序排序
        result.sort(key=lambda x: x[1], reverse=True)

        return result

    @staticmethod
    def filter_by_cuisine_type(restaurants: List[Restaurant], cuisine_types: List[str]) -> List[Restaurant]:
        """
        按菜系类型过滤餐馆

        Args:
            restaurants: 餐馆列表
            cuisine_types: 菜系类型列表

        Returns:
            过滤后的餐馆列表
        """
        if not cuisine_types:
            return restaurants

        # 将菜系类型转换为小写，用于不区分大小写的匹配
        cuisine_types_lower = [ct.lower() for ct in cuisine_types]

        result = []
        for restaurant in restaurants:
            if restaurant.cuisine_type and restaurant.cuisine_type.lower() in cuisine_types_lower:
                result.append(restaurant)

        return result

    @staticmethod
    def filter_by_price_range(restaurants: List[Restaurant], min_price: float = None, max_price: float = None) -> List[Restaurant]:
        """
        按价格范围过滤餐馆

        Args:
            restaurants: 餐馆列表
            min_price: 最低价格
            max_price: 最高价格

        Returns:
            过滤后的餐馆列表
        """
        result = []
        for restaurant in restaurants:
            price = restaurant.average_price_perperson

            if price is None:
                continue

            if min_price is not None and price < min_price:
                continue

            if max_price is not None and price > max_price:
                continue

            result.append(restaurant)

        return result

    # ===== 高效查找算法实现 =====

    @staticmethod
    def kmp_search(text: str, pattern: str) -> bool:
        """
        使用KMP算法进行字符串匹配（比Boyer-Moore更稳定）
        时间复杂度: O(n + m)，空间复杂度: O(m)

        Args:
            text: 待搜索的文本
            pattern: 搜索模式

        Returns:
            是否匹配成功
        """
        if not pattern:
            return True
        if not text:
            return False

        # 转换为小写进行不区分大小写的匹配
        text = text.lower()
        pattern = pattern.lower()

        # 构建部分匹配表（失效函数）
        lps = RestaurantFinder._compute_lps(pattern)

        i = j = 0  # i是text的索引，j是pattern的索引

        while i < len(text):
            if pattern[j] == text[i]:
                i += 1
                j += 1

            if j == len(pattern):
                return True  # 找到匹配
            elif i < len(text) and pattern[j] != text[i]:
                if j != 0:
                    j = lps[j - 1]
                else:
                    i += 1

        return False

    @staticmethod
    def _compute_lps(pattern: str) -> List[int]:
        """
        计算KMP算法的部分匹配表（LPS数组）

        Args:
            pattern: 模式字符串

        Returns:
            LPS数组
        """
        m = len(pattern)
        lps = [0] * m
        length = 0  # 最长相等前后缀的长度
        i = 1

        while i < m:
            if pattern[i] == pattern[length]:
                length += 1
                lps[i] = length
                i += 1
            else:
                if length != 0:
                    length = lps[length - 1]
                else:
                    lps[i] = 0
                    i += 1

        return lps

    @staticmethod
    def multi_field_hash_search(restaurants: List[Restaurant], search_criteria: dict) -> List[Restaurant]:
        """
        多字段哈希查找（支持多个条件同时查找）

        Args:
            restaurants: 餐馆列表
            search_criteria: 查找条件字典，例如 {'cuisine_type': '川菜', 'price_range': '中等'}

        Returns:
            匹配所有条件的餐馆列表
        """
        if not search_criteria:
            return restaurants

        # 构建多字段索引
        indices = {}
        for field in search_criteria.keys():
            indices[field] = {}
            for restaurant in restaurants:
                if hasattr(restaurant, field):
                    value = getattr(restaurant, field)
                    if value not in indices[field]:
                        indices[field][value] = set()
                    indices[field][value].add(restaurant)

        # 找到满足所有条件的餐馆
        result_sets = []
        for field, value in search_criteria.items():
            if field in indices and value in indices[field]:
                result_sets.append(indices[field][value])
            else:
                return []  # 如果任何条件都没有匹配，返回空列表

        # 计算交集
        if result_sets:
            result = result_sets[0]
            for s in result_sets[1:]:
                result = result.intersection(s)
            return list(result)

        return []

    @staticmethod
    def spatial_hash_search(restaurants: List[Restaurant], center_x: float, center_y: float,
                           radius: float) -> List[Restaurant]:
        """
        基于空间哈希的地理位置查找

        Args:
            restaurants: 餐馆列表
            center_x: 中心点X坐标
            center_y: 中心点Y坐标
            radius: 搜索半径

        Returns:
            在指定范围内的餐馆列表
        """
        result = []
        radius_squared = radius * radius

        for restaurant in restaurants:
            if hasattr(restaurant, 'x') and hasattr(restaurant, 'y'):
                dx = restaurant.x - center_x
                dy = restaurant.y - center_y
                distance_squared = dx * dx + dy * dy

                if distance_squared <= radius_squared:
                    result.append(restaurant)

        return result