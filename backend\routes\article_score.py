from flask import Blueprint, request, jsonify
from models.article import ArticleScore, Article
from services.article_score_service import ArticleScoreService

article_score_bp = Blueprint('article_score', __name__)

@article_score_bp.route('', methods=['POST'])
@article_score_bp.route('/set_score', methods=['POST'])  # 保留旧路由以兼容性
def set_score():
    """
    Set score for an article
    """
    try:
        data = request.get_json()

        # Validate required fields
        if not data:
            return jsonify({
                'code': 1,
                'message': 'No data provided',
                'data': None
            }), 400

        user_id = data.get('user_id')
        article_id = data.get('article_id')
        score = data.get('score')

        if not user_id or not article_id or score is None:
            return jsonify({
                'code': 1,
                'message': 'Missing required fields',
                'data': None
            }), 400

        # Initialize service
        service = ArticleScoreService()

        # Set score
        success = service.set_score(user_id, article_id, score)

        if not success:
            return jsonify({
                'code': 1,
                'message': 'Failed to set score',
                'data': None
            }), 500

        return jsonify({
            'code': 0,
            'message': 'Score set successfully',
            'data': {}
        }), 200

    except Exception as e:
        return jsonify({
            'code': 1,
            'message': str(e),
            'data': None
        }), 500

@article_score_bp.route('/get_average_score/<int:article_id>', methods=['GET'])
def get_average_score(article_id):
    """
    Get average score for an article
    """
    try:
        # Initialize service
        service = ArticleScoreService()

        # Get average score
        average_score = service.get_average_score(article_id)

        # 获取评分人数
        rating_count = ArticleScore.query.filter_by(article_id=article_id).count()

        return jsonify({
            'code': 0,
            'message': 'Average score retrieved successfully',
            'data': {
                'average_score': average_score,
                'rating_count': rating_count
            }
        }), 200

    except Exception as e:
        return jsonify({
            'code': 1,
            'message': str(e),
            'data': None
        }), 500

@article_score_bp.route('/get_all_scores/<int:article_id>', methods=['GET'])
def get_all_scores(article_id):
    """
    Get all scores for an article
    """
    try:
        # 直接检查文章是否存在
        article = Article.query.get(article_id)
        if not article:
            print(f"直接查询: Article with ID {article_id} not found")
            return jsonify({
                'code': 1,
                'message': 'Article not found',
                'data': None
            }), 404

        # 直接从数据库获取评分
        scores_db = ArticleScore.query.filter_by(article_id=article_id).all()

        # 如果没有评分，返回空数组
        if not scores_db:
            return jsonify({
                'code': 0,
                'message': 'No scores found for this article',
                'data': {
                    'scores': []
                }
            }), 200

        # 格式化评分数据
        scores = []
        for score in scores_db:
            from models.user import User
            user = User.query.get(score.user_id)
            if user:
                score_data = {
                    'user_id': user.user_id,
                    'username': user.username,
                    'score': score.score,
                    'created_at': None  # 默认为None
                }
                scores.append(score_data)

        return jsonify({
            'code': 0,
            'message': 'Scores retrieved successfully',
            'data': {
                'scores': scores
            }
        }), 200

    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({
            'code': 1,
            'message': f'Error getting scores: {str(e)}',
            'data': None
        }), 500

@article_score_bp.route('/get_user_score', methods=['POST'])
def get_user_score():
    """
    Get score for a specific user and article
    """
    try:
        data = request.get_json()

        # Validate required fields
        if not data:
            return jsonify({
                'code': 1,
                'message': 'No data provided',
                'data': None
            }), 400

        user_id = data.get('user_id')
        article_id = data.get('article_id')

        if not user_id or not article_id:
            return jsonify({
                'code': 1,
                'message': 'Missing required fields',
                'data': None
            }), 400

        # Get score
        score = ArticleScore.query.filter_by(
            user_id=user_id, article_id=article_id).first()

        if not score:
            return jsonify({
                'code': 1,
                'message': 'No score found for this user and article',
                'data': None
            }), 404

        return jsonify({
            'code': 0,
            'message': 'Score retrieved successfully',
            'data': {
                'score': score.score
            }
        }), 200

    except Exception as e:
        return jsonify({
            'code': 1,
            'message': f'Error getting score: {str(e)}',
            'data': None
        }), 500
