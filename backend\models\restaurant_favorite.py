"""
餐馆收藏模型
"""
from utils.database import db
from datetime import datetime


class RestaurantFavorite(db.Model):
    """餐馆收藏模型"""
    __tablename__ = 'restaurant_favorite'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    user_id = db.Column(db.Integer, nullable=False, index=True)
    restaurant_id = db.Column(db.Integer, db.ForeignKey('restaurant.id'), nullable=False, index=True)
    created_at = db.Column(db.DateTime, default=datetime.now)

    # 关联关系
    restaurant = db.relationship('Restaurant', backref=db.backref('favorites', lazy='dynamic'))

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'restaurant_id': self.restaurant_id,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None
        }
