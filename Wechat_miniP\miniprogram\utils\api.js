// api.js - 微信小程序兼容的API服务
const { API_BASE_URL } = require('../config/api.js');

/**
 * API服务类
 */
class ApiService {
  constructor() {
    this.baseUrl = API_BASE_URL;
  }

  /**
   * 构建查询字符串（微信小程序兼容版本）
   */
  buildQueryString(params) {
    const queryParts = [];
    for (const [key, value] of Object.entries(params)) {
      if (value !== undefined && value !== null && value !== '') {
        queryParts.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
      }
    }
    return queryParts.join('&');
  }

  /**
   * 通用请求方法
   */
  request(options) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${this.baseUrl}${options.url}`,
        method: options.method || 'GET',
        data: options.data,
        header: {
          'Content-Type': 'application/json',
          ...options.header
        },
        success: (res) => {
          if (res.statusCode === 200) {
            resolve({ data: res.data, statusCode: res.statusCode });
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${res.data}`));
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  }

  /**
   * 搜索景点（从景点数据表）
   */
  async searchLocations(params) {
    try {
      const queryParams = {};
      
      if (params.name) {
        queryParams.name = params.name;
      }
      if (params.type !== undefined) {
        queryParams.type = params.type;
      }
      if (params.limit) {
        queryParams.limit = params.limit;
      }

      const queryString = this.buildQueryString(queryParams);
      
      console.log(`搜索景点: ${this.baseUrl}/path/search-locations?${queryString}`);
      
      return new Promise((resolve, reject) => {
        wx.request({
          url: `${this.baseUrl}/path/search-locations?${queryString}`,
          method: 'GET',
          header: {
            'Content-Type': 'application/json'
          },
          success: (res) => {
            console.log('搜索景点响应:', res);
            if (res.statusCode === 200) {
              const locations = Array.isArray(res.data) ? res.data : [];
              resolve(locations);
            } else {
              console.error('搜索景点失败:', res.statusCode, res.data);
              resolve([]);
            }
          },
          fail: (err) => {
            console.error('搜索景点请求失败:', err);
            resolve([]);
          }
        });
      });
    } catch (error) {
      console.error('搜索景点失败:', error);
      return [];
    }
  }

  /**
   * 获取热门景点
   */
  async getPopularLocations(params = {}) {
    try {
      const queryParams = {
        limit: params.limit || 10
      };
      
      if (params.type !== undefined) {
        queryParams.type = params.type;
      }

      const queryString = this.buildQueryString(queryParams);
      
      console.log(`获取热门景点: ${this.baseUrl}/path/popular-locations?${queryString}`);
      
      return new Promise((resolve, reject) => {
        wx.request({
          url: `${this.baseUrl}/path/popular-locations?${queryString}`,
          method: 'GET',
          header: {
            'Content-Type': 'application/json'
          },
          success: (res) => {
            console.log('热门景点响应:', res);
            if (res.statusCode === 200) {
              const locations = Array.isArray(res.data) ? res.data : [];
              resolve(locations);
            } else {
              console.error('获取热门景点失败:', res.statusCode, res.data);
              resolve([]);
            }
          },
          fail: (err) => {
            console.error('获取热门景点请求失败:', err);
            resolve([]);
          }
        });
      });
    } catch (error) {
      console.error('获取热门景点失败:', error);
      return [];
    }
  }

  /**
   * 按名称搜索景点（原有方法，搜索路径节点）
   */
  async searchSpotsByName(params) {
    try {
      const queryParams = {
        name: params.name
      };
      
      if (params.type) {
        queryParams.type = params.type;
      }

      const queryString = this.buildQueryString(queryParams);
      
      console.log(`搜索景点: ${this.baseUrl}/path/search-by-name?${queryString}`);
      
      return new Promise((resolve, reject) => {
        wx.request({
          url: `${this.baseUrl}/path/search-by-name?${queryString}`,
          method: 'GET',
          header: {
            'Content-Type': 'application/json'
          },
          success: (res) => {
            console.log('搜索景点响应:', res);
            if (res.statusCode === 200) {
              const spots = Array.isArray(res.data) ? res.data : [];
              resolve(spots);
            } else {
              console.error('搜索景点失败:', res.statusCode, res.data);
              resolve([]);
            }
          },
          fail: (err) => {
            console.error('搜索景点请求失败:', err);
            resolve([]);
          }
        });
      });
    } catch (error) {
      console.error('按名称搜索景点失败:', error);
      return [];
    }
  }

  /**
   * 获取地点建议（自动完成）
   */
  async getLocationSuggestions(query, limit = 10) {
    try {
      const queryString = this.buildQueryString({
        q: query,
        limit: limit
      });
      
      console.log(`获取地点建议: ${this.baseUrl}/path/location-suggestions?${queryString}`);
      
      return new Promise((resolve, reject) => {
        wx.request({
          url: `${this.baseUrl}/path/location-suggestions?${queryString}`,
          method: 'GET',
          header: {
            'Content-Type': 'application/json'
          },
          success: (res) => {
            console.log('地点建议响应:', res);
            if (res.statusCode === 200) {
              const suggestions = Array.isArray(res.data) ? res.data : [];
              resolve(suggestions);
            } else {
              console.error('获取地点建议失败:', res.statusCode, res.data);
              resolve([]);
            }
          },
          fail: (err) => {
            console.error('获取地点建议请求失败:', err);
            resolve([]);
          }
        });
      });
    } catch (error) {
      console.error('获取地点建议失败:', error);
      return [];
    }
  }

  /**
   * 按条件搜索景点
   */
  async getSpotsByCriteria(params) {
    try {
      const response = await this.request({
        url: '/path/search-by-criteria',
        method: 'POST',
        data: params
      });
      return response.data || { nearby_spots: [] };
    } catch (error) {
      console.error('按条件搜索景点失败:', error);
      return { nearby_spots: [] };
    }
  }

  /**
   * 按起始位置搜索附近景点
   */
  async searchSpotsByStart(params) {
    try {
      const response = await this.request({
        url: '/path/search-by-start',
        method: 'POST',
        data: params
      });
      return response.data || { nearby_spots: [] };
    } catch (error) {
      console.error('搜索附近景点失败:', error);
      return { nearby_spots: [] };
    }
  }
}

// 创建单例
const apiService = new ApiService();

// 导出
module.exports = {
  apiService: apiService,
  default: apiService
};
