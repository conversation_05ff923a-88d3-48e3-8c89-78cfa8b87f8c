// settings.js
Page({
  data: {
    diaryPublic: true,
    locationEnabled: true,
    pushEnabled: true,
    cacheSize: '0KB'
  },

  onLoad: function() {
    console.log('设置页面加载');
    this.loadSettings();
    this.calculateCacheSize();
  },

  // 加载设置
  loadSettings: function() {
    const settings = wx.getStorageSync('userSettings') || {};
    this.setData({
      diaryPublic: settings.diaryPublic !== false,
      locationEnabled: settings.locationEnabled !== false,
      pushEnabled: settings.pushEnabled !== false
    });
  },

  // 保存设置
  saveSettings: function() {
    const settings = {
      diaryPublic: this.data.diaryPublic,
      locationEnabled: this.data.locationEnabled,
      pushEnabled: this.data.pushEnabled
    };
    wx.setStorageSync('userSettings', settings);
  },

  // 日记可见性设置
  onDiaryPublicChange: function(e) {
    this.setData({
      diaryPublic: e.detail.value
    });
    this.saveSettings();
    
    wx.showToast({
      title: e.detail.value ? '日记已设为公开' : '日记已设为私密',
      icon: 'success'
    });
  },

  // 位置信息设置
  onLocationChange: function(e) {
    this.setData({
      locationEnabled: e.detail.value
    });
    this.saveSettings();
    
    wx.showToast({
      title: e.detail.value ? '已允许获取位置' : '已禁止获取位置',
      icon: 'success'
    });
  },

  // 推送通知设置
  onPushChange: function(e) {
    this.setData({
      pushEnabled: e.detail.value
    });
    this.saveSettings();
    
    wx.showToast({
      title: e.detail.value ? '已开启推送通知' : '已关闭推送通知',
      icon: 'success'
    });
  },

  // 编辑个人资料
  editProfile: function() {
    wx.showModal({
      title: '个人资料',
      content: '个人资料编辑功能开发中',
      showCancel: false
    });
  },

  // 计算缓存大小
  calculateCacheSize: function() {
    const that = this;
    try {
      const info = wx.getStorageInfoSync();
      const sizeKB = Math.round(info.currentSize);
      let sizeText = sizeKB + 'KB';
      
      if (sizeKB > 1024) {
        const sizeMB = (sizeKB / 1024).toFixed(1);
        sizeText = sizeMB + 'MB';
      }
      
      this.setData({
        cacheSize: sizeText
      });
    } catch (e) {
      console.error('获取缓存大小失败:', e);
      this.setData({
        cacheSize: '未知'
      });
    }
  },

  // 清除缓存
  clearCache: function() {
    const that = this;
    wx.showModal({
      title: '清除缓存',
      content: '确定要清除所有缓存数据吗？这将删除已保存的用户信息和设置。',
      success: function(res) {
        if (res.confirm) {
          try {
            // 保留重要的用户信息
            const userInfo = wx.getStorageSync('userInfo');
            const userSettings = wx.getStorageSync('userSettings');
            
            // 清除所有缓存
            wx.clearStorageSync();
            
            // 恢复重要信息
            if (userInfo) {
              wx.setStorageSync('userInfo', userInfo);
            }
            if (userSettings) {
              wx.setStorageSync('userSettings', userSettings);
            }
            
            that.calculateCacheSize();
            
            wx.showToast({
              title: '缓存已清除',
              icon: 'success'
            });
          } catch (e) {
            console.error('清除缓存失败:', e);
            wx.showToast({
              title: '清除失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 检查更新
  checkUpdate: function() {
    wx.showLoading({
      title: '检查中...'
    });
    
    // 模拟检查更新
    setTimeout(function() {
      wx.hideLoading();
      wx.showModal({
        title: '检查更新',
        content: '当前已是最新版本',
        showCancel: false
      });
    }, 1500);
  },

  // 退出登录
  logout: function() {
    wx.showModal({
      title: '退出登录',
      content: '确定要退出登录吗？',
      success: function(res) {
        if (res.confirm) {
          // 清除用户信息
          wx.removeStorageSync('userInfo');
          wx.removeStorageSync('userToken');
          
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
          
          // 跳转到登录页面或首页
          setTimeout(function() {
            wx.reLaunch({
              url: '/pages/index/index'
            });
          }, 1500);
        }
      }
    });
  },

  // 返回
  goBack: function() {
    wx.navigateBack();
  }
});
