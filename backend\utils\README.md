# 工具函数说明

## 统一响应格式 (response.py)

为了与Java版本的`Result`类保持一致，我们实现了统一的响应格式工具函数。

### 使用方法

```python
from utils.response import success, error, not_found, unauthorized, forbidden, server_error

# 成功响应
@app.route('/api/example/success')
def example_success():
    data = {'name': 'example', 'value': 123}
    return success(data, '操作成功')  # 返回 {"code": 0, "message": "操作成功", "data": {...}}

# 错误响应
@app.route('/api/example/error')
def example_error():
    return error('参数错误')  # 返回 {"code": 1, "message": "参数错误", "data": null}

# 资源不存在
@app.route('/api/example/not-found')
def example_not_found():
    return not_found('资源不存在')  # 返回 {"code": 404, "message": "资源不存在", "data": null}

# 未授权
@app.route('/api/example/unauthorized')
def example_unauthorized():
    return unauthorized('未登录')  # 返回 {"code": 401, "message": "未登录", "data": null}

# 禁止访问
@app.route('/api/example/forbidden')
def example_forbidden():
    return forbidden('权限不足')  # 返回 {"code": 403, "message": "权限不足", "data": null}

# 服务器错误
@app.route('/api/example/server-error')
def example_server_error():
    return server_error('服务器内部错误')  # 返回 {"code": 500, "message": "服务器内部错误", "data": null}
```

### 响应格式

所有API响应都遵循以下格式：

```json
{
  "code": 0,       // 0表示成功，非0表示错误
  "message": "操作成功", // 响应消息
  "data": {        // 响应数据，可以是任何类型，错误时为null
    ...
  }
}
```

### 错误码

| 错误码 | 含义 |
|-------|------|
| 0 | 成功 |
| 1 | 一般错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器错误 |

## Boyer-Moore中文搜索算法 (boyer_moore_chinese.py)

用于高效搜索中文文本中的模式。

### 使用方法

```python
from utils.boyer_moore_chinese import BoyerMooreChinese

# 创建搜索器
searcher = BoyerMooreChinese("搜索词")

# 在文本中搜索所有匹配位置
positions = searcher.search_all("这是一段包含搜索词的文本，搜索词可能出现多次")
# 返回 [5, 15]
```

## Held-Karp算法 (held_karp.py)

用于解决小规模旅行商问题（TSP），适用于多目的地路径规划。

### 使用方法

```python
from utils.held_karp import held_karp

# 距离矩阵
distance_matrix = [
    [0, 10, 15, 20],
    [10, 0, 35, 25],
    [15, 35, 0, 30],
    [20, 25, 30, 0]
]

# 计算最优路径
path = held_karp(distance_matrix)
# 返回 [0, 1, 3, 2]
```

## 模拟退火算法 (simulated_annealing.py)

用于解决大规模旅行商问题（TSP），适用于多目的地路径规划。

### 使用方法

```python
from utils.simulated_annealing import simulated_annealing

# 距离矩阵
distance_matrix = [
    [0, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60],
    [10, 0, 35, 25, 20, 15, 30, 35, 40, 45, 50, 55],
    # ... 更多行
]

# 计算近似最优路径
path = simulated_annealing(distance_matrix)
```

## 排序算法 (sorting.py)

提供多种排序算法实现。

### 使用方法

```python
from utils.sorting import QuickSort, InsertSort, HeapSort

# 定义比较器
def compare(a, b):
    return a - b  # 升序

# 快速排序
items = [5, 3, 8, 1, 2]
QuickSort.quick_sort(items, compare)
# items 变为 [1, 2, 3, 5, 8]

# 插入排序
items = [5, 3, 8, 1, 2]
InsertSort.insert_sort(items, compare)
# items 变为 [1, 2, 3, 5, 8]

# 堆排序
items = [5, 3, 8, 1, 2]
HeapSort.heap_sort(items, compare)
# items 变为 [1, 2, 3, 5, 8]
```

## 优先队列 (priority_queue.py)

自定义优先队列实现。

### 使用方法

```python
from utils.priority_queue import MyPriorityQueue

# 定义比较器
def compare(a, b):
    return a - b  # 小的值优先级高

# 创建优先队列
queue = MyPriorityQueue(compare)

# 添加元素
queue.offer(5)
queue.offer(3)
queue.offer(8)

# 获取并移除最高优先级元素
highest = queue.poll()  # 返回 3

# 查看最高优先级元素但不移除
peek = queue.peek()  # 返回 5
```

## 基于位置的推荐 (location_based_recommend.py)

根据用户浏览历史推荐地点。

### 使用方法

```python
from utils.location_based_recommend import location_based_recommend
from models.location import Location

# 用户浏览计数
user_view_counts = {1: 5, 2: 3, 3: 1}  # 地点ID -> 浏览次数

# 所有地点
all_locations = Location.query.all()

# 获取推荐地点
recommended_locations = location_based_recommend(user_view_counts, all_locations)
```

## 基于用户的协同过滤 (user_based_cf.py)

根据用户评分推荐文章。

### 使用方法

```python
from utils.user_based_cf import UserBasedCF

# 用户-物品-评分数据
data = {
    1: {101: 5, 102: 3, 103: 4},  # 用户1的评分
    2: {101: 4, 103: 5, 104: 2},  # 用户2的评分
    3: {102: 4, 103: 3, 104: 5}   # 用户3的评分
}

# 创建推荐系统
cf = UserBasedCF(data)

# 为用户1推荐物品
recommendations = cf.recommend(1)  # 返回 [(104, 3.5)]
```
