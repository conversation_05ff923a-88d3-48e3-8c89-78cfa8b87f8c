// api-test.js - API测试页面
const { apiService } = require('../../utils/api.js');

Page({
  data: {
    testResults: [],
    loading: false
  },

  onLoad() {
    console.log('API测试页面加载');
    this.runTests();
  },

  async runTests() {
    this.setData({ loading: true, testResults: [] });
    
    const tests = [
      {
        name: '测试热门景点API',
        test: () => apiService.getPopularLocations({ limit: 5 })
      },
      {
        name: '测试搜索故宫',
        test: () => apiService.searchLocations({ name: '故宫', limit: 5 })
      },
      {
        name: '测试搜索天安门',
        test: () => apiService.searchLocations({ name: '天安门', limit: 5 })
      },
      {
        name: '测试地点建议',
        test: () => apiService.getLocationSuggestions('北京', 3)
      }
    ];

    const results = [];

    for (const test of tests) {
      try {
        console.log(`开始测试: ${test.name}`);
        const startTime = Date.now();
        const result = await test.test();
        const endTime = Date.now();
        
        results.push({
          name: test.name,
          success: true,
          data: result,
          count: Array.isArray(result) ? result.length : 0,
          time: endTime - startTime
        });
        
        console.log(`测试成功: ${test.name}`, result);
      } catch (error) {
        results.push({
          name: test.name,
          success: false,
          error: error.message,
          time: 0
        });
        
        console.error(`测试失败: ${test.name}`, error);
      }
    }

    this.setData({ 
      testResults: results,
      loading: false 
    });
  },

  onRetryTest() {
    this.runTests();
  },

  onViewResult(e) {
    const index = e.currentTarget.dataset.index;
    const result = this.data.testResults[index];
    
    if (result.success && result.data) {
      wx.showModal({
        title: result.name,
        content: JSON.stringify(result.data, null, 2),
        showCancel: false
      });
    }
  }
});
