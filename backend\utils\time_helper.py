"""
时间处理工具模块

该模块提供统一的时间处理功能，包括：
1. 时区转换
2. 时间格式化
3. 本地时间获取
"""

import datetime
import pytz
from typing import Optional, Union

# 中国时区
CHINA_TZ = pytz.timezone('Asia/Shanghai')
UTC_TZ = pytz.UTC

def get_china_now() -> datetime.datetime:
    """
    获取当前中国时间（带时区信息）
    
    Returns:
        当前中国时间
    """
    return datetime.datetime.now(CHINA_TZ)

def get_utc_now() -> datetime.datetime:
    """
    获取当前UTC时间（带时区信息）
    
    Returns:
        当前UTC时间
    """
    return datetime.datetime.now(UTC_TZ)

def utc_to_china(utc_time: datetime.datetime) -> datetime.datetime:
    """
    将UTC时间转换为中国时间
    
    Args:
        utc_time: UTC时间
        
    Returns:
        中国时间
    """
    if utc_time is None:
        return None
    
    # 如果时间没有时区信息，假设它是UTC时间
    if utc_time.tzinfo is None:
        utc_time = UTC_TZ.localize(utc_time)
    
    # 转换为中国时间
    return utc_time.astimezone(CHINA_TZ)

def china_to_utc(china_time: datetime.datetime) -> datetime.datetime:
    """
    将中国时间转换为UTC时间
    
    Args:
        china_time: 中国时间
        
    Returns:
        UTC时间
    """
    if china_time is None:
        return None
    
    # 如果时间没有时区信息，假设它是中国时间
    if china_time.tzinfo is None:
        china_time = CHINA_TZ.localize(china_time)
    
    # 转换为UTC时间
    return china_time.astimezone(UTC_TZ)

def format_time_for_display(time_obj: Optional[datetime.datetime], 
                           format_str: str = '%Y-%m-%d %H:%M:%S') -> str:
    """
    格式化时间用于显示（转换为中国时间）
    
    Args:
        time_obj: 时间对象
        format_str: 格式化字符串
        
    Returns:
        格式化后的时间字符串
    """
    if time_obj is None:
        return '未知时间'
    
    try:
        # 转换为中国时间
        china_time = utc_to_china(time_obj)
        return china_time.strftime(format_str)
    except Exception as e:
        print(f"时间格式化失败: {e}")
        return '时间格式错误'

def format_time_for_api(time_obj: Optional[datetime.datetime]) -> Optional[str]:
    """
    格式化时间用于API响应（ISO格式，中国时间）
    
    Args:
        time_obj: 时间对象
        
    Returns:
        ISO格式的时间字符串
    """
    if time_obj is None:
        return None
    
    try:
        # 转换为中国时间
        china_time = utc_to_china(time_obj)
        return china_time.isoformat()
    except Exception as e:
        print(f"时间API格式化失败: {e}")
        return None

def parse_time_from_string(time_str: str) -> Optional[datetime.datetime]:
    """
    从字符串解析时间
    
    Args:
        time_str: 时间字符串
        
    Returns:
        解析后的时间对象（UTC时间）
    """
    if not time_str:
        return None
    
    try:
        # 尝试解析ISO格式
        if 'T' in time_str:
            time_obj = datetime.datetime.fromisoformat(time_str.replace('Z', '+00:00'))
        else:
            # 尝试解析常见格式
            for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d', '%Y/%m/%d %H:%M:%S', '%Y/%m/%d']:
                try:
                    time_obj = datetime.datetime.strptime(time_str, fmt)
                    # 假设是中国时间，转换为UTC
                    time_obj = CHINA_TZ.localize(time_obj).astimezone(UTC_TZ)
                    break
                except ValueError:
                    continue
            else:
                return None
        
        # 确保时间有时区信息
        if time_obj.tzinfo is None:
            time_obj = UTC_TZ.localize(time_obj)
        
        return time_obj
    except Exception as e:
        print(f"时间解析失败: {e}")
        return None

def get_relative_time(time_obj: Optional[datetime.datetime]) -> str:
    """
    获取相对时间描述（如：刚刚、5分钟前、1小时前等）
    
    Args:
        time_obj: 时间对象
        
    Returns:
        相对时间描述
    """
    if time_obj is None:
        return '未知时间'
    
    try:
        # 转换为中国时间
        china_time = utc_to_china(time_obj)
        now = get_china_now()
        
        # 计算时间差
        diff = now - china_time
        
        # 如果是未来时间
        if diff.total_seconds() < 0:
            return format_time_for_display(time_obj, '%Y-%m-%d %H:%M')
        
        seconds = int(diff.total_seconds())
        
        if seconds < 60:
            return '刚刚'
        elif seconds < 3600:
            minutes = seconds // 60
            return f'{minutes}分钟前'
        elif seconds < 86400:
            hours = seconds // 3600
            return f'{hours}小时前'
        elif seconds < 2592000:  # 30天
            days = seconds // 86400
            return f'{days}天前'
        else:
            # 超过30天，显示具体日期
            return format_time_for_display(time_obj, '%Y-%m-%d')
            
    except Exception as e:
        print(f"相对时间计算失败: {e}")
        return format_time_for_display(time_obj, '%Y-%m-%d %H:%M')

def ensure_timezone(time_obj: Optional[datetime.datetime]) -> Optional[datetime.datetime]:
    """
    确保时间对象包含时区信息
    
    Args:
        time_obj: 时间对象
        
    Returns:
        包含时区信息的时间对象
    """
    if time_obj is None:
        return None
    
    if time_obj.tzinfo is None:
        # 假设是UTC时间
        return UTC_TZ.localize(time_obj)
    
    return time_obj

def is_same_day(time1: Optional[datetime.datetime], 
                time2: Optional[datetime.datetime]) -> bool:
    """
    判断两个时间是否在同一天（中国时间）
    
    Args:
        time1: 时间1
        time2: 时间2
        
    Returns:
        是否在同一天
    """
    if time1 is None or time2 is None:
        return False
    
    try:
        china_time1 = utc_to_china(time1)
        china_time2 = utc_to_china(time2)
        
        return (china_time1.year == china_time2.year and
                china_time1.month == china_time2.month and
                china_time1.day == china_time2.day)
    except Exception:
        return False

# 为了向后兼容，保留原有的函数名
def get_utc_now_legacy():
    """向后兼容的UTC时间获取函数"""
    return datetime.datetime.now(datetime.timezone.utc)
