<!--my-diaries.wxml-->
<view class="page">
  <!-- 顶部导航栏 -->
  <view class="nav-header">
    <view class="nav-left" bindtap="goBack">
      <text class="iconfont icon-arrow-left"></text>
    </view>
    <view class="nav-title">我的日记</view>
    <view class="nav-right" bindtap="goToCreate">
      <text class="create-btn">写日记</text>
    </view>
  </view>

  <!-- 统计信息 -->
  <view class="stats-bar">
    <view class="stat-item">
      <view class="stat-number">{{diaryList.length}}</view>
      <view class="stat-label">总数</view>
    </view>
    <view class="stat-item">
      <view class="stat-number">{{totalLikes}}</view>
      <view class="stat-label">获赞</view>
    </view>
    <view class="stat-item">
      <view class="stat-number">{{totalViews}}</view>
      <view class="stat-label">浏览</view>
    </view>
  </view>

  <!-- 日记列表 -->
  <view class="diary-list" wx:if="{{!loading && diaryList.length > 0}}">
    <view class="diary-item" wx:for="{{diaryList}}" wx:key="article_id" bindtap="goToDiaryDetail" data-id="{{item.article_id}}">
      <view class="diary-content">
        <view class="diary-header">
          <view class="diary-title">{{item.title}}</view>
          <view class="diary-date">{{item.created_at}}</view>
        </view>
        <view class="diary-text">{{item.content}}</view>
        <view class="diary-meta">
          <view class="meta-item">
            <text class="meta-icon">📍</text>
            <text class="meta-text">{{item.location || '未知地点'}}</text>
          </view>
          <view class="meta-item">
            <text class="meta-icon">❤️</text>
            <text class="meta-text">{{item.likes_count || 0}}</text>
          </view>
          <view class="meta-item">
            <text class="meta-icon">👁️</text>
            <text class="meta-text">{{item.views_count || 0}}</text>
          </view>
        </view>
        <view class="diary-tags" wx:if="{{item.tags && item.tags.length > 0}}">
          <view class="tag-item" wx:for="{{item.tags}}" wx:for-item="tag" wx:key="*this">
            <text class="tag-text">#{{tag}}</text>
          </view>
        </view>
      </view>
      <view class="diary-image" wx:if="{{item.image_url}}">
        <image src="{{item.image_url}}" mode="aspectFill" />
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && diaryList.length === 0}}">
    <view class="empty-icon">📝</view>
    <view class="empty-title">还没有日记</view>
    <view class="empty-desc">记录你的旅行故事，分享美好时光</view>
    <view class="empty-action" bindtap="goToCreate">
      <text class="action-text">写第一篇日记</text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <view class="loading-icon">⏳</view>
    <view class="loading-text">加载中...</view>
  </view>
</view>
