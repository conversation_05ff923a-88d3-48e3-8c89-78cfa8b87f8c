<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试前端视频展示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2rem;
        }

        .video-section {
            margin-bottom: 30px;
        }

        .video-section h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .video-player-wrapper {
            position: relative;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            background: #000;
        }

        .main-travel-video {
            width: 100%;
            height: auto;
            min-height: 300px;
            max-height: 600px;
            background: #000;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .main-travel-video:hover {
            transform: scale(1.02);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        /* 视频信息覆盖层样式已移除，提供更清洁的视频观看体验 */

        .test-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #4caf50;
            color: white;
        }

        .btn-success:hover {
            background: #45a049;
            transform: translateY(-2px);
        }

        .status-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }

        .status-info h4 {
            margin: 0 0 10px 0;
            color: #333;
        }

        .status-info p {
            margin: 5px 0;
            color: #666;
        }

        .error-message {
            background: #ffebee;
            color: #d32f2f;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #f44336;
        }

        .success-message {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #4caf50;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🎬 AIGC视频展示测试</h1>

        <div class="test-buttons">
            <button class="btn btn-primary" onclick="testLocalVideo()">测试本地视频</button>
            <button class="btn btn-success" onclick="checkVideoAvailability()">检查视频可用性</button>
            <button class="btn btn-primary" onclick="loadLatestVideo()">加载最新视频</button>
        </div>

        <div id="statusInfo" class="status-info">
            <h4>📊 状态信息</h4>
            <p>点击上方按钮开始测试视频功能</p>
        </div>

        <div class="video-section">
            <h3>🎞️ 生成的旅游视频</h3>
            <div class="video-player-wrapper">
                <video
                    id="mainVideo"
                    controls
                    preload="metadata"
                    class="main-travel-video"
                    poster="/images/video-poster.jpg"
                >
                    您的浏览器不支持视频播放
                </video>
                <!-- 视频信息覆盖层已移除，提供更清洁的视频观看体验 -->
            </div>
        </div>
    </div>

    <script>
        // 获取视频URL - 确保使用正确的基础URL
        function getVideoUrl(videoUrl) {
            if (!videoUrl) return '';

            // 如果是完整URL，直接返回
            if (videoUrl.startsWith('http://') || videoUrl.startsWith('https://')) {
                return videoUrl;
            }

            // 如果是相对路径，添加基础URL
            const baseUrl = 'http://localhost:5000';
            if (videoUrl.startsWith('/')) {
                return baseUrl + videoUrl;
            } else {
                return baseUrl + '/' + videoUrl;
            }
        }

        // 更新状态信息
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('statusInfo');
            statusDiv.className = type === 'error' ? 'error-message' :
                                 type === 'success' ? 'success-message' : 'status-info';
            statusDiv.innerHTML = `<h4>📊 状态信息</h4><p>${message}</p>`;
        }

        // 测试本地视频
        function testLocalVideo() {
            updateStatus('🔍 正在测试最新生成的视频...');

            // 使用最新生成的视频文件名
            const videoUrl = '/uploads/animations/travel_animation_1b48c0af.mp4';
            const fullUrl = getVideoUrl(videoUrl);

            const video = document.getElementById('mainVideo');
            video.src = fullUrl;

            video.onloadstart = function() {
                updateStatus('🎬 视频开始加载...');
            };

            video.onloadeddata = function() {
                updateStatus('✅ 视频加载成功，可以播放了！', 'success');
                console.log(`视频时长: ${Math.round(video.duration) || '未知'}秒`);
            };

            video.onerror = function() {
                updateStatus('❌ 视频加载失败，请检查文件是否存在', 'error');
            };
        }

        // 检查视频可用性
        async function checkVideoAvailability() {
            updateStatus('🔍 正在检查视频文件可用性...');

            const videoUrl = '/uploads/animations/travel_animation_1b48c0af.mp4';
            const fullUrl = getVideoUrl(videoUrl);

            try {
                const response = await fetch(fullUrl, { method: 'HEAD' });

                if (response.ok) {
                    updateStatus(`✅ 视频文件可用！文件大小: ${response.headers.get('content-length') || '未知'} 字节`, 'success');
                } else {
                    updateStatus(`❌ 视频文件不可用 (状态码: ${response.status})`, 'error');
                }
            } catch (error) {
                updateStatus(`❌ 检查失败: ${error.message}`, 'error');
            }
        }

        // 加载最新视频
        async function loadLatestVideo() {
            updateStatus('🔍 正在查找最新的视频文件...');

            try {
                // 尝试获取animations目录列表
                const response = await fetch('http://localhost:5000/uploads/animations/');

                if (response.ok) {
                    const text = await response.text();
                    // 简单的HTML解析，查找.mp4文件
                    const mp4Files = text.match(/travel_animation_[a-f0-9]+\.mp4/g);

                    if (mp4Files && mp4Files.length > 0) {
                        const latestFile = mp4Files[mp4Files.length - 1];
                        const videoUrl = `/uploads/animations/${latestFile}`;
                        const fullUrl = getVideoUrl(videoUrl);

                        const video = document.getElementById('mainVideo');
                        video.src = fullUrl;

                        updateStatus(`🎬 加载最新视频: ${latestFile}`, 'success');
                    } else {
                        updateStatus('❌ 未找到任何视频文件', 'error');
                    }
                } else {
                    updateStatus('❌ 无法访问视频目录', 'error');
                }
            } catch (error) {
                updateStatus(`❌ 查找失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动测试
        window.onload = function() {
            updateStatus('🚀 页面加载完成，准备测试视频功能');
        };
    </script>
</body>
</html>
