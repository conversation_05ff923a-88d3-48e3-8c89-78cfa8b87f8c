# 基于大模型的个性化旅游系统
# 一：问题描述
#### （1）当前大家经常利用假期去各地旅游。个性化旅游系统可以帮助大家管理自己的旅游活动，具备旅游地点推荐、旅游路线规划、旅游场所查询、旅游日记交流等功能。
- 旅游前：需先按照旅游目的地的热度、评价和个人兴趣选择旅游目的地；
- 旅游中：在景点（包括学校）内部也需要根据游览的目标规划最优的参观线路，在游览过程中还可以给出相应的景点介绍和场所查询；
- 旅游后：可以根据所拍照片和游览经历生成旅游日记。

#### （2）数据需求
1. 景区和校园数量至少200个，景区和校园内部可以一致；
2. 景区和校园内建筑物（景点、教学楼、办公楼、宿舍楼）数不少于20个；其它服务设施不少于10种（商店、饭店、洗手间、图书馆、食堂、超市、咖啡馆等），数量不少于50个；
3. 建立景区和校园内部道路图
4. 包括各种建筑物、服务设施等信息；
5. 边数不能少于200条（尽量接近真实景区和校园，建议爬取真实地图数据）；
6. 系统用户数不少于10人；

#### （3）功能需求
1. 所有列出的功能都需要实现，验收时需要能够演示所有功能，验收的要求会在验收前一个月公布；
2. 每个功能后面的注释部分都列出了核心算法，核心算法必须基于自己设计的数据结构，自己编程实现，是考查的重点；
3. 建议大家尝试使用大模型（DeepSeek等）进行算法的实现和纠错；使用大模型的核心在于要比较多种算法的性能和效果；
4. 系统基础要求为多用户系统；系统需要能够支持多个用户登录使用；
5. 系统架构和开发语言不做限制，可以根据自己小组擅长的技术选型。

# 二：基本功能需求
#### （1）旅游推荐
1. 用户可以根据自己的喜好选择不同的景点和学校作为旅游目的地；
2. 在游览前，系统会向用户推荐旅游景点和学校，可以按照旅游热度、评价和个人兴趣进行推荐；

3. 推荐算法基础要求为排序算法，可以按照用户选择的热度和评价进行排序；（核心算法为排序算法，考虑到用户通常只看前10个景点或者学校，要求不经过完全排序可以排好前10的景点或者学校，并且考虑数据动态变化）
   - **已实现**：使用堆排序算法实现了Top-K排序，时间复杂度为O(n log k)，比全排序O(n log n)更高效
   - **已实现**：使用哈希表和集合等数据结构优化算法性能
   - **已实现**：支持增量更新，当景点热度或评分发生变化时，只更新相关景点的排序位置，而不是重新排序所有景点
   - **已实现**：使用内存缓存减少数据库查询，提高响应速度

4. 用户可以输入景点和学校的名称、类别、关键字等进行查询，查询结果有多项时，可以对查询结果按照热度和评价进行排序。（核心算法为查找算法和排序算法）
   - **已实现**：使用哈希表实现O(1)时间复杂度的查找操作
   - **已实现**：支持多关键字查询和结果过滤
   - **已实现**：使用堆排序对查询结果进行排序

#### （2）旅游路线规划
1. 当进入景区或者学校后，用户可以输入目标景点或者场所信息，系统会为用户规划从当前位置出发到达景点或者场所的最优旅游线路；（核心算法为最短路径算法）
2. 当进入景区或者学校后，用户可以输入多个目标景点或者场所信息，系统会为用户规划从当前位置出发，参观多个景点或者场所的最优旅游线路。（核心算法为途经多点最短路径算法，从当前位置出发，参观完返回当前位置。）
3. 设计导航功能的图形界面，包括地图展示和输出路径展示；

##### 关于线路规划策略的要求:
1. 最短距离策略：距离最短即可；
2. 最短时间策略：假设每条道路拥挤度不一样，在这种情况下时间最短即可；拥挤度为小于等于1的一个正数，真实速度=拥挤度*理想速度；每条道路的拥挤度与理想速度自拟；
3. 交通工具的最短时间策略：校区内可以选择自行车和步行，选择自行车时，只能走自行车道路，默认自行车在校区任何地点都有；景区内可以选择步行和电瓶车，选择电瓶车时只能走电瓶车路线，电瓶车路线固定，默认上车即走；不同交通工具可以选择时，考虑不同拥挤度的情况下时间最短；（时间最短的线路，可以是多种交通工具混合）

#### （3）场所查询
1. 在景区或者学校内部时，选中某个景点或者场所，会找出附近一定范围内的超市、卫生间等设施，并根据距离进行排序；（核心算法为排序，不能根据直线距离）
2. 可以通过选择类别对结果进行过滤；
3. 可以由用户输入类别名称查找某个地点附近的服务设施，并根据距离进行排序；（核心算法为查找和排序）

#### （4）旅游日记管理
1. 用户旅游过程中或者旅游结束时可以撰写旅游日记，通过文字、图片和视频等方式记录旅游内容；
2. 需要对所有用户的旅游日记进行统一的管理；
3. 用户可以根据浏览和查询所有用户的旅游日记，旅游日记的浏览量即为该日记的热度，每位同学浏览完可以对旅游日记进行评分；
4. 用户在浏览所有旅游日记时，可以按照日记热度、评价和个人兴趣进行推荐，推荐算法基础要求为排序算法，可以根据热度和评分进行排序；（核心算法为排序算法）
5. 用户可以输入旅游目的地，对目的地相关的旅游日记根据热度和评分进行排序；（核心算法为查找算法和排序算法）
6. 用户可以输入旅游日记的名称进行精确查询；（核心算法为查找算法，考虑旅游日记数量较大，变化非常快的情况下进行高效查找）
7. 可以按日记内容进行全文检索（核心算法为文本搜索）
8. 可以对旅游日记进行压缩存储（核心算法为无损压缩）
9. 使用AIGC算法根据拍摄的景点或者学校的照片、文字描述等信息进行旅游动画生成。

# 三：选做功能
1. 选做一：室内导航策略：模拟教学楼的结构和景区内博物馆等建筑物的内部结构，进行室内导航，包括大门到电梯的导航、楼层间的电梯导航和楼层内到房间的导航；

2. 选做二：美食推荐，在选中游览景点和学校后，可以按照用户选择的热度、评价和距离进行排序，并根据菜系进行过滤；（核心算法为排序算法，考虑到用户通常只看前10个美食，要求不经过完全排序可以排好前10的美食）；可以输入美食名称、菜系、饭店或窗口名称等进行基于内容的模糊查询，查询结果有多项时，可以对查询结果按照热度、评价和距离进行排序。（核心算法为模糊查找算法和排序算法）

3. 选做三：采用基于内容推荐算法，针对个人兴趣进行景点、学校、美食和旅游日记的推荐；
   - **已实现**：使用基于内容的推荐算法，分析用户浏览历史中的景点特征（类型、关键词等），找出与这些景点相似的其他景点进行推荐
   - **已实现**：使用协同过滤算法，分析用户之间的相似性，找出与当前用户相似的其他用户，然后推荐这些相似用户喜欢但当前用户尚未浏览过的景点
   - **已实现**：使用混合推荐算法，结合基于内容的推荐、协同过滤和热度排序，通过加权平均的方式计算最终的推荐分数
   - **已实现**：使用余弦相似度计算用户之间的相似度，考虑了浏览次数等强度信息

# 四：其他补充
#### （1）多用户单并发
1. 多并发系统
2. C/S模式（服务器端多线程+数据库）
3. B/S模式（使用一些架构，前端Vue.js，后端Spring Boot等+数据库）
4. 单并发系统
5. 单机系统（Word等）

##### 多用户
1. 支持多个用户使用系统，保存多个用户的信息，每个用户登录显示不同内容；

#### （2）数据管理
1. 持久化；
2. 数据交互；
3. 初始数据导入（批量）
4. 运行时数据交互
5. 测试数据

##### 方法：
1. 数据库；
2. 自定义格式的文件；

#### （3）查询和排序
1. 查询不能使用O(n)复杂度的算法（最优策略）；
2. 查询可以是多关键字；
3. 可以多个关键字联合查询，也可以在查询结果中继续查询；
4. 排序时考虑不同关键字的不同排序策略；（最优策略）
5. 要考虑实际应用时的数据量。
6. 要考虑部分排序问题（top-K）。

#### （4）选路策略
1. 地图的建立：接近实际的地图，抽象成“有向图结构”。（交叉口和建筑物设为顶点）
2. 最短距离策略：距离最短即可；
3. 最短时间策略：假设每条道路拥挤度不一样，在这种情况下时间最短即可；拥挤度为小于等于1的一个正数，真实速度=拥挤度*理想速度；拥挤度与理想速度自拟；（道路的拥挤度可以随时间变化）
4. 交通工具的最短时间策略：校区内选择自行车时，只能走自行车道路，默认自行车在校区任何地点都有，这时在考虑不同拥挤度的情况下时间最短；

#### （5）途经最短距离策略
1. 最近邻点法
2. 途程改善法
3. 枚举算法（遍历）的特点是算法简单，但运算量大，当问题的规模变大，循环的阶数越大，执行的速度越慢。
4. 回溯法和分支限界法

