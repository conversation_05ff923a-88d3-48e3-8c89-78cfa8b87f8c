from flask import Blueprint, request
from models.article_comment import ArticleComment
from models.article import Article
from models.user import User
from utils.database import db
from utils.response import success, error
import datetime

article_comment_bp = Blueprint('article_comment', __name__)

@article_comment_bp.route('', methods=['POST'])
def add_comment():
    """
    Add a new comment to an article
    """
    try:
        data = request.get_json()

        # Validate required fields
        if not data:
            return error('No data provided', 400)

        user_id = data.get('user_id')
        article_id = data.get('article_id')
        content = data.get('content')

        if not user_id or not article_id or not content:
            return error('Missing required fields', 400)

        # Check if article exists
        article = Article.query.get(article_id)
        if not article:
            return error('Article not found', 404)

        # Check if user exists
        user = User.query.get(user_id)
        if not user:
            return error('User not found', 404)

        # Create new comment
        comment = ArticleComment(
            article_id=article_id,
            user_id=user_id,
            content=content,
            created_at=datetime.datetime.now(),
            updated_at=datetime.datetime.now()
        )

        # Save to database
        db.session.add(comment)
        db.session.commit()

        # Increase article popularity
        article.popularity = article.popularity + 1 if article.popularity else 1
        db.session.commit()

        # Return comment with user information
        result = comment.to_dict()
        result['username'] = user.username

        return success(result, 'Comment added successfully')

    except Exception as e:
        db.session.rollback()
        return error(str(e))

@article_comment_bp.route('/<int:article_id>', methods=['GET'])
def get_comments(article_id):
    """
    Get all comments for an article
    """
    try:
        # Check if article exists
        article = Article.query.get(article_id)
        if not article:
            return error('Article not found', 404)

        # Get comments
        comments = ArticleComment.query.filter_by(article_id=article_id).order_by(ArticleComment.created_at.desc()).all()

        # Get user information
        user_ids = [comment.user_id for comment in comments]
        users = {user.user_id: user for user in User.query.filter(User.user_id.in_(user_ids)).all()} if user_ids else {}

        # Build response
        result = []
        for comment in comments:
            comment_dict = comment.to_dict()
            user = users.get(comment.user_id)
            comment_dict['username'] = user.username if user else 'Unknown'
            comment_dict['avatar'] = user.avatar if user and user.avatar else None
            result.append(comment_dict)

        return success(result, 'Comments retrieved successfully')

    except Exception as e:
        return error(str(e))

@article_comment_bp.route('/<int:comment_id>', methods=['DELETE'])
def delete_comment(comment_id):
    """
    Delete a comment
    """
    try:
        data = request.get_json()

        # Validate required fields
        if not data:
            return error('No data provided', 400)

        user_id = data.get('user_id')

        if not user_id:
            return error('Missing user_id parameter', 400)

        # Get comment
        comment = ArticleComment.query.get(comment_id)

        if not comment:
            return error('Comment not found', 404)

        # Check if user is the owner
        if comment.user_id != user_id:
            return error('Unauthorized', 403)

        # Delete comment
        db.session.delete(comment)
        db.session.commit()

        return success({}, 'Comment deleted successfully')

    except Exception as e:
        db.session.rollback()
        return error(str(e))

@article_comment_bp.route('/user/<int:user_id>', methods=['GET'])
def get_user_comments(user_id):
    """
    Get all comments by a user
    """
    try:
        # Check if user exists
        user = User.query.get(user_id)
        if not user:
            return error('User not found', 404)

        # Get comments
        comments = ArticleComment.query.filter_by(user_id=user_id).order_by(ArticleComment.created_at.desc()).all()

        # Get article information
        article_ids = [comment.article_id for comment in comments]
        articles = {article.article_id: article for article in Article.query.filter(Article.article_id.in_(article_ids)).all()} if article_ids else {}

        # Build response
        result = []
        for comment in comments:
            comment_dict = comment.to_dict()

            # Add article information
            article = articles.get(comment.article_id)
            if article:
                # 解压文章内容
                content = article.content
                if content and isinstance(content, bytes):
                    try:
                        import zlib
                        content = zlib.decompress(content).decode('utf-8')
                    except Exception as e:
                        print(f"Error decompressing article {article.article_id}: {e}")
                        content = "内容解压失败"

                comment_dict['article_title'] = article.title
                comment_dict['article_content'] = content[:100] + '...' if content and len(content) > 100 else content
            else:
                comment_dict['article_title'] = 'Unknown Article'
                comment_dict['article_content'] = ''

            result.append(comment_dict)

        return success(result, 'User comments retrieved successfully')

    except Exception as e:
        return error(str(e))

@article_comment_bp.route('/count/<int:article_id>', methods=['GET'])
def get_comment_count(article_id):
    """
    Get the number of comments for an article
    """
    try:
        # Check if article exists
        article = Article.query.get(article_id)
        if not article:
            return error('Article not found', 404)

        # Get comment count
        count = ArticleComment.query.filter_by(article_id=article_id).count()

        return success({'count': count}, 'Comment count retrieved successfully')

    except Exception as e:
        return error(str(e))