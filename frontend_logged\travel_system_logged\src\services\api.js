import axios from 'axios';

const API_BASE_URL = 'http://localhost:5000/api';

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器，添加token
apiClient.interceptors.request.use(config => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// 认证相关API
export const authAPI = {
  login: async (credentials) => {
    const response = await apiClient.post('/auth/login', credentials);
    // 处理Flask后端的响应格式
    if (response.data.code === 0) {
      return {
        data: {
          token: response.data.data.token,
          user: response.data.data.user
        }
      };
    } else {
      throw {
        response: {
          data: {
            error: response.data.message || '登录失败'
          }
        }
      };
    }
  },
  register: (userData) => apiClient.post('/auth/register', userData)
};

export default {
  auth: authAPI
};
