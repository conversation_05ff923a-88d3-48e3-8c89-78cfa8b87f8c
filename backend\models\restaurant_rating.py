"""
餐馆评分模型模块

该模块定义了餐馆评分的数据模型，用于存储用户对餐馆的评分信息。
"""

from datetime import datetime
from utils.database import db


class RestaurantRating(db.Model):
    """餐馆评分模型类"""
    
    __tablename__ = 'restaurant_rating'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    restaurant_id = db.Column(db.Integer, db.ForeignKey('restaurant.id', ondelete='CASCADE'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.user_id', ondelete='CASCADE'), nullable=False)
    rating = db.Column(db.Float, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.now)
    
    # 定义唯一约束
    __table_args__ = (
        db.UniqueConstraint('user_id', 'restaurant_id', name='unique_user_restaurant'),
    )
    
    def __init__(self, restaurant_id, user_id, rating):
        """
        初始化餐馆评分对象
        
        Args:
            restaurant_id: 餐馆ID
            user_id: 用户ID
            rating: 评分
        """
        self.restaurant_id = restaurant_id
        self.user_id = user_id
        self.rating = rating
    
    def to_dict(self):
        """
        将餐馆评分对象转换为字典
        
        Returns:
            餐馆评分字典
        """
        return {
            'id': self.id,
            'restaurant_id': self.restaurant_id,
            'user_id': self.user_id,
            'rating': self.rating,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None
        }
