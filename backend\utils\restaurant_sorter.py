"""
餐馆排序工具类

提供高效的餐馆排序算法，包括：
1. 快速排序算法
2. 堆排序算法
3. 部分排序算法（只排序前K个元素）
"""
import random
from typing import List, Callable, Any
from models.restaurant import Restaurant


class RestaurantSorter:
    """餐馆排序工具类"""

    @staticmethod
    def quick_sort(restaurants: List[Restaurant], key_func: Callable[[Restaurant], Any], reverse: bool = False) -> List[Restaurant]:
        """
        使用快速排序算法对餐馆列表进行排序

        Args:
            restaurants: 餐馆列表
            key_func: 排序关键字函数，例如 lambda x: x.popularity
            reverse: 是否降序排序，默认为False（升序）

        Returns:
            排序后的餐馆列表
        """
        if not restaurants:
            return []

        # 创建副本，避免修改原始列表
        restaurants_copy = restaurants.copy()

        def _quick_sort(arr, low, high):
            if low < high:
                # 分区操作，返回分区点
                pivot_index = _partition(arr, low, high)
                # 对分区点左侧子数组进行递归排序
                _quick_sort(arr, low, pivot_index - 1)
                # 对分区点右侧子数组进行递归排序
                _quick_sort(arr, pivot_index + 1, high)

        def _partition(arr, low, high):
            # 选择随机元素作为pivot，减少最坏情况的发生
            pivot_index = random.randint(low, high)
            arr[pivot_index], arr[high] = arr[high], arr[pivot_index]

            pivot = key_func(arr[high])
            i = low - 1

            for j in range(low, high):
                # 根据reverse参数决定比较方式
                if (not reverse and key_func(arr[j]) <= pivot) or (reverse and key_func(arr[j]) >= pivot):
                    i += 1
                    arr[i], arr[j] = arr[j], arr[i]

            arr[i + 1], arr[high] = arr[high], arr[i + 1]
            return i + 1

        _quick_sort(restaurants_copy, 0, len(restaurants_copy) - 1)
        return restaurants_copy

    @staticmethod
    def heap_sort(restaurants: List[Restaurant], key_func: Callable[[Restaurant], Any], reverse: bool = False) -> List[Restaurant]:
        """
        使用堆排序算法对餐馆列表进行排序

        Args:
            restaurants: 餐馆列表
            key_func: 排序关键字函数，例如 lambda x: x.popularity
            reverse: 是否降序排序，默认为False（升序）

        Returns:
            排序后的餐馆列表
        """
        if not restaurants:
            return []

        # 创建副本，避免修改原始列表
        restaurants_copy = restaurants.copy()

        # 使用Python的heapq模块实现堆排序
        if reverse:
            # 降序排序，使用负值
            heap = [(- key_func(restaurant), i, restaurant) for i, restaurant in enumerate(restaurants_copy)]
        else:
            # 升序排序
            heap = [(key_func(restaurant), i, restaurant) for i, restaurant in enumerate(restaurants_copy)]

        # 使用自实现的堆排序
        return RestaurantSorter._heap_sort_implementation(heap, reverse)

    @staticmethod
    def top_k_sort(restaurants: List[Restaurant], k: int, key_func: Callable[[Restaurant], Any], reverse: bool = True) -> List[Restaurant]:
        """
        只排序前K个元素的高效算法

        Args:
            restaurants: 餐馆列表
            k: 需要排序的前K个元素
            key_func: 排序关键字函数，例如 lambda x: x.popularity
            reverse: 是否降序排序，默认为True（降序）

        Returns:
            排序后的前K个餐馆
        """
        if not restaurants:
            return []

        if k >= len(restaurants):
            # 如果k大于等于列表长度，直接使用完全排序
            return RestaurantSorter.quick_sort(restaurants, key_func, reverse)

        # 使用自实现的堆排序找出前K个元素
        return RestaurantSorter._custom_heap_top_k(restaurants, k, key_func, reverse)

    @staticmethod
    def multi_key_sort(restaurants: List[Restaurant], key_funcs: List[Callable[[Restaurant], Any]], reverse_list: List[bool] = None) -> List[Restaurant]:
        """
        多关键字排序

        Args:
            restaurants: 餐馆列表
            key_funcs: 排序关键字函数列表，按优先级排序
            reverse_list: 是否降序排序的列表，与key_funcs一一对应

        Returns:
            排序后的餐馆列表
        """
        if not restaurants:
            return []

        # 创建副本，避免修改原始列表
        restaurants_copy = restaurants.copy()

        # 如果reverse_list为None，默认所有关键字都是升序排序
        if reverse_list is None:
            reverse_list = [False] * len(key_funcs)

        # 确保reverse_list长度与key_funcs一致
        if len(reverse_list) != len(key_funcs):
            reverse_list = reverse_list + [False] * (len(key_funcs) - len(reverse_list))

        # 使用自实现的排序算法实现多关键字排序
        # 从最低优先级到最高优先级依次排序
        for i in range(len(key_funcs) - 1, -1, -1):
            restaurants_copy = RestaurantSorter.quick_sort(restaurants_copy, key_funcs[i], reverse_list[i])

        return restaurants_copy

    @staticmethod
    def sort_by_distance(restaurants: List[Restaurant], location_x: float, location_y: float, reverse: bool = False) -> List[Restaurant]:
        """
        按照与指定位置的距离排序

        Args:
            restaurants: 餐馆列表
            location_x: 位置x坐标
            location_y: 位置y坐标
            reverse: 是否降序排序，默认为False（升序）

        Returns:
            排序后的餐馆列表
        """
        if not restaurants:
            return []

        def distance_key(restaurant):
            # 计算欧几里得距离
            dx = restaurant.x - location_x
            dy = restaurant.y - location_y
            return (dx * dx + dy * dy) ** 0.5

        return RestaurantSorter.quick_sort(restaurants, distance_key, reverse)

    @staticmethod
    def _custom_heap_top_k(restaurants: List[Restaurant], k: int, key_func, reverse: bool = False) -> List[Restaurant]:
        """
        自实现的堆排序Top-K算法
        不使用Python内置的heapq模块

        Args:
            restaurants: 待排序的餐厅列表
            k: 需要的前K个元素
            key_func: 排序键函数
            reverse: 是否降序排序

        Returns:
            排序后的前K个元素
        """
        if not restaurants or k <= 0:
            return []

        if k >= len(restaurants):
            return RestaurantSorter.quick_sort(restaurants, key_func, reverse)

        # 构建最小堆（用于降序）或最大堆（用于升序）
        heap = []

        for restaurant in restaurants:
            key_value = key_func(restaurant)

            if len(heap) < k:
                # 堆未满，直接添加
                heap.append((key_value, restaurant))
                RestaurantSorter._heapify_up(heap, len(heap) - 1, reverse)
            else:
                # 堆已满，比较并可能替换堆顶
                if reverse:  # 降序，使用最小堆
                    if key_value > heap[0][0]:
                        heap[0] = (key_value, restaurant)
                        RestaurantSorter._heapify_down(heap, 0, reverse)
                else:  # 升序，使用最大堆
                    if key_value < heap[0][0]:
                        heap[0] = (key_value, restaurant)
                        RestaurantSorter._heapify_down(heap, 0, reverse)

        # 提取结果并排序
        result = [restaurant for _, restaurant in heap]
        return RestaurantSorter.quick_sort(result, key_func, reverse)

    @staticmethod
    def _heapify_up(heap: List, index: int, reverse: bool):
        """堆的上浮操作"""
        while index > 0:
            parent = (index - 1) // 2
            if reverse:  # 最小堆
                if heap[index][0] >= heap[parent][0]:
                    break
            else:  # 最大堆
                if heap[index][0] <= heap[parent][0]:
                    break

            heap[index], heap[parent] = heap[parent], heap[index]
            index = parent

    @staticmethod
    def _heapify_down(heap: List, index: int, reverse: bool):
        """堆的下沉操作"""
        size = len(heap)
        while True:
            target = index
            left = 2 * index + 1
            right = 2 * index + 2

            if reverse:  # 最小堆
                if left < size and heap[left][0] < heap[target][0]:
                    target = left
                if right < size and heap[right][0] < heap[target][0]:
                    target = right
            else:  # 最大堆
                if left < size and heap[left][0] > heap[target][0]:
                    target = left
                if right < size and heap[right][0] > heap[target][0]:
                    target = right

            if target == index:
                break

            heap[index], heap[target] = heap[target], heap[index]
            index = target

    @staticmethod
    def _heap_sort_implementation(heap: List, reverse: bool) -> List:
        """自实现的堆排序"""
        # 构建堆
        for i in range(len(heap) // 2 - 1, -1, -1):
            RestaurantSorter._heapify_down_for_sort(heap, i, len(heap), reverse)

        # 排序
        result = []
        heap_size = len(heap)

        for i in range(heap_size - 1, -1, -1):
            # 将堆顶元素移到结果中
            result.append(heap[0][2])  # 取restaurant对象
            # 将最后一个元素移到堆顶
            heap[0] = heap[i]
            # 重新调整堆
            RestaurantSorter._heapify_down_for_sort(heap, 0, i, reverse)

        if reverse:
            # 自实现反转，不使用内置reverse()
            result = RestaurantSorter._reverse_list(result)

        return result

    @staticmethod
    def _reverse_list(lst: List) -> List:
        """自实现的列表反转函数"""
        reversed_list = []
        for i in range(len(lst) - 1, -1, -1):
            reversed_list.append(lst[i])
        return reversed_list

    @staticmethod
    def _heapify_down_for_sort(heap: List, index: int, heap_size: int, reverse: bool):
        """堆排序专用的下沉操作"""
        while True:
            target = index
            left = 2 * index + 1
            right = 2 * index + 2

            if reverse:  # 最大堆（用于降序）
                if left < heap_size and heap[left][0] > heap[target][0]:
                    target = left
                if right < heap_size and heap[right][0] > heap[target][0]:
                    target = right
            else:  # 最小堆（用于升序）
                if left < heap_size and heap[left][0] < heap[target][0]:
                    target = left
                if right < heap_size and heap[right][0] < heap[target][0]:
                    target = right

            if target == index:
                break

            heap[index], heap[target] = heap[target], heap[index]
            index = target

    @staticmethod
    def top_k_by_distance(restaurants: List[Restaurant], k: int, location_x: float, location_y: float) -> List[Restaurant]:
        """
        找出距离指定位置最近的前K个餐馆

        Args:
            restaurants: 餐馆列表
            k: 需要的餐馆数量
            location_x: 位置x坐标
            location_y: 位置y坐标

        Returns:
            距离最近的前K个餐馆
        """
        def distance_key(restaurant):
            # 计算欧几里得距离
            dx = restaurant.x - location_x
            dy = restaurant.y - location_y
            return (dx * dx + dy * dy) ** 0.5

        return RestaurantSorter.top_k_sort(restaurants, k, distance_key, False)  # 升序排序
