from utils.database import db

class User(db.Model):
    """
    User model - represents a user in the system
    """
    __tablename__ = 'users'
    __table_args__ = {'extend_existing': True}

    user_id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(255), nullable=False)
    email = db.Column(db.String(255), nullable=False, unique=True)
    password = db.Column(db.String(255), nullable=True)
    avatar = db.Column(db.String(255), nullable=True, default='default_avatar.jpg')

    def set_password(self, password):
        """Set password"""
        self.password = password

    def check_password(self, password):
        """Check password"""
        return self.password == password

    def to_dict(self):
        """Convert user to dictionary"""
        return {
            'user_id': self.user_id,
            'username': self.username,
            'email': self.email,
            'avatar': self.avatar
        }

