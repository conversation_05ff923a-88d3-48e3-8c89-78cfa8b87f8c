import request from '@/utils/request';

/**
 * 获取美食推荐列表
 * @param {Object} params - 请求参数
 * @param {number} [params.limit=10] - 返回结果数量限制
 * @param {string} [params.cuisine_type] - 菜系类型
 * @param {string} [params.sort_by] - 排序方式，支持 'popularity'(默认), 'rating', 'distance', 'price'
 * @param {string} [params.order] - 排序顺序，支持 'desc'(默认), 'asc'
 * @param {number} [params.location_x] - 位置x坐标，用于按距离排序
 * @param {number} [params.location_y] - 位置y坐标，用于按距离排序
 * @param {number} [params.user_id] - 用户ID，用于个性化推荐
 * @returns {Promise} - 返回美食推荐列表的Promise
 */
export function getFoodRecommendations(params) {
  return request({
    url: '/api/food/recommendations',
    method: 'get',
    params
  });
}

/**
 * 获取用户收藏的餐馆列表
 * @param {number} userId - 用户ID
 * @returns {Promise} - 返回用户收藏餐馆列表的Promise
 */
export function getUserFavoriteRestaurants(userId) {
  return request({
    url: `/api/restaurant/favorite/user/${userId}`,
    method: 'get'
  });
}

/**
 * 检查餐馆是否已收藏
 * @param {number} userId - 用户ID
 * @param {number} restaurantId - 餐馆ID
 * @returns {Promise} - 返回检查结果的Promise
 */
export function checkRestaurantFavorite(userId, restaurantId) {
  return request({
    url: `/api/restaurant/favorite/check`,
    method: 'get',
    params: { user_id: userId, restaurant_id: restaurantId }
  });
}

/**
 * 获取餐馆名称建议
 * @param {string} keyword - 搜索关键词
 * @param {number} [limit=10] - 返回结果数量限制
 * @returns {Promise} - 返回餐馆名称建议的Promise
 */
export function getRestaurantSuggestions(keyword, limit = 10) {
  return request({
    url: '/api/food/suggestions',
    method: 'get',
    params: { keyword, limit }
  });
}

/**
 * 获取热门美食
 * @param {Object} params - 请求参数
 * @param {number} [params.limit=10] - 返回结果数量限制
 * @param {string} [params.cuisine_type] - 菜系类型
 * @returns {Promise} - 返回热门美食的Promise
 */
export function getPopularFood(params) {
  return request({
    url: '/api/food/popular',
    method: 'get',
    params
  });
}

/**
 * 获取特色美食
 * @param {Object} params - 请求参数
 * @param {number} [params.limit=10] - 返回结果数量限制
 * @param {string} [params.cuisine_type] - 菜系类型
 * @param {number} [params.min_rating] - 最低评分，默认4.0
 * @returns {Promise} - 返回特色美食的Promise
 */
export function getSpecialFood(params) {
  return request({
    url: '/api/food/special',
    method: 'get',
    params
  });
}

/**
 * 获取附近餐馆
 * @param {Object} params - 请求参数
 * @param {number} params.location_x - 位置x坐标（必需）
 * @param {number} params.location_y - 位置y坐标（必需）
 * @param {number} [params.limit=10] - 返回结果数量限制
 * @param {number} [params.max_distance] - 最大距离，默认无限制
 * @param {string} [params.cuisine_type] - 菜系类型
 * @returns {Promise} - 返回附近餐馆的Promise
 */
export function getNearbyFood(params) {
  return request({
    url: '/api/food/nearby',
    method: 'get',
    params
  });
}

/**
 * 获取美食详情
 * @param {number} restaurantId - 餐馆ID
 * @returns {Promise} - 返回美食详情的Promise
 */
export function getFoodDetail(restaurantId) {
  return request({
    url: `/api/food/${restaurantId}`,
    method: 'get'
  });
}

/**
 * 搜索美食
 * @param {Object} params - 搜索参数
 * @param {string} [params.keyword] - 搜索关键词，匹配餐馆名称、菜系或菜品
 * @param {string} [params.cuisine_type] - 菜系类型
 * @param {string} [params.sort_by] - 排序方式，支持 'relevance'(默认), 'popularity', 'rating', 'distance', 'price'
 * @param {string} [params.order] - 排序顺序，支持 'desc'(默认), 'asc'
 * @param {number} [params.location_x] - 位置x坐标，用于按距离排序
 * @param {number} [params.location_y] - 位置y坐标，用于按距离排序
 * @param {number} [params.limit] - 返回结果数量限制，默认10
 * @returns {Promise} - 返回搜索结果的Promise
 */
export function searchFood(params) {
  return request({
    url: '/api/food/search',
    method: 'get',
    params
  });
}

/**
 * 获取所有菜系类型
 * @returns {Promise} - 返回所有菜系类型的Promise
 */
export function getCuisineTypes() {
  return request({
    url: '/api/food/cuisine-types',
    method: 'get'
  });
}

/**
 * 获取美食评分
 * @param {number} foodId - 美食ID
 * @returns {Promise} - 返回美食评分的Promise
 */
export function getFoodRating(foodId) {
  return request({
    url: `/api/food/${foodId}/rating`,
    method: 'get'
  });
}

/**
 * 对美食进行评分
 * @param {Object} data - 评分数据
 * @param {number} data.food_id - 美食ID
 * @param {number} data.user_id - 用户ID
 * @param {number} data.rating - 评分（1-5）
 * @returns {Promise} - 返回评分结果的Promise
 */
export function rateFood(data) {
  return request({
    url: '/api/food/rate',
    method: 'post',
    data
  });
}

/**
 * 获取用户对美食的评分
 * @param {number} userId - 用户ID
 * @param {number} foodId - 美食ID
 * @returns {Promise} - 返回用户评分的Promise
 */
export function getUserFoodRating(userId, foodId) {
  return request({
    url: `/api/food/user/${userId}/food/${foodId}`,
    method: 'get'
  });
}

/**
 * 收藏餐馆
 * @param {Object} data - 收藏数据
 * @param {number} data.restaurant_id - 餐馆ID
 * @param {number} data.user_id - 用户ID
 * @returns {Promise} - 返回收藏结果的Promise
 */
export function favoriteRestaurant(data) {
  return request({
    url: '/api/restaurant/favorite',
    method: 'post',
    data
  });
}

/**
 * 取消收藏餐馆
 * @param {Object} data - 取消收藏数据
 * @param {number} data.restaurant_id - 餐馆ID
 * @param {number} data.user_id - 用户ID
 * @returns {Promise} - 返回取消收藏结果的Promise
 */
export function unfavoriteRestaurant(data) {
  return request({
    url: '/api/restaurant/unfavorite',
    method: 'post',
    data
  });
}

/**
 * 检查餐馆是否已收藏（POST方法）
 * @param {Object} data - 检查数据
 * @param {number} data.restaurant_id - 餐馆ID
 * @param {number} data.user_id - 用户ID
 * @returns {Promise} - 返回检查结果的Promise
 */
export function checkRestaurantFavoritePost(data) {
  return request({
    url: '/api/restaurant/favorite/check',
    method: 'post',
    data
  });
}

/**
 * 获取用户收藏的所有餐馆
 * @param {number} userId - 用户ID
 * @returns {Promise} - 返回用户收藏餐馆的Promise
 */
export function getUserRestaurantFavorites(userId) {
  return request({
    url: `/api/restaurant/favorite/user/${userId}`,
    method: 'get'
  });
}

/**
 * 评论餐馆
 * @param {Object} data - 评论数据
 * @param {number} data.restaurant_id - 餐馆ID
 * @param {number} data.user_id - 用户ID
 * @param {string} data.content - 评论内容
 * @param {number} data.rating - 评分（1-5）
 * @returns {Promise} - 返回评论结果的Promise
 */
export function commentRestaurant(data) {
  return request({
    url: '/api/restaurant/comment',
    method: 'post',
    data
  });
}

/**
 * 获取餐馆评论
 * @param {number} restaurantId - 餐馆ID
 * @param {number} [limit=10] - 返回结果数量限制
 * @returns {Promise} - 返回餐馆评论的Promise
 */
export function getRestaurantComments(restaurantId, limit = 10) {
  return request({
    url: `/api/restaurant/comment/${restaurantId}`,
    method: 'get',
    params: { limit }
  });
}

/**
 * 获取基于协同过滤的餐馆推荐
 * @param {number} userId - 用户ID
 * @param {number} [limit=10] - 返回结果数量限制
 * @param {Object} [options] - 其他可选参数
 * @param {string} [options.cuisine_type] - 菜系类型
 * @param {string} [options.sort_by] - 排序方式
 * @param {string} [options.order] - 排序顺序
 * @returns {Promise} - 返回协同过滤推荐的Promise
 */
export function getCollaborativeRecommendations(userId, limit = 10, options = {}) {
  return request({
    url: '/api/food/collaborative-recommendations',
    method: 'get',
    params: {
      user_id: userId,
      limit,
      ...options
    }
  });
}
