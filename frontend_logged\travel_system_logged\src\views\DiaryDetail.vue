<template>
  <div class="diary-detail">
    <div class="container">
      <div v-if="loading" class="loading">
        <el-skeleton :rows="10" animated />
      </div>

      <div v-else-if="error" class="error">
        <el-alert
          title="加载失败"
          type="error"
          description="无法加载日记详情，请稍后再试。"
          show-icon
        />
      </div>

      <div v-else class="diary-content">
        <!-- 封面图 -->
        <div class="diary-cover" v-if="diary.image_url">
          <!-- 返回按钮 - 显示在图片上方 -->
          <div class="back-button-container">
            <el-button
              :icon="ArrowLeft"
              @click="goBack"
              class="back-button"
              circle
              size="small"
            >
            </el-button>
          </div>

          <el-image
            :src="getFullImageUrl(diary.image_url)"
            fit="cover"
            class="cover-image"
          >
            <template #error>
              <div class="image-error">
                <el-icon><PictureFilled /></el-icon>
              </div>
            </template>
          </el-image>
          <div class="cover-overlay">
            <h1 class="cover-title">{{ diary.title }}</h1>
          </div>
        </div>

        <!-- 没有封面图时的返回按钮 -->
        <div v-else class="back-button-container no-cover-back">
          <el-button
            :icon="ArrowLeft"
            @click="goBack"
            class="back-button"
            circle
            size="small"
          >
          </el-button>
        </div>

        <div class="diary-header" :class="{'no-cover': !diary.image_url}">
          <h1 v-if="!diary.image_url">{{ diary.title }}</h1>
          <div class="diary-meta">
            <div class="author-info">
              <el-avatar :size="40" :src="getAvatarUrl(diary.avatar)||require('../../src/assets/belog.jpg')"></el-avatar>
              <div class="author-details">
                <span class="author-name">{{ diary.username }}</span>
                <span class="publish-date">{{ formatDate(diary.created_at) }}</span>
              </div>
              <!-- 编辑按钮 - 只有作者本人可见 -->
              <div v-if="isAuthor" class="author-actions">
                <el-button type="primary" size="small" @click="enterEditMode" class="edit-button">
                  <el-icon><Edit /></el-icon>
                  编辑日记
                </el-button>
              </div>
            </div>
            <div class="diary-stats">
              <span class="stat-item">
                <el-icon><View /></el-icon> {{ diary.popularity || 0 }} 阅读
              </span>
              <span class="stat-item">
                <el-icon :class="{ 'liked-icon': isLiked }"><Star /></el-icon> {{ likeCount || 0 }} 点赞
              </span>
              <span class="stat-item">
                <el-icon :class="{ 'favorited-icon': isFavorited }"><Collection /></el-icon> {{ favoriteCount || 0 }} 收藏
              </span>
            </div>
          </div>
        </div>

        <div class="diary-body">
          <!-- 日记内容区域 -->
          <div class="content-container">
            <div class="content-header">
              <div class="content-icon">
                <el-icon><i class="el-icon-document"></i></el-icon>
              </div>
              <h2 class="content-title">{{ isEditMode ? '编辑日记' : '日记内容' }}</h2>
              <!-- 全文检索按钮 - 只在查看模式显示 -->
              <div v-if="!isEditMode" class="content-search">
                <el-input
                  v-model="searchKeyword"
                  placeholder="搜索内容..."
                  size="small"
                  clearable
                  @input="handleContentSearch"
                  @clear="clearContentSearch"
                  class="search-input"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
                <el-button
                  v-if="searchKeyword"
                  @click="clearContentSearch"
                  size="small"
                  type="info"
                  plain
                  class="clear-search-btn"
                >
                  清除标记
                </el-button>
              </div>
              <!-- 编辑模式控制按钮 -->
              <div v-if="isEditMode" class="edit-controls">
                <el-button @click="cancelEdit" size="small">取消</el-button>
                <el-button type="primary" @click="saveEdit" :loading="editLoading" size="small">保存</el-button>
              </div>
            </div>

            <!-- 查看模式 -->
            <div v-if="!isEditMode" class="content-wrapper">
              <div class="content-card">
                <div class="diary-date">
                  <div class="date-icon">
                    <el-icon><i class="el-icon-date"></i></el-icon>
                  </div>
                  <span>{{ formatDate(diary.created_at) }}</span>
                </div>
                <div class="content-divider"></div>
                <div class="content" v-html="highlightedContent" ref="contentRef"></div>
                <div class="content-tags" v-if="diary.tags && diary.tags.length">
                  <div class="tag-icon">
                    <el-icon><i class="el-icon-collection-tag"></i></el-icon>
                  </div>
                  <div class="tags-list">
                    <el-tag v-for="(tag, index) in diary.tags" :key="index" size="small" effect="plain" class="diary-tag">
                      {{ tag }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>

            <!-- 编辑模式 -->
            <div v-else class="edit-wrapper">
              <div class="edit-form">
                <el-form :model="editForm" label-width="80px" class="diary-edit-form">
                  <el-form-item label="标题">
                    <el-input v-model="editForm.title" placeholder="请输入日记标题" maxlength="100" show-word-limit></el-input>
                  </el-form-item>

                  <el-form-item label="地点">
                    <el-input v-model="editForm.location" placeholder="请输入旅行地点" maxlength="50"></el-input>
                  </el-form-item>

                  <el-form-item label="标签">
                    <div class="tags-input">
                      <el-tag
                        v-for="(tag, index) in editForm.tags"
                        :key="index"
                        closable
                        @close="removeTag(index)"
                        class="edit-tag"
                      >
                        {{ tag }}
                      </el-tag>
                      <el-input
                        v-if="inputVisible"
                        ref="inputRef"
                        v-model="inputValue"
                        size="small"
                        @keyup.enter="handleInputConfirm"
                        @blur="handleInputConfirm"
                        class="tag-input"
                      />
                      <el-button v-else size="small" @click="showInput" class="add-tag-btn">+ 添加标签</el-button>
                    </div>
                  </el-form-item>

                  <el-form-item label="内容">
                    <el-input
                      v-model="editForm.content"
                      type="textarea"
                      :rows="15"
                      placeholder="请输入日记内容，支持HTML格式"
                      maxlength="5000"
                      show-word-limit
                    ></el-input>
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </div>

          <!-- 图片展示区域 -->
          <div class="media-section" v-if="allImages.length > 0">
            <div class="section-header">
              <div class="section-icon photo-icon">
                <el-icon><PictureFilled /></el-icon>
              </div>
              <h2 class="section-title">图片展示 ({{ allImages.length }}张)</h2>
            </div>
            <div class="media-card">
              <div class="image-carousel-container">
                <div class="image-carousel">
                  <el-image
                    :src="getFullImageUrl(allImages[currentImageIndex])"
                    :preview-src-list="allImages.map(img => getFullImageUrl(img))"
                    :initial-index="currentImageIndex"
                    class="carousel-image"
                    fit="contain"
                  >
                    <template #error>
                      <div class="image-error">
                        <el-icon><PictureFilled /></el-icon>
                      </div>
                    </template>
                  </el-image>

                  <!-- 左箭头 -->
                  <div
                    v-if="allImages.length > 1"
                    class="carousel-arrow carousel-arrow-left"
                    @click="previousImage"
                  >
                    <el-icon><ArrowLeft /></el-icon>
                  </div>

                  <!-- 右箭头 -->
                  <div
                    v-if="allImages.length > 1"
                    class="carousel-arrow carousel-arrow-right"
                    @click="nextImage"
                  >
                    <el-icon><ArrowLeft style="transform: rotate(180deg)" /></el-icon>
                  </div>

                  <!-- 指示器 -->
                  <div v-if="allImages.length > 1" class="carousel-indicators">
                    <span
                      v-for="(image, index) in allImages"
                      :key="index"
                      :class="['indicator', { 'active': index === currentImageIndex }]"
                      @click="currentImageIndex = index"
                    ></span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 视频展示区域 -->
          <div class="media-section" v-if="allVideos.length > 0">
            <div class="section-header">
              <div class="section-icon video-icon">
                <el-icon><i class="el-icon-video-camera"></i></el-icon>
                <div class="camera-lens"></div>
              </div>
              <h2 class="section-title">视频展示 ({{ allVideos.length }}个)</h2>
            </div>
            <div class="media-card">
              <div class="video-carousel-container">
                <div class="video-carousel">
                  <div class="video-container">
                    <video controls class="carousel-video" :src="getFullImageUrl(allVideos[currentVideoIndex])">
                      您的浏览器不支持视频播放
                    </video>
                  </div>

                  <!-- 左箭头 -->
                  <div
                    v-if="allVideos.length > 1"
                    class="carousel-arrow carousel-arrow-left"
                    @click="previousVideo"
                  >
                    <el-icon><ArrowLeft /></el-icon>
                  </div>

                  <!-- 右箭头 -->
                  <div
                    v-if="allVideos.length > 1"
                    class="carousel-arrow carousel-arrow-right"
                    @click="nextVideo"
                  >
                    <el-icon><ArrowLeft style="transform: rotate(180deg)" /></el-icon>
                  </div>

                  <!-- 指示器 -->
                  <div v-if="allVideos.length > 1" class="carousel-indicators">
                    <span
                      v-for="(video, index) in allVideos"
                      :key="index"
                      :class="['indicator', { 'active': index === currentVideoIndex }]"
                      @click="currentVideoIndex = index"
                    ></span>
                  </div>

                  <!-- 视频标签 -->
                  <div class="video-label">视频 {{ currentVideoIndex + 1 }} / {{ allVideos.length }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="diary-footer">
          <!-- 互动区域 -->
          <div class="interaction-area">
            <!-- 评分显示 -->
            <div class="rating-display">
              <h3>评分</h3>
              <div class="rating-info">
                <el-rate
                  v-model="diary.avgRating"
                  disabled
                  show-score
                  text-color="#ff9900"
                  score-template="{value}"
                  :score-format="formatRatingScore"
                ></el-rate>
                <span class="rating-count">{{ diary.ratingCount || 0 }} 人评分</span>
                <el-button type="primary" size="small" @click="ratingDialogVisible = true" class="rating-button">
                  <el-icon><StarFilled /></el-icon> 我要评分
                </el-button>
              </div>
            </div>

            <!-- 点赞和收藏按钮 -->
            <div class="interaction-buttons">
              <el-button
                type="primary"
                :plain="!isLiked"
                @click="handleLike"
                class="interaction-button"
              >
                <div class="button-thumb-up">
                  <div class="better-thumb-up-small" :class="{ 'liked': isLiked }">
                    <svg viewBox="0 0 24 24" width="16" height="16" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"></path>
                    </svg>
                  </div>
                  <span>{{ isLiked ? '已点赞' : '点赞' }}</span>
                </div>
              </el-button>

              <el-button
                type="warning"
                :plain="!isFavorited"
                @click="handleFavorite"
                class="interaction-button"
              >
                <el-icon :class="{ 'favorited-icon': isFavorited }"><Collection /></el-icon>
                {{ isFavorited ? '已收藏' : '收藏' }}
              </el-button>
            </div>
          </div>

          <!-- 统计信息显示 -->
          <div class="actions">
            <div class="action-item" @click="handleLike">
              <div class="action-icon-wrapper thumb-up-wrapper" :class="{ 'liked-wrapper': isLiked }">
                <div class="better-thumb-up" :class="{ 'liked': isLiked }">
                  <svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"></path>
                  </svg>
                </div>
              </div>
              <span class="action-text">{{ likeCount || 0 }} 点赞</span>
            </div>
            <div class="action-item" @click="focusComment">
              <div class="action-icon-wrapper">
                <el-icon><ChatDotRound /></el-icon>
              </div>
              <span class="action-text">{{ diary.comments && diary.comments.length || 0 }} 评论</span>
            </div>
            <div class="action-item" @click="handleFavorite">
              <div class="action-icon-wrapper">
                <el-icon :class="{ 'favorited-icon': isFavorited }"><Collection /></el-icon>
              </div>
              <span class="action-text">{{ favoriteCount || 0 }} 收藏</span>
            </div>
            <div class="action-item">
              <div class="action-icon-wrapper">
                <el-icon><View /></el-icon>
              </div>
              <span class="action-text">{{ diary.popularity || 0 }} 浏览</span>
            </div>
            <!-- AIGC动画按钮 -->
            <div class="action-item aigc-action" @click="handleAIGCAnimation">
              <div class="action-icon-wrapper aigc-icon-wrapper" :class="{ 'has-animation': diary.has_aigc_animation }">
                <el-icon><VideoCamera /></el-icon>
              </div>
              <span class="action-text">{{ diary.has_aigc_animation ? '查看动画' : '生成动画' }}</span>
            </div>
          </div>

          <div class="comments-section">
            <h3>评论 ({{ diary.comments && diary.comments.length || 0 }})</h3>

            <div class="comment-form">
              <el-input
                v-model="newComment"
                type="textarea"
                :rows="3"
                placeholder="写下你的评论..."
                ref="commentInput"
              ></el-input>
              <el-button type="primary" @click="submitComment" :disabled="!newComment.trim()">
                发表评论
              </el-button>
            </div>

            <div class="comments-list" v-if="diary.comments && diary.comments.length > 0">
              <div class="comment-item" v-for="(comment, index) in diary.comments" :key="index">
                <div class="comment-avatar">
                  <el-avatar :src="getAvatarUrl(comment.avatar)||require('../../src/assets/belog.jpg')"></el-avatar>
                </div>
                <div class="comment-content">
                  <div class="comment-header">
                    <span class="comment-author">{{ comment.username }}</span>
                    <span class="comment-date">{{ formatDate(comment.createTime) }}</span>
                  </div>
                  <div class="comment-text">{{ comment.content }}</div>
                </div>
              </div>
            </div>

            <div class="no-comments" v-else>
              <el-empty description="此处空空如也" :image-size="100">
                <template #description>
                  <p>此处空空如也，快来发表第一条评论吧！</p>
                </template>
              </el-empty>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 评分对话框 -->
  <el-dialog
    v-model="ratingDialogVisible"
    title="为日志评分"
    width="400px"
    center
  >
    <div class="rating-dialog-content">
      <p class="rating-dialog-tip">请为这篇日志评分（1-5分）</p>
      <el-rate
        v-model="userRating"
        :colors="['#FFCDD2', '#FF5722', '#E53935']"
        :show-text="true"
        :texts="['1分', '2分', '3分', '4分', '5分']"
        class="rating-stars"
      ></el-rate>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="ratingDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitRating" :disabled="!userRating">提交评分</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- AIGC动画查看对话框 -->
  <el-dialog
    v-model="aigcDialogVisible"
    title="🎬 AIGC动画"
    width="80%"
    center
    :before-close="handleAIGCDialogClose"
  >
    <div class="aigc-dialog-content">
      <div class="aigc-video-info">
        <h3>{{ diary.title }} - AI生成动画</h3>
        <p class="aigc-info-text">
          <el-icon><VideoCamera /></el-icon>
          生成时间: {{ formatDate(diary.aigc_animation_created_at) }}
        </p>
      </div>

      <div class="aigc-video-container">
        <video
          ref="aigcVideoRef"
          :src="diary.aigc_animation_url"
          controls
          autoplay
          class="aigc-video"
          @loadstart="onVideoLoadStart"
          @loadeddata="onVideoLoaded"
          @error="onVideoError"
        >
          您的浏览器不支持视频播放
        </video>

        <!-- 加载状态 -->
        <div v-if="videoLoading" class="video-loading">
          <el-icon class="is-loading"><Loading /></el-icon>
          <p>正在加载视频...</p>
        </div>

        <!-- 错误状态 -->
        <div v-if="videoError" class="video-error">
          <el-icon><Warning /></el-icon>
          <p>视频加载失败</p>
          <el-button @click="retryVideo" type="primary" size="small">重试</el-button>
        </div>
      </div>

      <div class="aigc-actions">
        <el-button @click="downloadAIGCVideo" type="primary">
          <el-icon><Download /></el-icon>
          下载视频
        </el-button>
        <el-button @click="shareAIGCVideo" type="success">
          <el-icon><Share /></el-icon>
          分享视频
        </el-button>
        <el-button @click="regenerateAIGC" type="warning">
          <el-icon><Refresh /></el-icon>
          重新生成
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, onMounted, watch, onBeforeUnmount, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { ArrowLeft, View, ChatDotRound, Collection, StarFilled, PictureFilled, Star, VideoCamera, Download, Share, Refresh, Loading, Warning, Edit, Search } from '@element-plus/icons-vue';
import axios from 'axios';
import { getCurrentUser } from '@/api/auth';
import { article as articleApi } from '@/api/articles';
// 这段代码已被下面的onMounted替换，保留作为备份
// onMounted(() => {
//   window.scrollTo(0, 0);
//   fetchDiaryDetail(true);
//   fetchCurrentUser();
//   watch(() => route.params.id, (newId, oldId) => {
//     if (newId !== oldId) {
//       window.scrollTo(0, 0);
//       fetchDiaryDetail(true);
//       loadUserRatingFromStorage();
//     }
//   });
// });

// 组件卸载前不增加浏览量
onBeforeUnmount(() => {
  console.log('组件卸载，不增加浏览量');
});
const route = useRoute();
const router = useRouter();

// 状态
const loading = ref(true);
const error = ref(false);
const newComment = ref('');
const commentInput = ref(null);
const currentUser = ref(null);
const isFavorited = ref(false);
const isLiked = ref(false);
const likeCount = ref(0);
const favoriteCount = ref(0);
const ratingDialogVisible = ref(false);
const userRating = ref(0);

// AIGC动画相关状态
const aigcDialogVisible = ref(false);
const aigcVideoRef = ref(null);
const videoLoading = ref(false);
const videoError = ref(false);

// 编辑模式相关状态
const isEditMode = ref(false);
const editForm = ref({
  title: '',
  content: '',
  location: '',
  tags: []
});
const editLoading = ref(false);

// 标签编辑相关状态
const inputVisible = ref(false);
const inputValue = ref('');
const inputRef = ref(null);

// 全文检索相关状态
const searchKeyword = ref('');
const contentRef = ref(null);

// 轮播图状态
const currentImageIndex = ref(0);
const currentVideoIndex = ref(0);

// 日记数据
const diary = ref({
  article_id: 0,
  user_id: 0,
  username: '用户名',
  title: '旅行日记标题',
  content: '<p>这是日记内容，支持HTML格式。</p><p>可以包含多个段落和格式化文本。</p>',
  location_id: 0,
  location_name: '旅行地点',
  location: {
    name: '旅行地点',
    description: '地点描述'
  },
  popularity: 123, // 浏览量
  evaluation: 0, // 评分
  likes: 45,
  liked: false,
  image_url: null, // 图片URL
  video_url: null, // 视频URL
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  comments: [],
  avgRating: 0, // 平均评分
  ratingCount: 0 // 评分人数
});

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return '未知时间';

  let date;

  // 处理不同类型的时间输入
  if (typeof timestamp === 'string') {
    // 如果是ISO格式的字符串
    if (timestamp.includes('T') || timestamp.includes('Z') || timestamp.includes('+')) {
      date = new Date(timestamp);
    } else {
      // 如果是普通字符串，尝试解析
      date = new Date(timestamp);
    }
  } else if (typeof timestamp === 'number') {
    // 如果是时间戳
    date = new Date(timestamp);
  } else {
    // 其他情况，直接尝试创建Date对象
    date = new Date(timestamp);
  }

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    console.error('无效的时间戳:', timestamp);
    return '未知时间';
  }

  // 使用toLocaleString获取本地时间格式
  const options = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  };

  // 返回本地时间格式的日期和时间
  return date.toLocaleString('zh-CN', options).replace(/\//g, '-');
};

// 格式化评分，保留一位小数
const formatRatingScore = (value) => {
  // 确保值是数字
  const numValue = parseFloat(value);
  if (isNaN(numValue)) return '0.0';

  // 使用toFixed(1)保留一位小数，然后再次转换为数字并格式化
  // 这样可以确保不会出现无限循环小数
  return parseFloat(numValue.toFixed(1)).toFixed(1);
};

// 计算属性：判断是否为作者
const isAuthor = computed(() => {
  return currentUser.value && diary.value && currentUser.value.user_id === diary.value.user_id;
});

// 计算属性：获取所有图片
const allImages = computed(() => {
  if (!diary.value) return [];

  const images = [];

  // 收集所有图片URL
  if (diary.value.image_url) images.push(diary.value.image_url);
  if (diary.value.image_url_2) images.push(diary.value.image_url_2);
  if (diary.value.image_url_3) images.push(diary.value.image_url_3);
  if (diary.value.image_url_4) images.push(diary.value.image_url_4);
  if (diary.value.image_url_5) images.push(diary.value.image_url_5);
  if (diary.value.image_url_6) images.push(diary.value.image_url_6);

  return images.filter(url => url && url.trim() !== '');
});

// 计算属性：获取所有视频
const allVideos = computed(() => {
  if (!diary.value) return [];

  const videos = [];

  // 收集所有视频URL
  if (diary.value.video_url) videos.push(diary.value.video_url);
  if (diary.value.video_url_2) videos.push(diary.value.video_url_2);
  if (diary.value.video_url_3) videos.push(diary.value.video_url_3);

  return videos.filter(url => url && url.trim() !== '');
});

// 计算属性：高亮显示的内容
const highlightedContent = computed(() => {
  if (!diary.value || !diary.value.content) return '';

  // 如果没有搜索关键词，返回原始内容
  if (!searchKeyword.value || !searchKeyword.value.trim()) {
    return diary.value.content;
  }

  const keyword = searchKeyword.value.trim();
  const content = diary.value.content;

  // 使用正则表达式进行全局不区分大小写的搜索和替换
  const regex = new RegExp(`(${keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');

  // 高亮匹配的文本
  return content.replace(regex, '<mark class="search-highlight">$1</mark>');
});

// 轮播图控制函数
const nextImage = () => {
  if (allImages.value.length > 1) {
    currentImageIndex.value = (currentImageIndex.value + 1) % allImages.value.length;
  }
};

const previousImage = () => {
  if (allImages.value.length > 1) {
    currentImageIndex.value = currentImageIndex.value === 0
      ? allImages.value.length - 1
      : currentImageIndex.value - 1;
  }
};

const nextVideo = () => {
  if (allVideos.value.length > 1) {
    currentVideoIndex.value = (currentVideoIndex.value + 1) % allVideos.value.length;
  }
};

const previousVideo = () => {
  if (allVideos.value.length > 1) {
    currentVideoIndex.value = currentVideoIndex.value === 0
      ? allVideos.value.length - 1
      : currentVideoIndex.value - 1;
  }
};

// 处理图片和视频URL
const backendBaseUrl = 'http://localhost:5000';
const getFullImageUrl = (url) => {
  if (!url) return '';
  if (typeof url === 'string') {
    // 如果是相对路径，添加服务器基础URL
    if (url.startsWith('/uploads/')) {
      return backendBaseUrl + url;
    }
    // 如果已经是完整URL或数据URL，直接返回
    if (
      url.startsWith('http://') ||
      url.startsWith('https://') ||
      url.startsWith('data:')
    ) {
      return url;
    }
    // 其他情况，尝试添加基础URL
    if (!url.startsWith('/')) {
      return backendBaseUrl + '/' + url;
    }
    return backendBaseUrl + url;
  }
  return url;
};

// 处理头像URL
const getAvatarUrl = (avatar) => {
  if (!avatar || avatar === '' || avatar === null || avatar === undefined) {
    // 如果没有头像，返回服务器上的默认头像
    return backendBaseUrl + '/uploads/avatars/default_avatar.jpg';
  }
  if (typeof avatar === 'string') {
    if (avatar === 'default_avatar.jpg') {
      return backendBaseUrl + '/uploads/avatars/default_avatar.jpg';
    }

    // 如果是相对路径，添加服务器基础URL
    if (avatar.startsWith('/uploads/')) {
      return backendBaseUrl + avatar;
    }
    if (
      avatar.startsWith('http://') ||
      avatar.startsWith('https://') ||
      avatar.startsWith('data:')
    ) {
      return avatar;
    }
    if (!avatar.startsWith('/')) {
      return backendBaseUrl + '/' + avatar;
    }
    return backendBaseUrl + avatar;
  }
  return backendBaseUrl + '/uploads/avatars/default_avatar.jpg';
};

// 返回游记社区
const goBack = () => {
  router.push('/diary');
};

// 获取日记详情
const fetchDiaryDetail = async (incrementPopularity = true) => {
  loading.value = true;
  error.value = false;

  try {
    const diaryId = route.params.id;
    if (!diaryId) {
      console.error('无效的日记ID');
      error.value = true;
      ElMessage.error('无效的日记ID');
      loading.value = false;
      return;
    }

    console.log('获取日记详情，ID:', diaryId, '增加浏览量:', incrementPopularity);

    // 使用API函数而不是直接使用axios，传递是否增加浏览量的参数
    const response = await articleApi.getArticleById(diaryId, incrementPopularity);
    console.log('日记详情响应:', response);

    if (response.data && response.data.code === 0 && response.data.data) {
      diary.value = response.data.data;
      console.log('日记详情:', diary.value);

      // 确保内容字段存在
      if (!diary.value.content) {
        diary.value.content = diary.value.description || '暂无内容';
      }

      // 初始化点赞数和收藏数
      if (diary.value.likes_count !== undefined) {
        likeCount.value = Number(diary.value.likes_count);
        console.log('从API响应初始化点赞数:', likeCount.value);
      }

      if (diary.value.favorites_count !== undefined) {
        favoriteCount.value = Number(diary.value.favorites_count);
        console.log('从API响应初始化收藏数:', favoriteCount.value);
      }

      // 依次执行后续操作，避免并发请求可能导致的问题
      try {
        // 获取最新的点赞数和收藏数
        const [likeResponse, favoriteResponse] = await Promise.all([
          axios.get(`http://localhost:5000/api/article_like/count/${diaryId}`),
          axios.get(`http://localhost:5000/api/favorites/count/${diaryId}`)
        ]);

        if (likeResponse.data && likeResponse.data.code === 0) {
          likeCount.value = Number(likeResponse.data.data.like_count);
          console.log('更新点赞数:', likeCount.value);
        }

        if (favoriteResponse.data && favoriteResponse.data.code === 0) {
          favoriteCount.value = Number(favoriteResponse.data.data.count);
          console.log('更新收藏数:', favoriteCount.value);
        }

        // 获取平均评分和评分人数（这会确保评分数据正确加载）
        await fetchAverageRating();

        // 检查是否已收藏
        await checkIfFavorited();

        // 获取文章评论
        await fetchArticleComments();

        // 获取用户评分
        await fetchUserRating();

        // 从本地存储加载用户评分
        loadUserRatingFromStorage();

        // 检查是否已点赞
        await checkIfLiked();
      } catch (subError) {
        console.error('获取附加信息失败:', subError);
        // 继续显示主要内容，不影响页面渲染
      }
    } else {
      error.value = true;
      ElMessage.error(response.data?.message || '获取日记详情失败');
    }
  } catch (err) {
    console.error('获取日记详情失败:', err);
    error.value = true;
    ElMessage.error('获取日记详情失败，请稍后再试');
  } finally {
    loading.value = false;
  }
};

// 获取文章评论
const fetchArticleComments = async () => {
  try {
    if (!diary.value || !diary.value.article_id) {
      console.warn('无法获取评论：文章ID不存在');
      return;
    }

    console.log('获取文章评论，文章ID:', diary.value.article_id);

    // 使用API函数获取评论
    const response = await articleApi.getArticleComments(diary.value.article_id);
    console.log('文章评论响应:', response);

    if (response.data && response.data.code === 0) {
      // 处理评论数据
      const comments = response.data.data || [];
      // 打印原始评论数据，帮助调试
      console.log( response.data.data )
      console.log('原始评论数据:', JSON.stringify(comments));
      // 确保comments是数组
      if (Array.isArray(comments)) {
        diary.value.comments = comments.map(comment => ({
          id: comment.id || comment.comment_id,
          username: comment.username || '未知用户',
          avatar: comment.avatar || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
          content: comment.content,
          createTime: comment.created_at || comment.createTime
        }));
      } else if (comments.comments && Array.isArray(comments.comments)) {
        // 处理可能的嵌套结构
        diary.value.comments = comments.comments.map(comment => ({
          id: comment.id || comment.comment_id,
          username: comment.username || '未知用户',
          avatar: comment.avatar || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
          content: comment.content,
          createTime: comment.created_at || comment.createTime
        }));
      } else {
        console.warn('评论数据格式不正确:', comments);
        diary.value.comments = [];
      }

      console.log('处理后的评论数据:', diary.value.comments);
    } else {
      console.warn('获取文章评论失败:', response.data?.message);
      diary.value.comments = [];
    }
  } catch (error) {
    console.error('获取文章评论失败:', error);
    diary.value.comments = [];
  }
};

// 获取用户评分
const fetchUserRating = async () => {
  try {
    if (!diary.value || !diary.value.article_id) {
      console.warn('无法获取用户评分：文章ID不存在');
      return;
    }

    // 尝试从多个来源获取用户ID
    let userId = null;
    if (currentUser.value) {
      userId = currentUser.value.user_id || currentUser.value.id;
    }

    if (!userId) {
      userId = localStorage.getItem('userId');
    }

    if (!userId) {
      console.warn('无法获取用户评分：用户ID不存在');
      return;
    }

    console.log('获取用户评分，用户ID:', userId, '文章ID:', diary.value.article_id);

    // 使用API函数获取用户评分
    const response = await articleApi.getUserArticleScore(userId, diary.value.article_id);
    console.log('用户评分响应:', response);

    if (response.data && response.data.code === 0 && response.data.data) {
      userRating.value = response.data.data.score;
      console.log('设置用户评分为:', userRating.value);

      // 同时更新平均评分和评分人数
      await fetchAverageRating();
    }
  } catch (error) {
    console.error('获取用户评分失败:', error);
    // 继续执行，不影响页面渲染
  }
};

// 获取平均评分和评分人数
const fetchAverageRating = async () => {
  try {
    if (!diary.value || !diary.value.article_id) {
      console.warn('无法获取平均评分：文章ID不存在');
      return;
    }

    console.log('获取平均评分，文章ID:', diary.value.article_id);

    // 使用API函数获取平均评分
    const response = await articleApi.getArticleAverageScore(diary.value.article_id);
    console.log('平均评分响应:', response);

    if (response.data && response.data.code === 0 && response.data.data) {
      // 保存原始值用于日志
      const oldAvgRating = diary.value.avgRating;
      const oldRatingCount = diary.value.ratingCount;

      // 更新值
      const rawAvgRating = response.data.data.average_score || 0;
      // 使用formatRatingScore函数确保一致的格式化
      diary.value.avgRating = parseFloat(formatRatingScore(rawAvgRating));
      diary.value.ratingCount = response.data.data.rating_count || 0;

      console.log('更新平均评分: 从', oldAvgRating, '到', diary.value.avgRating);
      console.log('更新评分人数: 从', oldRatingCount, '到', diary.value.ratingCount);

      // 如果评分为0但评分人数不为0，可能是后端计算错误，尝试手动计算
      if (diary.value.avgRating === 0 && diary.value.ratingCount > 0) {
        console.warn('检测到可能的评分计算错误：评分为0但评分人数不为0');

        // 尝试获取所有评分并手动计算平均值
        try {
          const allScoresResponse = await axios.get(`http://localhost:5000/api/article_score/get_all_scores/${diary.value.article_id}`);
          if (allScoresResponse.data && allScoresResponse.data.code === 0 && allScoresResponse.data.data && allScoresResponse.data.data.scores) {
            const scores = allScoresResponse.data.data.scores;
            if (scores.length > 0) {
              const totalScore = scores.reduce((sum, score) => sum + score.score, 0);
              // 使用formatRatingScore函数确保一致的格式化
              const calculatedAvg = parseFloat(formatRatingScore(totalScore / scores.length));
              console.log('手动计算的平均评分:', calculatedAvg, '基于', scores.length, '个评分');
              diary.value.avgRating = calculatedAvg;

              // 确保评分人数与实际评分数量一致
              diary.value.ratingCount = scores.length;
              console.log('更新评分人数为实际评分数量:', diary.value.ratingCount);
            }
          }
        } catch (err) {
          console.error('手动计算平均评分失败:', err);
        }
      }
    } else {
      // 如果API没有返回有效数据，尝试获取所有评分并手动计算
      try {
        const allScoresResponse = await axios.get(`http://localhost:5000/api/article_score/get_all_scores/${diary.value.article_id}`);
        if (allScoresResponse.data && allScoresResponse.data.code === 0 && allScoresResponse.data.data && allScoresResponse.data.data.scores) {
          const scores = allScoresResponse.data.data.scores;
          if (scores.length > 0) {
            const totalScore = scores.reduce((sum, score) => sum + score.score, 0);
            // 使用formatRatingScore函数确保一致的格式化
            const calculatedAvg = parseFloat(formatRatingScore(totalScore / scores.length));
            console.log('API返回无效数据，手动计算的平均评分:', calculatedAvg, '基于', scores.length, '个评分');
            diary.value.avgRating = calculatedAvg;
            diary.value.ratingCount = scores.length;
          }
        }
      } catch (err) {
        console.error('手动计算平均评分失败:', err);
      }
    }
  } catch (error) {
    console.error('获取平均评分失败:', error);
    // 尝试获取所有评分并手动计算
    try {
      const allScoresResponse = await axios.get(`http://localhost:5000/api/article_score/get_all_scores/${diary.value.article_id}`);
      if (allScoresResponse.data && allScoresResponse.data.code === 0 && allScoresResponse.data.data && allScoresResponse.data.data.scores) {
        const scores = allScoresResponse.data.data.scores;
        if (scores.length > 0) {
          const totalScore = scores.reduce((sum, score) => sum + score.score, 0);
          // 使用formatRatingScore函数确保一致的格式化
          const calculatedAvg = parseFloat(formatRatingScore(totalScore / scores.length));
          console.log('API调用失败，手动计算的平均评分:', calculatedAvg, '基于', scores.length, '个评分');
          diary.value.avgRating = calculatedAvg;
          diary.value.ratingCount = scores.length;
        }
      }
    } catch (err) {
      console.error('手动计算平均评分失败:', err);
    }
  }
};

// 检查是否已点赞
const checkIfLiked = async () => {
  try {
    if (!diary.value || !diary.value.article_id) return;

    // 尝试从多个来源获取用户ID
    let userId = null;

    // 1. 从currentUser对象获取
    if (currentUser.value) {
      userId = currentUser.value.user_id || currentUser.value.id;
    }

    // 2. 从localStorage获取
    if (!userId) {
      userId = localStorage.getItem('userId');
    }

    // 3. 尝试从localStorage获取用户信息
    if (!userId && !currentUser.value) {
      const userStr = localStorage.getItem('currentUser');
      if (userStr) {
        try {
          currentUser.value = JSON.parse(userStr);
          userId = currentUser.value.user_id || currentUser.value.id;
        } catch (e) {
          console.error('解析用户信息失败:', e);
        }
      }
    }

    // 如果仍然没有用户ID，直接返回
    if (!userId) {
      console.warn('无法检查点赞状态：用户ID不存在');
      return;
    }

    // 确保userId是数字类型
    userId = parseInt(userId, 10);
    if (!userId || isNaN(userId)) {
      console.error('无效的用户ID:', userId);
      return;
    }

    // 确保文章ID是数字类型
    const articleId = parseInt(diary.value.article_id, 10);
    if (!articleId || isNaN(articleId)) {
      console.error('无效的文章ID:', diary.value.article_id);
      return;
    }

    console.log('检查是否已点赞，用户ID:', userId, '文章ID:', articleId);

    try {
      // 使用axios直接调用API
      const response = await axios.post('http://localhost:5000/api/article_like/check', {
        user_id: userId,
        article_id: articleId
      });

      console.log('点赞状态响应:', response);

      if (response.data && response.data.code === 0) {
        isLiked.value = response.data.data.is_liked;
        console.log('点赞状态:', isLiked.value ? '已点赞' : '未点赞');
      } else {
        // 如果API返回错误，默认设置为未点赞
        console.warn('API返回错误，默认设置为未点赞:', response.data);
        isLiked.value = false;
      }
    } catch (apiError) {
      console.error('API调用失败:', apiError);
      // API调用失败，默认设置为未点赞
      isLiked.value = false;
    }

    // 获取点赞数
    await fetchLikeCount();
  } catch (error) {
    console.error('检查点赞状态失败:', error);
    // 默认设置为未点赞
    isLiked.value = false;
  }
};

// 获取点赞数
const fetchLikeCount = async () => {
  try {
    if (!diary.value || !diary.value.article_id) return;

    // 确保文章ID是数字类型
    const articleId = parseInt(diary.value.article_id, 10);

    if (!articleId || isNaN(articleId)) {
      console.error('无效的文章ID:', diary.value.article_id);
      return;
    }

    console.log('获取点赞数，文章ID:', articleId);
    const response = await like.getLikeCount(articleId);
    console.log('点赞数响应:', response);

    if (response.data && response.data.code === 0) {
      likeCount.value = response.data.data.like_count;
      console.log('文章点赞数:', likeCount.value);
    }
  } catch (error) {
    console.error('获取点赞数失败:', error);
  }
};

// 点赞/取消点赞
const handleLike = async () => {
  try {
    if (!diary.value || !diary.value.article_id) {
      console.warn('无法执行点赞操作：文章ID不存在');
      ElMessage.warning('无法执行点赞操作');
      return;
    }

    // 尝试从多个来源获取用户ID
    let userId = null;

    // 1. 从currentUser对象获取
    if (currentUser.value) {
      userId = currentUser.value.user_id || currentUser.value.id;
    }

    // 2. 从localStorage获取
    if (!userId) {
      userId = localStorage.getItem('userId');
    }

    // 3. 尝试从localStorage获取用户信息
    if (!userId && !currentUser.value) {
      const userStr = localStorage.getItem('currentUser');
      if (userStr) {
        try {
          currentUser.value = JSON.parse(userStr);
          userId = currentUser.value.user_id || currentUser.value.id;
        } catch (e) {
          console.error('解析用户信息失败:', e);
        }
      }
    }

    // 如果仍然没有用户ID，提示登录
    if (!userId) {
      console.warn('无法执行点赞操作：用户ID不存在');
      ElMessage.warning('请先登录后再点赞');
      router.push('/login');
      return;
    }

    // 确保userId是数字类型
    userId = parseInt(userId, 10);
    if (!userId || isNaN(userId)) {
      console.error('无效的用户ID:', userId);
      ElMessage.warning('用户信息不完整，请重新登录');
      return;
    }

    // 确保文章ID是数字类型
    const articleId = parseInt(diary.value.article_id, 10);
    if (!articleId || isNaN(articleId)) {
      console.error('无效的文章ID:', diary.value.article_id);
      ElMessage.error('无法获取文章信息');
      return;
    }

    console.log('点赞操作 - 用户ID:', userId, '文章ID:', articleId, '当前点赞数:', likeCount.value);

    // 保存当前点赞状态，用于在API调用失败时恢复
    const originalLikeStatus = isLiked.value;
    const originalLikeCount = likeCount.value;

    // 更新UI前先确定要执行的操作
    const willLike = !isLiked.value;

    try {
      // 更新UI状态
      isLiked.value = willLike;

      // 如果当前点赞数是有效数字，则更新UI
      if (!isNaN(likeCount.value)) {
        likeCount.value = Math.max(0, likeCount.value + (willLike ? 1 : -1));
      }

      console.log('UI更新 - 新的点赞状态:', isLiked.value, '新的点赞数:', likeCount.value);

      // 使用axios直接调用API
      const url = willLike
        ? 'http://localhost:5000/api/article_like'
        : 'http://localhost:5000/api/article_like/unlike';

      const response = await axios.post(url, {
        user_id: userId,
        article_id: articleId
      });

      console.log('点赞操作响应:', response);

      if (response.data && response.data.code === 0) {
        // 从API响应更新点赞数
        if (response.data.data && response.data.data.like_count !== undefined) {
          const apiLikeCount = Number(response.data.data.like_count);
          if (!isNaN(apiLikeCount)) {
            likeCount.value = apiLikeCount;
            console.log('从API更新点赞数:', likeCount.value);
          }
        }

        ElMessage({
          message: willLike ? '点赞成功' : '已取消点赞',
          type: willLike ? 'success' : 'info',
          duration: 1500
        });

        // 再次获取最新的点赞数，确保UI显示正确
        setTimeout(async () => {
          try {
            const refreshResponse = await axios.get(`http://localhost:5000/api/article_like/count/${articleId}`);
            if (refreshResponse.data && refreshResponse.data.code === 0) {
              likeCount.value = Number(refreshResponse.data.data.like_count);
              console.log('刷新点赞数:', likeCount.value);
            }
          } catch (refreshError) {
            console.error('刷新点赞数失败:', refreshError);
          }
        }, 500);
      } else {
        // 如果API调用失败，回滚UI更改
        isLiked.value = originalLikeStatus;
        likeCount.value = originalLikeCount;
        ElMessage.error(response.data?.message || '点赞操作失败');
      }
    } catch (apiError) {
      console.error('API调用失败:', apiError);
      // 如果API调用失败，回滚UI更改
      isLiked.value = originalLikeStatus;
      likeCount.value = originalLikeCount;
      ElMessage.error(willLike ? '点赞失败' : '取消点赞失败');
    }
  } catch (error) {
    console.error('点赞操作失败:', error);
    ElMessage.error('点赞操作失败，请稍后再试');
  }
};

// 聚焦评论输入框
const focusComment = () => {
  if (commentInput.value) {
    commentInput.value.focus();
  }
};

// 提交评论
const submitComment = async () => {
  if (!newComment.value.trim()) return;

  try {
    if (!diary.value || !diary.value.article_id) {
      console.warn('无法提交评论：文章ID不存在');
      ElMessage.warning('无法提交评论');
      return;
    }

    // 尝试从多个来源获取用户ID
    let userId = null;

    // 1. 从currentUser对象获取
    if (currentUser.value) {
      userId = currentUser.value.user_id || currentUser.value.id;
    }

    // 2. 从localStorage获取
    if (!userId) {
      userId = localStorage.getItem('userId');
    }

    // 3. 尝试从localStorage获取用户信息
    if (!userId && !currentUser.value) {
      const userStr = localStorage.getItem('currentUser');
      if (userStr) {
        try {
          currentUser.value = JSON.parse(userStr);
          userId = currentUser.value.user_id || currentUser.value.id;
        } catch (e) {
          console.error('解析用户信息失败:', e);
        }
      }
    }

    // 4. 尝试获取当前用户
    if (!userId || !currentUser.value) {
      try {
        const userResponse = await getCurrentUser();
        if (userResponse && userResponse.status === 'success' && userResponse.user) {
          currentUser.value = userResponse.user;
          userId = currentUser.value.user_id || currentUser.value.id;
        }
      } catch (e) {
        console.error('获取当前用户失败:', e);
      }
    }

    // 如果仍然没有用户ID，提示登录
    if (!userId) {
      console.warn('无法提交评论：用户ID不存在');
      ElMessage.warning('请先登录后再评论');
      return;
    }

    console.log('提交评论:', {
      article_id: diary.value.article_id,
      user_id: userId,
      content: newComment.value
    });

    try {
      // 使用API函数提交评论
      const response = await articleApi.addArticleComment(userId, diary.value.article_id, newComment.value);
      console.log('评论提交响应:', response);

      if (response.data && response.data.code === 0) {
        // 确保comments是数组
        if (!Array.isArray(diary.value.comments)) {
          diary.value.comments = [];
        }

        // 添加新评论到列表
        diary.value.comments.unshift({
          id: response.data.data.comment_id,
          username: currentUser.value.username,
          avatar: currentUser.value.avatar || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
          content: newComment.value,
          createTime: new Date()
        });

        // 清空评论框
        newComment.value = '';

        ElMessage.success('评论发表成功');
      } else {
        ElMessage.error(response.data?.message || '评论发表失败');
      }
    } catch (apiError) {
      console.error('API调用失败:', apiError);
      ElMessage.error('评论发表失败，请稍后再试');
    }
  } catch (error) {
    console.error('评论发表失败:', error);
    ElMessage.error('评论发表失败，请稍后再试');
  }
};

// 获取收藏数
const fetchFavoriteCount = async () => {
  try {
    if (!diary.value || !diary.value.article_id) {
      console.warn('无法获取收藏数：文章ID不存在');
      return;
    }

    // 确保文章ID是数字类型
    const articleId = parseInt(diary.value.article_id, 10);
    if (!articleId || isNaN(articleId)) {
      console.error('无效的文章ID:', diary.value.article_id);
      return;
    }

    console.log('获取收藏数，文章ID:', articleId);

    // 调用API获取收藏数
    const response = await axios.get(`http://localhost:5000/api/favorites/count/${articleId}`);
    console.log('收藏数响应:', response);

    if (response.data && response.data.code === 0) {
      favoriteCount.value = response.data.data.count;
      console.log('文章收藏数:', favoriteCount.value);
    }
  } catch (error) {
    console.error('获取收藏数失败:', error);
  }
};

// 检查是否已收藏
const checkIfFavorited = async () => {
  try {
    if (!diary.value || !diary.value.article_id) {
      console.warn('无法检查收藏状态：文章ID不存在');
      return;
    }

    // 确保文章ID是数字类型
    const articleId = parseInt(diary.value.article_id, 10);
    if (!articleId || isNaN(articleId)) {
      console.error('无效的文章ID:', diary.value.article_id);
      return;
    }

    // 尝试从多个来源获取用户ID
    let userId = null;

    // 1. 从currentUser对象获取
    if (currentUser.value) {
      userId = currentUser.value.user_id || currentUser.value.id;
    }

    // 2. 从localStorage获取
    if (!userId) {
      userId = localStorage.getItem('userId');
    }

    // 3. 尝试从localStorage获取用户信息
    if (!userId && !currentUser.value) {
      const userStr = localStorage.getItem('currentUser');
      if (userStr) {
        try {
          currentUser.value = JSON.parse(userStr);
          userId = currentUser.value.user_id || currentUser.value.id;
        } catch (e) {
          console.error('解析用户信息失败:', e);
        }
      }
    }

    // 如果仍然没有用户ID，直接返回
    if (!userId) {
      console.warn('无法检查收藏状态：用户ID不存在');
      return;
    }

    // 确保userId是数字类型
    userId = parseInt(userId, 10);
    if (!userId || isNaN(userId)) {
      console.error('无效的用户ID:', userId);
      return;
    }

    console.log('检查收藏状态 - 用户ID:', userId, '文章ID:', articleId);

    // 使用API函数检查是否已收藏
    try {
      // 调用API检查是否已收藏
      const response = await axios.post(`http://localhost:5000/api/favorites/check`, {
        user_id: userId,
        article_id: articleId
      });

      console.log('收藏状态响应:', response);

      if (response.data && response.data.code === 0) {
        isFavorited.value = response.data.data.is_favorite;
        console.log('收藏状态:', isFavorited.value ? '已收藏' : '未收藏');
      } else {
        // 如果API返回错误，默认设置为未收藏
        console.warn('API返回错误，默认设置为未收藏:', response.data);
        isFavorited.value = false;
      }

      // 获取收藏数
      await fetchFavoriteCount();
    } catch (apiError) {
      console.error('API调用失败:', apiError);
      // API调用失败，默认设置为未收藏
      isFavorited.value = false;
    }
  } catch (error) {
    console.error('检查收藏状态失败:', error);
    // 继续执行，不影响页面渲染
    // 默认设置为未收藏
    isFavorited.value = false;
  }
};

// 收藏/取消收藏
const handleFavorite = async () => {
  try {
    if (!diary.value || !diary.value.article_id) {
      console.warn('无法执行收藏操作：文章ID不存在');
      ElMessage.warning('无法执行收藏操作');
      return;
    }

    // 尝试从多个来源获取用户ID
    let userId = null;

    // 1. 从currentUser对象获取
    if (currentUser.value) {
      userId = currentUser.value.user_id || currentUser.value.id;
    }

    // 2. 从localStorage获取
    if (!userId) {
      userId = localStorage.getItem('userId');
    }

    // 3. 尝试从localStorage获取用户信息
    if (!userId && !currentUser.value) {
      const userStr = localStorage.getItem('currentUser');
      if (userStr) {
        try {
          currentUser.value = JSON.parse(userStr);
          userId = currentUser.value.user_id || currentUser.value.id;
        } catch (e) {
          console.error('解析用户信息失败:', e);
        }
      }
    }

    // 如果仍然没有用户ID，提示登录
    if (!userId) {
      console.warn('无法执行收藏操作：用户ID不存在');
      ElMessage.warning('请先登录后再收藏');
      return;
    }

    // 确保用户ID和文章ID都是数字类型
    const userIdNum = parseInt(userId, 10);
    const articleIdNum = parseInt(diary.value.article_id, 10);

    if (!userIdNum || isNaN(userIdNum)) {
      console.error('无效的用户ID:', userId);
      ElMessage.warning('用户信息不完整，请重新登录');
      return;
    }

    if (!articleIdNum || isNaN(articleIdNum)) {
      console.error('无效的文章ID:', diary.value.article_id);
      ElMessage.error('无法获取文章信息');
      return;
    }

    console.log('收藏操作 - 用户ID:', userIdNum, '文章ID:', articleIdNum);

    // 保存当前收藏状态，用于在API调用失败时恢复
    const originalFavoriteStatus = isFavorited.value;

    // 更新UI前先确定要执行的操作
    const willAddFavorite = !isFavorited.value;

    try {
      let response;

      if (willAddFavorite) {
        // 添加收藏
        // 先更新UI状态
        isFavorited.value = true;

        // 先检查是否已经收藏
        const checkResponse = await axios.post('http://localhost:5000/api/favorites/check', {
          user_id: userIdNum,
          article_id: articleIdNum
        });

        console.log('检查收藏状态响应:', checkResponse);

        if (checkResponse.data && checkResponse.data.code === 0 && checkResponse.data.data.is_favorite) {
          // 已经收藏过了，直接显示成功
          console.log('文章已经收藏过了');
          ElMessage.success('收藏成功');
          return;
        }

        response = await axios.post('http://localhost:5000/api/favorites/add', {
          user_id: userIdNum,
          article_id: articleIdNum
        });
        console.log('收藏响应:', response);

        if (response.data && response.data.code === 0) {
          ElMessage.success('收藏成功');
          // 更新收藏数
          await fetchFavoriteCount();
        } else {
          // 如果API调用失败，回滚UI更改
          isFavorited.value = originalFavoriteStatus;
          ElMessage.error(response.data?.message || '收藏失败');
        }
      } else {
        // 取消收藏
        // 先更新UI状态
        isFavorited.value = false;

        // 先检查是否已经收藏
        const checkResponse = await axios.post('http://localhost:5000/api/favorites/check', {
          user_id: userIdNum,
          article_id: articleIdNum
        });

        console.log('检查收藏状态响应:', checkResponse);

        if (checkResponse.data && checkResponse.data.code === 0 && !checkResponse.data.data.is_favorite) {
          // 没有收藏过，直接显示成功
          console.log('文章没有收藏过');
          ElMessage.success('已取消收藏');
          return;
        }

        response = await axios.post('http://localhost:5000/api/favorites/remove', {
          user_id: userIdNum,
          article_id: articleIdNum
        });
        console.log('取消收藏响应:', response);

        if (response.data && response.data.code === 0) {
          ElMessage.success('已取消收藏');
          // 更新收藏数
          await fetchFavoriteCount();
        } else {
          // 如果API调用失败，回滚UI更改
          isFavorited.value = originalFavoriteStatus;
          ElMessage.error(response.data?.message || '取消收藏失败');
        }
      }
    } catch (apiError) {
      console.error('API调用失败:', apiError);
      // 如果API调用失败，回滚UI更改
      isFavorited.value = originalFavoriteStatus;
      ElMessage.error(willAddFavorite ? '收藏失败' : '取消收藏失败');
    }
  } catch (error) {
    console.error('收藏操作失败:', error);
    ElMessage.error('收藏操作失败，请稍后再试');
  }
};

// 提交评分
const submitRating = async () => {
  try {
    if (!diary.value || !diary.value.article_id) {
      console.warn('无法提交评分：文章ID不存在');
      ElMessage.warning('无法提交评分');
      return;
    }

    // 尝试从多个来源获取用户ID
    let userId = null;

    // 1. 从currentUser对象获取
    if (currentUser.value) {
      userId = currentUser.value.user_id || currentUser.value.id;
    }

    // 2. 从localStorage获取
    if (!userId) {
      userId = localStorage.getItem('userId');
    }

    // 3. 尝试从localStorage获取用户信息
    if (!userId && !currentUser.value) {
      const userStr = localStorage.getItem('currentUser');
      if (userStr) {
        try {
          currentUser.value = JSON.parse(userStr);
          userId = currentUser.value.user_id || currentUser.value.id;
        } catch (e) {
          console.error('解析用户信息失败:', e);
        }
      }
    }

    // 如果仍然没有用户ID，提示登录
    if (!userId) {
      console.warn('无法提交评分：用户ID不存在');
      ElMessage.warning('请先登录后再评分');
      router.push('/login');
      return;
    }

    // 确保userId是数字类型
    userId = parseInt(userId, 10);
    if (!userId || isNaN(userId)) {
      console.error('无效的用户ID:', userId);
      ElMessage.warning('用户信息不完整，请重新登录');
      return;
    }

    // 确保文章ID是数字类型
    const articleId = parseInt(diary.value.article_id, 10);
    if (!articleId || isNaN(articleId)) {
      console.error('无效的文章ID:', diary.value.article_id);
      ElMessage.error('无法获取文章信息');
      return;
    }

    if (!userRating.value) {
      ElMessage.warning('请选择评分');
      return;
    }

    console.log('提交评分:', {
      article_id: articleId,
      user_id: userId,
      score: userRating.value
    });

    // 保存原始评分值，用于在API调用失败时恢复
    const originalRating = userRating.value;
    const originalAvgRating = diary.value.avgRating;
    const originalRatingCount = diary.value.ratingCount;

    try {
      // 确保评分是格式化的
      const formattedScore = parseFloat(formatRatingScore(userRating.value));

      // 直接使用axios调用API提交评分
      const response = await axios.post('http://localhost:5000/api/article_score', {
        user_id: userId,
        article_id: articleId,
        score: formattedScore
      });

      console.log('评分提交响应:', response);

      if (response.data && response.data.code === 0) {
        // 不立即更新UI，而是先获取最新的评分数据
        console.log('评分提交成功，获取最新评分数据');

        // 关闭评分对话框
        ratingDialogVisible.value = false;

        // 显示成功消息
        ElMessage({
          type: 'success',
          message: '评分提交成功，感谢您的反馈！',
          duration: 2000
        });

        // 立即获取最新的评分数据
        await fetchAverageRating();

        console.log('UI更新 - 新的平均评分:', diary.value.avgRating, '评分人数:', diary.value.ratingCount);

        // 保存评分到本地存储，以便在页面刷新后仍能显示
        try {
          // 获取现有的评分记录
          let userRatings = localStorage.getItem('userRatings');
          userRatings = userRatings ? JSON.parse(userRatings) : {};

          // 更新当前文章的评分，确保使用格式化的评分
          userRatings[articleId] = parseFloat(formatRatingScore(userRating.value));

          // 保存回本地存储
          localStorage.setItem('userRatings', JSON.stringify(userRatings));
          console.log('用户评分已保存到本地存储');
        } catch (err) {
          console.error('保存评分到本地存储失败:', err);
        }
      } else {
        // 如果API调用失败，回滚UI更改
        userRating.value = originalRating;
        diary.value.avgRating = originalAvgRating;
        diary.value.ratingCount = originalRatingCount;

        ElMessage.error(response.data?.message || '评分提交失败');
      }
    } catch (apiError) {
      console.error('API调用失败:', apiError);

      // 如果API调用失败，回滚UI更改
      userRating.value = originalRating;
      diary.value.avgRating = originalAvgRating;
      diary.value.ratingCount = originalRatingCount;

      ElMessage.error('评分提交失败，请稍后再试');
    }
  } catch (error) {
    console.error('评分提交失败:', error);
    ElMessage.error('评分提交失败，请稍后再试');
  }
};

// 处理AIGC动画
const handleAIGCAnimation = async () => {
  try {
    if (!diary.value || !diary.value.article_id) {
      ElMessage.warning('无法获取文章信息');
      return;
    }

    // 检查是否已有AIGC动画
    if (diary.value.has_aigc_animation && diary.value.aigc_animation_url) {
      // 如果已有动画，显示动画查看对话框
      showAIGCAnimationDialog();
    } else {
      // 如果没有动画，跳转到AIGC生成页面
      router.push({
        path: '/ai-generator',
        query: {
          tab: 'travel-animation',
          articleId: diary.value.article_id
        }
      });
    }
  } catch (error) {
    console.error('处理AIGC动画失败:', error);
    ElMessage.error('操作失败，请稍后再试');
  }
};

// 显示AIGC动画查看对话框
const showAIGCAnimationDialog = () => {
  if (diary.value.aigc_animation_url) {
    // 重置视频状态
    videoLoading.value = true;
    videoError.value = false;

    // 显示对话框
    aigcDialogVisible.value = true;

    ElMessage({
      type: 'success',
      message: '正在加载AIGC动画...',
      duration: 1500
    });
  } else {
    ElMessage.error('AIGC动画URL不存在');
  }
};

// 视频加载开始
const onVideoLoadStart = () => {
  videoLoading.value = true;
  videoError.value = false;
};

// 视频加载完成
const onVideoLoaded = () => {
  videoLoading.value = false;
  videoError.value = false;
};

// 视频加载错误
const onVideoError = () => {
  videoLoading.value = false;
  videoError.value = true;
  ElMessage.error('视频加载失败，请检查网络连接');
};

// 重试加载视频
const retryVideo = () => {
  if (aigcVideoRef.value) {
    videoLoading.value = true;
    videoError.value = false;
    aigcVideoRef.value.load();
  }
};

// 关闭AIGC对话框
const handleAIGCDialogClose = () => {
  // 暂停视频播放
  if (aigcVideoRef.value) {
    aigcVideoRef.value.pause();
  }
  aigcDialogVisible.value = false;
};

// 下载AIGC视频
const downloadAIGCVideo = () => {
  if (diary.value.aigc_animation_url) {
    const link = document.createElement('a');
    link.href = diary.value.aigc_animation_url;
    link.download = `${diary.value.title}_AIGC动画.mp4`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    ElMessage.success('开始下载视频');
  }
};

// 分享AIGC视频
const shareAIGCVideo = () => {
  if (navigator.share && diary.value.aigc_animation_url) {
    navigator.share({
      title: `${diary.value.title} - AIGC动画`,
      text: `查看这个精彩的AI生成旅游动画！`,
      url: diary.value.aigc_animation_url
    }).then(() => {
      ElMessage.success('分享成功');
    }).catch(() => {
      // 如果原生分享失败，复制链接到剪贴板
      copyToClipboard();
    });
  } else {
    // 复制链接到剪贴板
    copyToClipboard();
  }
};

// 复制链接到剪贴板
const copyToClipboard = () => {
  if (diary.value.aigc_animation_url) {
    navigator.clipboard.writeText(diary.value.aigc_animation_url).then(() => {
      ElMessage.success('视频链接已复制到剪贴板');
    }).catch(() => {
      ElMessage.error('复制失败，请手动复制链接');
    });
  }
};

// 重新生成AIGC动画
const regenerateAIGC = () => {
  aigcDialogVisible.value = false;
  // 跳转到AIGC生成页面
  router.push({
    path: '/ai-generator',
    query: {
      tab: 'travel-animation',
      articleId: diary.value.article_id
    }
  });
};

// 进入编辑模式
const enterEditMode = () => {
  // 初始化编辑表单数据
  editForm.value = {
    title: diary.value.title || '',
    content: diary.value.content || '',
    location: diary.value.location || '',
    tags: [...(diary.value.tags || [])]
  };
  isEditMode.value = true;

  ElMessage.info('已进入编辑模式');
};

// 取消编辑
const cancelEdit = () => {
  isEditMode.value = false;
  // 重置表单数据
  editForm.value = {
    title: '',
    content: '',
    location: '',
    tags: []
  };

  ElMessage.info('已取消编辑');
};

// 保存编辑
const saveEdit = async () => {
  try {
    // 验证必填字段
    if (!editForm.value.title.trim()) {
      ElMessage.error('标题不能为空');
      return;
    }

    if (!editForm.value.content.trim()) {
      ElMessage.error('内容不能为空');
      return;
    }

    if (!editForm.value.location.trim()) {
      ElMessage.error('地点不能为空');
      return;
    }

    editLoading.value = true;

    // 准备更新数据
    const updateData = {
      user_id: currentUser.value.user_id,
      title: editForm.value.title.trim(),
      content: editForm.value.content.trim(),
      location: editForm.value.location.trim(),
      tags: editForm.value.tags
    };

    // 调用更新API
    const response = await axios.put(`http://localhost:5000/api/articles/${diary.value.article_id}`, updateData);

    if (response.data.code === 0) {
      ElMessage.success('日记更新成功');

      // 更新本地数据
      diary.value.title = editForm.value.title;
      diary.value.content = editForm.value.content;
      diary.value.location = editForm.value.location;
      diary.value.tags = [...editForm.value.tags];
      diary.value.updated_at = new Date().toISOString();

      // 退出编辑模式
      isEditMode.value = false;

      // 重新获取日记详情以确保数据同步
      setTimeout(() => {
        fetchDiaryDetail(false); // 不增加浏览量
      }, 500);

    } else {
      ElMessage.error(response.data.message || '更新失败');
    }

  } catch (error) {
    console.error('更新日记失败:', error);
    ElMessage.error('更新失败，请稍后再试');
  } finally {
    editLoading.value = false;
  }
};

// 标签相关函数
const removeTag = (index) => {
  editForm.value.tags.splice(index, 1);
};

const showInput = () => {
  inputVisible.value = true;
  // 下一个tick后聚焦输入框
  setTimeout(() => {
    if (inputRef.value) {
      inputRef.value.focus();
    }
  }, 100);
};

const handleInputConfirm = () => {
  const value = inputValue.value.trim();
  if (value && !editForm.value.tags.includes(value)) {
    editForm.value.tags.push(value);
  }
  inputVisible.value = false;
  inputValue.value = '';
};

// 全文检索相关方法
const handleContentSearch = (keyword) => {
  if (!keyword || !keyword.trim()) {
    clearContentSearch();
    return;
  }

  // 搜索关键词已经通过v-model绑定到searchKeyword
  // highlightedContent计算属性会自动更新
  console.log('搜索关键词:', keyword);

  // 滚动到第一个匹配项
  setTimeout(() => {
    scrollToFirstMatch();
  }, 100);
};

const clearContentSearch = () => {
  searchKeyword.value = '';
  console.log('清除搜索标记');
};

const scrollToFirstMatch = () => {
  if (!contentRef.value) return;

  const firstHighlight = contentRef.value.querySelector('.search-highlight');
  if (firstHighlight) {
    firstHighlight.scrollIntoView({
      behavior: 'smooth',
      block: 'center'
    });
    console.log('滚动到第一个匹配项');
  }
};

// 获取当前用户信息
const fetchCurrentUser = async () => {
  try {
    const response = await getCurrentUser();
    if (response && response.status === 'success' && response.user) {
      currentUser.value = response.user;
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
  }
};

// 从本地存储中获取用户评分
const loadUserRatingFromStorage = () => {
  try {
    if (!diary.value || !diary.value.article_id) return;

    const userRatings = localStorage.getItem('userRatings');
    if (userRatings) {
      const ratings = JSON.parse(userRatings);
      if (ratings[diary.value.article_id]) {
        userRating.value = ratings[diary.value.article_id];
        console.log('从本地存储加载用户评分:', userRating.value);
      }
    }
  } catch (error) {
    console.error('从本地存储加载用户评分失败:', error);
  }
};

// 组件挂载时获取数据
onMounted(() => {
  // 滚动到页面顶部
  window.scrollTo(0, 0);

  // 首次进入页面时增加浏览量
  console.log('页面初始化 - 增加浏览量');
  fetchDiaryDetail(true);
  fetchCurrentUser();

  // 监听路由变化，确保在文章ID变化时重新加载数据
  watch(() => route.params.id, (newId, oldId) => {
    if (newId !== oldId) {
      // 滚动到页面顶部
      window.scrollTo(0, 0);

      // 路由变化时也增加浏览量（新文章）
      console.log('路由变化 - 增加浏览量');
      fetchDiaryDetail(true);
      loadUserRatingFromStorage();
    }
  });
});

// 组件卸载前不增加浏览量
onBeforeUnmount(() => {
  console.log('组件卸载，不增加浏览量');
});
</script>

<style scoped>
.diary-detail {
  padding: 0;
  background-color: #f9f9f9;
  min-height: 100vh;
  font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, Roboto, 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes floatUp {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
  100% {
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

.container {
  max-width: 900px;
  margin: 0 auto;
  background: #fff;
  border-radius: 0;
  box-shadow: 0 2px 20px 0 rgba(0, 0, 0, 0.1);
  padding: 0;
  overflow: hidden;
}

/* 移除旧的返回按钮样式，使用新的样式 */

/* 封面图样式 */
.diary-cover {
  position: relative;
  width: 100%;
  height: 400px;
  overflow: hidden;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
  padding: 30px 40px;
  color: white;
}

.cover-title {
  margin: 0;
  font-size: 32px;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0,0,0,0.5);
}

/* 日记头部样式 */
.diary-header {
  margin: 0;
  padding: 30px 40px;
  border-bottom: 1px solid #eee;
}

.diary-header.no-cover {
  padding-top: 60px;
}

.diary-header h1 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 28px;
  color: #303133;
}

.diary-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.author-details {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-weight: 600;
  color: #303133;
  font-size: 16px;
}

.publish-date {
  color: #909399;
  font-size: 14px;
  margin-top: 4px;
}

.diary-stats {
  display: flex;
  gap: 15px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #606266;
  font-size: 14px;
}

/* 日记内容样式 */
.diary-body {
  padding: 0 40px;
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

/* 内容区域样式 */
.content-container {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  margin-bottom: 30px;
  border: none;
  position: relative;
  animation: fadeIn 0.6s ease-out;
  animation-fill-mode: both;
  animation-delay: 0.1s;
}

.content-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.04);
}

.content-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  opacity: 0;
  transition: opacity 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  border-radius: 16px;
  z-index: -1;
}

.content-container:hover::after {
  opacity: 1;
}

.content-header, .section-header {
  display: flex;
  align-items: center;
  padding: 24px 28px;
  background: linear-gradient(to bottom, #ffffff, #fafafa);
  color: #42b983;
  border-bottom: none;
  position: relative;
  margin-bottom: 5px;
}

.content-header::after, .section-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 28px;
  right: 28px;
  height: 1px;
  background-color: #f0f0f0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
}

.content-icon, .section-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 38px;
  height: 38px;
  background-color: transparent;
  margin-right: 16px;
  font-size: 20px;
  color: #42b983;
  opacity: 0.9;
  transition: transform 0.3s ease;
  position: relative;
}

.content-icon::before {
  content: '';
  position: absolute;
  width: 28px;
  height: 34px;
  background-color: #f0f9f6;
  border-radius: 4px;
  border-left: 3px solid #42b983;
  z-index: -1;
  transform: rotate(-5deg);
  box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.12);
}

.content-icon::after {
  content: '';
  position: absolute;
  width: 24px;
  height: 4px;
  background-color: #42b983;
  opacity: 0.3;
  border-radius: 2px;
  bottom: -3px;
  left: 6px;
  transform: rotate(-5deg);
}

.content-container:hover .content-icon {
  transform: rotate(5deg) scale(1.1);
}

.content-container:hover .content-icon::before {
  transform: rotate(-10deg);
  background-color: #e6f7f1;
}

.content-title, .section-title {
  font-size: 19px;
  font-weight: 600;
  margin: 0;
  letter-spacing: 0.3px;
  color: #262626;
  font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, Roboto, 'Helvetica Neue', sans-serif;
  padding: 3px 0;
}

.content-wrapper {
  padding: 0;
  margin: 0;
}

.content-card {
  background-color: #fff;
  padding: 24px;
  transition: all 0.3s ease;
}

.diary-date {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  color: #bfbfbf;
  font-size: 12px;
  font-weight: 400;
  font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, Roboto, 'Helvetica Neue', sans-serif;
  transition: all 0.3s ease;
  opacity: 0.8;
}

.content-container:hover .diary-date {
  opacity: 1;
}

.date-icon {
  margin-right: 6px;
  color: #bfbfbf;
  font-size: 14px;
  transition: transform 0.3s ease;
}

.content-container:hover .date-icon {
  transform: translateX(-2px);
}

.content-divider {
  display: none;
}

.content {
  line-height: 1.8;
  font-size: 15px;
  color: #434343;
  white-space: pre-wrap;
  font-weight: 400;
  letter-spacing: 0.2px;
  font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, Roboto, 'Helvetica Neue', sans-serif;
  transition: color 0.3s ease;
}

.content-container:hover .content {
  color: #262626;
}

.content p {
  margin-bottom: 16px;
  transition: transform 0.3s ease;
  transform-origin: left center;
  animation: fadeIn 0.5s ease-out;
  animation-fill-mode: both;
}

.content p:nth-child(1) {
  animation-delay: 0.2s;
}

.content p:nth-child(2) {
  animation-delay: 0.3s;
}

.content p:nth-child(3) {
  animation-delay: 0.4s;
}

.content p:nth-child(4) {
  animation-delay: 0.5s;
}

.content p:nth-child(5) {
  animation-delay: 0.6s;
}

.content p:hover {
  transform: translateX(2px);
}

.content-tags {
  display: flex;
  align-items: center;
  margin-top: 24px;
  flex-wrap: wrap;
  padding-top: 16px;
  border-top: 1px solid #f5f5f5;
  transition: all 0.3s ease;
}

.content-container:hover .content-tags {
  border-color: #e6f7ff;
}

.tag-icon {
  margin-right: 8px;
  color: #bfbfbf;
  font-size: 14px;
  transition: transform 0.3s ease;
}

.content-container:hover .tag-icon {
  transform: rotate(15deg);
  color: #42b983;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.diary-tag {
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  background-color: #f9f9f9 !important;
  border-color: transparent !important;
  color: #8c8c8c !important;
  font-size: 12px !important;
  height: 24px !important;
  line-height: 22px !important;
  padding: 0 12px !important;
  border-radius: 12px !important;
  font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, Roboto, 'Helvetica Neue', sans-serif !important;
  font-weight: 400 !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02) !important;
}

.diary-tag:hover {
  background-color: #e6f7ff !important;
  color: #1890ff !important;
  border-color: transparent !important;
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 3px 6px rgba(24, 144, 255, 0.1) !important;
}

/* 返回按钮样式 */
.back-button-container {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 100;
}

/* 没有封面图时的返回按钮 */
.no-cover-back {
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: 1000;
}

.back-button {
  background-color: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.8);
  color: #409EFF;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  width: 40px;
  height: 40px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.back-button:hover {
  background-color: #409EFF;
  border-color: #409EFF;
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 6px 20px rgba(64, 158, 255, 0.3);
  color: white;
}

.back-button .el-icon {
  font-size: 18px;
}

/* 位置信息样式 */
.location-info {
  display: flex;
  align-items: center;
  margin-top: 20px;
  padding: 12px 15px;
  background-color: #f0f9ff;
  border-radius: 8px;
  border-left: 4px solid #1890ff;
}

.location-icon {
  font-size: 20px;
  color: #1890ff;
  margin-right: 10px;
}

/* 媒体区域样式 */
.media-section {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
  margin: 0 0 30px 0;
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  border: none;
  position: relative;
  animation: fadeIn 0.6s ease-out;
  animation-fill-mode: both;
  animation-delay: 0.2s;
}

.media-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.04);
}

.media-section::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  opacity: 0;
  transition: opacity 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  border-radius: 16px;
  z-index: -1;
}

.media-section:hover::after {
  opacity: 1;
}

.section-header {
  background: #ffffff;
  color: #1890ff;
  border-bottom: none;
  padding: 16px 20px;
  position: relative;
}

.section-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 20px;
  right: 20px;
  height: 1px;
  background-color: #f5f5f5;
}

.section-icon.video-icon::before {
  content: '';
  position: absolute;
  width: 28px;
  height: 20px;
  background-color: #e6f7ff;
  border-radius: 4px;
  border: 1.5px solid #1890ff;
  z-index: -1;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
}

.section-icon.video-icon::after {
  content: '';
  position: absolute;
  width: 10px;
  height: 10px;
  background-color: #1890ff;
  border-radius: 50%;
  top: 9px;
  left: 9px;
  box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.2);
}

.section-icon.video-icon .camera-lens {
  position: absolute;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #fff;
  top: 11px;
  left: 11px;
}

.section-icon.photo-icon::before {
  content: '';
  position: absolute;
  width: 30px;
  height: 24px;
  background-color: #e6f7ff;
  border-radius: 4px;
  border: 1.5px solid #1890ff;
  z-index: -1;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
}

.section-icon.photo-icon::after {
  content: '';
  position: absolute;
  width: 12px;
  height: 12px;
  border: 2.5px solid #1890ff;
  border-radius: 50%;
  top: 6px;
  left: 14px;
  box-shadow: inset 0 0 2px rgba(24, 144, 255, 0.5);
}

.media-section:hover .section-icon {
  transform: rotate(5deg) scale(1.1);
}

.media-section:hover .section-icon.video-icon::before,
.media-section:hover .section-icon.photo-icon::before {
  background-color: #d4edff;
}

.media-card {
  background-color: #fff;
  padding: 20px;
}

/* 轮播图容器样式 */
.image-carousel-container,
.video-carousel-container {
  position: relative;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.image-carousel,
.video-carousel {
  position: relative;
  width: 100%;
  height: 400px;
  border-radius: 12px;
  overflow: hidden;
  background: #f5f5f5;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.carousel-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: #fff;
}

.carousel-video {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: #000;
}

/* 轮播图箭头样式 */
.carousel-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
  color: white;
}

.carousel-arrow:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: translateY(-50%) scale(1.1);
}

.carousel-arrow-left {
  left: 15px;
}

.carousel-arrow-right {
  right: 15px;
}

.carousel-arrow .el-icon {
  font-size: 18px;
}

/* 轮播图指示器样式 */
.carousel-indicators {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
  z-index: 10;
}

.indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator.active {
  background: #409EFF;
  transform: scale(1.2);
}

.indicator:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: scale(1.1);
}

/* 视频标签样式 */
.video-label {
  position: absolute;
  top: 15px;
  left: 15px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
  z-index: 10;
}

/* 图片网格布局 */
.images-grid {
  display: grid;
  gap: 15px;
  perspective: 1000px;
  padding: 5px;
}

/* 根据图片数量调整网格布局 */
.images-grid:has(:nth-child(1):last-child) {
  grid-template-columns: 1fr;
}

.images-grid:has(:nth-child(2):last-child) {
  grid-template-columns: 1fr 1fr;
}

.images-grid:has(:nth-child(3):last-child) {
  grid-template-columns: 2fr 1fr 1fr;
}

.images-grid:has(:nth-child(4):last-child) {
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
}

.images-grid:has(:nth-child(5):last-child) {
  grid-template-columns: 2fr 1fr 1fr;
  grid-template-rows: 1fr 1fr;
}

.images-grid:has(:nth-child(6):last-child) {
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: 1fr 1fr;
}

/* 第一张图片在多图布局中占据更大空间 */
.images-grid:has(:nth-child(3):last-child) :first-child,
.images-grid:has(:nth-child(5):last-child) :first-child {
  grid-row: 1 / 3;
}

/* 视频网格布局 */
.videos-grid {
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.video-item {
  position: relative;
}

.video-label {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
}

.images {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  perspective: 1000px;
  padding: 5px;
}

.detail-image {
  width: 100%;
  height: 250px;
  object-fit: cover;
  border-radius: 10px;
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  border: none;
  filter: saturate(1.02);
  transform-origin: center center;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  animation: fadeIn 0.8s ease-out;
  animation-fill-mode: both;
  cursor: pointer;
}

/* 单张图片时使用不同的样式 */
.images-grid:has(:nth-child(1):last-child) .detail-image {
  height: 400px;
  object-fit: contain;
  max-height: 550px;
}

/* 第一张图片在多图布局中的特殊样式 */
.images-grid:has(:nth-child(3):last-child) :first-child .detail-image,
.images-grid:has(:nth-child(5):last-child) :first-child .detail-image {
  height: 100%;
  min-height: 300px;
}

/* 为多张图片添加交错动画效果 */
.images > :nth-child(1) .detail-image {
  animation-delay: 0.3s;
}

.images > :nth-child(2) .detail-image {
  animation-delay: 0.4s;
}

.images > :nth-child(3) .detail-image {
  animation-delay: 0.5s;
}

.images > :nth-child(4) .detail-image {
  animation-delay: 0.6s;
}

.images > :nth-child(5) .detail-image {
  animation-delay: 0.7s;
}

.detail-image:hover {
  transform: scale(1.01) translateY(-5px);
  filter: saturate(1.05) contrast(1.02);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
}

.image-error {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 180px;
  background: #fafafa;
  color: #d9d9d9;
  font-size: 13px;
  border-radius: 8px;
  border: 1px dashed #f0f0f0;
  font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, Roboto, 'Helvetica Neue', sans-serif;
  transition: all 0.3s ease;
}

.image-error:hover {
  background: #f5f5f5;
  border-color: #e6f7ff;
}

.diary-footer {
  margin-top: 30px;
}

.interaction-area {
  margin: 0;
  padding: 30px 40px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
}

.interaction-buttons {
  display: flex;
  gap: 20px;
  margin-top: 20px;
  justify-content: center;
}

.interaction-button {
  min-width: 120px;
  transition: all 0.3s;
  border-radius: 50px;
  padding: 12px 25px;
  font-weight: 600;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.interaction-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.interaction-button .el-icon {
  margin-right: 8px;
  font-size: 18px;
}

.button-thumb-up {
  display: flex;
  align-items: center;
  justify-content: center;
}

.button-thumb-up span {
  margin-left: 5px;
}

/* 按钮中的大拇指图标 */
.thumb-up-icon-small {
  position: relative;
  width: 16px;
  height: 16px;
  display: inline-block;
  margin-right: 5px;
  transform: scale(1);
  transition: transform 0.3s ease;
}

.thumb-up-icon-small.liked {
  transform: scale(1.2);
}

.thumb-small {
  position: absolute;
  width: 6px;
  height: 12px;
  background-color: currentColor;
  border-radius: 2px;
  bottom: 0;
  left: 5px;
  transition: all 0.3s ease;
}

.thumb-end-small {
  position: absolute;
  width: 12px;
  height: 6px;
  background-color: currentColor;
  border-radius: 2px;
  top: 0;
  left: 0;
  transform: rotate(-20deg);
  transform-origin: bottom right;
  transition: all 0.3s ease;
}

.interaction-button:hover .thumb-up-icon-small {
  animation: thumbUpSmall 1s;
}

@keyframes thumbUpSmall {
  0% {
    transform: scale(1);
  }
  40% {
    transform: scale(1.3) translateY(-2px);
  }
  80% {
    transform: scale(1.2) translateY(0);
  }
  100% {
    transform: scale(1);
  }
}

.actions {
  display: flex;
  justify-content: space-around;
  margin: 0;
  padding: 20px 0;
  border-bottom: 1px solid #eee;
  background-color: white;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px 15px;
  border-radius: 4px;
  transition: all 0.3s;
  cursor: pointer;
}

.action-item:hover {
  transform: translateY(-2px);
}

.action-icon-wrapper {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f2f5;
  border-radius: 50%;
  margin-bottom: 8px;
  transition: all 0.3s;
}

.action-item:hover .action-icon-wrapper {
  background-color: #e6f7ff;
  transform: scale(1.1);
}

.action-item .el-icon {
  font-size: 22px;
}

.action-text {
  font-size: 14px;
  margin-top: 5px;
  font-weight: 500;
}

/* AIGC动画按钮特殊样式 */
.aigc-action {
  position: relative;
}

.aigc-icon-wrapper {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transition: all 0.3s ease;
}

.aigc-icon-wrapper.has-animation {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  animation: pulse 2s infinite;
}

.aigc-action:hover .aigc-icon-wrapper {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: scale(1.1) rotate(5deg);
}

.aigc-action:hover .aigc-icon-wrapper.has-animation {
  background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
  transform: scale(1.1) rotate(-5deg);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(76, 175, 80, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
  }
}

.comments-section {
  margin: 0;
  padding: 40px;
  background-color: #fff;
}

.comments-section h3 {
  margin-bottom: 25px;
  font-size: 22px;
  color: #303133;
  border-left: 4px solid #409EFF;
  padding-left: 15px;
  font-weight: 600;
}

.comment-form {
  margin-bottom: 30px;
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.comment-form .el-button {
  margin-top: 15px;
  float: right;
  border-radius: 50px;
  padding: 12px 25px;
  font-weight: 600;
}

.comments-list {
  margin-top: 30px;
}

.comment-item {
  display: flex;
  margin-bottom: 25px;
  padding: 20px;
  border-radius: 12px;
  background-color: #f8f9fa;
  transition: all 0.3s;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.comment-item:hover {
  background-color: #f0f0f0;
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.08);
}

.comment-avatar {
  margin-right: 20px;
}

.comment-content {
  flex: 1;
}

.comment-header {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.comment-author {
  font-weight: 600;
  margin-right: 10px;
  color: #409EFF;
  font-size: 16px;
}

.comment-date {
  color: #909399;
  font-size: 13px;
  background-color: rgba(144, 147, 153, 0.1);
  padding: 3px 8px;
  border-radius: 12px;
}

.comment-text {
  line-height: 1.8;
  color: #303133;
  font-size: 15px;
}

.no-comments {
  text-align: center;
  color: #909399;
  padding: 40px 0;
  background-color: #f8f9fa;
  border-radius: 12px;
  margin-top: 20px;
}

/* 点赞和收藏图标样式 */
.liked-icon {
  color: #E53935 !important;
  transform: scale(1.2);
  transition: all 0.3s;
}

.favorited-icon {
  color: #FF9800 !important;
  transform: scale(1.2);
  transition: all 0.3s;
}

.action-item:hover .favorited-icon {
  animation: starRotate 1s;
}

/* 大拇指点赞样式 */
.thumb-up-wrapper {
  position: relative;
  transition: all 0.3s ease;
  overflow: visible !important;
}

.liked-wrapper {
  background-color: rgba(229, 57, 53, 0.1) !important;
}

.better-thumb-up {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.better-thumb-up svg {
  stroke: #909399;
  transition: all 0.3s ease;
}

.better-thumb-up.liked svg {
  stroke: #E53935;
  fill: rgba(229, 57, 53, 0.1);
}

.action-item:hover .better-thumb-up {
  animation: thumbUp 1s;
}

.better-thumb-up-small {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
  margin-right: 5px;
}

.better-thumb-up-small svg {
  stroke: currentColor;
  transition: all 0.3s ease;
}

.better-thumb-up-small.liked svg {
  fill: rgba(255, 255, 255, 0.2);
}

@keyframes thumbUp {
  0% {
    transform: scale(1);
  }
  40% {
    transform: scale(1.3) translateY(-5px);
  }
  80% {
    transform: scale(1.2) translateY(0);
  }
  100% {
    transform: scale(1);
  }
}

/* 收藏图标动画 */
@keyframes starRotate {
  0% {
    transform: scale(1.2) rotate(0deg);
  }
  25% {
    transform: scale(1.2) rotate(15deg);
  }
  50% {
    transform: scale(1.2) rotate(0deg);
  }
  75% {
    transform: scale(1.2) rotate(-15deg);
  }
  100% {
    transform: scale(1.2) rotate(0deg);
  }
}

/* 已收藏状态下的图标容器样式 */
.action-item:has(.favorited-icon) .action-icon-wrapper {
  background-color: rgba(255, 152, 0, 0.1);
}

/* 评分对话框样式 */
.rating-dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 0;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.rating-dialog-tip {
  margin-bottom: 20px;
  color: #303133;
  font-size: 16px;
  font-weight: 500;
}

.rating-stars {
  margin-bottom: 20px;
  transform: scale(1.2);
}

.rating-stars :deep(.el-rate__icon) {
  margin-right: 8px;
  font-size: 24px;
}

.rating-stars :deep(.el-rate__text) {
  font-size: 14px;
  margin-left: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 10px;
}

/* 媒体区域样式 */
.media-section {
  margin-bottom: 30px;
}

.media-section h3 {
  margin: 0;
  font-size: 15px;
  color: #595959;
  font-weight: 500;
  letter-spacing: 0.2px;
  font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, Roboto, 'Helvetica Neue', sans-serif;
}

/* 视频容器样式 */
.video-container {
  width: 100%;
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  border: none;
  background-color: #000;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transform-origin: center center;
  animation: fadeIn 0.8s ease-out;
  animation-fill-mode: both;
  animation-delay: 0.3s;
}

.video-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, rgba(0,0,0,0) 70%);
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: 1;
  pointer-events: none;
}

.video-container:hover {
  transform: scale(1.01) translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.video-container:hover::before {
  opacity: 1;
}

.diary-video {
  width: 100%;
  max-height: 450px;
  border-radius: 8px;
  display: block;
  background-color: #000;
  filter: brightness(1.02);
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.video-container:hover .diary-video {
  filter: brightness(1.05) contrast(1.02);
}

/* 评分显示样式 */
.rating-display {
  margin-bottom: 0;
  padding: 25px;
  background: linear-gradient(135deg, #fff9c4 0%, #fffde7 100%);
  border-radius: 0;
  box-shadow: 0 4px 20px rgba(255, 152, 0, 0.1);
  border-left: none;
  position: relative;
  overflow: hidden;
}

.rating-display::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6px;
  height: 100%;
  background: linear-gradient(to bottom, #FFC107, #FF9800);
}

.rating-display h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 22px;
  color: #FF9800;
  font-weight: 700;
  letter-spacing: 0.5px;
}

.rating-info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.rating-info :deep(.el-rate__icon) {
  font-size: 24px;
  margin-right: 6px;
  transition: transform 0.3s;
}

.rating-info:hover :deep(.el-rate__icon) {
  transform: scale(1.1);
}

.rating-info :deep(.el-rate__text) {
  font-size: 20px;
  font-weight: 700;
  color: #FF9800;
  background: -webkit-linear-gradient(#FFC107, #FF9800);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  min-width: 40px;
  text-align: center;
}

.rating-count {
  margin-left: 15px;
  color: #FF9800;
  font-size: 15px;
  background-color: rgba(255, 152, 0, 0.1);
  padding: 5px 12px;
  border-radius: 20px;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(255, 152, 0, 0.2);
}

.rating-button {
  margin-left: auto;
  margin-right: 10px;
  border-radius: 50px;
  padding: 8px 20px;
  font-weight: 600;
  background: linear-gradient(135deg, #FFC107, #FF9800);
  border: none;
  color: white;
  box-shadow: 0 4px 10px rgba(255, 152, 0, 0.3);
  transition: all 0.3s;
}

.rating-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(255, 152, 0, 0.4);
}

/* AIGC动画对话框样式 */
.aigc-dialog-content {
  padding: 20px 0;
}

.aigc-video-info {
  text-align: center;
  margin-bottom: 20px;
}

.aigc-video-info h3 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.aigc-info-text {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #606266;
  font-size: 14px;
  margin: 0;
}

.aigc-video-container {
  position: relative;
  width: 100%;
  max-width: 800px;
  margin: 0 auto 20px auto;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
}

.aigc-video {
  width: 100%;
  height: auto;
  max-height: 450px;
  display: block;
}

.video-loading,
.video-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;
  background: rgba(0, 0, 0, 0.7);
  padding: 20px;
  border-radius: 8px;
}

.video-loading .el-icon,
.video-error .el-icon {
  font-size: 32px;
  margin-bottom: 10px;
}

.video-loading p,
.video-error p {
  margin: 0 0 10px 0;
  font-size: 16px;
}

.aigc-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
}

.aigc-actions .el-button {
  min-width: 120px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .aigc-video-container {
    margin: 0 -20px 20px -20px;
    border-radius: 0;
  }

  .aigc-actions {
    flex-direction: column;
    align-items: center;
  }

  .aigc-actions .el-button {
    width: 200px;
  }
}

/* 编辑模式样式 */
.author-actions {
  margin-left: auto;
}

.edit-button {
  border-radius: 20px;
  padding: 6px 16px;
  font-size: 13px;
  font-weight: 500;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  border: none;
  color: white;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
}

.edit-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  background: linear-gradient(135deg, #337ecc, #529b2e);
}

.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.edit-controls {
  display: flex;
  gap: 10px;
}

.edit-wrapper {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.diary-edit-form {
  max-width: 100%;
}

.diary-edit-form .el-form-item {
  margin-bottom: 25px;
}

.diary-edit-form .el-form-item__label {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.diary-edit-form .el-input__wrapper {
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.diary-edit-form .el-input__wrapper:hover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.diary-edit-form .el-textarea__inner {
  border-radius: 8px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  line-height: 1.6;
  resize: vertical;
  min-height: 300px;
}

.tags-input {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  min-height: 32px;
  padding: 4px 0;
}

.edit-tag {
  background: linear-gradient(135deg, #E1F5FE, #B3E5FC);
  border: 1px solid #81D4FA;
  color: #0277BD;
  font-weight: 500;
  border-radius: 16px;
  padding: 4px 12px;
  font-size: 12px;
  transition: all 0.3s ease;
}

.edit-tag:hover {
  background: linear-gradient(135deg, #B3E5FC, #81D4FA);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(1, 119, 189, 0.2);
}

.tag-input {
  width: 120px;
  height: 24px;
}

.tag-input .el-input__wrapper {
  height: 24px;
  padding: 0 8px;
}

.tag-input .el-input__inner {
  height: 22px;
  line-height: 22px;
  font-size: 12px;
}

.add-tag-btn {
  height: 24px;
  padding: 0 12px;
  font-size: 12px;
  border-radius: 12px;
  border: 1px dashed #d9d9d9;
  background: #fafafa;
  color: #666;
  transition: all 0.3s ease;
}

.add-tag-btn:hover {
  border-color: #409EFF;
  color: #409EFF;
  background: #f0f9ff;
}

/* 编辑模式响应式设计 */
@media (max-width: 768px) {
  .author-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .author-actions {
    margin-left: 0;
    width: 100%;
  }

  .edit-button {
    width: 100%;
    justify-content: center;
  }

  .content-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .edit-controls {
    width: 100%;
    justify-content: flex-end;
  }

  .edit-wrapper {
    padding: 20px 15px;
    margin: 0 -15px;
    border-radius: 0;
  }

  .diary-edit-form .el-form-item__label {
    width: 100% !important;
    text-align: left !important;
    margin-bottom: 8px;
  }

  .diary-edit-form .el-form-item__content {
    margin-left: 0 !important;
  }
}

/* 全文检索样式 */
.content-search {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-left: auto;
}

.search-input {
  width: 200px;
}

.clear-search-btn {
  white-space: nowrap;
}

.search-highlight {
  background: linear-gradient(135deg, #FFE082, #FFC107);
  color: #E65100;
  padding: 2px 4px;
  border-radius: 3px;
  font-weight: 600;
  box-shadow: 0 1px 3px rgba(255, 193, 7, 0.3);
  animation: highlight-pulse 2s ease-in-out;
}

@keyframes highlight-pulse {
  0% {
    background: linear-gradient(135deg, #FFE082, #FFC107);
    transform: scale(1);
  }
  50% {
    background: linear-gradient(135deg, #FFC107, #FF8F00);
    transform: scale(1.05);
  }
  100% {
    background: linear-gradient(135deg, #FFE082, #FFC107);
    transform: scale(1);
  }
}

/* 响应式搜索样式 */
@media (max-width: 768px) {
  .content-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .content-search {
    width: 100%;
    margin-left: 0;
  }

  .search-input {
    flex: 1;
    width: auto;
  }
}
</style>
