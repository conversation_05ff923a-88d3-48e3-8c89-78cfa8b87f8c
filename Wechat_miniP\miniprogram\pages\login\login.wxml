<!--login.wxml-->
<view class="page">
  <view class="container">
    <!-- Logo和标题 -->
    <view class="header">
      <view class="logo">🧭</view>
      <view class="title">鸿雁智游</view>
      <view class="subtitle">个性化旅行系统</view>
    </view>

    <!-- 登录表单 -->
    <view class="login-form">
      <view class="form-item">
        <view class="form-label">
          <text class="label-icon">👤</text>
          <text class="label-text">用户名</text>
        </view>
        <input 
          class="form-input" 
          type="text" 
          placeholder="请输入用户名" 
          value="{{loginForm.username}}"
          bindinput="onUsernameInput"
        />
      </view>

      <view class="form-item">
        <view class="form-label">
          <text class="label-icon">🔒</text>
          <text class="label-text">密码</text>
        </view>
        <input 
          class="form-input" 
          type="password" 
          placeholder="请输入密码" 
          value="{{loginForm.password}}"
          bindinput="onPasswordInput"
        />
      </view>

      <view class="login-actions">
        <button 
          class="login-btn" 
          bindtap="handleLogin"
          disabled="{{loading}}"
        >
          <text wx:if="{{loading}}">登录中...</text>
          <text wx:else>登录</text>
        </button>

        <view class="action-links">
          <text class="link-text" bindtap="goToRegister">还没有账号？立即注册</text>
          <text class="link-text" bindtap="guestLogin">游客访问</text>
        </view>
      </view>
    </view>

    <!-- 快速登录 -->
    <view class="quick-login">
      <view class="quick-title">快速体验</view>
      <view class="quick-buttons">
        <button class="quick-btn" bindtap="quickLogin" data-type="admin">
          <text class="quick-icon">👨‍💼</text>
          <text class="quick-text">管理员登录</text>
        </button>
        <button class="quick-btn" bindtap="quickLogin" data-type="user">
          <text class="quick-icon">👤</text>
          <text class="quick-text">普通用户</text>
        </button>
      </view>
    </view>
  </view>
</view>
