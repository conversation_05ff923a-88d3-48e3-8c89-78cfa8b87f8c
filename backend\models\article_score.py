from utils.database import db
from sqlalchemy import <PERSON>umn, Integer, SmallInteger, ForeignKey, DateTime, UniqueConstraint
from sqlalchemy.sql import func
import datetime

class ArticleScore(db.Model):
    """
    文章评分模型
    """
    __tablename__ = 'article_scores'

    score_id = Column(Integer, primary_key=True, autoincrement=True)
    article_id = Column(Integer, ForeignKey('articles.article_id', ondelete='CASCADE'), nullable=False)
    user_id = Column(Integer, ForeignKey('users.user_id', ondelete='CASCADE'), nullable=False)
    score = Column(SmallInteger, nullable=False)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    __table_args__ = (
        UniqueConstraint('article_id', 'user_id', name='unique_article_user_score'),
        {'extend_existing': True}
    )

    def __init__(self, article_id, user_id, score):
        self.article_id = article_id
        self.user_id = user_id
        self.score = score

    def to_dict(self):
        """
        将对象转换为字典
        """
        return {
            'score_id': self.score_id,
            'article_id': self.article_id,
            'user_id': self.user_id,
            'score': self.score,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
