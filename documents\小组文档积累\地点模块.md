# 地点模块 API 文档

本文档详细描述了 Flask 版本后端的地点模块 API 接口及其在 Postman 中的测试用例。

## 目录

1. [获取所有地点](#获取所有地点)
2. [按条件查询地点](#按条件查询地点)
3. [更新地点浏览计数](#更新地点浏览计数)
4. [获取地点浏览计数](#获取地点浏览计数)
5. [搜索地点](#搜索地点)

## 获取所有地点

### 接口信息

- **URL**: `/api/locations`
- **方法**: `GET`
- **描述**: 获取所有地点信息
- **请求参数**: 无

### 响应

#### 成功响应 (200 OK)

```json
[
    {
        "location_id": 1,
        "name": "北京大学",
        "type": 0,
        "keyword": "教育,大学",
        "popularity": 100,
        "evaluation": 4.8
    },
    {
        "location_id": 2,
        "name": "故宫博物院",
        "type": 1,
        "keyword": "文化,历史,博物馆",
        "popularity": 150,
        "evaluation": 4.9
    }
]
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `GET`
4. 输入 URL: `http://localhost:5000/api/locations`
5. 点击 Send 按钮发送请求

## 按条件查询地点

### 接口信息

- **URL**: `/api/locations/query`
- **方法**: `GET`
- **描述**: 按条件查询地点
- **请求参数**: 查询字符串

### 请求参数

| 参数名    | 类型    | 必填 | 描述                                                  |
| --------- | ------- | ---- | ----------------------------------------------------- |
| name      | string  | 否   | 地点名称，支持模糊查询                                |
| type      | integer | 否   | 地点类型，0: 教育, 1: 文化, 2: 自然, 3: 娱乐, 4: 交通 |
| keyword   | string  | 否   | 关键词，支持模糊查询                                  |
| sortOrder | integer | 否   | 排序方式，0: 按人气排序, 1: 按评分排序                |

### 响应

#### 成功响应 (200 OK)

```json
[
    {
        "location_id": 1,
        "name": "北京大学",
        "type": 0,
        "keyword": "教育,大学",
        "popularity": 100,
        "evaluation": 4.8
    },
    {
        "location_id": 3,
        "name": "北京师范大学",
        "type": 0,
        "keyword": "教育,大学",
        "popularity": 90,
        "evaluation": 4.7
    }
]
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `GET`
4. 输入 URL: `http://localhost:5000/api/locations/query?name=北京&type=0&sortOrder=0`
5. 点击 Send 按钮发送请求

### 测试用例

#### 1. 按名称查询

```
GET http://localhost:5000/api/locations/query?name=北京
```

#### 2. 按类型查询

```
GET http://localhost:5000/api/locations/query?type=0
```

#### 3. 按关键词查询

```
GET http://localhost:5000/api/locations/query?keyword=大学
```

#### 4. 按人气排序

```
GET http://localhost:5000/api/locations/query?sortOrder=0
```

#### 5. 按评分排序

```
GET http://localhost:5000/api/locations/query?sortOrder=1
```

#### 6. 组合查询

```
GET http://localhost:5000/api/locations/query?name=北京&type=0&keyword=大学&sortOrder=0
```

## 更新地点浏览计数

### 接口信息

- **URL**: `/api/locations/browse/{location_id}` 或 `/api/locations/browse`
- **方法**: `POST`
- **描述**: 更新地点浏览计数
- **URL 参数**: location_id - 地点 ID（可选，如果不在URL中提供，则必须在请求体中提供）
- **请求体**: JSON

### 请求参数

| 参数名      | 类型    | 必填 | 描述                                           |
| ----------- | ------- | ---- | ---------------------------------------------- |
| user_id     | integer | 是   | 用户 ID                                        |
| location_id | integer | 否   | 地点 ID（如果不在URL中提供，则在请求体中必填） |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Browse count updated successfully",
    "data": {}
}
```

#### 错误响应

- **地点不存在** (404 Not Found)

```json
{
    "code": 1,
    "message": "Location not found",
    "data": null
}
```

- **用户不存在** (404 Not Found)

```json
{
    "code": 1,
    "message": "User not found",
    "data": null
}
```

### Postman 测试

#### 方式一：通过 URL 参数提供地点 ID

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `POST`
4. 输入 URL: `http://localhost:5000/api/locations/browse/1` (替换 `1` 为实际的地点 ID)
5. 选择 Body 选项卡，选择 raw，设置类型为 JSON
6. 输入请求体:

```json
{
    "user_id": 1
}
```

7. 点击 Send 按钮发送请求

#### 方式二：通过请求体提供地点 ID

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `POST`
4. 输入 URL: `http://localhost:5000/api/locations/browse`
5. 选择 Body 选项卡，选择 raw，设置类型为 JSON
6. 输入请求体:

```json
{
    "user_id": 1,
    "location_id": 1
}
```

7. 点击 Send 按钮发送请求

## 获取地点浏览计数

### 接口信息

- **URL**: `/api/locations/browse/{location_id}`
- **方法**: `GET`
- **描述**: 获取地点浏览计数
- **URL 参数**: location_id - 地点 ID

### 响应

#### 成功响应 (200 OK)

```json
{
    "count": 10
}
```

#### 错误响应

- **地点不存在** (404 Not Found)

```json
{
    "error": "Location not found"
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `GET`
4. 输入 URL: `http://localhost:5000/api/locations/browse/1` (替换 `1` 为实际的地点 ID)
5. 点击 Send 按钮发送请求

## 搜索地点

> **注意**：原 `/api/locations/search` 接口已被合并到 `/api/locations/query` 接口中。请使用 `/api/locations/query` 接口并提供 `keyword` 参数来搜索地点。

### 示例

```
GET http://localhost:5000/api/locations/query?keyword=大学
```

这将返回所有名称或关键词中包含"大学"的地点。

## 地点类型说明

地点类型是一个整数值，表示地点的分类：

| 类型值 | 描述                         |
| ------ | ---------------------------- |
| 0      | 教育类（如大学、学校）       |
| 1      | 文化类（如博物馆、历史遗迹） |
| 2      | 自然类（如公园、自然保护区） |
| 3      | 娱乐类（如商场、餐厅）       |
| 4      | 交通类（如机场、火车站）     |

## 地点模型

地点模型包含以下字段：

| 字段名      | 类型    | 描述                         |
| ----------- | ------- | ---------------------------- |
| location_id | integer | 地点 ID                      |
| name        | string  | 地点名称                     |
| type        | integer | 地点类型                     |
| keyword     | string  | 关键词，多个关键词用逗号分隔 |
| popularity  | integer | 人气值                       |
| evaluation  | float   | 评分，范围 0-5               |

## 完整的 Postman 测试流程

以下是一个完整的测试流程，按照顺序执行可以测试地点模块的所有功能：

### 1. 获取所有地点

```
GET http://localhost:5000/api/locations
```

### 2. 按条件查询地点

```
GET http://localhost:5000/api/locations/query?name=北京&type=0&sortOrder=0
```

### 3. 按关键词搜索地点

```
GET http://localhost:5000/api/locations/query?keyword=大学
```

### 4. 更新地点浏览计数（方式一：URL参数）

```
POST http://localhost:5000/api/locations/browse/1
Content-Type: application/json

{
    "user_id": 1
}
```

### 5. 更新地点浏览计数（方式二：请求体）

```
POST http://localhost:5000/api/locations/browse
Content-Type: application/json

{
    "user_id": 1,
    "location_id": 1
}
```

### 6. 获取地点浏览计数

```
GET http://localhost:5000/api/locations/browse/1
```

## 地点浏览历史

当用户浏览地点时，系统会记录浏览历史，用于推荐系统的协同过滤算法。浏览历史记录包含以下信息：

| 字段名      | 类型     | 描述     |
| ----------- | -------- | -------- |
| id          | integer  | 记录 ID  |
| user_id     | integer  | 用户 ID  |
| location_id | integer  | 地点 ID  |
| browse_time | datetime | 浏览时间 |

## 地点浏览计数

系统还会记录每个用户对每个地点的浏览次数，用于分析用户兴趣。浏览计数记录包含以下信息：

| 字段名      | 类型    | 描述     |
| ----------- | ------- | -------- |
| user_id     | integer | 用户 ID  |
| location_id | integer | 地点 ID  |
| count       | integer | 浏览次数 |

## 注意事项

1. 地点的人气值会随着用户浏览次数的增加而增加。
2. 地点的评分是用户评分的平均值，范围为 0-5。
3. 地点的关键词用于搜索和基于内容的推荐算法。
4. 地点的类型用于按类型筛选和基于内容的推荐算法。
5. 地点浏览历史用于协同过滤推荐算法。
