import { createApp } from 'vue'
import App from './App.vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import router from './router'
import store from './store' // 导入Vuex store
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'process/browser'
import './shims/resize-observer'
import axios from 'axios'

// 导入highlight.js样式
import 'highlight.js/styles/dark.css'

// 配置Axios
axios.defaults.withCredentials = true // 允许跨域请求携带凭证
// 移除错误的请求头设置
// Access-Control-Allow-Origin 是响应头，不应该在请求中设置

window.process = { env: { NODE_ENV: 'development' } } // 添加环境变量模拟

import { Plus, Message, Star, StarFilled } from '@element-plus/icons-vue'

const app = createApp(App)

// 全局注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 确保这些常用图标被正确注册 - 使用多词组件名称以符合ESLint规则
app.component('IconPlus', Plus)
app.component('IconMessage', Message)
app.component('IconStar', Star)
app.component('IconStarFilled', StarFilled)

// 调试信息
console.log('已注册的图标组件:', {
  IconPlus: Plus,
  IconMessage: Message,
  IconStar: Star,
  IconStarFilled: StarFilled
})

app.use(ElementPlus)
app.use(router)
app.use(store) // 使用Vuex store
app.mount('#app')