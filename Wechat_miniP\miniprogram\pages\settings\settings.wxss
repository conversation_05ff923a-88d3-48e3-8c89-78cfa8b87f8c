/* settings.wxss */
.page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 顶部导航栏 */
.nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-left, .nav-right {
  width: 120rpx;
  display: flex;
  align-items: center;
}

.nav-left {
  justify-content: flex-start;
}

.nav-right {
  justify-content: flex-end;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
}

.container {
  padding: 24rpx;
}

/* 设置分组 */
.setting-section {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 16rpx;
  padding-left: 8rpx;
  opacity: 0.9;
}

.setting-list {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 24rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: background-color 0.3s ease;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item:active {
  background-color: rgba(102, 126, 234, 0.05);
}

.setting-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.setting-icon {
  font-size: 40rpx;
  margin-right: 24rpx;
  width: 60rpx;
  text-align: center;
}

.setting-content {
  flex: 1;
}

.setting-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.setting-desc {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

.setting-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.cache-size {
  font-size: 24rpx;
  color: #999;
}

.icon-arrow-right {
  font-size: 24rpx;
  color: #ccc;
}

/* 开关样式 */
switch {
  transform: scale(0.8);
}

/* 退出登录按钮 */
.logout-section {
  margin-top: 48rpx;
  padding: 0 8rpx;
}

.logout-btn {
  background: linear-gradient(45deg, #ff4757, #ff3742);
  border-radius: 16rpx;
  padding: 32rpx;
  text-align: center;
  box-shadow: 0 8rpx 24rpx rgba(255, 71, 87, 0.3);
  transition: all 0.3s ease;
}

.logout-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(255, 71, 87, 0.2);
}

.logout-text {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
}
