<!--api-test.wxml-->
<view class="container">
  <view class="header">
    <text class="title">API测试页面</text>
    <button class="retry-btn" bindtap="onRetryTest" disabled="{{loading}}">
      {{loading ? '测试中...' : '重新测试'}}
    </button>
  </view>

  <view class="loading" wx:if="{{loading}}">
    <text>正在测试API连接...</text>
  </view>

  <view class="results" wx:if="{{!loading && testResults.length > 0}}">
    <view class="result-item" wx:for="{{testResults}}" wx:key="name">
      <view class="result-header">
        <text class="result-name">{{item.name}}</text>
        <text class="result-status {{item.success ? 'success' : 'error'}}">
          {{item.success ? '✓' : '✗'}}
        </text>
      </view>
      
      <view class="result-details">
        <text class="result-time">耗时: {{item.time}}ms</text>
        <text class="result-count" wx:if="{{item.success}}">
          结果数量: {{item.count}}
        </text>
        <text class="result-error" wx:if="{{!item.success}}">
          错误: {{item.error}}
        </text>
      </view>
      
      <button 
        class="view-btn" 
        wx:if="{{item.success && item.count > 0}}"
        bindtap="onViewResult"
        data-index="{{index}}"
      >
        查看结果
      </button>
    </view>
  </view>

  <view class="empty" wx:if="{{!loading && testResults.length === 0}}">
    <text>暂无测试结果</text>
  </view>
</view>
