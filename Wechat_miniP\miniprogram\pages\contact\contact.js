// contact.js
Component({
  data: {
    contactInfo: [
      {
        icon: '📧',
        title: '邮箱',
        content: '<EMAIL>',
        type: 'email'
      },
      {
        icon: '📱',
        title: '客服电话',
        content: '************',
        type: 'phone'
      },
      {
        icon: '💬',
        title: '在线客服',
        content: '工作日 9:00-18:00',
        type: 'chat'
      },
      {
        icon: '📍',
        title: '公司地址',
        content: '北京市朝阳区xxx路xxx号',
        type: 'address'
      }
    ],
    feedbackText: '',
    contactType: 0,
    contactTypes: ['问题反馈', '功能建议', '合作咨询', '其他']
  },

  methods: {
    // 联系方式点击
    onContactTap: function(e) {
      const item = e.currentTarget.dataset.item;
      
      switch (item.type) {
        case 'phone':
          wx.makePhoneCall({
            phoneNumber: item.content
          });
          break;
        case 'email':
          wx.setClipboardData({
            data: item.content,
            success: function() {
              wx.showToast({
                title: '邮箱已复制',
                icon: 'success'
              });
            }
          });
          break;
        default:
          wx.showToast({
            title: '功能开发中',
            icon: 'none'
          });
      }
    },

    // 反馈类型选择
    onContactTypeChange: function(e) {
      this.setData({
        contactType: e.detail.value
      });
    },

    // 反馈内容输入
    onFeedbackInput: function(e) {
      this.setData({
        feedbackText: e.detail.value
      });
    },

    // 提交反馈
    submitFeedback: function() {
      if (!this.data.feedbackText.trim()) {
        wx.showToast({
          title: '请输入反馈内容',
          icon: 'none'
        });
        return;
      }

      wx.showLoading({
        title: '提交中...'
      });

      // 模拟提交
      setTimeout(() => {
        wx.hideLoading();
        wx.showToast({
          title: '提交成功',
          icon: 'success'
        });
        
        this.setData({
          feedbackText: '',
          contactType: 0
        });
      }, 1000);
    }
  }
})
