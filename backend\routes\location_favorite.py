from flask import Blueprint, request, jsonify
from models.location_favorite import LocationFavorite
from models.location import Location
from models.user import User
from utils.database import db
from utils.response import success, error

location_favorite_bp = Blueprint('location_favorite', __name__)

@location_favorite_bp.route('/favorite', methods=['POST'])
def favorite_location():
    """
    Add a location to user's favorites
    """
    try:
        data = request.get_json()

        # Validate required fields
        if not all(key in data for key in ['location_id', 'user_id']):
            return error('Missing required fields: location_id, user_id')

        location_id = data['location_id']
        user_id = data['user_id']

        # Check if location and user exist
        location = Location.query.get(location_id)
        user = User.query.get(user_id)

        if not location:
            return error('Location not found', 404)

        if not user:
            return error('User not found', 404)

        # Check if user has already favorited this location
        existing_favorite = LocationFavorite.query.filter_by(
            user_id=user_id, location_id=location_id).first()

        if existing_favorite:
            return success({
                'is_favorite': True
            }, 'Location is already in favorites')

        # Create new favorite
        new_favorite = LocationFavorite(
            user_id=user_id,
            location_id=location_id
        )
        db.session.add(new_favorite)
        db.session.commit()

        return success({
            'is_favorite': True
        }, 'Location added to favorites')

    except Exception as e:
        db.session.rollback()
        import traceback
        traceback.print_exc()
        return error(f'Error adding location to favorites: {str(e)}')

@location_favorite_bp.route('/unfavorite', methods=['POST'])
def unfavorite_location():
    """
    Remove a location from user's favorites
    """
    try:
        data = request.get_json()

        # Validate required fields
        if not all(key in data for key in ['location_id', 'user_id']):
            return error('Missing required fields: location_id, user_id')

        location_id = data['location_id']
        user_id = data['user_id']

        # Check if favorite exists
        favorite = LocationFavorite.query.filter_by(
            user_id=user_id, location_id=location_id).first()

        if not favorite:
            return success({
                'is_favorite': False
            }, 'Location is not in favorites')

        # Remove favorite
        db.session.delete(favorite)
        db.session.commit()

        return success({
            'is_favorite': False
        }, 'Location removed from favorites')

    except Exception as e:
        db.session.rollback()
        return error(f'Error removing location from favorites: {str(e)}')

@location_favorite_bp.route('/check', methods=['POST'])
def check_favorite():
    """
    Check if a location is in user's favorites
    """
    try:
        data = request.get_json()

        # Validate required fields
        if not all(key in data for key in ['location_id', 'user_id']):
            return error('Missing required fields: location_id, user_id')

        location_id = data['location_id']
        user_id = data['user_id']

        # Check if favorite exists
        favorite = LocationFavorite.query.filter_by(
            user_id=user_id, location_id=location_id).first()

        return success({
            'is_favorite': favorite is not None
        }, 'Favorite status retrieved')

    except Exception as e:
        return error(f'Error checking favorite status: {str(e)}')

@location_favorite_bp.route('/user/<int:user_id>', methods=['GET'])
def get_user_favorites(user_id):
    """
    Get all favorites for a user
    """
    try:
        # Check if user exists
        user = User.query.get(user_id)

        if not user:
            return error('User not found', 404)

        # Get all favorites
        favorites = LocationFavorite.query.filter_by(user_id=user_id).all()

        # Get location details for each favorite
        favorite_locations = []
        for favorite in favorites:
            location = Location.query.get(favorite.location_id)
            if location:
                location_data = location.to_dict()
                # 确保时间包含时区信息
                if favorite.created_at:
                    if favorite.created_at.tzinfo is None:
                        # 如果时间没有时区信息，添加UTC时区
                        import datetime
                        favorited_at = favorite.created_at.replace(tzinfo=datetime.timezone.utc)
                        location_data['favorited_at'] = favorited_at.isoformat()
                    else:
                        # 已有时区信息
                        location_data['favorited_at'] = favorite.created_at.isoformat()
                else:
                    location_data['favorited_at'] = None
                favorite_locations.append(location_data)

        return success({
            'favorites': favorite_locations,
            'count': len(favorite_locations)
        }, 'User favorites retrieved successfully')

    except Exception as e:
        return error(f'Error retrieving user favorites: {str(e)}')
