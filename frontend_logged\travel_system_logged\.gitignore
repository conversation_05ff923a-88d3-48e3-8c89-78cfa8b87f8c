# 依赖目录
node_modules
/dist
/.pnp
.pnp.js

# 构建输出
/build
/coverage

# 缓存目录
.npm
.eslintcache
.stylelintcache
.node_repl_history
.yarn-integrity
.cache
.parcel-cache
.next
.nuxt
.vuepress/dist
.serverless/
.fusebox/
.dynamodb/
.tern-port
.webpack/
.grunt
bower_components

# 本地环境文件
.env.local
.env.*.local
.env.development.local
.env.test.local
.env.production.local
.env

# 日志文件
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 编辑器目录和文件
.idea
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# 操作系统文件
.DS_Store
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk

# 测试覆盖率报告
/coverage
.nyc_output

# 临时文件
*.tmp
*.temp
.temp
.tmp
tmp/
temp/

# 类型声明文件生成的文件
*.tsbuildinfo

# 可选的npm缓存目录
.npm

# 可选的REPL历史记录
.node_repl_history

# Vue CLI生成的文件
.vue-cli-service/

# 本地历史文件
.history/
.ionide

# 调试文件
debug.log

# 前端项目需要提交src文件夹
# /src/
# src/
