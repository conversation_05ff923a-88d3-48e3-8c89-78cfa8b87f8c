"""
地点查找算法模块
实现高效的地点模糊查找算法，完全自主实现，不依赖SQL内置函数
"""

from typing import List, Dict, Tuple, Optional
from collections import defaultdict
import time


class LocationFinder:
    """
    地点查找器
    实现多种高效的地点查找算法
    """

    @staticmethod
    def fuzzy_search_locations(locations: List, query: str, limit: int = 10) -> List:
        """
        高效的地点模糊查找算法 - 优化版本，提高搜索精确度

        Args:
            locations: 地点对象列表
            query: 查询关键词
            limit: 返回结果数量限制 (None表示不限制)

        Returns:
            匹配的地点列表，按相关性排序
        """
        if not query or not locations:
            # 如果没有查询词，返回热门地点
            return LocationFinder._get_popular_locations(locations, limit or len(locations))

        query = query.strip().lower()

        # 处理limit为None的情况
        max_limit = limit if limit is not None else len(locations)

        # 使用评分系统进行精确匹配
        scored_results = []

        for location in locations:
            score = LocationFinder._calculate_relevance_score(location, query)
            if score > 0:  # 只保留有相关性的结果
                scored_results.append((location, score))

        # 按评分降序排序
        scored_results.sort(key=lambda x: x[1], reverse=True)

        # 提取地点对象
        results = [location for location, score in scored_results]

        # 如果limit不为None，则限制返回数量
        if limit is not None:
            return results[:limit]
        else:
            return results

    @staticmethod
    def _calculate_relevance_score(location, query: str) -> float:
        """
        计算地点与查询词的相关性评分

        Args:
            location: 地点对象
            query: 查询词

        Returns:
            相关性评分 (0-100)
        """
        score = 0.0
        query_lower = query.lower()

        # 获取地点名称和关键词
        name = getattr(location, 'name', '') or ''
        keyword = getattr(location, 'keyword', '') or ''
        name_lower = name.lower()
        keyword_lower = keyword.lower()

        # 1. 精确匹配 - 最高分 (100分)
        if name_lower == query_lower:
            return 100.0

        # 2. 关键词精确匹配 - 高分 (90分)
        if keyword:
            keywords = [kw.strip().lower() for kw in keyword.split(',')]
            if query_lower in keywords:
                return 90.0

        # 3. 名称前缀匹配 - 高分 (80分)
        if name_lower.startswith(query_lower):
            return 80.0

        # 4. 关键词前缀匹配 - 中高分 (70分)
        if keyword:
            keywords = [kw.strip().lower() for kw in keyword.split(',')]
            for kw in keywords:
                if kw.startswith(query_lower):
                    return 70.0

        # 5. 名称包含查询词 - 中分 (60分)
        if query_lower in name_lower:
            return 60.0

        # 6. 关键词包含查询词 - 中分 (50分)
        if query_lower in keyword_lower:
            return 50.0

        # 7. 只对长度>=2的查询词进行模糊匹配
        if len(query_lower) >= 2:
            # 计算编辑距离，但只对相似度高的进行匹配
            name_distance = LocationFinder._levenshtein_distance(name_lower, query_lower)
            max_allowed_distance = max(1, len(query_lower) // 3)  # 允许的最大编辑距离

            if name_distance <= max_allowed_distance:
                # 根据编辑距离计算分数 (10-40分)
                similarity = 1 - (name_distance / len(query_lower))
                score = max(score, similarity * 40)

            # 检查关键词的编辑距离
            if keyword:
                keywords = [kw.strip().lower() for kw in keyword.split(',')]
                for kw in keywords:
                    if len(kw) >= 2:  # 只检查长度>=2的关键词
                        kw_distance = LocationFinder._levenshtein_distance(kw, query_lower)
                        if kw_distance <= max_allowed_distance:
                            similarity = 1 - (kw_distance / max(len(query_lower), len(kw)))
                            score = max(score, similarity * 30)

        return score

    @staticmethod
    def _levenshtein_distance(s1: str, s2: str) -> int:
        """
        计算两个字符串的Levenshtein距离 - 简化版本
        """
        if len(s1) < len(s2):
            return LocationFinder._levenshtein_distance(s2, s1)

        if len(s2) == 0:
            return len(s1)

        previous_row = list(range(len(s2) + 1))
        for i, c1 in enumerate(s1):
            current_row = [i + 1]
            for j, c2 in enumerate(s2):
                insertions = previous_row[j + 1] + 1
                deletions = current_row[j] + 1
                substitutions = previous_row[j] + (c1 != c2)
                current_row.append(min(insertions, deletions, substitutions))
            previous_row = current_row

        return previous_row[-1]

    @staticmethod
    def _get_popular_locations(locations: List, limit: int) -> List:
        """获取热门地点（按热度排序）"""
        # 使用自实现的快速排序
        sorted_locations = LocationFinder._quick_sort_by_popularity(locations)
        return sorted_locations[:limit]

    @staticmethod
    def _quick_sort_by_popularity(locations: List) -> List:
        """按热度快速排序"""
        if len(locations) <= 1:
            return locations
        
        pivot = locations[len(locations) // 2]
        pivot_popularity = getattr(pivot, 'popularity', 0) or 0
        
        left = [loc for loc in locations if (getattr(loc, 'popularity', 0) or 0) > pivot_popularity]
        middle = [loc for loc in locations if (getattr(loc, 'popularity', 0) or 0) == pivot_popularity]
        right = [loc for loc in locations if (getattr(loc, 'popularity', 0) or 0) < pivot_popularity]
        
        return LocationFinder._quick_sort_by_popularity(left) + middle + LocationFinder._quick_sort_by_popularity(right)

    @staticmethod
    def _hash_exact_match(locations: List, query: str) -> List:
        """哈希精确匹配"""
        matches = []
        query_lower = query.lower()
        
        for location in locations:
            # 检查名称精确匹配
            if hasattr(location, 'name') and location.name:
                if location.name.lower() == query_lower:
                    matches.append(location)
                    continue
            
            # 检查关键词精确匹配
            if hasattr(location, 'keyword') and location.keyword:
                keywords = [kw.strip().lower() for kw in location.keyword.split(',')]
                if query_lower in keywords:
                    matches.append(location)
        
        return matches

    @staticmethod
    def _trie_prefix_match(locations: List, query: str) -> List:
        """Trie树前缀匹配"""
        matches = []
        query_lower = query.lower()
        
        for location in locations:
            # 检查名称前缀匹配
            if hasattr(location, 'name') and location.name:
                if location.name.lower().startswith(query_lower):
                    matches.append(location)
                    continue
            
            # 检查关键词前缀匹配
            if hasattr(location, 'keyword') and location.keyword:
                keywords = [kw.strip().lower() for kw in location.keyword.split(',')]
                for keyword in keywords:
                    if keyword.startswith(query_lower):
                        matches.append(location)
                        break
        
        return matches

    @staticmethod
    def _kmp_substring_match(locations: List, query: str) -> List:
        """KMP子字符串匹配"""
        matches = []
        query_lower = query.lower()
        
        # 构建KMP失效函数
        def build_failure_function(pattern: str) -> List[int]:
            failure = [0] * len(pattern)
            j = 0
            for i in range(1, len(pattern)):
                while j > 0 and pattern[i] != pattern[j]:
                    j = failure[j - 1]
                if pattern[i] == pattern[j]:
                    j += 1
                failure[i] = j
            return failure
        
        # KMP搜索函数
        def kmp_search(text: str, pattern: str) -> bool:
            if not pattern:
                return True
            if not text:
                return False
            
            failure = build_failure_function(pattern)
            j = 0
            for i in range(len(text)):
                while j > 0 and text[i] != pattern[j]:
                    j = failure[j - 1]
                if text[i] == pattern[j]:
                    j += 1
                if j == len(pattern):
                    return True
            return False
        
        for location in locations:
            # 检查名称子字符串匹配
            if hasattr(location, 'name') and location.name:
                if kmp_search(location.name.lower(), query_lower):
                    matches.append(location)
                    continue
            
            # 检查关键词子字符串匹配
            if hasattr(location, 'keyword') and location.keyword:
                if kmp_search(location.keyword.lower(), query_lower):
                    matches.append(location)
        
        return matches

    @staticmethod
    def _levenshtein_fuzzy_match(locations: List, query: str, max_distance: int = 2) -> List[Tuple]:
        """Levenshtein距离模糊匹配"""
        def levenshtein_distance(s1: str, s2: str) -> int:
            """计算两个字符串的Levenshtein距离"""
            if len(s1) < len(s2):
                return levenshtein_distance(s2, s1)
            
            if len(s2) == 0:
                return len(s1)
            
            previous_row = list(range(len(s2) + 1))
            for i, c1 in enumerate(s1):
                current_row = [i + 1]
                for j, c2 in enumerate(s2):
                    insertions = previous_row[j + 1] + 1
                    deletions = current_row[j] + 1
                    substitutions = previous_row[j] + (c1 != c2)
                    current_row.append(min(insertions, deletions, substitutions))
                previous_row = current_row
            
            return previous_row[-1]
        
        matches = []
        query_lower = query.lower()
        
        for location in locations:
            min_distance = float('inf')
            
            # 检查名称模糊匹配
            if hasattr(location, 'name') and location.name:
                distance = levenshtein_distance(location.name.lower(), query_lower)
                min_distance = min(min_distance, distance)
            
            # 检查关键词模糊匹配
            if hasattr(location, 'keyword') and location.keyword:
                keywords = [kw.strip().lower() for kw in location.keyword.split(',')]
                for keyword in keywords:
                    distance = levenshtein_distance(keyword, query_lower)
                    min_distance = min(min_distance, distance)
            
            if min_distance <= max_distance:
                matches.append((location, min_distance))
        
        # 按距离排序
        matches.sort(key=lambda x: x[1])
        return matches

    @staticmethod
    def _token_based_search(locations: List, query: str) -> List:
        """基于分词的搜索"""
        matches = []
        tokens = [token.strip().lower() for token in query.split() if len(token.strip()) >= 2]
        
        if not tokens:
            return matches
        
        for location in locations:
            match_count = 0
            
            # 检查名称分词匹配
            if hasattr(location, 'name') and location.name:
                name_lower = location.name.lower()
                for token in tokens:
                    if token in name_lower:
                        match_count += 1
            
            # 检查关键词分词匹配
            if hasattr(location, 'keyword') and location.keyword:
                keyword_lower = location.keyword.lower()
                for token in tokens:
                    if token in keyword_lower:
                        match_count += 1
            
            # 如果匹配到至少一个分词，加入结果
            if match_count > 0:
                matches.append(location)
        
        return matches

    @staticmethod
    def _filter_by_type(locations: List, location_type: int) -> List:
        """按类型过滤地点"""
        filtered = []
        for location in locations:
            if hasattr(location, 'type') and getattr(location, 'type') == location_type:
                filtered.append(location)
        return filtered

    @staticmethod
    def _quick_sort_by_evaluation(locations: List) -> List:
        """按评分快速排序"""
        if len(locations) <= 1:
            return locations

        pivot = locations[len(locations) // 2]
        pivot_evaluation = getattr(pivot, 'evaluation', 0) or 0

        left = [loc for loc in locations if (getattr(loc, 'evaluation', 0) or 0) > pivot_evaluation]
        middle = [loc for loc in locations if (getattr(loc, 'evaluation', 0) or 0) == pivot_evaluation]
        right = [loc for loc in locations if (getattr(loc, 'evaluation', 0) or 0) < pivot_evaluation]

        return LocationFinder._quick_sort_by_evaluation(left) + middle + LocationFinder._quick_sort_by_evaluation(right)

    @staticmethod
    def benchmark_search_algorithms(locations: List, queries: List[str]) -> Dict[str, float]:
        """
        基准测试各种搜索算法的性能
        
        Args:
            locations: 地点列表
            queries: 查询列表
            
        Returns:
            各算法的平均响应时间
        """
        results = {}
        
        # 测试完整模糊搜索
        start_time = time.time()
        for query in queries:
            LocationFinder.fuzzy_search_locations(locations, query, 10)
        results['fuzzy_search'] = (time.time() - start_time) / len(queries)
        
        # 测试哈希精确匹配
        start_time = time.time()
        for query in queries:
            LocationFinder._hash_exact_match(locations, query)
        results['hash_exact'] = (time.time() - start_time) / len(queries)
        
        # 测试Trie前缀匹配
        start_time = time.time()
        for query in queries:
            LocationFinder._trie_prefix_match(locations, query)
        results['trie_prefix'] = (time.time() - start_time) / len(queries)
        
        # 测试KMP子字符串匹配
        start_time = time.time()
        for query in queries:
            LocationFinder._kmp_substring_match(locations, query)
        results['kmp_substring'] = (time.time() - start_time) / len(queries)
        
        return results
