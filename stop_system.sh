#!/bin/bash

echo "========================================"
echo "停止鸿雁智游系统服务"
echo "========================================"

# 读取PID文件并停止服务
if [ -f .backend.pid ]; then
    BACKEND_PID=$(cat .backend.pid)
    if kill -0 $BACKEND_PID 2>/dev/null; then
        kill $BACKEND_PID 2>/dev/null
        echo "✓ 后端服务已停止 (PID: $BACKEND_PID)"
    else
        echo "- 后端服务未运行"
    fi
    rm .backend.pid
fi

if [ -f .proxy.pid ]; then
    PROXY_PID=$(cat .proxy.pid)
    if kill -0 $PROXY_PID 2>/dev/null; then
        kill $PROXY_PID 2>/dev/null
        echo "✓ 代理服务已停止 (PID: $PROXY_PID)"
    else
        echo "- 代理服务未运行"
    fi
    rm .proxy.pid
fi

if [ -f .frontend.pid ]; then
    FRONTEND_PID=$(cat .frontend.pid)
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        kill $FRONTEND_PID 2>/dev/null
        echo "✓ 前端服务已停止 (PID: $FRONTEND_PID)"
    else
        echo "- 前端服务未运行"
    fi
    rm .frontend.pid
fi

# 强制杀死可能残留的进程
echo ""
echo "检查残留进程..."

# 查找并杀死Flask进程
FLASK_PIDS=$(pgrep -f "python.*app.py")
if [ ! -z "$FLASK_PIDS" ]; then
    echo "发现Flask残留进程: $FLASK_PIDS"
    kill $FLASK_PIDS 2>/dev/null
    echo "✓ Flask残留进程已清理"
fi

# 查找并杀死Node.js代理进程
PROXY_PIDS=$(pgrep -f "node.*proxy-server.js")
if [ ! -z "$PROXY_PIDS" ]; then
    echo "发现代理服务残留进程: $PROXY_PIDS"
    kill $PROXY_PIDS 2>/dev/null
    echo "✓ 代理服务残留进程已清理"
fi

# 查找并杀死Vue开发服务器进程
VUE_PIDS=$(pgrep -f "vue-cli-service serve")
if [ ! -z "$VUE_PIDS" ]; then
    echo "发现Vue开发服务器残留进程: $VUE_PIDS"
    kill $VUE_PIDS 2>/dev/null
    echo "✓ Vue开发服务器残留进程已清理"
fi

echo ""
echo "========================================"
echo "所有服务已停止"
echo "========================================"
