import { createStore } from 'vuex'

// 从localStorage获取用户信息
const getStoredUser = () => {
  try {
    const userString = localStorage.getItem('user')
    return userString ? JSON.parse(userString) : null
  } catch (error) {
    console.error('Error parsing stored user:', error)
    return null
  }
}

// 从localStorage获取token
const getStoredToken = () => {
  return localStorage.getItem('token') || null
}

export default createStore({
  state: {
    user: getStoredUser(),
    token: getStoredToken(),
    isAuthenticated: !!getStoredToken()
  },
  getters: {
    isAuthenticated: state => !!state.token,
    currentUser: state => state.user,
    userId: state => state.user?.user_id || null,
    username: state => state.user?.username || null
  },
  mutations: {
    SET_USER(state, user) {
      state.user = user
      // 保存到localStorage
      if (user) {
        localStorage.setItem('user', JSON.stringify(user))
      } else {
        localStorage.removeItem('user')
      }
    },
    SET_TOKEN(state, token) {
      state.token = token
      state.isAuthenticated = !!token
      // 保存到localStorage
      if (token) {
        localStorage.setItem('token', token)
      } else {
        localStorage.removeItem('token')
      }
    },
    LOGOUT(state) {
      state.user = null
      state.token = null
      state.isAuthenticated = false
      // 清除所有localStorage中的用户相关信息
      localStorage.removeItem('user')
      localStorage.removeItem('token')
      localStorage.removeItem('currentUser')
      localStorage.removeItem('authToken')
      localStorage.removeItem('userId')
      localStorage.removeItem('userType')
      // 清除其他可能的用户相关缓存
      localStorage.removeItem('refreshToken')
    }
  },
  actions: {
    login({ commit }, { user, token }) {
      commit('SET_USER', user)
      commit('SET_TOKEN', token)
    },
    logout({ commit }) {
      commit('LOGOUT')
    },
    // 检查并恢复登录状态
    checkAuth({ commit, state }) {
      if (!state.token) {
        const token = localStorage.getItem('token')
        const userStr = localStorage.getItem('user')

        if (token && userStr) {
          try {
            const user = JSON.parse(userStr)
            commit('SET_USER', user)
            commit('SET_TOKEN', token)
            return true
          } catch (error) {
            console.error('Error restoring auth state:', error)
            commit('LOGOUT')
            return false
          }
        }
        return false
      }
      return !!state.token
    }
  },
  modules: {
  }
})
