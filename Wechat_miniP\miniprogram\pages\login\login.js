// login.js
Page({
  data: {
    loginForm: {
      username: '',
      password: ''
    },
    loading: false
  },

  onLoad: function() {
    console.log('登录页面加载');
    
    // 检查是否已经登录
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');
    
    if (userInfo && token) {
      // 已登录，跳转到首页
      wx.reLaunch({
        url: '/pages/index/index'
      });
    }
  },

  // 用户名输入
  onUsernameInput: function(e) {
    this.setData({
      'loginForm.username': e.detail.value
    });
  },

  // 密码输入
  onPasswordInput: function(e) {
    this.setData({
      'loginForm.password': e.detail.value
    });
  },

  // 处理登录
  handleLogin: function() {
    const { username, password } = this.data.loginForm;
    
    // 表单验证
    if (!username.trim()) {
      wx.showToast({
        title: '请输入用户名',
        icon: 'none'
      });
      return;
    }
    
    if (!password.trim()) {
      wx.showToast({
        title: '请输入密码',
        icon: 'none'
      });
      return;
    }

    this.login(username, password);
  },

  // 登录请求
  login: function(username, password) {
    const that = this;
    this.setData({ loading: true });

    wx.request({
      url: 'http://localhost:5000/api/auth/login',
      method: 'POST',
      data: {
        username: username,
        password: password
      },
      header: {
        'Content-Type': 'application/json'
      },
      success: function(res) {
        console.log('登录响应:', res);
        
        if (res.data.code === 0 && res.data.data) {
          const { user, token } = res.data.data;
          
          // 保存用户信息和token
          wx.setStorageSync('userInfo', user);
          wx.setStorageSync('token', token);
          wx.setStorageSync('userId', user.user_id);
          
          wx.showToast({
            title: '登录成功',
            icon: 'success'
          });
          
          // 跳转到首页
          setTimeout(function() {
            wx.reLaunch({
              url: '/pages/index/index'
            });
          }, 1500);
          
        } else {
          wx.showToast({
            title: res.data.message || '登录失败',
            icon: 'none'
          });
        }
      },
      fail: function(error) {
        console.error('登录请求失败:', error);
        wx.showToast({
          title: '网络连接失败',
          icon: 'none'
        });
      },
      complete: function() {
        that.setData({ loading: false });
      }
    });
  },

  // 快速登录
  quickLogin: function(e) {
    const type = e.currentTarget.dataset.type;
    
    if (type === 'admin') {
      this.login('admin', 'admin123');
    } else if (type === 'user') {
      this.login('testuser', 'password123');
    }
  },

  // 游客登录
  guestLogin: function() {
    // 设置游客模式
    wx.setStorageSync('userInfo', {
      user_id: 0,
      username: '游客',
      avatar: '/images/icon/user.png'
    });
    wx.setStorageSync('userType', 'guest');
    
    wx.showToast({
      title: '进入游客模式',
      icon: 'success'
    });
    
    setTimeout(function() {
      wx.reLaunch({
        url: '/pages/index/index'
      });
    }, 1500);
  },

  // 跳转到注册页面
  goToRegister: function() {
    wx.navigateTo({
      url: '/pages/register/register'
    });
  }
});
