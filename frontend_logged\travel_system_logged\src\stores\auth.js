// src/stores/auth.js
import { defineStore } from 'pinia';
import { login, register, getCurrentUser } from '@/api/auth';

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: null,
    token: localStorage.getItem('authToken') || null
  }),
  actions: {
    async login(credentials) {
      try {
        const result = await login(credentials);
        const { token, user } = result;
        this.token = token;
        this.user = user;
        localStorage.setItem('authToken', token);
        return { success: true };
      } catch (error) {
        console.error('登录失败:', error);
        return { success: false, error: error.message };
      }
    },
    async loadUser() {
      if (this.token) {
        this.user = await getCurrentUser();
      }
    }
  }
});