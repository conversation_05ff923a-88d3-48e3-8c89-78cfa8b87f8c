# AI 生成模块技术文档

本文档详细描述了个性化旅游系统中的AI生成功能模块。系统集成了多种AI技术和智能算法，为用户提供智能化的旅游内容生成、个性化推荐和数据处理服务。

## 目录

1. [AI服务核心架构](#ai服务核心架构)
2. [AI生成功能模块](#ai生成功能模块)
3. [智能推荐系统](#智能推荐系统)
4. [数据结构与算法](#数据结构与算法)
5. [API接口说明](#api接口说明)
6. [技术实现细节](#技术实现细节)

## AI服务核心架构

### 多AI提供商支持

系统支持多个AI服务提供商，实现了统一的AI服务接口：

- **DeepSeek API**: 主要AI服务提供商，支持文本生成和图像理解
- **OpenAI API**: 备用AI服务提供商，提供GPT模型支持
- **百度文心一言**: 国内AI服务提供商，支持中文优化

### AI服务管理器 (`services/ai_service.py`)

```python
class AIService:
    def __init__(self):
        self.provider = 'deepseek'  # 默认使用DeepSeek
        self.cache = {}  # 响应缓存

    def generate_text(self, prompt: str, max_tokens: int = None, temperature: float = None) -> str:
        """统一的文本生成接口"""
        # 支持缓存机制，避免重复调用
        # 自动故障转移，API失败时切换到备用方案
```

**核心特性**：
- **智能缓存**: 避免重复的API调用，提高响应速度
- **故障转移**: API失败时自动切换到本地生成方案
- **多提供商支持**: 可配置使用不同的AI服务提供商
- **错误处理**: 完善的错误处理和降级机制

## AI生成功能模块

### 1. 智能旅游计划生成 (`routes/ai_generator.py`)

**功能描述**: 基于用户选择的地点和天数，使用AI生成个性化的旅游计划。

**技术实现**:
```python
@ai_generator_bp.route('/generate_plan', methods=['POST'])
def generate_travel_plan():
    """生成旅游计划"""
    # 构建AI提示词
    prompt = f"""
    请为一个{days}天的旅行生成详细的旅行计划。旅行将包括以下地点：
    {json.dumps(location_info, ensure_ascii=False, indent=2)}
    用户偏好: {preferences}

    请以JSON格式返回，包含每天的详细行程安排。
    """

    # 调用AI生成
    ai_response = ai_service.generate_text(prompt, max_tokens=2000)
```

**核心特性**:
- 支持多地点、多天数的复杂行程规划
- 考虑用户偏好（文化、自然、美食等）
- 智能安排时间和路线
- 提供详细的活动建议和注意事项

### 2. 智能文章摘要生成

**功能描述**: 自动为旅游文章生成简洁、准确的摘要。

**技术实现**:
```python
@ai_generator_bp.route('/generate_summary', methods=['POST'])
def generate_article_summary():
    """生成文章摘要"""
    prompt = f"""
    请为以下文章生成一个简洁、准确的摘要，长度不超过{max_length}个字符：

    标题: {article.title}
    内容: {content[:3000]}  # 限制输入长度

    请确保摘要包含文章的主要观点和关键信息。
    """
```

**核心特性**:
- 支持长文本的智能压缩
- 保留关键信息和主要观点
- 可配置摘要长度
- 支持中文文本的语义理解

### 3. 地点描述生成

**功能描述**: 为旅游景点生成详细、生动的描述文本。

**技术实现**:
- 根据地点信息生成个性化描述
- 支持不同风格（简洁、详细、文学）
- 可指定描述重点（历史、文化、建筑）

### 4. 旅游建议生成

**功能描述**: 基于目的地、季节、预算等因素生成个性化旅游建议。

**技术实现**:
```python
@ai_generator_bp.route('/generate_tips', methods=['POST'])
def generate_travel_tips():
    """生成旅游建议"""
    prompt = f"""
    请为以下旅游计划生成详细的旅游建议：

    目的地: {location_names}
    季节: {season}
    用户偏好: {preferences}
    预算水平: {budget}

    请提供以下类型的建议：
    1. 通用旅游建议
    2. 各地点的具体建议
    3. 美食推荐
    4. 预算建议
    """
```

### 5. 场景描述与动画生成

**功能描述**: 基于文本提示或图片生成详细的场景描述，用于动画制作。

**技术实现**:
- 支持文本提示词输入
- 支持图片上传和分析
- 生成适合动画制作的详细描述
- 包含视觉元素、色彩、氛围等信息

### 6. 日记动画生成服务 (`services/diary_animation_service.py`)

**功能描述**: 基于旅游日记内容生成动画脚本和故事板。

**技术实现**:
```python
class DiaryAnimationService:
    def generate_diary_animation(self, article_id: int, style: str = 'cinematic'):
        """基于日记内容生成旅游动画"""
        # 提取文章内容
        content_data = self._extract_article_content(article)

        # 生成动画脚本
        animation_script = self._generate_animation_script(content_data, style)

        # 根据风格生成动画
        if style == 'cinematic':
            return self._create_cinematic_animation(content_data, animation_script)
```

**支持的动画风格**:
- **cinematic**: 电影级动画效果
- **slideshow**: 幻灯片风格
- **storytelling**: 故事叙述风格

### 7. AIGC服务 (`services/aigc_service.py`)

**功能描述**: AI生成内容服务，支持图像到动画的转换。

**技术实现**:
```python
class AIGCService:
    def generate_animation_from_image(self, image_url: str, location_name: str = None):
        """从图片生成动画"""
        # 下载图片并转换为base64
        base64_image = self._image_to_base64(image_url)

        # 构建提示词
        prompt = f"Create a beautiful travel animation based on this image of {location_name}"

        # 调用AI API生成动画描述
        return self._call_ai_api(prompt, base64_image)
```

## 智能推荐系统

### 1. 高级推荐算法 (`routes/advanced_recommend.py`)

系统实现了多种先进的推荐算法，为用户提供个性化的景点和内容推荐。

#### 协同过滤推荐

**技术实现**:
```python
@advanced_recommend_bp.route('/collaborative', methods=['POST'])
def get_collaborative_recommendations():
    """基于协同过滤的推荐"""
    # 获取用户浏览历史
    user_history = data_manager.get_user_browse_history(user_id)

    # 获取所有用户的浏览历史
    all_users_history = data_manager.get_all_users_browse_history()

    # 使用协同过滤算法
    recommendations = RecommendationAlgorithms.collaborative_filtering(
        locations, user_history, all_users_history, limit
    )
```

**算法原理**:
- 基于用户行为相似性进行推荐
- 计算用户间的余弦相似度
- 推荐相似用户喜欢的景点
- 时间复杂度: O(u²·l)，其中u是用户数，l是景点数

#### 基于内容的推荐

**技术实现**:
```python
def content_based_filtering(locations, user_history, limit=None):
    """基于内容的景点推荐"""
    # 分析用户偏好
    user_preferences = analyze_user_preferences(user_history)

    # 计算景点与用户偏好的匹配度
    for location in locations:
        content_score = calculate_content_similarity(location, user_preferences)
```

**算法特性**:
- 基于景点属性和用户历史偏好
- 分析关键词和类型匹配度
- 支持冷启动问题（新用户）
- 时间复杂度: O(n + m log m)

#### 混合推荐算法

**技术实现**:
```python
@advanced_recommend_bp.route('/hybrid', methods=['POST'])
def get_hybrid_recommendations():
    """混合推荐算法"""
    weights = {
        'collaborative': 1.0,
        'content': 1.0,
        'popularity': 0.5
    }

    recommendations = RecommendationAlgorithms.hybrid_recommendation(
        locations, user_history, all_users_history, weights, limit
    )
```

**算法优势**:
- 结合多种推荐方法的优点
- 可配置不同算法的权重
- 提高推荐准确性和多样性
- 解决单一算法的局限性

### 2. 餐厅推荐系统 (`utils/restaurant_recommender.py`)

**功能描述**: 专门针对餐厅的智能推荐系统。

**技术实现**:
```python
class RestaurantRecommender:
    @staticmethod
    def collaborative_filtering_recommend(user_id: int, limit: int = 10):
        """基于协同过滤的餐馆推荐"""
        # 获取用户收藏和评分历史
        user_favorite_ids = RestaurantRecommender.get_user_favorites(user_id)
        user_rating_ids = RestaurantRecommender.get_user_ratings(user_id)

        # 找到相似用户
        similar_users = RestaurantRecommender.find_similar_users(user_id, limit=20)

        # 生成推荐
        return generate_recommendations(similar_users, user_interacted_ids)
```

**推荐策略**:
- 基于用户收藏和评分行为
- 考虑地理位置因素
- 支持多种排序方式（评分、距离、热度）
- 个性化推荐算法

### 3. 推荐算法工厂 (`utils/recommendation_factory.py`)

**设计模式**: 使用工厂模式和单例模式管理推荐系统实例。

**技术实现**:
```python
class RecommendationFactory:
    _instance = None

    @classmethod
    def get_algorithm_recommendation_system(cls):
        """获取算法推荐系统实例"""
        if cls._instance is None:
            cls._instance = AlgorithmRecommendationSystem()
        return cls._instance
```

### 4. 混合推荐引擎 (`utils/recommender/hybrid.py`)

**功能描述**: 实现复杂的混合推荐算法。

**技术实现**:
```python
def calculate_hybrid_score(journal, user_id):
    """混合推荐算法实现"""
    # 热度计算（基于时间衰减）
    hours_since_creation = (datetime.utcnow() - journal.created_at).total_seconds() / 3600
    hotness = views * (0.95 ** hours_since_creation)  # 每小时衰减5%

    # 评分计算（加权平均）
    rating_weight = min(total_raters / 100, 1.0) if total_raters > 0 else 0
    rating = average_rating * rating_weight * 0.3

    # 个人偏好模拟
    user_preference = 0.1 * get_user_preference_simulation(user_id, journal)
```

**算法特性**:
- 时间衰减机制
- 多因子评分系统
- 个性化权重调整
- 实时推荐更新

## 数据结构与算法

### 1. 文本压缩算法 - Huffman编码 (`services/article_service.py`)

**功能描述**: 使用Huffman编码对文章内容进行无损压缩，减少存储空间。

**技术实现**:
```python
class ArticleService:
    def build_huffman_tree(self, text: str) -> HuffmanNode:
        """构建Huffman树"""
        # 统计字符频率
        frequency = {}
        for char in text:
            frequency[char] = frequency.get(char, 0) + 1

        # 创建优先队列
        priority_queue = []
        for char, freq in frequency.items():
            node = HuffmanNode(char, freq)
            heapq.heappush(priority_queue, node)

        # 构建Huffman树
        while len(priority_queue) > 1:
            left = heapq.heappop(priority_queue)
            right = heapq.heappop(priority_queue)
            merged = HuffmanNode(None, left.freq + right.freq)
            merged.left = left
            merged.right = right
            heapq.heappush(priority_queue, merged)

        return priority_queue[0]
```

**算法特性**:
- 时间复杂度: O(n log n)，其中n是不同字符的数量
- 空间复杂度: O(n)
- 压缩率: 对中文文本通常可达40%-60%
- 无损压缩，完全可逆

### 2. 路径规划算法 (`services/path_planning_service.py`)

#### Dijkstra最短路径算法

**技术实现**:
```python
def dijkstra(self, start_id: int, end_id: int, strategy: int = 0):
    """Dijkstra算法寻找最短路径"""
    # 初始化距离和前驱节点
    distances = {vertex: float('infinity') for vertex in self.graph}
    distances[start_id] = 0
    previous = {vertex: None for vertex in self.graph}

    # 优先队列
    pq = [(0, start_id)]
    visited = set()

    while pq:
        current_distance, current_vertex = heapq.heappop(pq)

        if current_vertex in visited:
            continue

        visited.add(current_vertex)

        # 更新邻居节点
        for neighbor, weight in self.graph[current_vertex]:
            distance = current_distance + weight

            if distance < distances[neighbor]:
                distances[neighbor] = distance
                previous[neighbor] = current_vertex
                heapq.heappush(pq, (distance, neighbor))
```

**算法特性**:
- 时间复杂度: O((V + E) log V)，其中V是顶点数，E是边数
- 适用于非负权重图
- 支持多种策略（最短距离、最短时间、适合骑行）

#### 旅行商问题(TSP)算法

**Held-Karp动态规划算法** (`utils/held_karp.py`):
```python
def held_karp(distance_matrix):
    """使用动态规划解决TSP问题"""
    n = len(distance_matrix)

    # dp[mask][i] 表示访问了mask中的城市，当前在城市i的最小距离
    dp = {}

    # 初始化：从起点0开始，只访问了起点
    for i in range(1, n):
        dp[(1 << i, i)] = distance_matrix[0][i]

    # 动态规划填表
    for subset_size in range(2, n):
        for subset in itertools.combinations(range(1, n), subset_size):
            bits = 0
            for bit in subset:
                bits |= 1 << bit

            for k in subset:
                prev_bits = bits & ~(1 << k)
                res = []
                for m in subset:
                    if m == k:
                        continue
                    res.append(dp[(prev_bits, m)] + distance_matrix[m][k])
                dp[(bits, k)] = min(res)
```

**算法特性**:
- 时间复杂度: O(n²·2ⁿ)
- 适用于小规模TSP问题（n ≤ 15）
- 精确算法，保证最优解

**模拟退火算法** (`utils/simulated_annealing.py`):
```python
def simulated_annealing(distance_matrix, max_iterations=10000):
    """使用模拟退火算法解决大规模TSP"""
    n = len(distance_matrix)

    # 初始解：随机排列
    current_path = list(range(n))
    random.shuffle(current_path[1:])  # 保持起点为0

    current_distance = calculate_total_distance(current_path, distance_matrix)
    best_path = current_path[:]
    best_distance = current_distance

    # 初始温度和冷却率
    temperature = 1000.0
    cooling_rate = 0.995

    for iteration in range(max_iterations):
        # 生成邻居解：交换两个城市
        new_path = current_path[:]
        i, j = random.sample(range(1, n), 2)
        new_path[i], new_path[j] = new_path[j], new_path[i]

        new_distance = calculate_total_distance(new_path, distance_matrix)

        # 接受准则
        if (new_distance < current_distance or
            random.random() < math.exp(-(new_distance - current_distance) / temperature)):
            current_path = new_path
            current_distance = new_distance

            if new_distance < best_distance:
                best_path = new_path[:]
                best_distance = new_distance

        # 降温
        temperature *= cooling_rate
```

**算法特性**:
- 时间复杂度: O(k·n)，其中k是迭代次数
- 适用于大规模TSP问题（n > 15）
- 启发式算法，获得近似最优解

### 3. 文本搜索算法 (`utils/boyer_moore_chinese.py`)

**Boyer-Moore中文搜索算法**:

**技术实现**:
```python
class BoyerMooreChinese:
    def __init__(self, pattern):
        self.pattern = pattern
        self.pattern_length = len(pattern)
        self.bad_char_table = self._build_bad_char_table()

    def _build_bad_char_table(self):
        """构建坏字符表"""
        table = {}
        for i in range(self.pattern_length):
            table[self.pattern[i]] = i
        return table

    def search_all(self, text):
        """搜索所有匹配位置"""
        matches = []
        text_length = len(text)
        i = 0

        while i <= text_length - self.pattern_length:
            j = self.pattern_length - 1

            # 从右向左匹配
            while j >= 0 and self.pattern[j] == text[i + j]:
                j -= 1

            if j < 0:
                # 找到匹配
                matches.append(i)
                i += 1
            else:
                # 使用坏字符规则跳跃
                bad_char = text[i + j]
                if bad_char in self.bad_char_table:
                    i += max(1, j - self.bad_char_table[bad_char])
                else:
                    i += j + 1

        return matches
```

**算法特性**:
- 平均时间复杂度: O(n/m)，其中n是文本长度，m是模式长度
- 最坏时间复杂度: O(n·m)
- 特别优化了中文字符的处理
- 支持多模式匹配

### 4. 排序算法 (`utils/diary_sorter.py`, `utils/restaurant_sorter.py`)

#### 快速排序算法

**技术实现**:
```python
class DiarySorter:
    @staticmethod
    def quick_sort(diaries, key_func, reverse=False):
        """快速排序实现"""
        def _quick_sort(arr, low, high):
            if low < high:
                pivot_index = _partition(arr, low, high)
                _quick_sort(arr, low, pivot_index - 1)
                _quick_sort(arr, pivot_index + 1, high)

        def _partition(arr, low, high):
            # 随机选择pivot，避免最坏情况
            pivot_index = random.randint(low, high)
            arr[pivot_index], arr[high] = arr[high], arr[pivot_index]

            pivot = key_func(arr[high])
            i = low - 1

            for j in range(low, high):
                if (not reverse and key_func(arr[j]) <= pivot) or \
                   (reverse and key_func(arr[j]) >= pivot):
                    i += 1
                    arr[i], arr[j] = arr[j], arr[i]

            arr[i + 1], arr[high] = arr[high], arr[i + 1]
            return i + 1
```

**算法特性**:
- 平均时间复杂度: O(n log n)
- 最坏时间复杂度: O(n²)
- 空间复杂度: O(log n)
- 原地排序算法

#### 堆排序算法

**技术实现**:
```python
@staticmethod
def heap_sort(items, key_func, reverse=False):
    """堆排序实现"""
    if reverse:
        heap = [(-key_func(item), i, item) for i, item in enumerate(items)]
    else:
        heap = [(key_func(item), i, item) for i, item in enumerate(items)]

    heapq.heapify(heap)

    result = []
    while heap:
        _, _, item = heapq.heappop(heap)
        result.append(item)

    return result
```

**算法特性**:
- 时间复杂度: O(n log n)
- 空间复杂度: O(1)
- 稳定的性能表现

## API接口说明

### 1. AI生成相关接口

#### 生成旅游计划
- **URL**: `/api/ai/generate_plan`
- **方法**: `POST`
- **功能**: 基于地点和天数生成个性化旅游计划

#### 生成文章摘要
- **URL**: `/api/ai/generate_summary`
- **方法**: `POST`
- **功能**: 为旅游文章生成智能摘要

#### 生成地点描述
- **URL**: `/api/ai/generate_description`
- **方法**: `POST`
- **功能**: 为景点生成详细描述

#### 生成旅游建议
- **URL**: `/api/ai/generate_tips`
- **方法**: `POST`
- **功能**: 基于多因素生成个性化旅游建议

#### 生成场景描述
- **URL**: `/api/ai/generate`
- **方法**: `POST`
- **功能**: 生成动画场景描述

#### 生成日记故事板
- **URL**: `/api/ai/generate_storyboard`
- **方法**: `POST`
- **功能**: 基于日记内容生成动画故事板

### 2. 推荐系统接口

#### 协同过滤推荐
- **URL**: `/api/recommend/collaborative`
- **方法**: `POST`
- **功能**: 基于用户行为的协同过滤推荐

#### 混合推荐
- **URL**: `/api/recommend/hybrid`
- **方法**: `POST`
- **功能**: 结合多种算法的混合推荐

#### 个性化推荐
- **URL**: `/api/recommend`
- **方法**: `POST`
- **功能**: 智能个性化推荐（游客/登录用户）

#### 餐厅推荐
- **URL**: `/api/food/smart_recommend`
- **方法**: `POST`
- **功能**: 基于协同过滤的餐厅推荐

## 技术实现细节

### 1. AI服务架构设计

#### 服务提供商管理
```python
class AIService:
    def __init__(self):
        self.providers = {
            'deepseek': {
                'api_key': DEEPSEEK_API_KEY,
                'base_url': DEEPSEEK_API_BASE,
                'model': 'deepseek-chat'
            },
            'openai': {
                'api_key': OPENAI_API_KEY,
                'base_url': 'https://api.openai.com/v1',
                'model': 'gpt-3.5-turbo'
            },
            'baidu': {
                'api_key': BAIDU_API_KEY,
                'secret_key': BAIDU_SECRET_KEY,
                'model': 'ernie-bot-turbo'
            }
        }
```

#### 缓存机制
```python
def _get_cache(self, request_data):
    """智能缓存机制"""
    request_hash = hashlib.md5(
        json.dumps(request_data, sort_keys=True).encode()
    ).hexdigest()

    cache_entry = AICache.query.filter_by(request_hash=request_hash).first()

    if cache_entry and cache_entry.expires_at > datetime.now():
        return cache_entry.response

    return None
```

#### 故障转移机制
```python
def generate_text(self, prompt, max_tokens=None, temperature=None):
    """带故障转移的文本生成"""
    try:
        # 尝试主要提供商
        if self.provider == 'deepseek':
            return self._call_deepseek(prompt, max_tokens, temperature)
    except Exception as e:
        print(f"主要AI服务失败: {e}")

        # 尝试备用提供商
        try:
            return self._call_openai(prompt, max_tokens, temperature)
        except Exception as e2:
            print(f"备用AI服务失败: {e2}")

            # 使用本地生成方案
            return self._local_fallback(prompt)
```

### 2. 推荐算法优化

#### 相似度计算优化
```python
def calculate_cosine_similarity(user1_history, user2_history):
    """优化的余弦相似度计算"""
    # 使用稀疏向量表示
    common_items = set(user1_history.keys()) & set(user2_history.keys())

    if not common_items:
        return 0.0

    # 计算点积和模长
    dot_product = sum(user1_history[item] * user2_history[item]
                     for item in common_items)

    norm1 = math.sqrt(sum(count ** 2 for count in user1_history.values()))
    norm2 = math.sqrt(sum(count ** 2 for count in user2_history.values()))

    if norm1 == 0 or norm2 == 0:
        return 0.0

    return dot_product / (norm1 * norm2)
```

#### 推荐结果多样性保证
```python
def ensure_diversity(recommendations, diversity_threshold=0.3):
    """确保推荐结果的多样性"""
    diverse_recs = []
    categories_used = set()

    for rec in recommendations:
        category = rec.get('category', 'unknown')

        # 控制同类别推荐数量
        category_count = sum(1 for r in diverse_recs
                           if r.get('category') == category)

        if category_count < len(recommendations) * diversity_threshold:
            diverse_recs.append(rec)
            categories_used.add(category)

    return diverse_recs
```

### 3. 性能优化策略

#### 数据预处理和缓存
```python
class DataManager:
    def __init__(self):
        self.cache_expiry = 3600  # 1小时缓存
        self.location_cache = {}
        self.user_history_cache = {}

    def get_all_locations(self):
        """带缓存的地点数据获取"""
        cache_key = 'all_locations'

        if (cache_key in self.location_cache and
            time.time() - self.location_cache[cache_key]['timestamp'] < self.cache_expiry):
            return self.location_cache[cache_key]['data']

        # 从数据库获取数据
        locations = Location.query.all()
        location_data = [loc.to_dict() for loc in locations]

        # 更新缓存
        self.location_cache[cache_key] = {
            'data': location_data,
            'timestamp': time.time()
        }

        return location_data
```

#### 异步处理
```python
from concurrent.futures import ThreadPoolExecutor
import asyncio

class AsyncAIService:
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=4)

    async def generate_multiple_contents(self, prompts):
        """并行生成多个内容"""
        loop = asyncio.get_event_loop()

        tasks = [
            loop.run_in_executor(self.executor, self.generate_text, prompt)
            for prompt in prompts
        ]

        results = await asyncio.gather(*tasks)
        return results
```

### 4. 错误处理和监控

#### 统一错误处理
```python
class AIServiceError(Exception):
    """AI服务异常基类"""
    pass

class APIQuotaExceededError(AIServiceError):
    """API配额超限异常"""
    pass

class NetworkTimeoutError(AIServiceError):
    """网络超时异常"""
    pass

def handle_ai_errors(func):
    """AI服务错误处理装饰器"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except requests.exceptions.Timeout:
            raise NetworkTimeoutError("AI服务请求超时")
        except requests.exceptions.RequestException as e:
            if "quota" in str(e).lower():
                raise APIQuotaExceededError("API配额已用完")
            raise AIServiceError(f"AI服务请求失败: {e}")
    return wrapper
```

#### 性能监控
```python
class PerformanceMonitor:
    def __init__(self):
        self.metrics = {
            'ai_requests': 0,
            'ai_failures': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'avg_response_time': 0
        }

    def record_ai_request(self, success, response_time):
        """记录AI请求性能指标"""
        self.metrics['ai_requests'] += 1
        if not success:
            self.metrics['ai_failures'] += 1

        # 更新平均响应时间
        self.metrics['avg_response_time'] = (
            (self.metrics['avg_response_time'] * (self.metrics['ai_requests'] - 1) + response_time) /
            self.metrics['ai_requests']
        )
```

### 5. 安全性考虑

#### API密钥管理
```python
import os
from cryptography.fernet import Fernet

class SecureConfig:
    def __init__(self):
        self.cipher_suite = Fernet(os.environ.get('ENCRYPTION_KEY'))

    def get_api_key(self, provider):
        """安全获取API密钥"""
        encrypted_key = os.environ.get(f'{provider.upper()}_API_KEY_ENCRYPTED')
        if encrypted_key:
            return self.cipher_suite.decrypt(encrypted_key.encode()).decode()
        return None
```

#### 输入验证和清理
```python
def sanitize_prompt(prompt):
    """清理和验证AI提示词"""
    # 移除潜在的恶意内容
    dangerous_patterns = [
        r'<script.*?>.*?</script>',
        r'javascript:',
        r'on\w+\s*=',
    ]

    for pattern in dangerous_patterns:
        prompt = re.sub(pattern, '', prompt, flags=re.IGNORECASE)

    # 限制长度
    if len(prompt) > 10000:
        prompt = prompt[:10000]

    return prompt.strip()
```

## 总结

个性化旅游系统的AI生成模块集成了多种先进技术：

1. **AI生成功能**: 支持旅游计划、文章摘要、地点描述等多种内容的智能生成
2. **智能推荐系统**: 实现了协同过滤、基于内容、混合推荐等多种算法
3. **数据结构与算法**: 包含Huffman编码、路径规划、文本搜索、排序等核心算法
4. **性能优化**: 通过缓存、异步处理、数据预处理等技术提升系统性能
5. **可靠性保障**: 实现了故障转移、错误处理、性能监控等机制

这些技术的综合应用为用户提供了智能化、个性化的旅游服务体验。