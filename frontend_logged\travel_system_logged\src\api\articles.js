import axios from 'axios';

// 文章相关API
export const article = {
  // 获取文章列表
  getArticles(params) {
    return axios.get('/api/articles', { params });
  },

  // 获取所有文章（用于AI生成模块）
  getAllArticles() {
    return axios.get('/api/articles', {
      params: {
        page: 1,
        size: 100 // 获取更多文章用于选择
      }
    });
  },

  // 获取文章详情
  getArticleById(id, incrementPopularity = true) {
    return axios.get(`/api/articles/${id}`, {
      params: { increment_popularity: incrementPopularity }
    });
  },

  // 添加文章
  addArticle(data) {
    return axios.post('/api/articles', data);
  },

  // 更新文章
  updateArticle(id, data) {
    return axios.put(`/api/articles/${id}`, data);
  },

  // 删除文章
  deleteArticle(id) {
    return axios.delete(`/api/articles/${id}`);
  },

  // 搜索文章
  searchArticlesByTitle(title) {
    return axios.get('/api/articles/search', { params: { title } });
  },

  // 更新文章人气
  updateArticlePopularity(id) {
    return axios.post(`/api/articles/${id}/popularity`);
  },

  // 设置文章评分
  setArticleScore(userId, articleId, score) {
    return axios.post('/api/article_score', { user_id: userId, article_id: articleId, score });
  },

  // 获取文章平均评分
  getArticleAverageScore(articleId) {
    return axios.get(`/api/article_score/get_average_score/${articleId}`);
  },

  // 获取用户对文章的评分
  getUserArticleScore(userId, articleId) {
    return axios.post('/api/article_score/get_user_score', { user_id: userId, article_id: articleId });
  },

  // 获取协同过滤推荐文章
  getCollaborativeRecommendations(userId, limit = 10) {
    return axios.post('/api/articles/collaborative-recommendations', {
      user_id: userId,
      limit: limit
    });
  },

  // 获取"为您推荐"的文章
  getForYouRecommendations(userId = null, isGuest = false, limit = 20) {
    const data = {
      limit: limit,
      is_guest: isGuest
    };

    if (!isGuest && userId) {
      data.user_id = userId;
    }

    return axios.post('/api/articles/for-you', data);
  },

  // 全文检索
  fullTextSearch(query, limit = 10) {
    return axios.post('/api/articles/full-text-search', {
      query: query,
      limit: limit
    });
  },

  // 获取搜索建议
  getSearchSuggestions(query, limit = 5) {
    return axios.post('/api/articles/search-suggestions', {
      query: query,
      limit: limit
    });
  },

  // 模糊搜索文章（用于搜索建议）
  fuzzySearchArticles(query, limit = 10) {
    return axios.get('/api/articles/fuzzy_search', {
      params: {
        query: query,
        limit: limit
      }
    });
  },

  // 添加文章评论
  addArticleComment(userId, articleId, content) {
    return axios.post('/api/article_comment', {
      user_id: userId,
      article_id: articleId,
      content
    });
  },

  // 获取文章评论
  getArticleComments(articleId) {
    return axios.get(`/api/article_comment/${articleId}`);
  },

  // 删除文章评论
  deleteArticleComment(commentId, userId) {
    return axios.delete(`/api/article_comment/${commentId}`, {
      data: { user_id: userId }
    });
  }
};

// 地点相关API
export const location = {
  // 获取所有地点
  getAllLocations() {
    return axios.get('/api/locations');
  },

  // 获取地点详情
  getLocationById(id) {
    return axios.get(`/api/locations/${id}`);
  },

  // 搜索地点
  searchLocations(keyword) {
    return axios.get('/api/locations/search', { params: { keyword } });
  }
};

// 上传相关API
export const upload = {
  // 上传文章图片
  uploadArticleImage(file) {
    const formData = new FormData();
    formData.append('image', file);
    return axios.post('/api/uploads/article-images', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  }
};

// 收藏相关API
export const favorite = {
  // 添加收藏
  addArticleFavorite(userId, articleId) {
    return axios.post('/api/favorites', { user_id: userId, article_id: articleId });
  },

  // 移除收藏
  removeArticleFavorite(userId, articleId) {
    return axios.delete(`/api/favorites/${userId}/${articleId}`);
  },

  // 获取用户收藏列表
  getUserFavorites(userId) {
    return axios.get(`/api/users/${userId}/favorites`);
  }
};

// 点赞相关API
export const like = {
  // 点赞文章
  likeArticle(userId, articleId) {
    return axios.post('http://localhost:5000/api/article_like', { user_id: userId, article_id: articleId });
  },

  // 取消点赞
  unlikeArticle(userId, articleId) {
    return axios.post('http://localhost:5000/api/article_like/unlike', { user_id: userId, article_id: articleId });
  },

  // 检查是否已点赞
  checkLike(userId, articleId) {
    return axios.post('http://localhost:5000/api/article_like/check', { user_id: userId, article_id: articleId });
  },

  // 获取文章点赞数
  getLikeCount(articleId) {
    return axios.get(`http://localhost:5000/api/article_like/count/${articleId}`);
  }
};

// 默认导出
const articlesApi = article;
export default articlesApi;