/* diary-detail.wxss */
.page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

/* 顶部导航栏 */
.nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-left, .nav-right {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  font-size: 32rpx;
  transition: all 0.3s ease;
}

.nav-left:active, .nav-right:active {
  transform: scale(0.9);
  background: rgba(102, 126, 234, 0.2);
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 60vh;
  gap: 30rpx;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid rgba(102, 126, 234, 0.2);
  border-top: 6rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: white;
  font-size: 28rpx;
  font-weight: 500;
}

/* 日记详情 */
.diary-detail {
  background: rgba(255, 255, 255, 0.95);
  margin: 20rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 头部信息 */
.diary-header {
  padding: 40rpx 30rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.diary-title {
  font-size: 42rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 24rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.diary-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  gap: 20rpx;
}

.diary-location, .diary-date {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 20rpx;
  font-size: 24rpx;
}

.location-icon, .date-icon {
  font-size: 28rpx;
}

.location-text, .date-text {
  color: #666;
  font-weight: 500;
}

.diary-stats {
  display: flex;
  gap: 24rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
  font-size: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.stat-icon {
  font-size: 28rpx;
}

.stat-text {
  color: #666;
  font-weight: 500;
}

/* 图片展示 */
.diary-images {
  padding: 30rpx;
}

.images-header, .content-header, .tags-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.section-icon {
  font-size: 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.image-carousel {
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.image-swiper {
  height: 500rpx;
}

.image-item {
  position: relative;
  width: 100%;
  height: 100%;
}

.diary-image {
  width: 100%;
  height: 100%;
}

.image-overlay {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

/* 内容 */
.diary-content {
  padding: 30rpx;
}

.content-body {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16rpx;
  padding: 30rpx;
  border-left: 6rpx solid #667eea;
}

.content-text {
  font-size: 30rpx;
  line-height: 1.8;
  color: #333;
  word-break: break-all;
  text-align: justify;
}

/* 标签 */
.diary-tags {
  padding: 30rpx;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.tag-item {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  font-size: 24rpx;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
  transition: transform 0.3s ease;
}

.tag-item:active {
  transform: scale(0.95);
}

.tag-text {
  font-weight: 500;
}

/* 操作栏 */
.action-bar {
  display: flex;
  justify-content: space-around;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  position: sticky;
  bottom: 0;
  margin: 0 20rpx 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  min-width: 120rpx;
}

.action-item:active {
  transform: scale(0.95);
  background: rgba(102, 126, 234, 0.1);
}

.action-item.active {
  background: rgba(102, 126, 234, 0.1);
}

.action-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(102, 126, 234, 0.1);
  transition: all 0.3s ease;
}

.action-item.active .action-icon {
  background: linear-gradient(45deg, #667eea, #764ba2);
  transform: scale(1.1);
}

.action-emoji {
  font-size: 36rpx;
}

.action-text {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.action-item.active .action-text {
  color: #667eea;
  font-weight: bold;
}

/* 安全区域 */
.safe-area {
  height: 40rpx;
}

/* 错误状态 */
.error-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80vh;
  padding: 60rpx;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  padding: 60rpx 40rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  text-align: center;
}

.error-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10rpx);
  }
  60% {
    transform: translateY(-5rpx);
  }
}

.error-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.error-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  line-height: 1.6;
}

.error-action {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 20rpx 40rpx;
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.4);
  transition: transform 0.3s ease;
}

.error-action:active {
  transform: scale(0.95);
}
