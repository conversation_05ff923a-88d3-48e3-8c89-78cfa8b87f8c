"""
Location browse count model
"""
from utils.database import db

class LocationBrowseCount(db.Model):
    """
    Location browse count model - tracks how many times a user has viewed a location
    """
    __tablename__ = 'location_browse_counts'
    __table_args__ = {'extend_existing': True}

    user_id = db.<PERSON>umn(db.<PERSON>, db.<PERSON>('users.user_id'), primary_key=True)
    location_id = db.Column(db.Integer, db.ForeignKey('locations.location_id'), primary_key=True)
    count = db.Column(db.Integer, default=0, nullable=False)

    # 关系
    user = db.relationship('User', backref='location_browse_counts')
    location = db.relationship('Location', backref='browse_counts')
