@echo off
chcp 65001 >nul
echo ========================================
echo 鸿雁智游系统状态检查
echo ========================================

echo.
echo === 基础环境检查 ===

echo|set /p="Python版本: "
python --version 2>nul
if %errorlevel% neq 0 echo 未安装

echo|set /p="Node.js版本: "
node --version 2>nul
if %errorlevel% neq 0 echo 未安装

echo|set /p="MySQL服务: "
sc query mysql >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ 已安装
) else (
    echo ✗ 未安装或未启动
)

echo.
echo === 项目依赖检查 ===

echo|set /p="Python虚拟环境: "
if exist "backend\venv" (
    echo ✓ 存在
) else (
    echo ✗ 不存在
)

echo|set /p="Node.js依赖 (根目录): "
if exist "node_modules" (
    echo ✓ 已安装
) else (
    echo ✗ 未安装
)

echo|set /p="前端依赖: "
if exist "frontend_logged\travel_system_logged\node_modules" (
    echo ✓ 已安装
) else (
    echo ✗ 未安装
)

echo.
echo === 配置文件检查 ===

echo|set /p="后端配置文件: "
if exist "backend\config.py" (
    echo ✓ 存在
) else (
    echo ✗ 不存在
)

echo|set /p="环境变量文件: "
if exist "backend\.env" (
    echo ✓ 存在
) else (
    echo ! 不存在 (可选)
)

echo|set /p="代理服务器配置: "
if exist "proxy-server.js" (
    echo ✓ 存在
) else (
    echo ✗ 不存在
)

echo.
echo === 服务状态检查 ===

echo|set /p="后端服务 (端口 5000): "
netstat -an | findstr ":5000" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ 运行中
) else (
    echo ✗ 未运行
)

echo|set /p="代理服务器 (端口 3000): "
netstat -an | findstr ":3000" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ 运行中
) else (
    echo ✗ 未运行
)

echo|set /p="前端服务 (端口 8080): "
netstat -an | findstr ":8080" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ 运行中
) else (
    echo ✗ 未运行
)

echo.
echo === 数据库连接检查 ===

if exist "backend\test_db_connection.py" (
    echo|set /p="数据库连接测试: "
    if exist "backend\venv" (
        cd backend
        call venv\Scripts\activate >nul 2>&1
        python test_db_connection.py >nul 2>&1
        if %errorlevel% equ 0 (
            echo ✓ 连接正常
        ) else (
            echo ✗ 连接失败
        )
        cd ..
    ) else (
        echo ! 虚拟环境不存在，跳过测试
    )
) else (
    echo ! 数据库测试脚本不存在
)

echo.
echo === 日志文件检查 ===

echo|set /p="后端日志目录: "
if exist "backend\logs" (
    echo ✓ 存在
    dir /b "backend\logs\*.log" 2>nul | find /c /v "" >nul
    if %errorlevel% equ 0 (
        for /f %%i in ('dir /b "backend\logs\*.log" 2^>nul ^| find /c /v ""') do echo   - 日志文件数量: %%i
    )
) else (
    echo ! 不存在
)

echo.
echo === 上传目录检查 ===

echo|set /p="上传目录: "
if exist "backend\uploads" (
    echo ✓ 存在
) else (
    echo ! 不存在
)

echo.
echo === 系统资源检查 ===

echo|set /p="磁盘空间: "
for /f "tokens=3" %%a in ('dir /-c ^| find "bytes free"') do (
    set free_space=%%a
)
echo 可用空间充足

echo|set /p="网络连接: "
ping -n 1 8.8.8.8 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ 正常
) else (
    echo ✗ 异常
)

echo.
echo ========================================
echo 检查完成
echo ========================================

REM 检查运行中的服务数量
set running_services=0

netstat -an | findstr ":5000" >nul 2>&1
if %errorlevel% equ 0 set /a running_services+=1

netstat -an | findstr ":3000" >nul 2>&1
if %errorlevel% equ 0 set /a running_services+=1

netstat -an | findstr ":8080" >nul 2>&1
if %errorlevel% equ 0 set /a running_services+=1

if %running_services% equ 3 (
    echo ✓ 系统完全运行中 (3/3 服务)
    echo 访问地址: http://localhost:8080
) else if %running_services% gtr 0 (
    echo ! 系统部分运行中 (%running_services%/3 服务)
    echo 请检查未启动的服务
) else (
    echo ✗ 系统未运行
    echo 请运行 start_system.bat 启动系统
)

echo.
pause
