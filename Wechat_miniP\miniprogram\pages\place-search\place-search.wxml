<!--place-search.wxml-->
<view class="page">
  <!-- 搜索区域 -->
  <view class="search-section">
    <view class="search-box">
      <view class="search-input-wrapper">
        <input
          class="search-input"
          placeholder="输入景点名称，如：故宫、天安门、北京"
          value="{{searchKeyword}}"
          bindinput="onSearchInput"
          placeholder-class="search-placeholder"
        />
        <view class="search-icon" wx:if="{{!searchKeyword}}">🔍</view>
      </view>
      <button class="clear-btn" bindtap="onClearSearch" wx:if="{{searchKeyword}}">
        清空
      </button>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="content">
    <!-- 搜索状态提示 -->
    <view class="search-status" wx:if="{{loading}}">
      <view class="loading-icon">⏳</view>
      <text class="status-text">正在搜索"{{searchKeyword}}"...</text>
    </view>

    <!-- 热门景点 -->
    <view class="section" wx:if="{{showPopular && popularLocations.length > 0}}">
      <view class="section-header">
        <text class="section-title">🔥 热门景点推荐</text>
        <text class="section-subtitle">为您推荐热门景点</text>
      </view>
      <view class="location-list">
        <view
          class="location-item popular-item"
          wx:for="{{popularLocations}}"
          wx:key="location_id"
          data-index="{{index}}"
          bindtap="onPopularTap"
        >
          <view class="location-info">
            <view class="location-name">{{item.name}}</view>
            <view class="location-meta">
              <text class="location-type">{{item.type === 1 ? '景点' : '学校'}}</text>
              <text class="location-popularity">🔥 {{item.popularity}}</text>
              <text class="location-evaluation">⭐ {{item.evaluation}}</text>
            </view>
            <view class="location-description" wx:if="{{item.description}}">
              {{item.description}}
            </view>
          </view>
          <view class="location-arrow">›</view>
        </view>
      </view>
    </view>

    <!-- 搜索结果 -->
    <view class="section" wx:if="{{searchResults.length > 0}}">
      <view class="section-header">
        <text class="section-title">搜索结果</text>
        <text class="section-subtitle">找到 {{searchResults.length}} 个相关景点</text>
      </view>
      <view class="location-list">
        <view
          class="location-item result-item"
          wx:for="{{searchResults}}"
          wx:key="location_id"
          data-index="{{index}}"
          bindtap="onResultTap"
        >
          <view class="location-info">
            <view class="location-name">{{item.name}}</view>
            <view class="location-meta">
              <text class="location-type">{{item.type === 1 ? '景点' : '学校'}}</text>
              <text class="location-popularity">🔥 {{item.popularity}}</text>
              <text class="location-evaluation">⭐ {{item.evaluation}}</text>
            </view>
            <view class="location-description" wx:if="{{item.description}}">
              {{item.description}}
            </view>
            <view class="location-address" wx:if="{{item.address}}">
              📍 {{item.address}}
            </view>
          </view>
          <view class="location-arrow">›</view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!loading && hasSearched && searchResults.length === 0}}">
      <view class="empty-icon">🔍</view>
      <view class="empty-title">未找到相关景点</view>
      <view class="empty-text">没有找到包含"{{searchKeyword}}"的景点</view>
      <view class="empty-tips">
        <text>• 尝试使用更简单的关键词</text>
        <text>• 检查关键词是否正确</text>
        <text>• 尝试搜索其他景点名称</text>
      </view>
      <button class="retry-btn" bindtap="onClearSearch">重新搜索</button>
    </view>

    <!-- 默认状态 -->
    <view class="default-state" wx:if="{{!loading && showPopular && popularLocations.length === 0}}">
      <view class="loading-icon">⏳</view>
      <view class="default-text">正在加载热门景点...</view>
    </view>
  </view>
</view>
