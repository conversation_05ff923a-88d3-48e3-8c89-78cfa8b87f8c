# 用户管理模块 API 文档

本文档详细描述了 Flask 版本后端的用户管理模块 API 接口及其在 Postman 中的测试用例。

## 目录

1. [获取用户信息](#获取用户信息)
2. [更新用户头像](#更新用户头像)
3. [更新用户邮箱](#更新用户邮箱)
4. [更新用户密码](#更新用户密码)
5. [获取用户收藏的文章](#获取用户收藏的文章)
6. [上传头像](#上传头像)
7. [上传文章图片](#上传文章图片)
8. [上传文章视频](#上传文章视频)

## 获取用户信息

### 接口信息

- **URL**: `/api/user/{user_id}`
- **方法**: `GET`
- **描述**: 获取用户信息
- **URL 参数**: user_id - 用户 ID

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "User information retrieved successfully",
    "data": {
        "user": {
            "user_id": 1,
            "username": "testuser",
            "email": "<EMAIL>",
            "avatar": "default_avatar.jpg"
        }
    }
}
```

#### 错误响应

- **用户不存在** (404 Not Found)

```json
{
    "code": 1,
    "message": "User not found",
    "data": null
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `GET`
4. 输入 URL: `http://localhost:5000/api/user/1` (替换 `1` 为实际的用户 ID)
5. 点击 Send 按钮发送请求

## 更新用户头像

### 接口信息

- **URL**: `/api/user/avatar`
- **方法**: `POST`
- **描述**: 更新用户头像
- **请求体**: form-data

### 请求参数

| 参数名  | 类型   | 必填 | 描述                                          |
| ------- | ------ | ---- | --------------------------------------------- |
| user_id | string | 是   | 用户 ID                                       |
| avatar  | file   | 是   | 头像文件，支持 png, jpg, jpeg, gif, webp 格式 |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Avatar updated successfully",
    "data": {
        "avatar_url": "/uploads/avatars/avatar_1_abc123.jpg"
    }
}
```

#### 错误响应

- **用户不存在** (404 Not Found)

```json
{
    "code": 1,
    "message": "User not found",
    "data": null
}
```

- **文件类型不支持** (400 Bad Request)

```json
{
    "code": 1,
    "message": "Invalid file type. Allowed types: png, jpg, jpeg, gif, webp",
    "data": null
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `POST`
4. 输入 URL: `http://localhost:5000/api/user/avatar`
5. 选择 Body 选项卡，选择 form-data
6. 添加键值对:
   - Key: `user_id`, Value: `1` (替换为实际的用户 ID), Type: Text
   - Key: `avatar`, Value: [选择文件], Type: File
7. 点击 Send 按钮发送请求

## 更新用户邮箱

### 接口信息

- **URL**: `/api/user/email`
- **方法**: `PUT`
- **描述**: 更新用户邮箱
- **请求体**: JSON

### 请求参数

| 参数名    | 类型   | 必填 | 描述                   |
| --------- | ------ | ---- | ---------------------- |
| user_id   | number | 是   | 用户 ID                |
| new_email | string | 是   | 新邮箱地址             |
| password  | string | 是   | 用户密码，用于验证身份 |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Email updated successfully",
    "data": {}
}
```

#### 错误响应

- **用户不存在** (404 Not Found)

```json
{
    "code": 1,
    "message": "User not found",
    "data": null
}
```

- **密码错误** (401 Unauthorized)

```json
{
    "code": 1,
    "message": "Invalid password",
    "data": null
}
```

- **邮箱已存在** (400 Bad Request)

```json
{
    "code": 1,
    "message": "Email already exists",
    "data": null
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `PUT`
4. 输入 URL: `http://localhost:5000/api/user/email`
5. 选择 Body 选项卡，选择 raw，设置类型为 JSON
6. 输入请求体:

```json
{
    "user_id": 1,
    "new_email": "<EMAIL>",
    "password": "password123"
}
```

7. 点击 Send 按钮发送请求

## 更新用户密码

### 接口信息

- **URL**: `/api/user/password`
- **方法**: `PUT`
- **描述**: 更新用户密码
- **请求体**: JSON

### 请求参数

| 参数名           | 类型   | 必填 | 描述                         |
| ---------------- | ------ | ---- | ---------------------------- |
| user_id          | number | 是   | 用户 ID                      |
| current_password | string | 是   | 当前密码                     |
| new_password     | string | 是   | 新密码，长度至少为6个字符    |
| confirm_password | string | 是   | 确认新密码，必须与新密码一致 |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Password updated successfully",
    "data": {}
}
```

#### 错误响应

- **用户不存在** (404 Not Found)

```json
{
    "code": 1,
    "message": "User not found",
    "data": null
}
```

- **当前密码错误** (401 Unauthorized)

```json
{
    "code": 1,
    "message": "Current password is incorrect",
    "data": null
}
```

- **新密码和确认密码不匹配** (400 Bad Request)

```json
{
    "code": 1,
    "message": "New password and confirm password do not match",
    "data": null
}
```

- **新密码长度不足** (400 Bad Request)

```json
{
    "code": 1,
    "message": "Password must be at least 6 characters long",
    "data": null
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `PUT`
4. 输入 URL: `http://localhost:5000/api/user/password`
5. 选择 Body 选项卡，选择 raw，设置类型为 JSON
6. 输入请求体:

```json
{
    "user_id": 1,
    "current_password": "oldpassword123",
    "new_password": "newpassword123",
    "confirm_password": "newpassword123"
}
```

7. 点击 Send 按钮发送请求

## 获取用户收藏的文章

### 接口信息

- **URL**: `/api/user/{user_id}/favorites`
- **方法**: `GET`
- **描述**: 获取用户收藏的文章列表
- **URL 参数**: user_id - 用户 ID

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Favorites retrieved successfully",
    "data": {
        "favorites": [
            {
                "article_id": 1,
                "title": "测试文章",
                "content": "这是一篇测试文章",
                "user_id": 2,
                "author": "testuser2",
                "location_id": 1,
                "popularity": 10,
                "evaluation": 4.5,
                "image_url": "/uploads/images/image123.jpg",
                "video_url": "/uploads/videos/video123.mp4",
                "created_at": "2023-05-01T12:00:00Z",
                "updated_at": "2023-05-01T12:00:00Z",
                "favorited_at": "2023-05-02T12:00:00Z"
            }
        ]
    }
}
```

#### 错误响应

- **用户不存在** (404 Not Found)

```json
{
    "code": 1,
    "message": "User not found",
    "data": null
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `GET`
4. 输入 URL: `http://localhost:5000/api/user/1/favorites` (替换 `1` 为实际的用户 ID)
5. 点击 Send 按钮发送请求

## 上传头像

### 接口信息

- **URL**: `/api/upload/avatar`
- **方法**: `POST`
- **描述**: 上传用户头像
- **请求体**: form-data

### 请求参数

| 参数名  | 类型   | 必填 | 描述                                          |
| ------- | ------ | ---- | --------------------------------------------- |
| file    | file   | 是   | 头像文件，支持 png, jpg, jpeg, gif, webp 格式 |
| user_id | string | 是   | 用户 ID                                       |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Avatar uploaded successfully",
    "data": {
        "url": "/uploads/avatars/avatar_1_abc123.jpg"
    }
}
```

#### 错误响应

- **文件缺失** (400 Bad Request)

```json
{
    "code": 1,
    "message": "No file part",
    "data": null
}
```

- **用户不存在** (404 Not Found)

```json
{
    "code": 1,
    "message": "User not found",
    "data": null
}
```

- **文件类型不支持** (400 Bad Request)

```json
{
    "code": 1,
    "message": "File type not allowed. Allowed types: png, jpg, jpeg, gif, webp",
    "data": null
}
```

- **文件过大** (400 Bad Request)

```json
{
    "code": 1,
    "message": "File too large. Maximum size: 50MB",
    "data": null
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `POST`
4. 输入 URL: `http://localhost:5000/api/upload/avatar`
5. 选择 Body 选项卡，选择 form-data
6. 添加键值对:
   - Key: `file`, Value: [选择文件], Type: File
   - Key: `user_id`, Value: `1` (替换为实际的用户 ID), Type: Text
7. 点击 Send 按钮发送请求

## 上传文章图片

### 接口信息

- **URL**: `/api/upload/image`
- **方法**: `POST`
- **描述**: 上传文章图片
- **请求体**: form-data

### 请求参数

| 参数名     | 类型   | 必填 | 描述                                          |
| ---------- | ------ | ---- | --------------------------------------------- |
| file       | file   | 是   | 图片文件，支持 png, jpg, jpeg, gif, webp 格式 |
| article_id | string | 否   | 文章 ID，如果提供，则会自动更新文章的图片 URL |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Image uploaded successfully",
    "data": {
        "url": "/uploads/images/image_abc123.jpg"
    }
}
```

#### 错误响应

- **文件缺失** (400 Bad Request)

```json
{
    "code": 1,
    "message": "No file part",
    "data": null
}
```

- **文章不存在** (404 Not Found)

```json
{
    "code": 1,
    "message": "Article not found",
    "data": null
}
```

- **文件类型不支持** (400 Bad Request)

```json
{
    "code": 1,
    "message": "File type not allowed. Allowed types: png, jpg, jpeg, gif, webp",
    "data": null
}
```

- **文件过大** (400 Bad Request)

```json
{
    "code": 1,
    "message": "File too large. Maximum size: 50MB",
    "data": null
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `POST`
4. 输入 URL: `http://localhost:5000/api/upload/image`
5. 选择 Body 选项卡，选择 form-data
6. 添加键值对:
   - Key: `file`, Value: [选择文件], Type: File
   - Key: `article_id`, Value: `1` (可选，替换为实际的文章 ID), Type: Text
7. 点击 Send 按钮发送请求

## 上传文章视频

### 接口信息

- **URL**: `/api/upload/video`
- **方法**: `POST`
- **描述**: 上传文章视频
- **请求体**: form-data

### 请求参数

| 参数名     | 类型   | 必填 | 描述                                          |
| ---------- | ------ | ---- | --------------------------------------------- |
| file       | file   | 是   | 视频文件，支持 mp4, webm, ogg, mov 格式       |
| article_id | string | 否   | 文章 ID，如果提供，则会自动更新文章的视频 URL |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Video uploaded successfully",
    "data": {
        "url": "/uploads/videos/video_abc123.mp4"
    }
}
```

#### 错误响应

- **文件缺失** (400 Bad Request)

```json
{
    "code": 1,
    "message": "No file part",
    "data": null
}
```

- **文章不存在** (404 Not Found)

```json
{
    "code": 1,
    "message": "Article not found",
    "data": null
}
```

- **文件类型不支持** (400 Bad Request)

```json
{
    "code": 1,
    "message": "File type not allowed. Allowed types: mp4, webm, ogg, mov",
    "data": null
}
```

- **文件过大** (400 Bad Request)

```json
{
    "code": 1,
    "message": "File too large. Maximum size: 50MB",
    "data": null
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `POST`
4. 输入 URL: `http://localhost:5000/api/upload/video`
5. 选择 Body 选项卡，选择 form-data
6. 添加键值对:
   - Key: `file`, Value: [选择文件], Type: File
   - Key: `article_id`, Value: `1` (可选，替换为实际的文章 ID), Type: Text
7. 点击 Send 按钮发送请求

## 完整的 Postman 测试流程

以下是一个完整的测试流程，按照顺序执行可以测试用户管理模块的所有功能：

### 1. 获取用户信息

```
GET http://localhost:5000/api/user/1
```

### 2. 上传头像

```
POST http://localhost:5000/api/upload/avatar
Content-Type: multipart/form-data

file: [选择文件]
user_id: 1
```

### 3. 更新用户头像

```
POST http://localhost:5000/api/user/avatar
Content-Type: multipart/form-data

user_id: 1
avatar: [选择文件]
```

### 4. 更新用户邮箱

```
PUT http://localhost:5000/api/user/email
Content-Type: application/json

{
    "user_id": 1,
    "new_email": "<EMAIL>",
    "password": "password123"
}
```

### 5. 更新用户密码

```
PUT http://localhost:5000/api/user/password
Content-Type: application/json

{
    "user_id": 1,
    "current_password": "password123",
    "new_password": "newpassword123",
    "confirm_password": "newpassword123"
}
```

### 6. 获取用户收藏的文章

```
GET http://localhost:5000/api/user/1/favorites
```

### 7. 上传文章图片

```
POST http://localhost:5000/api/upload/image
Content-Type: multipart/form-data

file: [选择文件]
article_id: 1
```

### 8. 上传文章视频

```
POST http://localhost:5000/api/upload/video
Content-Type: multipart/form-data

file: [选择文件]
article_id: 1
```