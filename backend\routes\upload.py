from flask import Blueprint, request, jsonify, current_app
import os
import uuid
from werkzeug.utils import secure_filename
from utils.response import success, error
from utils.database import db
from models.user import User
from models.article import Article
from flask import send_from_directory
upload_bp = Blueprint('upload', __name__)

# 允许的文件类型
ALLOWED_IMAGE_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
ALLOWED_VIDEO_EXTENSIONS = {'mp4', 'webm', 'ogg', 'mov'}

# 文件大小限制（50MB）
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB in bytes

def allowed_image(filename):
    """检查是否是允许的图片文件类型"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_IMAGE_EXTENSIONS

def allowed_video(filename):
    """检查是否是允许的视频文件类型"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_VIDEO_EXTENSIONS

def get_unique_filename(filename):
    """生成唯一的文件名"""
    ext = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''
    return f"{uuid.uuid4().hex}.{ext}"

@upload_bp.route('/avatar', methods=['POST'])
def upload_avatar():
    """上传用户头像"""
    try:
        if 'file' not in request.files:
            return error('No file part')

        file = request.files['file']

        if file.filename == '':
            return error('No selected file')

        # 检查是否指定了用户ID
        user_id = request.form.get('user_id')
        if not user_id:
            return error('Missing user_id parameter')

        # 检查用户是否存在
        user = User.query.get(user_id)
        if not user:
            return error('User not found')

        if not allowed_image(file.filename):
            return error('File type not allowed. Allowed types: ' + ', '.join(ALLOWED_IMAGE_EXTENSIONS))

        if request.content_length > MAX_FILE_SIZE:
            return error(f'File too large. Maximum size: {MAX_FILE_SIZE / (1024 * 1024)}MB')

        # 使用用户ID创建文件名
        filename = f"avatar_{user_id}_{uuid.uuid4().hex}.{file.filename.rsplit('.', 1)[1].lower()}"
        upload_folder = os.path.join(current_app.config['UPLOAD_FOLDER'], 'avatars')

        # 确保上传目录存在
        os.makedirs(upload_folder, exist_ok=True)

        file_path = os.path.join(upload_folder, filename)
        file.save(file_path)

        # 返回文件URL
        file_url = f"/uploads/avatars/{filename}"

        # 更新用户头像
        user.avatar = file_url
        db.session.commit()

        return success({'url': file_url}, 'Avatar uploaded successfully')
    except Exception as e:
        db.session.rollback()
        return error(str(e))

@upload_bp.route('/image', methods=['POST'])
def upload_image():
    """上传文章图片"""
    try:
        if 'file' not in request.files:
            return error('No file part')

        file = request.files['file']

        if file.filename == '':
            return error('No selected file')

        # 检查是否指定了文章ID（可选）
        article_id = request.form.get('article_id')

        if not allowed_image(file.filename):
            return error('File type not allowed. Allowed types: ' + ', '.join(ALLOWED_IMAGE_EXTENSIONS))

        if request.content_length > MAX_FILE_SIZE:
            return error(f'File too large. Maximum size: {MAX_FILE_SIZE / (1024 * 1024)}MB')

        # 生成文件名，如果有文章ID则包含在文件名中
        if article_id:
            filename = f"image_{article_id}_{uuid.uuid4().hex}.{file.filename.rsplit('.', 1)[1].lower()}"
        else:
            filename = f"image_{uuid.uuid4().hex}.{file.filename.rsplit('.', 1)[1].lower()}"

        upload_folder = os.path.join(current_app.config['UPLOAD_FOLDER'], 'images')

        # 确保上传目录存在
        os.makedirs(upload_folder, exist_ok=True)

        file_path = os.path.join(upload_folder, filename)
        file.save(file_path)

        # 返回文件URL
        file_url = f"/uploads/images/{filename}"

        # 如果指定了文章ID，更新文章的图片URL
        if article_id:
            article = Article.query.get(article_id)
            if article:
                # 检查是否指定了图片序号（1-6）
                image_index = request.form.get('image_index', '1')
                try:
                    image_index = int(image_index)
                    if image_index < 1 or image_index > 6:
                        image_index = 1  # 默认为第一张图片
                except ValueError:
                    image_index = 1  # 默认为第一张图片

                # 根据序号更新对应的图片URL
                if image_index == 1:
                    article.image_url = file_url
                elif image_index == 2:
                    article.image_url_2 = file_url
                elif image_index == 3:
                    article.image_url_3 = file_url
                elif image_index == 4:
                    article.image_url_4 = file_url
                elif image_index == 5:
                    article.image_url_5 = file_url
                elif image_index == 6:
                    article.image_url_6 = file_url

                db.session.commit()
            else:
                return error('Article not found')

        # 返回文件URL和图片序号（如果有）
        response_data = {'url': file_url}
        if article_id:
            response_data['article_id'] = article_id
            response_data['image_index'] = image_index
        return success(response_data, 'Image uploaded successfully')
    except Exception as e:
        db.session.rollback()
        return error(str(e))

@upload_bp.route('/video', methods=['POST'])
def upload_video():
    """上传文章视频"""
    try:
        if 'file' not in request.files:
            return error('No file part')

        file = request.files['file']

        if file.filename == '':
            return error('No selected file')

        # 检查是否指定了文章ID（可选）
        article_id = request.form.get('article_id')

        if not allowed_video(file.filename):
            return error('File type not allowed. Allowed types: ' + ', '.join(ALLOWED_VIDEO_EXTENSIONS))

        if request.content_length > MAX_FILE_SIZE:
            return error(f'File too large. Maximum size: {MAX_FILE_SIZE / (1024 * 1024)}MB')

        # 生成文件名，如果有文章ID则包含在文件名中
        if article_id:
            filename = f"video_{article_id}_{uuid.uuid4().hex}.{file.filename.rsplit('.', 1)[1].lower()}"
        else:
            filename = f"video_{uuid.uuid4().hex}.{file.filename.rsplit('.', 1)[1].lower()}"

        upload_folder = os.path.join(current_app.config['UPLOAD_FOLDER'], 'videos')

        # 确保上传目录存在
        os.makedirs(upload_folder, exist_ok=True)

        file_path = os.path.join(upload_folder, filename)
        file.save(file_path)

        # 返回文件URL
        file_url = f"/uploads/videos/{filename}"

        # 如果指定了文章ID，更新文章的视频URL
        if article_id:
            article = Article.query.get(article_id)
            if article:
                # 检查是否指定了视频序号（1-3）
                video_index = request.form.get('video_index', '1')
                try:
                    video_index = int(video_index)
                    if video_index < 1 or video_index > 3:
                        video_index = 1  # 默认为第一个视频
                except ValueError:
                    video_index = 1  # 默认为第一个视频

                # 根据序号更新对应的视频URL
                if video_index == 1:
                    article.video_url = file_url
                elif video_index == 2:
                    article.video_url_2 = file_url
                elif video_index == 3:
                    article.video_url_3 = file_url

                db.session.commit()
            else:
                return error('Article not found')

        # 返回文件URL和视频序号（如果有）
        response_data = {'url': file_url}
        if article_id:
            response_data['article_id'] = article_id
            response_data['video_index'] = video_index
        return success(response_data, 'Video uploaded successfully')
    except Exception as e:
        db.session.rollback()
        return error(str(e))

@upload_bp.route('/images', methods=['POST'])
def upload_images():
    """批量上传图片"""
    try:
        if 'file' not in request.files:
            return error('No file part')

        file = request.files['file']

        if file.filename == '':
            return error('No selected file')

        if not allowed_image(file.filename):
            return error('File type not allowed. Allowed types: ' + ', '.join(ALLOWED_IMAGE_EXTENSIONS))

        if request.content_length > MAX_FILE_SIZE:
            return error(f'File too large. Maximum size: {MAX_FILE_SIZE / (1024 * 1024)}MB')

        # 生成唯一文件名
        filename = f"aigc_image_{uuid.uuid4().hex}.{file.filename.rsplit('.', 1)[1].lower()}"
        upload_folder = os.path.join(current_app.config['UPLOAD_FOLDER'], 'images')

        # 确保上传目录存在
        os.makedirs(upload_folder, exist_ok=True)

        file_path = os.path.join(upload_folder, filename)
        file.save(file_path)

        # 返回文件URL
        file_url = f"/uploads/images/{filename}"

        return success({'url': file_url}, 'Image uploaded successfully')
    except Exception as e:
        return error(str(e))

@upload_bp.route('/videos', methods=['POST'])
def upload_videos():
    """批量上传视频"""
    try:
        if 'file' not in request.files:
            return error('No file part')

        file = request.files['file']

        if file.filename == '':
            return error('No selected file')

        if not allowed_video(file.filename):
            return error('File type not allowed. Allowed types: ' + ', '.join(ALLOWED_VIDEO_EXTENSIONS))

        # 视频文件大小限制更大（100MB）
        video_max_size = 100 * 1024 * 1024  # 100MB
        if request.content_length > video_max_size:
            return error(f'File too large. Maximum size: {video_max_size / (1024 * 1024)}MB')

        # 生成唯一文件名
        filename = f"aigc_video_{uuid.uuid4().hex}.{file.filename.rsplit('.', 1)[1].lower()}"
        upload_folder = os.path.join(current_app.config['UPLOAD_FOLDER'], 'videos')

        # 确保上传目录存在
        os.makedirs(upload_folder, exist_ok=True)

        file_path = os.path.join(upload_folder, filename)
        file.save(file_path)

        # 返回文件URL
        file_url = f"/uploads/videos/{filename}"

        return success({'url': file_url}, 'Video uploaded successfully')
    except Exception as e:
        return error(str(e))
