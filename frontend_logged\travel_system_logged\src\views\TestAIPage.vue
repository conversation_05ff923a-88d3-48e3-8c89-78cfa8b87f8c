<template>
  <div class="test-ai-page">
    <div class="container">
      <h1>🧪 AI功能测试页面</h1>
      <p>这是一个简化的AI功能测试页面，用于验证路由和基本功能是否正常。</p>
      
      <div class="test-sections">
        <!-- 路由测试 -->
        <div class="test-section">
          <h2>📍 路由测试</h2>
          <p>如果您能看到这个页面，说明路由功能正常。</p>
          <el-button type="primary" @click="goToAIGenerator">
            前往完整AI生成器
          </el-button>
        </div>

        <!-- 简单的AIGC测试 -->
        <div class="test-section">
          <h2>🎨 简单AIGC测试</h2>
          <el-form :model="testForm" label-width="120px">
            <el-form-item label="选择文章">
              <el-select v-model="testForm.articleId" placeholder="请选择文章">
                <el-option
                  v-for="article in articles"
                  :key="article.id"
                  :label="article.title"
                  :value="article.id"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="动画风格">
              <el-select v-model="testForm.style" placeholder="选择风格">
                <el-option label="温馨" value="温馨" />
                <el-option label="活泼" value="活泼" />
                <el-option label="文艺" value="文艺" />
              </el-select>
            </el-form-item>
            
            <el-form-item>
              <el-button 
                type="primary" 
                @click="testAIGC" 
                :loading="loading"
              >
                测试AIGC生成
              </el-button>
            </el-form-item>
          </el-form>
          
          <!-- 结果显示 -->
          <div v-if="result" class="result-section">
            <h3>生成结果：</h3>
            <pre>{{ JSON.stringify(result, null, 2) }}</pre>
          </div>
          
          <div v-if="error" class="error-section">
            <el-alert
              :title="error"
              type="error"
              :closable="false"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import axios from 'axios'
import { ElMessage } from 'element-plus'

export default {
  name: 'TestAIPage',
  setup() {
    const router = useRouter()
    const loading = ref(false)
    const result = ref(null)
    const error = ref('')
    const articles = ref([])
    
    const testForm = ref({
      articleId: null,
      style: '温馨'
    })

    // 获取文章列表
    const fetchArticles = async () => {
      try {
        const response = await axios.get('http://localhost:5000/api/articles')
        if (response.data.code === 0) {
          articles.value = response.data.data.articles || []
        }
      } catch (err) {
        console.error('获取文章列表失败:', err)
        // 添加一些测试数据
        articles.value = [
          { id: 1, title: '故宫游记' },
          { id: 2, title: '西湖之旅' },
          { id: 3, title: '长城探险' }
        ]
      }
    }

    // 测试AIGC功能
    const testAIGC = async () => {
      if (!testForm.value.articleId) {
        ElMessage.warning('请选择文章')
        return
      }

      loading.value = true
      error.value = ''
      result.value = null

      try {
        const response = await axios.post('http://localhost:5000/api/ai/generate_aigc_animation', {
          article_id: testForm.value.articleId,
          animation_style: testForm.value.style,
          duration: '中等',
          focus_elements: '风景,情感'
        })

        if (response.data.code === 0) {
          result.value = response.data.data
          ElMessage.success('AIGC生成成功！')
        } else {
          error.value = response.data.message || '生成失败'
          ElMessage.error(error.value)
        }
      } catch (err) {
        console.error('AIGC生成失败:', err)
        error.value = `请求失败: ${err.message}`
        ElMessage.error('请求失败，请检查后端服务是否启动')
      } finally {
        loading.value = false
      }
    }

    // 前往完整AI生成器
    const goToAIGenerator = () => {
      router.push('/ai-generator')
    }

    onMounted(() => {
      fetchArticles()
    })

    return {
      testForm,
      loading,
      result,
      error,
      articles,
      testAIGC,
      goToAIGenerator
    }
  }
}
</script>

<style scoped>
.test-ai-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40px 20px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

h1 {
  text-align: center;
  color: #333;
  margin-bottom: 20px;
}

.test-sections {
  display: grid;
  gap: 30px;
}

.test-section {
  background: #f8f9fa;
  border-radius: 15px;
  padding: 25px;
  border: 1px solid #e9ecef;
}

.test-section h2 {
  color: #495057;
  margin-bottom: 15px;
}

.result-section {
  margin-top: 20px;
  background: #e8f5e8;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #c3e6c3;
}

.result-section pre {
  background: white;
  padding: 10px;
  border-radius: 5px;
  overflow-x: auto;
  font-size: 12px;
}

.error-section {
  margin-top: 20px;
}
</style>
