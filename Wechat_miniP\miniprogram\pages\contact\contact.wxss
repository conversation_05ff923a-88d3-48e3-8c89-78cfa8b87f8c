/* contact.wxss */

.container {
  flex: 1;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
}

.contact-card {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.contact-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.contact-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  text-align: center;
}

.contact-item {
  display: flex;
  margin-bottom: 30rpx;
  align-items: center;
}

.contact-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(64, 158, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.contact-info {
  flex: 1;
}

.contact-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 6rpx;
}

.contact-value {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.feedback-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.feedback-input {
  width: 100%;
  height: 240rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  margin-bottom: 30rpx;
}

.submit-btn {
  background-color: #409EFF;
  color: white;
  font-size: 30rpx;
  padding: 20rpx 0;
  border-radius: 8rpx;
  width: 100%;
}
