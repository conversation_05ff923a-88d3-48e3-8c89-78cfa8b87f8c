/**
 * 餐馆相关API
 */

import request from './axios';

/**
 * 对餐馆进行评分
 * @param {Object} data - 评分数据
 * @param {number} data.user_id - 用户ID
 * @param {number} data.restaurant_id - 餐馆ID
 * @param {number} data.rating - 评分
 * @returns {Promise} - 返回评分结果的Promise
 */
export function rateRestaurant(data) {
  return request({
    url: '/api/restaurant/rating',
    method: 'post',
    data
  });
}

/**
 * 获取餐馆评分
 * @param {number} restaurantId - 餐馆ID
 * @param {number} [userId] - 用户ID（可选）
 * @returns {Promise} - 返回餐馆评分的Promise
 */
export function getRestaurantRating(restaurantId, userId) {
  return request({
    url: `/api/restaurant/rating/${restaurantId}`,
    method: 'get',
    params: userId ? { user_id: userId } : {}
  });
}

/**
 * 收藏餐馆
 * @param {Object} data - 收藏数据
 * @param {number} data.user_id - 用户ID
 * @param {number} data.restaurant_id - 餐馆ID
 * @returns {Promise} - 返回收藏结果的Promise
 */
export function favoriteRestaurant(data) {
  return request({
    url: '/api/restaurant/favorite',
    method: 'post',
    data
  });
}

/**
 * 取消收藏餐馆
 * @param {Object} data - 取消收藏数据
 * @param {number} data.user_id - 用户ID
 * @param {number} data.restaurant_id - 餐馆ID
 * @returns {Promise} - 返回取消收藏结果的Promise
 */
export function unfavoriteRestaurant(data) {
  return request({
    url: '/api/restaurant/unfavorite',
    method: 'post',
    data
  });
}

/**
 * 检查是否已收藏餐馆
 * @param {number} userId - 用户ID
 * @param {number} restaurantId - 餐馆ID
 * @returns {Promise} - 返回检查结果的Promise
 */
export function checkRestaurantFavorite(userId, restaurantId) {
  return request({
    url: '/api/restaurant/favorite/check',
    method: 'get',
    params: {
      user_id: userId,
      restaurant_id: restaurantId
    }
  });
}

/**
 * 获取用户收藏的餐馆列表
 * @param {number} userId - 用户ID
 * @returns {Promise} - 返回用户收藏餐馆列表的Promise
 */
export function getUserFavoriteRestaurants(userId) {
  return request({
    url: `/api/restaurant/favorite/user/${userId}`,
    method: 'get'
  });
}

/**
 * 评论餐馆
 * @param {Object} data - 评论数据
 * @param {number} data.user_id - 用户ID
 * @param {number} data.restaurant_id - 餐馆ID
 * @param {string} data.content - 评论内容
 * @param {number} data.rating - 评分
 * @returns {Promise} - 返回评论结果的Promise
 */
export function commentRestaurant(data) {
  return request({
    url: '/api/restaurant/comment',
    method: 'post',
    data
  });
}

/**
 * 获取餐馆评论列表
 * @param {number} restaurantId - 餐馆ID
 * @param {number} [limit=10] - 返回结果数量限制
 * @returns {Promise} - 返回餐馆评论列表的Promise
 */
export function getRestaurantComments(restaurantId, limit = 10) {
  return request({
    url: `/api/restaurant/comment/${restaurantId}`,
    method: 'get',
    params: { limit }
  });
}
