"""
游记推荐工具类

该模块实现了基于协同过滤的游记推荐算法
"""

from typing import List, Dict, <PERSON><PERSON>
import numpy as np
from models.article import Article, ArticleFavorite
from models.user import User
from utils.database import db

class DiaryRecommender:
    """游记推荐工具类"""
    
    @staticmethod
    def get_user_article_matrix(users: List[User], articles: List[Article]) -> Tuple[np.ndarray, Dict[int, int], Dict[int, int]]:
        """
        构建用户-游记交互矩阵
        
        Args:
            users: 用户列表
            articles: 游记列表
            
        Returns:
            用户-游记交互矩阵，用户ID到索引的映射，游记ID到索引的映射
        """
        # 创建用户ID和游记ID到矩阵索引的映射
        user_to_idx = {user.user_id: i for i, user in enumerate(users)}
        article_to_idx = {article.article_id: i for i, article in enumerate(articles)}
        
        # 初始化交互矩阵
        matrix = np.zeros((len(users), len(articles)))
        
        # 填充交互矩阵
        # 1. 收藏关系
        favorites = ArticleFavorite.query.filter(
            ArticleFavorite.user_id.in_([user.user_id for user in users]),
            ArticleFavorite.article_id.in_([article.article_id for article in articles])
        ).all()
        
        for favorite in favorites:
            user_idx = user_to_idx.get(favorite.user_id)
            article_idx = article_to_idx.get(favorite.article_id)
            if user_idx is not None and article_idx is not None:
                matrix[user_idx, article_idx] = 1.0
        
        # 2. 浏览历史（如果有）
        # 这里可以添加浏览历史的权重，例如0.5
        
        # 3. 评分行为（如果有）
        # 这里可以添加评分的权重，例如评分/5.0
        
        return matrix, user_to_idx, article_to_idx
    
    @staticmethod
    def compute_user_similarity(matrix: np.ndarray) -> np.ndarray:
        """
        计算用户之间的相似度
        
        Args:
            matrix: 用户-游记交互矩阵
            
        Returns:
            用户相似度矩阵
        """
        # 使用余弦相似度
        # 避免除以0
        norms = np.sqrt(np.sum(matrix**2, axis=1))
        norms[norms == 0] = 1e-10
        
        # 计算余弦相似度
        similarity = np.dot(matrix, matrix.T) / np.outer(norms, norms)
        
        return similarity
    
    @staticmethod
    def recommend_articles(user_id: int, top_n: int = 5) -> List[Tuple[Article, float]]:
        """
        为用户推荐游记
        
        Args:
            user_id: 用户ID
            top_n: 推荐数量
            
        Returns:
            推荐的游记列表及其推荐分数
        """
        # 获取所有用户和游记
        users = User.query.all()
        articles = Article.query.all()
        
        # 构建用户-游记交互矩阵
        matrix, user_to_idx, article_to_idx = DiaryRecommender.get_user_article_matrix(users, articles)
        
        # 计算用户相似度
        user_similarity = DiaryRecommender.compute_user_similarity(matrix)
        
        # 获取目标用户的索引
        user_idx = user_to_idx.get(user_id)
        if user_idx is None:
            return []
        
        # 获取目标用户的相似用户
        similar_users = [(i, user_similarity[user_idx, i]) for i in range(len(users)) if i != user_idx]
        similar_users.sort(key=lambda x: x[1], reverse=True)
        
        # 获取目标用户已交互的游记
        interacted_articles = set()
        for i, val in enumerate(matrix[user_idx]):
            if val > 0:
                article_id = next((aid for aid, idx in article_to_idx.items() if idx == i), None)
                if article_id:
                    interacted_articles.add(article_id)
        
        # 计算推荐分数
        article_scores = {}
        for user_idx, similarity in similar_users[:10]:  # 只考虑前10个最相似的用户
            if similarity <= 0:
                continue
                
            for i, val in enumerate(matrix[user_idx]):
                if val > 0:
                    article_id = next((aid for aid, idx in article_to_idx.items() if idx == i), None)
                    if article_id and article_id not in interacted_articles:
                        article_scores[article_id] = article_scores.get(article_id, 0) + similarity * val
        
        # 获取推荐游记
        recommended_articles = []
        for article_id, score in sorted(article_scores.items(), key=lambda x: x[1], reverse=True)[:top_n]:
            article = next((a for a in articles if a.article_id == article_id), None)
            if article:
                recommended_articles.append((article, score))
        
        return recommended_articles
