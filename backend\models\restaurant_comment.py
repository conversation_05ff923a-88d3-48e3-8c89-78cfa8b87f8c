"""
餐馆评论模型
"""
from utils.database import db
from datetime import datetime
from models.user import User


class RestaurantComment(db.Model):
    """餐馆评论模型"""
    __tablename__ = 'restaurant_comment'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    user_id = db.Column(db.Integer, db.Foreign<PERSON>ey('users.user_id'), nullable=False, index=True)
    restaurant_id = db.Column(db.Integer, db.ForeignKey('restaurant.id'), nullable=False, index=True)
    content = db.Column(db.Text, nullable=False)
    rating = db.Column(db.Float, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # 关联关系
    restaurant = db.relationship('Restaurant', backref=db.backref('comments', lazy='dynamic'))
    user = db.relationship('User', backref=db.backref('restaurant_comments', lazy='dynamic'))

    def to_dict(self):
        """转换为字典"""
        # 获取用户信息
        user = User.query.get(self.user_id)
        username = user.username if user else f'用户{self.user_id}'
        user_avatar = user.avatar if user and user.avatar else '/uploads/avatars/default.jpg'

        return {
            'id': self.id,
            'user_id': self.user_id,
            'restaurant_id': self.restaurant_id,
            'content': self.content,
            'rating': self.rating,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            'username': username,
            'user_avatar': user_avatar
        }
