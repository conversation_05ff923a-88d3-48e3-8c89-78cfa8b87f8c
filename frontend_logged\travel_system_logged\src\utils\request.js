import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建 axios 实例
const service = axios.create({
  baseURL: 'http://localhost:5000',  // 后端API地址
  timeout: 15000,  // 请求超时时间
  withCredentials: true  // 允许携带cookie
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    const token = localStorage.getItem('authToken'); // 从 localStorage 获取 token (确保 key 正确)
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`; // 添加 Bearer token
    }
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data
    
    if (res.code === 0 || res.status === 'success') {
      return res
    }
    
    ElMessage.error(res.message || '请求失败')
    return Promise.reject(new Error(res.message || '请求失败'))
  },
  error => {
    console.log("requests 响应错误拦截")
    console.error('响应错误:', error.response || error.message || error); // 打印更详细的错误

    // --- 修改：处理 HTTP 错误状态码 ---
    let message = '请求失败';
    if (error.response) {
        // 请求已发出，但服务器响应的状态码不在 2xx 范围
        console.error('错误响应数据:', error.response.data);
        console.error('错误响应状态:', error.response.status);
        console.error('错误响应头:', error.response.headers);
        if (error.response.status === 401) {
            message = '认证失败，请重新登录';
            // 这里可以添加跳转到登录页的逻辑
            // router.push('/login');
        } else if (error.response.data && error.response.data.message) {
            message = error.response.data.message; // 优先使用后端返回的错误信息
        } else if (error.response.data && error.response.data.error) {
            message = error.response.data.error; // 兼容可能的 'error' 字段
        } else {
            message = `请求错误 ${error.response.status}`;
        }
    } else if (error.request) {
        // 请求已发出，但没有收到响应
        console.error('无响应请求:', error.request);
        message = '服务器无响应，请检查网络或联系管理员';
    } else {
        // 在设置请求时触发了一个错误
        console.error('请求设置错误:', error.message);
        message = error.message || '请求发送失败';
    }
    ElMessage.error(message);
    return Promise.reject(new Error(message)); // 返回一个带消息的 Error 对象
    // --- 修改结束 ---
  }
)

export default service