# 项目根目录 .gitignore

# ===== 通用忽略规则 =====
# 系统文件
.DS_Store
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk

# 编辑器和IDE文件
.idea/
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
*.sublime-project
*.komodoproject

# 日志文件
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 临时文件
*.tmp
*.temp
.temp/
.tmp/
tmp/
temp/

# ===== 前端忽略规则 =====
# 依赖目录
**/node_modules/
**/dist/
**/.pnp
**/.pnp.js

# 构建输出
**/build/
**/coverage/

# 缓存目录
**/.npm
**/.eslintcache
**/.stylelintcache
**/.node_repl_history
**/.yarn-integrity
**/.cache
**/.parcel-cache
**/.next
**/.nuxt
**/.vuepress/dist
**/.serverless/
**/.fusebox/
**/.dynamodb/
**/.tern-port
**/.webpack/
**/.grunt
**/bower_components

# Vue CLI生成的文件
**/.vue-cli-service/

# ===== 后端忽略规则 =====
# Python虚拟环境
**/.venv/
**/venv/
**/ENV/
**/env/
**/env.bak/
**/venv.bak/
**/pythonenv*/

# Python缓存文件
**/__pycache__/
**/*.py[cod]
**/*$py.class
**/*.egg-info/
**/*.egg
**/.pytest_cache/
**/.coverage
**/htmlcov/
**/coverage.xml
**/*.cover
**/.mypy_cache/
**/.dmypy.json
**/dmypy.json

# Flask相关
**/instance/
**/backend/instance/
**/.webassets-cache/
**/migrations/


# 上传文件目录
**/uploads/
**/media/
**/static/user_uploads/

# 敏感配置文件
**/*.env
**/*.secret
**/.env.local
**/.env.*.local

# Celery
**/celerybeat-schedule
**/celerybeat.pid

# Jupyter Notebook
**/.ipynb_checkpoints

# ===== 项目特定忽略规则 =====


# 忽略文档（如果需要共享文档，请移除此行）
# /documents/

# 忽略非核心代码文件
**/test/
**/tests/
**/__tests__/
**/examples/
**/demo/
**/samples/
**/docs/
**/doc/

# 忽略配置文件
**/.babelrc
**/.browserslistrc
**/.editorconfig
**/.eslintrc*
**/.prettierrc*
**/.stylelintrc*
**/babel.config.js
**/postcss.config.js
**/vue.config.js
**/webpack.config.js
**/tsconfig.json
**/jsconfig.json
**/package-lock.json
**/yarn.lock
**/pnpm-lock.yaml

# 忽略本地开发配置
**/.env.development
**/.env.local
**/.env.development.local
**/.env.test.local
**/.env.production.local



# 忽略临时文件和备份文件
**/*.bak
**/*.swp
**/*.swo
**/*~
**/*.backup
**/*.old

# 忽略编译后的二进制文件
**/*.pyc
**/*.pyo
**/*.pyd
**/*.so
**/*.dll
**/*.exe

# 忽略日志和报告
**/reports/
**/report/
**/logs/
**/log/

# 前端项目需要提交src文件夹
# **/src/
# src/
# 但可以忽略特定的src文件夹
# backend/src/

**/Parallax-Scrolling-Website-master/
Parallax-Scrolling-Website-master/