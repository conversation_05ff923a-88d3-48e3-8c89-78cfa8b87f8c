# 小程序快速测试指南

## 🚀 立即测试

### 1. 打开简化测试页面
我已经创建了一个简化的测试页面，避免了复杂的模块导入问题：

**在微信开发者工具中**：
1. 重新编译项目
2. 小程序会自动打开 `simple-search` 页面（已设为首页）
3. 或者手动访问：`pages/simple-search/simple-search`

### 2. 测试步骤

#### 步骤1：测试网络连接
- 点击右上角的 **"测试连接"** 按钮
- 如果显示"连接成功"，说明网络正常
- 如果失败，检查后端服务和IP地址

#### 步骤2：查看热门景点
- 页面加载后应该自动显示热门景点
- 查看是否有景点列表显示
- 点击任意景点查看详细信息

#### 步骤3：搜索测试
- 在搜索框输入 **"故宫"**
- 点击 **"搜索"** 按钮
- 查看是否返回搜索结果

#### 步骤4：其他搜索测试
- 搜索 **"天安门"**
- 搜索 **"北京"**
- 搜索 **"大学"**

## 🔍 预期结果

### 正常情况下应该看到：
✅ **热门景点推荐**：显示10个热门景点，包含热度和评分  
✅ **故宫搜索**：能找到故宫相关景点  
✅ **天安门搜索**：能找到天安门相关景点  
✅ **详细信息**：点击景点显示热度、评分、描述等信息  

### 调试信息
页面底部显示：
- API地址：`http://**************:5000/api`
- 热门景点数量：应该 > 0
- 搜索结果数量：搜索后应该 > 0

## 🐛 如果出现问题

### 1. 网络连接失败
```
解决方案：
1. 确保后端服务运行：cd backend && python app.py
2. 检查IP地址是否正确：**************
3. 在微信开发者工具中关闭"域名校验"
```

### 2. 热门景点不显示
```
解决方案：
1. 查看控制台日志
2. 检查后端API：GET /api/path/popular-locations
3. 运行后端测试：python backend/test_location_api.py
```

### 3. 搜索无结果
```
解决方案：
1. 确认数据库中有景点数据
2. 检查后端API：GET /api/path/search-locations?name=故宫
3. 查看后端控制台日志
```

### 4. 模块导入错误（如果仍然出现）
```
解决方案：
1. 使用简化测试页面：simple-search
2. 避免使用复杂的TypeScript页面
3. 确保使用纯JavaScript实现
```

## 📱 控制台调试

### 查看日志
在微信开发者工具的控制台中查看：
```
正在加载热门景点...
请求URL: http://**************:5000/api/path/popular-locations?limit=10
热门景点响应: {statusCode: 200, data: [...]}
热门景点加载成功: [...]
```

### 网络请求
在"网络"标签中查看：
- 请求状态：200 OK
- 响应数据：包含景点数组
- 请求时间：< 1000ms

## 🔄 修复原始页面

如果简化页面工作正常，说明API和后端都没问题，可以修复原始的 `place-search` 页面：

### 方案1：使用纯JavaScript
将 `place-search.ts` 重命名为 `place-search.js` 并移除TypeScript语法

### 方案2：修复模块导入
确保所有依赖文件都正确编译为JavaScript

### 方案3：内嵌API代码
将API代码直接写在页面文件中（已实现）

## 📊 测试数据

### 预期的热门景点（示例）
- 故宫博物院
- 天安门广场  
- 颐和园
- 长城
- 天坛
- 北海公园
- 景山公园
- 雍和宫
- 圆明园
- 香山公园

### 搜索测试用例
| 关键词 | 预期结果 |
|--------|----------|
| 故宫 | 故宫博物院等相关景点 |
| 天安门 | 天安门广场等相关景点 |
| 北京 | 多个北京景点 |
| 大学 | 各大学校园 |
| 公园 | 各类公园景点 |

## ✅ 成功标志

当看到以下情况时，说明修复成功：
1. **页面正常加载**，无JavaScript错误
2. **热门景点显示**，数量 > 0
3. **搜索功能正常**，能找到故宫等景点
4. **网络请求成功**，状态码200
5. **数据格式正确**，包含热度、评分等字段

## 🎯 下一步

测试成功后：
1. 修复原始的 `place-search` 页面
2. 恢复正常的首页设置
3. 删除测试页面
4. 完善搜索功能和UI

---

**立即开始测试**：在微信开发者工具中重新编译项目，然后查看简化搜索页面！
