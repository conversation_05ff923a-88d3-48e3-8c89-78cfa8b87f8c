/**index.wxss**/

.scrollarea {
  flex: 1;
  overflow-y: auto;
  padding-bottom: calc(50px + env(safe-area-inset-bottom));
}

/* 轮播图样式 */
.carousel {
  height: 400rpx;
  width: 100%;
  margin: 20rpx;
  width: calc(100% - 40rpx);
}

.carousel-item {
  height: 100%;
  width: 100%;
  position: relative;
  overflow: hidden;
  border-radius: 16rpx;
}

.carousel-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.carousel-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  color: white;
}

.carousel-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.carousel-desc {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 功能区域样式 - 旧版本 */
.features {
  padding: 30rpx;
  background-color: white;
  margin: 20rpx 0;
  display: none; /* 隐藏旧版功能卡片 */
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
  position: relative;
}

.section-title::after {
  content: '';
  display: block;
  width: 80rpx;
  height: 6rpx;
  background: linear-gradient(90deg, #409EFF, #64B5F6);
  margin: 16rpx auto 30rpx;
  border-radius: 3rpx;
}

/* 新功能卡片样式 */
.features-new {
  padding: 30rpx;
  background-color: white;
  margin: 20rpx 0;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
  border-radius: 16rpx;
  padding: 20rpx 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.feature-item:active {
  transform: scale(0.95);
  background-color: #f0f0f0;
}

.feature-icon {
  font-size: 36rpx;
  line-height: 1;
  margin-bottom: 10rpx;
  background-color: rgba(64, 158, 255, 0.1);
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-text {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  text-align: center;
}

/* 保留旧版功能卡片样式，但不使用 */
.feature-cards {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding-bottom: 20rpx;
}

.feature-card {
  width: calc(33.33% - 20rpx);
  background-color: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 20rpx 10rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform 0.3s;
  box-sizing: border-box;
}

.feature-card:active {
  transform: scale(0.98);
}

.icon-container {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: rgba(64, 158, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10rpx;
}

.icon {
  font-size: 40rpx;
  color: #409EFF;
}

.feature-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 6rpx;
  text-align: center;
}

.feature-desc {
  font-size: 20rpx;
  color: #666;
  text-align: center;
  margin-bottom: 10rpx;
  height: 60rpx;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.feature-btn {
  font-size: 20rpx;
  padding: 6rpx 20rpx;
  background-color: #409EFF;
  color: white;
  border-radius: 30rpx;
  border: none;
  min-height: 0;
  line-height: 1.5;
}

/* 热门景点样式 */
.popular-places {
  padding: 30rpx;
  background-color: white;
}

.places-list {
  display: flex;
  flex-direction: column;
}

.place-item {
  margin-bottom: 30rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.place-image-container {
  height: 300rpx;
  position: relative;
  overflow: hidden;
}

.place-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.place-name {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}

.place-info {
  padding: 20rpx;
  background-color: white;
}

.place-rating {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.rating-text {
  color: #ff9800;
  font-weight: bold;
  margin-right: 10rpx;
}

.review-count {
  font-size: 24rpx;
  color: #999;
}

.place-tags {
  display: flex;
  flex-wrap: wrap;
}

.tag {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  background-color: #f0f0f0;
  color: #666;
  border-radius: 20rpx;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
}
