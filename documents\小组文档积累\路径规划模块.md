# 路径规划模块 API 文档

本文档详细描述了 Flask 版本后端的路径规划模块 API 接口及其在 Postman 中的测试用例。路径规划模块支持单目的地和多目的地路径规划，使用多种算法实现最优路径计算。

## 目录

1. [获取所有顶点](#获取所有顶点)
2. [获取所有边](#获取所有边)
3. [获取单个顶点信息](#获取单个顶点信息)
4. [批量获取顶点信息](#批量获取顶点信息)
5. [单目的地路径规划](#单目的地路径规划)
6. [多目的地路径规划](#多目的地路径规划)
7. [获取附近景点](#获取附近景点)
8. [路径规划算法](#路径规划算法)
9. [顶点类型说明](#顶点类型说明)
10. [路径规划策略说明](#路径规划策略说明)

## 获取所有顶点

### 接口信息

- **URL**: `/api/path/vertexes` 或 `/api/path/vertices`
- **方法**: `GET`
- **描述**: 获取所有顶点（地点）信息
- **请求参数**: 无

> **注意**：`/api/path/vertexes` 和 `/api/path/vertices` 是同一个接口的两种不同URL写法，功能完全相同。

### 响应

#### 成功响应 (200 OK)

```json
[
    {
        "vertex_id": 1,
        "x": 116.3,
        "y": 39.9,
        "label": "北京大学",
        "type": 0
    },
    {
        "vertex_id": 2,
        "x": 116.4,
        "y": 39.9,
        "label": "故宫博物院",
        "type": 1
    }
]
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `GET`
4. 输入 URL: `http://localhost:5000/api/path/vertexes`
5. 点击 Send 按钮发送请求

## 获取所有边

### 接口信息

- **URL**: `/api/path/edges`
- **方法**: `GET`
- **描述**: 获取所有边（路径）信息
- **请求参数**: 无

## 获取单个顶点信息

### 接口信息

- **URL**: `/api/path/vertex/<vertex_id>`
- **方法**: `GET`
- **描述**: 根据顶点ID获取顶点的详细信息，包括顶点基本属性、相邻顶点列表、出边和入边信息
- **请求参数**:
  - `vertex_id`: 顶点ID，作为URL路径的一部分

### 响应

#### 成功响应 (200 OK)

```json
{
    "vertex_id": 72,
    "label": "北京邮电大学",
    "x": 116358,
    "y": 39961,
    "type": 0,
    "connected_vertices": [73, 74, 75],
    "outgoing_edges": [
        {
            "edge_id": 150,
            "src_id": 72,
            "dest_id": 73,
            "weight": 100,
            "crowding": 1.0,
            "is_rideable": true
        },
        {
            "edge_id": 151,
            "src_id": 72,
            "dest_id": 74,
            "weight": 150,
            "crowding": 1.2,
            "is_rideable": true
        }
    ],
    "incoming_edges": [
        {
            "edge_id": 149,
            "src_id": 71,
            "dest_id": 72,
            "weight": 80,
            "crowding": 0.8,
            "is_rideable": true
        }
    ]
}
```

#### 错误响应

- **顶点不存在** (404 Not Found)

```json
{
    "error": "Vertex with ID 9999 not found"
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `GET`
4. 输入 URL: `http://localhost:5000/api/path/vertex/72`
5. 点击 Send 按钮发送请求

## 批量获取顶点信息

### 接口信息

- **URL**: `/api/path/vertices-by-ids`
- **方法**: `GET` 或 `POST`
- **描述**: 根据多个顶点ID批量获取顶点的详细信息
- **请求参数**:
  - GET方式: 使用查询参数 `ids` 传递逗号分隔的顶点ID列表
  - POST方式: 使用JSON请求体传递顶点ID数组

### 请求参数

#### GET 方式

| 参数名 | 类型   | 必填 | 描述                            |
| ------ | ------ | ---- | ------------------------------- |
| ids    | string | 是   | 逗号分隔的顶点ID列表，如"1,2,3" |

#### POST 方式

请求体示例:

```json
{
    "ids": [1, 2, 3, 72]
}
```

### 响应

#### 成功响应 (200 OK)

```json
{
    "1": {
        "vertex_id": 1,
        "label": "北京大学",
        "x": 116300,
        "y": 39900,
        "type": 0,
        "connected_vertices": [2, 5],
        "outgoing_edges": [...],
        "incoming_edges": [...]
    },
    "2": {
        "vertex_id": 2,
        "label": "中关村",
        "x": 116320,
        "y": 39950,
        "type": 3,
        "connected_vertices": [1, 3],
        "outgoing_edges": [...],
        "incoming_edges": [...]
    },
    "72": {
        "vertex_id": 72,
        "label": "北京邮电大学",
        "x": 116358,
        "y": 39961,
        "type": 0,
        "connected_vertices": [73, 74, 75],
        "outgoing_edges": [...],
        "incoming_edges": [...]
    }
}
```

#### 错误响应

- **参数缺失** (400 Bad Request)

```json
{
    "error": "No vertex IDs provided"
}
```

- **参数格式错误** (400 Bad Request)

```json
{
    "error": "Invalid vertex ID format in query parameter"
}
```

- **请求体格式错误** (400 Bad Request)

```json
{
    "error": "ids parameter must be an array"
}
```

### Postman 测试

#### GET 方式

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `GET`
4. 输入 URL: `http://localhost:5000/api/path/vertices-by-ids?ids=1,2,72,100`
5. 点击 Send 按钮发送请求

#### POST 方式

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `POST`
4. 输入 URL: `http://localhost:5000/api/path/vertices-by-ids`
5. 选择 Body 选项卡，选择 raw，设置类型为 JSON
6. 输入请求体:

```json
{
    "ids": [1, 2, 72, 100]
}
```

7. 点击 Send 按钮发送请求

### 响应

#### 成功响应 (200 OK)

```json
[
    {
        "edge_id": 1,
        "start_id": 1,
        "end_id": 2,
        "weight": 5.0,
        "congestion": 0.8,
        "is_bike_allowed": true
    },
    {
        "edge_id": 2,
        "start_id": 2,
        "end_id": 3,
        "weight": 3.0,
        "congestion": 0.5,
        "is_bike_allowed": false
    }
]
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `GET`
4. 输入 URL: `http://localhost:5000/api/path/edges`
5. 点击 Send 按钮发送请求

## 单目的地路径规划

### 接口信息

- **URL**: `/api/path/plan`
- **方法**: `POST`
- **描述**: 单目的地路径规划，使用Dijkstra算法计算最短路径

> **注意**：单目的地和多目的地路径规划共用同一个接口。系统会根据请求参数自动判断是单目的地还是多目的地路径规划。
- **请求体**: JSON

### 请求参数

| 参数名   | 类型    | 必填 | 描述                                                                         |
| -------- | ------- | ---- | ---------------------------------------------------------------------------- |
| start_id | integer | 是   | 起点 ID                                                                      |
| end_id   | integer | 是   | 终点 ID（也可以使用dest_id作为参数名）                                       |
| strategy | integer | 否   | 路径规划策略，0: 最短距离, 1: 最短时间（考虑拥挤度）, 2: 可骑行优先，默认为0 |

### 响应

#### 成功响应 (200 OK)

```json
{
    "path": [1, 2, 3, 5],
    "total_distance": 12.5,
    "total_time": 25.0,
    "vertexes": [
        {
            "vertex_id": 1,
            "label": "北京大学",
            "x": 116.3,
            "y": 39.9,
            "type": 0
        },
        {
            "vertex_id": 2,
            "label": "中关村",
            "x": 116.32,
            "y": 39.95,
            "type": 3
        },
        {
            "vertex_id": 3,
            "label": "颐和园",
            "x": 116.28,
            "y": 40.0,
            "type": 2
        },
        {
            "vertex_id": 5,
            "label": "天坛",
            "x": 116.4,
            "y": 39.88,
            "type": 1
        }
    ],
    "path_details": [
        {
            "from": "北京大学",
            "to": "中关村",
            "distance": 3.5,
            "crowding": 0.5,
            "is_rideable": true
        },
        {
            "from": "中关村",
            "to": "颐和园",
            "distance": 4.0,
            "crowding": 0.7,
            "is_rideable": true
        },
        {
            "from": "颐和园",
            "to": "天坛",
            "distance": 5.0,
            "crowding": 0.3,
            "is_rideable": false
        }
    ],
    "strategy": 0
}
```

#### 错误响应

- **数据缺失** (400 Bad Request)

```json
{
    "error": "No data provided"
}
```

- **参数缺失** (400 Bad Request)

```json
{
    "error": "Missing start_id parameter"
}
```

```json
{
    "error": "Missing destination parameter"
}
```

- **顶点不存在** (404 Not Found)

```json
{
    "error": "Vertex not found"
}
```

- **无法找到路径** (400 Bad Request)

```json
{
    "error": "No path found between the specified vertices"
}
```

### 实现细节

- 使用Dijkstra算法计算最短路径
- 根据策略参数调整边的权重计算方式：
  - 策略0：使用边的原始权重
  - 策略1：考虑拥挤度，权重 = 原始权重 * (1 + 拥挤度)
  - 策略2：考虑是否可骑行，不可骑行的边权重加倍
- 响应中包含完整的路径信息，包括顶点列表和路径详情
- 如果起点和终点相同，直接返回只包含该点的路径

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `POST`
4. 输入 URL: `http://localhost:5000/api/path/plan`
5. 选择 Body 选项卡，选择 raw，设置类型为 JSON
6. 输入请求体:

```json
{
    "start_id": 1,
    "end_id": 5,
    "strategy": 0
}
```

7. 点击 Send 按钮发送请求

## 多目的地路径规划

### 接口信息

- **URL**: `/api/path/plan`
- **方法**: `POST`
- **描述**: 多目的地路径规划（旅行商问题），使用Held-Karp或模拟退火算法
- **请求体**: JSON

> **注意**：`/api/path/plan_multi` 路由已被移除，请使用 `/api/path/plan` 接口。系统会根据请求参数自动判断是单目的地还是多目的地路径规划。

### 请求参数

| 参数名       | 类型    | 必填 | 描述                                                                                   |
| ------------ | ------- | ---- | -------------------------------------------------------------------------------------- |
| start_id     | integer | 是   | 起点 ID                                                                                |
| destinations | array   | 是   | 目的地 ID 数组（也可以使用dest_ids作为参数名）                                         |
| strategy     | integer | 否   | 路径规划策略，0: 最短距离, 1: 最短时间（考虑拥挤度）, 2: 可骑行优先，默认为0           |
| algorithm    | string  | 否   | 使用的算法，可选值: "auto", "held_karp", "simulated_annealing", "simple"，默认为"auto" |

### 响应

#### 成功响应 (200 OK)

```json
{
    "path": [1, 2, 5, 3, 1],
    "total_distance": 25.0,
    "path_details": [
        {
            "from": "北京大学",
            "to": "中关村",
            "distance": 3.5,
            "crowding": 0.5,
            "is_rideable": true
        },
        {
            "from": "中关村",
            "to": "天坛",
            "distance": 7.0,
            "crowding": 0.3,
            "is_rideable": true
        },
        {
            "from": "天坛",
            "to": "颐和园",
            "distance": 8.5,
            "crowding": 0.6,
            "is_rideable": false
        },
        {
            "from": "颐和园",
            "to": "北京大学",
            "distance": 6.0,
            "crowding": 0.4,
            "is_rideable": true
        }
    ],
    "vertexes": [
        {
            "vertex_id": 1,
            "label": "北京大学",
            "x": 116.3,
            "y": 39.9,
            "type": 0
        },
        {
            "vertex_id": 2,
            "label": "中关村",
            "x": 116.32,
            "y": 39.95,
            "type": 3
        },
        {
            "vertex_id": 5,
            "label": "天坛",
            "x": 116.4,
            "y": 39.88,
            "type": 1
        },
        {
            "vertex_id": 3,
            "label": "颐和园",
            "x": 116.28,
            "y": 40.0,
            "type": 2
        },
        {
            "vertex_id": 1,
            "label": "北京大学",
            "x": 116.3,
            "y": 39.9,
            "type": 0
        }
    ],
    "algorithm": "held_karp",
    "strategy": 0
}
```

#### 错误响应

- **数据缺失** (400 Bad Request)

```json
{
    "error": "No data provided"
}
```

- **参数缺失** (400 Bad Request)

```json
{
    "error": "Missing start_id parameter"
}
```

```json
{
    "error": "Missing destinations parameter"
}
```

- **格式错误** (400 Bad Request)

```json
{
    "error": "Invalid destinations format"
}
```

- **无目的地** (400 Bad Request)

```json
{
    "error": "No destinations specified"
}
```

### 实现细节

- 根据目的地数量和算法参数自动选择最合适的算法：
  - 目的地数量 ≤ 10 且 algorithm="auto"或"held_karp"：使用Held-Karp算法（精确解）
  - 目的地数量 > 10 且 algorithm="auto"或"simulated_annealing"：使用模拟退火算法（近似解）
  - algorithm="simple"：使用简单顺序路径（贪心解）
- 算法选择逻辑：
  - Held-Karp算法：适用于小规模问题（≤10个目的地），能找到精确的最优解
  - 模拟退火算法：适用于大规模问题（>10个目的地），能找到接近最优的解
  - 简单顺序路径：按照输入顺序访问所有目的地，计算速度最快但路径可能不是最优
- 响应中的path字段包含完整路径，包括起点、所有目的地和返回起点的路径
- 如果只有一个目的地，会自动调用单目的地路径规划
- 如果存在不可达的顶点，会使用一个非常大的值作为距离，确保算法能够继续运行

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `POST`
4. 输入 URL: `http://localhost:5000/api/path/plan`
5. 选择 Body 选项卡，选择 raw，设置类型为 JSON
6. 输入请求体:

```json
{
    "start_id": 1,
    "destinations": [2, 3, 5],
    "strategy": 0,
    "algorithm": "held_karp"
}
```

7. 点击 Send 按钮发送请求

## 获取附近景点

### 接口信息

- **URL**: `/api/path/spots`
- **方法**: `POST` 或 `GET`
- **描述**: 获取指定坐标附近的景点，使用欧几里得距离计算
- **请求体**: JSON（POST）或查询参数（GET）

### 请求参数

| 参数名   | 类型    | 必填 | 描述                                                              |
| -------- | ------- | ---- | ----------------------------------------------------------------- |
| x        | float   | 是   | 坐标 x（经度）                                                    |
| y        | float   | 是   | 坐标 y（纬度）                                                    |
| distance | float   | 是   | 搜索半径（单位：米）                                              |
| type     | integer | 否   | 景点类型，不提供则返回所有类型，参见[顶点类型说明](#顶点类型说明) |
| limit    | integer | 否   | 返回结果数量限制，默认不限制                                      |

### 响应

#### 成功响应 (200 OK)

```json
[
    {
        "vertex_id": 2,
        "label": "中关村",
        "x": 116.32,
        "y": 39.95,
        "type": 3,
        "distance": 120.5
    },
    {
        "vertex_id": 3,
        "label": "颐和园",
        "x": 116.28,
        "y": 40.0,
        "type": 2,
        "distance": 350.2
    }
]
```

#### 错误响应

- **参数缺失** (400 Bad Request)

```json
{
    "error": "Missing required parameters"
}
```

- **参数类型错误** (400 Bad Request)

```json
{
    "error": "Invalid parameter type"
}
```

### 实现细节

- 使用欧几里得距离公式计算点之间的距离：`distance = sqrt((x2 - x1)^2 + (y2 - y1)^2)`
- 返回的结果按距离升序排列，距离最近的景点排在前面
- 如果指定了type参数，只返回该类型的景点
- 如果指定了limit参数，最多返回指定数量的景点
- 每个返回的景点都包含到中心点的距离
- 支持POST和GET两种请求方式，方便不同场景使用

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `POST`
4. 输入 URL: `http://localhost:5000/api/path/spots`
5. 选择 Body 选项卡，选择 raw，设置类型为 JSON
6. 输入请求体:

```json
{
    "x": 116.3,
    "y": 39.9,
    "distance": 500,
    "type": 2,
    "limit": 5
}
```

7. 点击 Send 按钮发送请求

或者使用GET请求：

```
GET http://localhost:5000/api/path/spots?x=116.3&y=39.9&distance=500&type=2&limit=5
```

## 路径规划算法

路径规划模块使用以下算法：

1. **单目的地路径规划**：使用 Dijkstra 算法计算最短路径。
2. **多目的地路径规划**：使用近似算法解决旅行商问题（TSP）。
3. **附近景点搜索**：使用欧几里得距离计算点之间的距离。

### Dijkstra 算法

Dijkstra 算法是一种用于计算图中单源最短路径的算法。它的基本思想是：

1. 初始化起点到所有顶点的距离为无穷大，起点到自身的距离为0。
2. 将起点加入优先队列。
3. 从优先队列中取出距离最小的顶点，更新其邻居的距离。
4. 重复步骤3，直到队列为空或找到终点。

### 旅行商问题（TSP）近似算法

旅行商问题是一个NP难问题，我们使用近似算法来解决：

1. 从起点开始，每次选择距离当前点最近的未访问点。
2. 访问完所有点后，返回起点。

### 欧几里得距离

欧几里得距离是两点之间的直线距离，计算公式为：

```
distance = sqrt((x2 - x1)^2 + (y2 - y1)^2)
```

## 顶点类型说明

顶点类型是一个整数值，表示地点的分类：

| 类型值 | 描述                         |
| ------ | ---------------------------- |
| 0      | 教育类（如大学、学校）       |
| 1      | 文化类（如博物馆、历史遗迹） |
| 2      | 自然类（如公园、自然保护区） |
| 3      | 娱乐类（如商场、餐厅）       |
| 4      | 交通类（如机场、火车站）     |

## 路径规划策略说明

路径规划策略是一个整数值，表示路径规划的优化目标：

| 策略值 | 描述                                         |
| ------ | -------------------------------------------- |
| 0      | 最短距离：优先选择距离最短的路径             |
| 1      | 最短时间：考虑拥挤度，优先选择时间最短的路径 |
| 2      | 可骑行优先：优先选择可骑行的路径             |

## 完整的 Postman 测试流程

以下是一个完整的测试流程，按照顺序执行可以测试路径规划模块的所有功能：

### 1. 获取所有顶点

```
GET http://localhost:5000/api/path/vertexes
```

### 2. 获取所有边

```
GET http://localhost:5000/api/path/edges
```

### 3. 获取单个顶点信息

```
GET http://localhost:5000/api/path/vertex/72
```

### 4. 批量获取顶点信息 (GET方式)

```
GET http://localhost:5000/api/path/vertices-by-ids?ids=1,2,72,100
```

### 5. 批量获取顶点信息 (POST方式)

```
POST http://localhost:5000/api/path/vertices-by-ids
Content-Type: application/json

{
    "ids": [1, 2, 72, 100]
}
```

### 6. 单目的地路径规划

```
POST http://localhost:5000/api/path/plan
Content-Type: application/json

{
    "start_id": 1,
    "end_id": 5,
    "strategy": 0
}
```

### 7. 多目的地路径规划

```
POST http://localhost:5000/api/path/plan
Content-Type: application/json

{
    "start_id": 1,
    "destinations": [2, 3, 5],
    "strategy": 0
}
```

### 8. 获取附近景点

```
POST http://localhost:5000/api/path/spots
Content-Type: application/json

{
    "x": 116.3,
    "y": 39.9,
    "distance": 500,
    "type": 2
}
```

## 代码实现

路径规划模块的核心代码位于 `backend/routes/path.py` 文件中。以下是关键部分的代码实现：

### Dijkstra 算法实现

```python
def dijkstra(graph, start, end, strategy=0):
    """
    Dijkstra algorithm for finding shortest path

    Args:
        graph: Graph object
        start: Start vertex ID
        end: End vertex ID
        strategy: 0 for shortest distance, 1 for shortest time, 2 for bike-friendly

    Returns:
        path: List of vertex IDs
        distance: Total distance
        time: Total time
    """
    # Initialize distances
    distances = {vertex: float('infinity') for vertex in graph.vertices}
    distances[start] = 0

    # Initialize previous vertices
    previous = {vertex: None for vertex in graph.vertices}

    # Priority queue
    pq = [(0, start)]

    while pq:
        current_distance, current_vertex = heapq.heappop(pq)

        # If we reached the end
        if current_vertex == end:
            break

        # If we've already found a shorter path
        if current_distance > distances[current_vertex]:
            continue

        # Check all neighbors
        for neighbor, weight, congestion, is_bike_allowed in graph.get_neighbors(current_vertex):
            # Calculate edge weight based on strategy
            if strategy == 0:  # Shortest distance
                edge_weight = weight
            elif strategy == 1:  # Shortest time
                edge_weight = weight * (1 + congestion)
            elif strategy == 2:  # Bike-friendly
                edge_weight = weight * (2 if not is_bike_allowed else 1)

            # Calculate distance
            distance = current_distance + edge_weight

            # If we found a shorter path
            if distance < distances[neighbor]:
                distances[neighbor] = distance
                previous[neighbor] = current_vertex
                heapq.heappush(pq, (distance, neighbor))

    # Reconstruct path
    path = []
    current = end

    while current:
        path.append(current)
        current = previous[current]

    # Reverse path
    path.reverse()

    # Calculate total distance and time
    total_distance = 0
    total_time = 0

    for i in range(len(path) - 1):
        edge = graph.get_edge(path[i], path[i + 1])
        total_distance += edge[0]
        total_time += edge[0] * (1 + edge[1])

    return path, total_distance, total_time
```

## 注意事项

1. 路径规划结果包含完整的路径信息，包括起点、中间点和终点。
2. 多目的地路径规划会返回一个闭环路径，即最后会回到起点。
3. 获取附近景点时，距离单位为米，返回结果按距离升序排列。
4. 路径规划策略会影响路径的选择，不同策略可能会得到不同的路径。
5. 顶点类型可用于筛选特定类型的景点，如只获取自然类景点。
