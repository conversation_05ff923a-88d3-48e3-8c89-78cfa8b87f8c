/**
 * 搜索相关API
 */
import request from '@/utils/request'

/**
 * 模糊搜索文章
 * @param {string} query - 搜索关键词
 * @param {number} limit - 返回结果数量限制
 * @returns {Promise} - 返回搜索结果的Promise
 */
export function fuzzySearchArticles(query, limit = 10) {
  return request({
    url: '/api/articles/fuzzy_search',
    method: 'get',
    params: { query, limit }
  })
}

/**
 * 模糊搜索菜系
 * @param {string} query - 搜索关键词
 * @param {number} limit - 返回结果数量限制
 * @returns {Promise} - 返回搜索结果的Promise
 */
export function fuzzySearchCuisine(query, limit = 10) {
  return request({
    url: '/api/food/fuzzy_search_cuisine',
    method: 'get',
    params: { query, limit }
  })
}

/**
 * 模糊搜索餐厅
 * @param {string} query - 搜索关键词
 * @param {string} cuisine_type - 菜系类型
 * @param {number} limit - 返回结果数量限制
 * @returns {Promise} - 返回搜索结果的Promise
 */
export function fuzzySearchRestaurants(query, cuisine_type = '', limit = 10) {
  return request({
    url: '/api/food/fuzzy_search_restaurants',
    method: 'get',
    params: { query, cuisine_type, limit }
  })
}

// 默认导出对象，包含所有搜索API函数
const searchApi = {
  fuzzySearchArticles,
  fuzzySearchCuisine,
  fuzzySearchRestaurants
}

export default searchApi
