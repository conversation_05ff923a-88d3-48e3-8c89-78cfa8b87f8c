# AIGC增强功能部署指南

## 概述

本指南详细介绍了个性化旅游系统中新增的AIGC增强功能的部署、配置和使用方法。

## 🚀 新增功能特性

### 1. 算法优化升级
- **景点推荐排序**: 从Python内置排序升级为优化快速排序 + 堆排序Top-K
- **评分排序**: 从简单排序升级为优化归并排序 + 堆排序Top-K
- **旅游日记查找**: 从二分查找升级为插值查找 + Trie树 + 模糊查找
- **美食推荐查找**: 从线性查找升级为哈希查找 + KMP字符串匹配 + 多字段查找

### 2. AIGC增强功能
- **豆包AI集成**: 支持文生图、图生视频、文生视频三种模式
- **AI水印去除**: 使用计算机视觉技术自动去除生成内容的水印
- **背景音乐合成**: 支持多种音频格式，自动音视频同步
- **智能内容分析**: 情感色调分析、视觉元素提取、叙事结构分析

## 📋 系统要求

### 硬件要求
- **CPU**: 4核心以上，推荐8核心
- **内存**: 8GB以上，推荐16GB
- **存储**: 50GB可用空间（用于存储生成的媒体文件）
- **GPU**: 可选，用于加速AI处理（推荐NVIDIA GTX 1060以上）

### 软件要求
- **操作系统**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **Python**: 3.8+
- **Node.js**: 16.0+
- **FFmpeg**: 4.0+（用于音视频处理）
- **OpenCV**: 4.5+（用于图像处理）

## 🔧 安装和配置

### 1. 环境准备

#### 安装FFmpeg
```bash
# Windows (使用Chocolatey)
choco install ffmpeg

# macOS (使用Homebrew)
brew install ffmpeg

# Ubuntu
sudo apt update
sudo apt install ffmpeg
```

#### 安装Python依赖
```bash
cd backend
pip install -r requirements.txt

# 额外安装图像处理依赖
pip install opencv-python
pip install Pillow
pip install numpy
```

### 2. 目录结构创建

```bash
# 创建必要的目录
mkdir -p backend/uploads/music
mkdir -p backend/uploads/animations
mkdir -p backend/uploads/temp
```

### 3. 配置文件设置

#### 豆包API配置
编辑 `backend/services/enhanced_aigc_service.py`:
```python
self.doubao_config = {
    'api_key': 'your_actual_doubao_api_key_here',  # 替换为实际的API密钥
    'text_to_image_url': 'https://api.doubao.com/v1/text-to-image',
    'image_to_video_url': 'https://api.doubao.com/v1/image-to-video',
    'text_to_video_url': 'https://api.doubao.com/v1/text-to-video'
}
```

#### 环境变量设置
创建 `.env` 文件:
```env
# AIGC增强功能配置
DOUBAO_API_KEY=your_doubao_api_key
ENABLE_ENHANCED_AIGC=true
MAX_CONCURRENT_GENERATIONS=3
TEMP_FILE_CLEANUP_HOURS=24

# 文件存储配置
UPLOAD_MAX_SIZE=100MB
MUSIC_FOLDER=backend/uploads/music
ANIMATIONS_FOLDER=backend/uploads/animations
```

### 4. 背景音乐准备

#### 添加背景音乐文件
将音乐文件放入 `backend/uploads/music/` 目录，建议命名格式：
```
轻松愉快_城市漫步.mp3
壮观史诗_山川大海.mp3
温馨浪漫_夕阳西下.mp3
冒险刺激_户外探险.mp3
宁静舒缓_湖边小憩.mp3
```

#### 支持的音频格式
- MP3 (.mp3) - 推荐格式，兼容性最好
- WAV (.wav) - 无损格式，质量最高
- AAC (.aac) - 高质量压缩格式
- M4A (.m4a) - Apple格式，质量较好

#### 音乐文件要求
- **时长**: 建议2-5分钟（系统会自动根据视频时长智能截取或循环）
- **质量**: 比特率128kbps以上，采样率44.1kHz或48kHz
- **大小**: 单个文件建议不超过50MB
- **命名**: 使用UTF-8编码，避免特殊字符

#### 智能时长处理
系统会自动处理音乐与视频的时长匹配：
- **音乐较长**: 智能截取最佳片段（避开开头和结尾）
- **音乐较短**: 循环播放并在结尾淡出
- **时长相近**: 直接使用并添加淡入淡出效果

## 🚀 启动服务

### 1. 启动后端服务
```bash
cd backend
python app.py
```

### 2. 启动前端服务
```bash
cd frontend_logged/travel_system_logged
npm install
npm run serve
```

### 3. 验证服务状态
访问以下URL验证服务：
- 前端界面: http://localhost:8080
- 后端API: http://localhost:5000/api/health
- AIGC功能: http://localhost:5000/api/ai/get_available_music

## 🎯 功能使用指南

### 1. 基础AIGC动画生成

#### 步骤1: 选择文章
1. 登录系统
2. 进入"AI生成器"页面
3. 在"智能旅游动画生成器"中选择已有的旅游日记

#### 步骤2: 配置基础选项
- **动画风格**: 温馨、壮观、文艺、现代、古典
- **视频时长**: 短片(15秒)、中等(30秒)、长片(60秒)
- **重点元素**: 自然风景、建筑特色、人物活动、美食展示、文化元素、情感表达

#### 步骤3: 生成动画
点击"生成旅游动画"按钮，等待生成完成。

### 2. 增强AIGC功能使用

#### 步骤1: 展开增强选项
在基础配置下方，点击"🚀 AIGC增强选项"展开增强功能面板。

#### 步骤2: 配置增强功能

##### 豆包AI生成功能
- ☑️ **豆包文生图**: 使用豆包AI根据日记内容生成高质量图片
- ☑️ **豆包图生视频**: 将生成的图片转换为动态视频
- ☑️ **豆包文生视频**: 直接从文本生成视频内容

##### 背景音乐
- 从下拉菜单中选择合适的背景音乐
- 音乐会自动与视频合成

##### 后处理选项
- ☑️ **AI水印去除**: 自动检测并去除生成图片和视频中的AI水印

#### 步骤3: 生成增强动画
配置完成后，点击"生成旅游动画"按钮。系统会自动检测增强选项并调用相应的API。

### 3. 生成结果查看

#### 标准模式结果
- 视频播放器
- 生成的图片展示
- 基础元数据信息

#### 增强模式结果
- 高质量视频播放器
- 无水印图片展示
- 背景音乐集成
- 详细的生成元数据
- 生成方法说明

## 🔧 故障排除

### 常见问题

#### 1. 豆包API调用失败
**症状**: 提示"豆包API调用失败"
**解决方案**:
- 检查API密钥是否正确配置
- 验证网络连接是否正常
- 确认豆包API服务状态
- 查看后端日志获取详细错误信息

#### 2. 背景音乐无法加载
**症状**: 音乐下拉菜单为空
**解决方案**:
- 确认音乐文件已放入正确目录
- 检查文件格式是否支持
- 验证文件权限设置
- 重启后端服务

#### 3. 水印去除效果不佳
**症状**: 水印去除后图片质量下降
**解决方案**:
- 检查原图质量
- 调整水印检测参数
- 尝试不同的修复算法
- 手动指定水印区域

#### 4. 视频合成失败
**症状**: 提示"视频合成失败"
**解决方案**:
- 检查FFmpeg是否正确安装
- 验证临时文件权限
- 确认磁盘空间充足
- 查看FFmpeg错误日志

### 日志查看

#### 后端日志
```bash
# 查看应用日志
tail -f backend/logs/app.log

# 查看AIGC服务日志
tail -f backend/logs/aigc.log
```

#### 前端日志
打开浏览器开发者工具，查看Console面板的错误信息。

## 📊 性能优化

### 1. 服务器配置优化

#### 内存优化
```python
# 在enhanced_aigc_service.py中配置
MAX_CONCURRENT_GENERATIONS = 2  # 降低并发数
TEMP_FILE_CLEANUP_INTERVAL = 3600  # 定期清理临时文件
```

#### 存储优化
```bash
# 定期清理旧文件
find backend/uploads/animations -name "*.mp4" -mtime +7 -delete
find backend/uploads/temp -name "*" -mtime +1 -delete
```

### 2. 算法性能优化

#### 排序算法优化
- 小数据集自动使用Top-K堆排序
- 大数据集使用优化的快速排序
- 根据数据特征自动选择最优算法

#### 查找算法优化
- 精确查找优先使用哈希表
- 前缀搜索使用Trie树
- 模糊搜索使用Levenshtein距离

## 📈 监控和维护

### 1. 性能监控

#### 关键指标
- API响应时间
- 内存使用率
- 磁盘空间使用
- 并发用户数
- 生成成功率

#### 监控脚本
```bash
# 运行性能基准测试
cd backend/tests
python algorithm_benchmark.py

# 运行功能演示
cd backend/demo
python aigc_enhanced_demo.py
```

### 2. 定期维护

#### 每日维护
- 检查服务状态
- 清理临时文件
- 备份重要数据

#### 每周维护
- 更新依赖包
- 检查日志文件
- 性能基准测试

#### 每月维护
- 系统安全更新
- 数据库优化
- 功能使用统计分析

## 🔒 安全注意事项

### 1. API密钥安全
- 不要在代码中硬编码API密钥
- 使用环境变量存储敏感信息
- 定期轮换API密钥

### 2. 文件上传安全
- 限制上传文件类型和大小
- 扫描上传文件的恶意内容
- 使用安全的文件存储路径

### 3. 用户权限控制
- 实施适当的用户认证
- 限制AIGC功能的使用频率
- 记录用户操作日志

---

## 📞 技术支持

如果在部署或使用过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查系统日志获取详细错误信息
3. 运行测试脚本验证功能状态
4. 联系技术支持团队

**祝您使用愉快！** 🎉
