// diary-create.ts
interface TagOption {
  id: string;
  name: string;
  selected: boolean;
}

Component({
  data: {
    title: '',
    location: '',
    content: '',
    imageList: [] as string[],
    customTag: '',
    tagOptions: [
      { id: '1', name: '美食', selected: false },
      { id: '2', name: '风景', selected: false },
      { id: '3', name: '文化', selected: false },
      { id: '4', name: '历史', selected: false },
      { id: '5', name: '购物', selected: false },
      { id: '6', name: '娱乐', selected: false },
      { id: '7', name: '住宿', selected: false },
      { id: '8', name: '交通', selected: false }
    ] as TagOption[],
    userId: 1 // 默认用户ID
  },

  lifetimes: {
    attached() {
      // 获取当前用户ID
      this.getCurrentUserId();
    }
  },
  
  methods: {
    // 获取当前用户ID
    getCurrentUserId() {
      const userInfo = wx.getStorageSync('userInfo');
      const userId = wx.getStorageSync('userId');

      if (userInfo && userInfo.user_id) {
        this.setData({ userId: userInfo.user_id });
      } else if (userId) {
        this.setData({ userId: parseInt(userId) });
      } else {
        this.setData({ userId: 1 }); // 默认用户ID
      }
    },

    // 返回上一页
    goBack() {
      wx.navigateBack();
    },

    // 标题输入
    onTitleInput(e: any) {
      this.setData({
        title: e.detail.value
      });
    },
    
    // 内容输入
    onContentInput(e: any) {
      this.setData({
        content: e.detail.value
      });
    },
    
    // 地点输入
    onLocationInput(e: any) {
      this.setData({
        location: e.detail.value
      });
    },
    
    // 选择图片
    chooseImage() {
      const remainCount = 6 - this.data.imageList.length;
      wx.chooseMedia({
        count: remainCount,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFiles = res.tempFiles.map(file => file.tempFilePath);
          this.setData({
            imageList: [...this.data.imageList, ...tempFiles]
          });
        }
      });
    },
    
    // 预览图片
    previewImage(e: any) {
      const index = e.currentTarget.dataset.index;
      wx.previewImage({
        current: this.data.imageList[index],
        urls: this.data.imageList
      });
    },
    
    // 删除图片
    deleteImage(e: any) {
      const index = e.currentTarget.dataset.index;
      const imageList = [...this.data.imageList];
      imageList.splice(index, 1);
      this.setData({ imageList });
    },
    
    // 切换标签
    toggleTag(e: any) {
      const tagId = e.currentTarget.dataset.id;
      const tagOptions = this.data.tagOptions.map(tag => {
        if (tag.id === tagId) {
          return { ...tag, selected: !tag.selected };
        }
        return tag;
      });
      this.setData({ tagOptions });
    },
    
    // 自定义标签输入
    onCustomTagInput(e: any) {
      this.setData({
        customTag: e.detail.value
      });
    },
    
    // 添加自定义标签
    addCustomTag() {
      const customTag = this.data.customTag.trim();
      if (!customTag) {
        wx.showToast({
          title: '请输入标签内容',
          icon: 'none'
        });
        return;
      }
      
      // 检查是否已存在
      const exists = this.data.tagOptions.some(tag => tag.name === customTag);
      if (exists) {
        wx.showToast({
          title: '标签已存在',
          icon: 'none'
        });
        return;
      }
      
      const newTag: TagOption = {
        id: Date.now().toString(),
        name: customTag,
        selected: true
      };
      
      this.setData({
        tagOptions: [...this.data.tagOptions, newTag],
        customTag: ''
      });
    },
    
    // 保存草稿
    saveDraft() {
      wx.showToast({
        title: '草稿已保存',
        icon: 'success'
      });
    },
    
    // 发布日记
    async publishDiary() {
      // 验证必填字段
      if (!this.data.title.trim()) {
        wx.showToast({
          title: '请输入标题',
          icon: 'none'
        });
        return;
      }

      if (!this.data.content.trim()) {
        wx.showToast({
          title: '请输入内容',
          icon: 'none'
        });
        return;
      }

      if (!this.data.location.trim()) {
        wx.showToast({
          title: '请选择旅行地点',
          icon: 'none'
        });
        return;
      }
      
      wx.showLoading({
        title: '发布中...'
      });
      
      try {
        // 获取选中的标签
        const selectedTags = this.data.tagOptions
          .filter(tag => tag.selected)
          .map(tag => tag.name);
        
        // 构建请求数据
        const articleData = {
          user_id: this.data.userId,
          title: this.data.title,
          content: this.data.content,
          location: this.data.location,
          tags: selectedTags,
          image_url: this.data.imageList[0] || null,
          image_url_2: this.data.imageList[1] || null,
          image_url_3: this.data.imageList[2] || null,
          image_url_4: this.data.imageList[3] || null,
          image_url_5: this.data.imageList[4] || null,
          image_url_6: this.data.imageList[5] || null
        };
        
        const response = await this.createArticle(articleData);
        
        wx.hideLoading();
        
        // 修复响应格式判断：后端返回 code: 0 表示成功
        if (response.code === 0) {
          wx.showToast({
            title: '发布成功',
            icon: 'success'
          });

          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        } else {
          console.error('发布失败:', response);
          wx.showToast({
            title: response.message || '发布失败',
            icon: 'none'
          });
        }
      } catch (error) {
        wx.hideLoading();
        console.error('发布日记失败:', error);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    },
    
    // 创建文章
    createArticle(data: any) {
      return new Promise((resolve) => {
        wx.request({
          url: 'http://localhost:5000/api/articles',
          method: 'POST',
          data: data,
          header: {
            'Content-Type': 'application/json'
          },
          success: (res) => {
            resolve(res.data);
          },
          fail: (error) => {
            resolve({ code: 1, message: '网络错误', error });
          }
        });
      });
    }
  }
});
