"""
火山方舟豆包大模型服务
实现文生图功能
"""

import requests
import json
import time
import base64
from typing import List, Dict, Any
from config.ai_config import AIConfig


class DoubaoService:
    """火山方舟豆包大模型服务类"""

    def __init__(self):
        self.config = AIConfig.DOUBAO_CONFIG
        self.api_key = self.config['api_key']
        self.base_url = self.config['base_url']
        self.model = self.config['image_model']

    def generate_image(self, prompt: str, size: str = "1024x1024", style: str = "realistic", count: int = 1) -> List[Dict[str, Any]]:
        """
        生成图片

        Args:
            prompt: 图片描述
            size: 图片尺寸 (如 "1024x1024")
            style: 图片风格
            count: 生成数量

        Returns:
            生成的图片列表
        """
        try:
            # 解析尺寸
            width, height = map(int, size.split('x'))

            # 构建请求头
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }

            # 根据火山方舟API文档构建请求体
            request_data = {
                "model": self.model,
                "prompt": prompt,
                "n": count,
                "size": f"{width}x{height}",
                "response_format": "url",
                "style": style
            }

            print(f"发送到火山方舟API的请求: {request_data}")

            # 发送请求
            response = requests.post(
                f"{self.base_url}/images/generations",
                headers=headers,
                json=request_data,
                timeout=60
            )

            print(f"火山方舟API响应状态码: {response.status_code}")
            print(f"火山方舟API响应内容: {response.text}")

            if response.status_code == 200:
                result = response.json()

                # 解析响应
                images = []
                if 'data' in result:
                    for item in result['data']:
                        if 'url' in item:
                            images.append({
                                'url': item['url'],
                                'width': width,
                                'height': height,
                                'style': style,
                                'provider': 'doubao'
                            })
                        elif 'b64_json' in item:
                            # 如果返回的是base64编码的图片
                            images.append({
                                'url': f"data:image/png;base64,{item['b64_json']}",
                                'width': width,
                                'height': height,
                                'style': style,
                                'provider': 'doubao'
                            })

                return images
            else:
                print(f"火山方舟API调用失败: {response.status_code} - {response.text}")
                raise Exception(f"API调用失败: {response.status_code}")

        except Exception as e:
            print(f"火山方舟API调用异常: {str(e)}")
            raise e

    def enhance_prompt(self, prompt: str, style: str = "realistic") -> str:
        """
        增强提示词

        Args:
            prompt: 原始提示词
            style: 图片风格

        Returns:
            增强后的提示词
        """
        # 根据风格添加相应的提示词
        style_prompts = {
            "realistic": "photorealistic, high quality, detailed, 8k resolution",
            "artistic": "artistic, creative, beautiful composition, masterpiece",
            "cartoon": "cartoon style, colorful, cute, animated",
            "anime": "anime style, manga, japanese animation",
            "oil_painting": "oil painting style, classical art, brush strokes",
            "watercolor": "watercolor painting, soft colors, artistic"
        }

        style_suffix = style_prompts.get(style, style_prompts["realistic"])

        # 组合提示词
        enhanced_prompt = f"{prompt}, {style_suffix}"

        return enhanced_prompt

    def is_available(self) -> bool:
        """
        检查服务是否可用

        Returns:
            是否可用
        """
        return bool(self.api_key)

    def generate_text(self, prompt: str, max_tokens: int = 1000, temperature: float = 0.7) -> str:
        """
        生成文本内容

        Args:
            prompt: 提示词
            max_tokens: 最大token数
            temperature: 温度参数

        Returns:
            生成的文本
        """
        try:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            payload = {
                "model": AIConfig.DOUBAO_CONFIG['model'],
                "messages": [
                    {"role": "system", "content": "你是一个专业的旅游内容创作助手，擅长生成有趣、生动的旅游相关内容。"},
                    {"role": "user", "content": prompt}
                ],
                "max_tokens": max_tokens,
                "temperature": temperature,
                "stream": False
            }

            print(f"调用豆包API生成文本")

            response = requests.post(f"{self.base_url}/chat/completions", headers=headers, json=payload, timeout=30)

            if response.status_code == 200:
                result = response.json()
                content = result.get("choices", [{}])[0].get("message", {}).get("content", "").strip()
                print(f"豆包文本生成成功，长度: {len(content)}")
                return content
            else:
                print(f"豆包API错误: {response.status_code} - {response.text}")
                return f"生成失败: {response.status_code}"

        except Exception as e:
            print(f"豆包文本生成异常: {str(e)}")
            return f"生成异常: {str(e)}"

    def generate_travel_animation(self, article_data: Dict[str, Any], style: str, duration: str, focus_elements: str) -> Dict[str, Any]:
        """
        生成旅游动画

        Args:
            article_data: 文章数据
            style: 动画风格
            duration: 动画时长
            focus_elements: 重点元素

        Returns:
            动画生成结果
        """
        try:
            print(f"使用豆包服务生成旅游动画: {article_data['title']}")

            # 1. 生成扩写描述
            description_prompt = f"""
            请为以下旅游内容生成一段详细、生动的扩写描述：

            标题：{article_data['title']}
            地点：{article_data['location']}
            内容：{article_data['content']}
            风格：{style}
            重点：{focus_elements}

            请用{style}的风格，生成一段200-300字的扩写描述，让读者能够身临其境地感受这次旅行的美好。
            """

            enhanced_description = self.generate_text(description_prompt, max_tokens=500)

            # 2. 生成AI配图
            ai_images = []
            image_prompts = [
                f"{article_data['location']}的{style}风格风景照",
                f"{article_data['title']}主题的{style}氛围图",
                f"旅游{style}风格插画，展现{focus_elements}"
            ]

            for i, image_prompt in enumerate(image_prompts):
                try:
                    images = self.generate_image(image_prompt, style=style)
                    if images and len(images) > 0:
                        ai_images.append({
                            'url': images[0]['url'],
                            'prompt': image_prompt,
                            'scene_type': f'AI配图{i+1}',
                            'platform': 'doubao',
                            'id': f'doubao_image_{i+1}'
                        })
                        print(f"豆包配图{i+1}生成成功")
                    else:
                        print(f"豆包配图{i+1}生成失败")
                except Exception as img_error:
                    print(f"生成豆包配图{i+1}时出错: {str(img_error)}")
                    continue

            # 3. 生成视频（使用本地合成服务）
            try:
                from services.video_composition_service import VideoCompositionService
                video_service = VideoCompositionService()

                # 收集所有图片（用户上传的 + AI生成的）
                all_images = article_data.get('images', []) + ai_images

                if len(all_images) >= 3:
                    video_params = {
                        'title': article_data['title'],
                        'location': article_data['location'],
                        'content': article_data['content'],
                        'style': style,
                        'duration': duration,
                        'images': all_images[:6],  # 最多使用6张图片
                        'user_videos': article_data.get('videos', [])
                    }

                    video_result = video_service.create_slideshow_video(video_params)

                    if video_result and video_result.get('success'):
                        print(f"豆包视频生成成功: {video_result['video_url']}")

                        return {
                            'success': True,
                            'animation': {
                                'id': f"doubao_animation_{int(time.time())}",
                                'title': article_data['title'],
                                'status': 'completed',
                                'video_url': video_result['video_url'],
                                'metadata': {
                                    'total_duration': video_result.get('duration', 30),
                                    'composition_type': 'doubao_slideshow'
                                }
                            },
                            'ai_images': ai_images,
                            'user_media': {
                                'images': article_data.get('images', []),
                                'videos': article_data.get('videos', [])
                            },
                            'enhanced_description': enhanced_description,
                            'metadata': {
                                'generation_time': time.time(),
                                'style': style,
                                'duration': duration,
                                'ai_service': 'doubao',
                                'ai_images_count': len(ai_images)
                            }
                        }
                    else:
                        print(f"豆包视频生成失败: {video_result}")
                else:
                    print(f"图片数量不足，无法生成视频。当前图片数量: {len(all_images)}")

            except Exception as video_error:
                print(f"豆包视频生成时出错: {str(video_error)}")

            # 如果视频生成失败，返回基本的AI内容
            return {
                'success': True,
                'ai_images': ai_images,
                'user_media': {
                    'images': article_data.get('images', []),
                    'videos': article_data.get('videos', [])
                },
                'enhanced_description': enhanced_description,
                'metadata': {
                    'generation_time': time.time(),
                    'style': style,
                    'duration': duration,
                    'ai_service': 'doubao',
                    'ai_images_count': len(ai_images)
                }
            }

        except Exception as e:
            print(f"豆包旅游动画生成失败: {str(e)}")
            return {
                'success': False,
                'error': f'豆包服务生成失败: {str(e)}'
            }

    def test_connection(self) -> bool:
        """
        测试API连接

        Returns:
            连接是否成功
        """
        try:
            # 发送一个简单的测试请求
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }

            # 使用最小的请求进行测试
            test_data = {
                "model": self.model,
                "prompt": "test",
                "n": 1,
                "size": "256x256"
            }

            response = requests.post(
                f"{self.base_url}/images/generations",
                headers=headers,
                json=test_data,
                timeout=10
            )

            return response.status_code in [200, 400]  # 400也算连接成功，只是参数问题

        except Exception as e:
            print(f"火山方舟API连接测试失败: {str(e)}")
            return False


def create_doubao_service() -> DoubaoService:
    """
    创建火山方舟服务实例

    Returns:
        DoubaoService实例
    """
    return DoubaoService()
