from models.article_like import ArticleLike
from models.article import Article
from models.user import User
from utils.database import db
from sqlalchemy.exc import IntegrityError

class ArticleLikeService:
    """
    Service for handling article likes
    """
    
    def like_article(self, user_id, article_id):
        """
        Like an article
        
        Args:
            user_id (int): User ID
            article_id (int): Article ID
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Check if user exists
            user = User.query.get(user_id)
            if not user:
                return False, "User not found"
                
            # Check if article exists
            article = Article.query.get(article_id)
            if not article:
                return False, "Article not found"
                
            # Check if already liked
            existing_like = ArticleLike.query.filter_by(
                user_id=user_id,
                article_id=article_id
            ).first()
            
            if existing_like:
                return False, "Article already liked"
                
            # Create new like
            like = ArticleLike(user_id=user_id, article_id=article_id)
            db.session.add(like)
            db.session.commit()
            
            return True, "Article liked successfully"
            
        except IntegrityError:
            db.session.rollback()
            return False, "Database integrity error"
        except Exception as e:
            db.session.rollback()
            return False, str(e)
    
    def unlike_article(self, user_id, article_id):
        """
        Unlike an article
        
        Args:
            user_id (int): User ID
            article_id (int): Article ID
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Check if like exists
            like = ArticleLike.query.filter_by(
                user_id=user_id,
                article_id=article_id
            ).first()
            
            if not like:
                return False, "Article not liked"
                
            # Remove like
            db.session.delete(like)
            db.session.commit()
            
            return True, "Article unliked successfully"
            
        except Exception as e:
            db.session.rollback()
            return False, str(e)
    
    def check_like(self, user_id, article_id):
        """
        Check if user has liked an article
        
        Args:
            user_id (int): User ID
            article_id (int): Article ID
            
        Returns:
            bool: True if liked, False otherwise
        """
        like = ArticleLike.query.filter_by(
            user_id=user_id,
            article_id=article_id
        ).first()
        
        return like is not None
    
    def get_article_likes_count(self, article_id):
        """
        Get the number of likes for an article
        
        Args:
            article_id (int): Article ID
            
        Returns:
            int: Number of likes
        """
        return ArticleLike.query.filter_by(article_id=article_id).count()
    
    def get_user_liked_articles(self, user_id):
        """
        Get all articles liked by a user
        
        Args:
            user_id (int): User ID
            
        Returns:
            list: List of articles
        """
        likes = ArticleLike.query.filter_by(user_id=user_id).all()
        article_ids = [like.article_id for like in likes]
        
        articles = Article.query.filter(Article.article_id.in_(article_ids)).all()
        return articles
