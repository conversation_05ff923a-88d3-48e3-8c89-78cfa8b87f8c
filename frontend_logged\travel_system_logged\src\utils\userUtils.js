/**
 * 用户相关工具函数 - 统一用户状态管理
 */

/**
 * 检查用户是否已登录
 * @returns {boolean} 是否已登录
 */
export const isUserLoggedIn = () => {
  try {
    // 检查token
    const token = localStorage.getItem('token') || localStorage.getItem('authToken')
    if (!token) {
      return false
    }

    // 检查用户信息
    const userId = getCurrentUserId()
    if (!userId) {
      return false
    }

    console.log('用户登录状态检查通过:', { userId, hasToken: !!token })
    return true
  } catch (error) {
    console.error('检查登录状态时出错:', error)
    return false
  }
}

/**
 * 获取当前用户ID
 * 尝试从多个localStorage位置获取用户ID
 * @returns {string|null} 用户ID或null
 */
export const getCurrentUserId = () => {
  try {
    // 1. 尝试从 localStorage.userId 获取
    const userId = localStorage.getItem('userId')
    if (userId) {
      console.log('从localStorage.userId获取到用户ID:', userId)
      return userId
    }

    // 2. 尝试从 localStorage.currentUser 获取
    const currentUserStr = localStorage.getItem('currentUser')
    if (currentUserStr) {
      const userInfo = JSON.parse(currentUserStr)
      const id = userInfo.id || userInfo.user_id
      if (id) {
        console.log('从localStorage.currentUser获取到用户ID:', id)
        // 同步到userId
        localStorage.setItem('userId', id)
        return id
      }
    }

    // 3. 尝试从 localStorage.user 获取 (Vuex store)
    const userStr = localStorage.getItem('user')
    if (userStr) {
      const user = JSON.parse(userStr)
      const id = user.id || user.user_id
      if (id) {
        console.log('从localStorage.user获取到用户ID:', id)
        // 同步到userId
        localStorage.setItem('userId', id)
        return id
      }
    }

    console.warn('无法获取用户ID，所有方法都失败了')
  } catch (e) {
    console.error('解析用户信息失败:', e)
  }
  return null
}

/**
 * 获取用户token
 * @returns {string|null} token或null
 */
export const getUserToken = () => {
  return localStorage.getItem('token') || localStorage.getItem('authToken')
}

/**
 * 获取用户类型
 * @returns {string|null} 用户类型或null
 */
export const getUserType = () => {
  return localStorage.getItem('userType')
}

/**
 * 获取当前用户信息
 * @returns {object|null} 用户信息对象或null
 */
export const getCurrentUser = () => {
  try {
    // 1. 尝试从 localStorage.currentUser 获取
    const currentUserStr = localStorage.getItem('currentUser')
    if (currentUserStr) {
      const userInfo = JSON.parse(currentUserStr)
      if (userInfo && (userInfo.id || userInfo.user_id)) {
        console.log('从localStorage.currentUser获取到用户信息:', userInfo)
        return userInfo
      }
    }

    // 2. 尝试从 localStorage.user 获取 (Vuex store)
    const userStr = localStorage.getItem('user')
    if (userStr) {
      const user = JSON.parse(userStr)
      if (user && (user.id || user.user_id)) {
        console.log('从localStorage.user获取到用户信息:', user)
        return user
      }
    }

    console.warn('无法获取用户信息')
  } catch (e) {
    console.error('解析用户信息失败:', e)
  }
  return null
}



/**
 * 获取用户认证token
 * @returns {string|null} token或null
 */
export const getAuthToken = () => {
  return localStorage.getItem('authToken') || localStorage.getItem('token') || null
}

/**
 * 清除所有用户相关的localStorage数据
 */
export const clearUserData = () => {
  const keysToRemove = [
    'user',
    'token',
    'currentUser',
    'authToken',
    'userId',
    'userType',
    'refreshToken'
  ]

  keysToRemove.forEach(key => {
    localStorage.removeItem(key)
  })

  console.log('已清除所有用户相关数据')
}

/**
 * 保存用户信息到localStorage
 * @param {object} user 用户信息对象
 * @param {string} token 认证token
 */
export const saveUserData = (user, token) => {
  try {
    if (user) {
      // 确保用户对象有ID字段
      if (!user.user_id && user.id) {
        user.user_id = user.id
      }

      localStorage.setItem('currentUser', JSON.stringify(user))
      localStorage.setItem('userId', user.id || user.user_id)
    }

    if (token) {
      localStorage.setItem('authToken', token)
      localStorage.setItem('token', token)
    }

    console.log('用户数据已保存到localStorage')
  } catch (e) {
    console.error('保存用户数据失败:', e)
  }
}
