/* diary.wxss */
.page {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.nav-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #409EFF;
  color: white;
}

.nav-bar-title {
  font-size: 36rpx;
  font-weight: bold;
}

.nav-bar-actions {
  display: flex;
  gap: 20rpx;
}

.search-btn, .add-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

.search-btn .iconfont, .add-btn .iconfont {
  font-size: 32rpx;
}

/* 搜索容器 */
.search-container {
  background-color: white;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 50rpx;
  padding: 0 20rpx;
  margin-bottom: 20rpx;
}

.search-input {
  flex: 1;
  height: 70rpx;
  font-size: 28rpx;
  padding: 0 20rpx;
}

.search-action {
  color: #409EFF;
  font-size: 28rpx;
  padding: 10rpx 20rpx;
}

.filter-tabs {
  display: flex;
  gap: 20rpx;
}

.filter-tab {
  padding: 10rpx 20rpx;
  font-size: 26rpx;
  color: #666;
  border-radius: 30rpx;
  background-color: #f5f5f5;
}

.filter-tab.active {
  color: #409EFF;
  background-color: #e6f3ff;
}

/* 容器 */
.container {
  padding: 20rpx 30rpx;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 60rpx 0;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

/* 日记列表 */
.diary-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.diary-item {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.diary-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.diary-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  margin-right: 20rpx;
}

.diary-date {
  font-size: 24rpx;
  color: #999;
  white-space: nowrap;
}

.diary-content {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.diary-text {
  flex: 1;
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.diary-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 10rpx;
  overflow: hidden;
}

.diary-image image {
  width: 100%;
  height: 100%;
}

.diary-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.diary-location {
  display: flex;
  align-items: center;
  gap: 8rpx;
  color: #999;
  font-size: 24rpx;
}

.diary-location .iconfont {
  font-size: 24rpx;
}

.diary-stats {
  display: flex;
  gap: 30rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  color: #999;
  font-size: 24rpx;
}

.stat-item .iconfont {
  font-size: 24rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 0;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.empty-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.empty-action {
  display: inline-block;
  padding: 20rpx 40rpx;
  background-color: #409EFF;
  color: white;
  border-radius: 50rpx;
  font-size: 28rpx;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx 0;
}

.load-more-btn {
  display: inline-block;
  padding: 20rpx 40rpx;
  background-color: #f5f5f5;
  color: #666;
  border-radius: 50rpx;
  font-size: 28rpx;
}
