<!--place-detail.wxml-->
<view class="page">
  <!-- 顶部导航栏 -->
  <view class="nav-header">
    <view class="nav-left" bindtap="goBack">
      <text class="iconfont icon-arrow-left"></text>
    </view>
    <view class="nav-title">景点详情</view>
    <view class="nav-right" bindtap="toggleFavorite">
      <text class="iconfont {{isFavorited ? 'icon-heart-fill' : 'icon-heart'}} {{isFavorited ? 'favorited' : ''}}"></text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 景点详情 -->
  <view class="place-detail" wx:if="{{!loading && placeData}}">
    <!-- 景点图片 -->
    <view class="place-image-section">
      <image src="{{placeImageUrl}}" mode="aspectFill" class="place-image" bindtap="previewImage" />
      <view class="image-overlay">
        <view class="place-title">{{placeData.name}}</view>
        <view class="place-type">{{placeTypeText}}</view>
      </view>
    </view>

    <!-- 景点信息 -->
    <view class="place-info">
      <view class="info-header">
        <view class="place-name">{{placeData.name}}</view>
        <view class="place-rating">
          <text class="rating-icon">⭐</text>
          <text class="rating-value">{{placeData.evaluation}}</text>
          <text class="rating-count">({{placeData.popularity}} 人气)</text>
        </view>
      </view>

      <!-- 基本信息 -->
      <view class="basic-info">
        <view class="info-item" wx:if="{{placeData.address}}">
          <text class="info-icon">📍</text>
          <text class="info-label">地址</text>
          <text class="info-value">{{placeData.address}}</text>
        </view>
        <view class="info-item" wx:if="{{placeData.keyword}}">
          <text class="info-icon">🏷️</text>
          <text class="info-label">关键词</text>
          <text class="info-value">{{placeData.keyword}}</text>
        </view>
      </view>

      <!-- 景点描述 -->
      <view class="place-description" wx:if="{{placeData.description}}">
        <view class="section-header">
          <text class="section-icon">📖</text>
          <text class="section-title">景点介绍</text>
        </view>
        <view class="description-content">
          <text class="description-text">{{placeData.description}}</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <view class="action-btn primary" bindtap="navigateToPlace">
          <text class="btn-icon">🧭</text>
          <text class="btn-text">导航</text>
        </view>
        <view class="action-btn secondary" bindtap="sharePlace">
          <text class="btn-icon">📤</text>
          <text class="btn-text">分享</text>
        </view>
        <view class="action-btn {{isFavorited ? 'favorited' : ''}}" bindtap="toggleFavorite">
          <text class="btn-icon">{{isFavorited ? '❤️' : '🤍'}}</text>
          <text class="btn-text">{{isFavorited ? '已收藏' : '收藏'}}</text>
        </view>
      </view>
    </view>

    <!-- 相关景点推荐 -->
    <view class="related-places" wx:if="{{relatedPlaces.length > 0}}">
      <view class="section-header">
        <text class="section-icon">🎯</text>
        <text class="section-title">附近景点</text>
      </view>
      <scroll-view class="related-list" scroll-x="true">
        <view class="related-item" wx:for="{{relatedPlaces}}" wx:key="location_id" bindtap="onRelatedPlaceTap" data-id="{{item.location_id}}">
          <image src="{{getLocationImageUrl(item.image_url)}}" mode="aspectFill" class="related-image" />
          <view class="related-info">
            <view class="related-name">{{item.name}}</view>
            <view class="related-rating">
              <text class="rating-icon">⭐</text>
              <text>{{item.evaluation}}</text>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 底部安全区域 -->
    <view class="safe-area"></view>
  </view>

  <!-- 错误状态 -->
  <view class="error-state" wx:if="{{!loading && !placeData}}">
    <view class="error-container">
      <view class="error-icon">🏛️</view>
      <view class="error-title">景点不存在</view>
      <view class="error-desc">该景点可能已被删除或不存在</view>
      <view class="error-action" bindtap="goBack">
        <text class="iconfont icon-arrow-left"></text>
        <text>返回</text>
      </view>
    </view>
  </view>
</view>
