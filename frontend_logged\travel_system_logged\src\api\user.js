import request from '@/utils/request'
 
/**
 * 更新用户头像
 * @param {FormData} formData 包含user_id和avatar文件
 * @returns {Promise}
 */
export function updateAvatar(formData) {
  // const formData = new FormData()
  // formData.append('user_id', data.user_id)
  // formData.append('avatar', data.avatar)

  return request({
    url: '/api/user/avatar',
    method: 'POST',
    data: formData
  })
}

/**
 * 更新用户邮箱
 * @param {Object} data 包含user_id、new_email和password
 * @returns {Promise}
 */
export function updateEmail(data) {
  return request({
    url: '/api/user/email',
    method: 'PUT',
    data: {
      user_id: data.user_id,
      new_email: data.new_email,
      password: data.password
    }
  })
}

/**
 * 更新用户名
 * @param {Object} data 包含user_id、new_username和password
 * @returns {Promise}
 */
export function updateUsername(data) {
  return request({
    url: '/api/user/username',
    method: 'PUT',
    data: {
      user_id: data.user_id,
      new_username: data.new_username,
      password: data.password
    }
  })
}

/**
 * 更新用户密码
 * @param {Object} data 包含user_id、current_password、new_password和confirm_password
 * @returns {Promise}
 */
export function updatePassword(data) {
  return request({
    url: '/api/user/password',
    method: 'PUT',
    data: {
      user_id: data.user_id,
      current_password: data.current_password,
      new_password: data.new_password,
      confirm_password: data.confirm_password
    }
  })
}

/**
 * 获取用户收藏的文章
 * @param {number} userId 用户ID
 * @returns {Promise}
 */
export function getUserFavorites(userId) {
  return request({
    url: `/api/user/${userId}/favorites`,
    method: 'GET'
  })
}