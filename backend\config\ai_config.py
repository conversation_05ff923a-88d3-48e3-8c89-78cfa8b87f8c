"""
AI服务配置文件
包含各种AI服务的API配置
"""

import os
from typing import Dict, Any

class AIConfig:
    """AI服务配置类"""

    # 火山方舟API配置
    DOUBAO_CONFIG = {
        'api_key': os.getenv('DOUBAO_API_KEY', '4e139657-8476-44d5-aacb-e2b21b6c1761'),
        'base_url': 'https://ark.cn-beijing.volces.com/api/v3',
        'model': 'doubao-pro-32k',
        'image_model': 'doubao-seedream-3-0-t2i-250415'  # 正确的图片生成模型
    }

    # 腾讯混元API配置
    HUNYUAN_CONFIG = {
        'api_url': 'https://hunyuan.tencentcloudapi.com/',
        'secret_id': os.getenv('TENCENT_SECRET_ID', ''),
        'secret_key': os.getenv('TENCENT_SECRET_KEY', ''),
        'region': 'ap-beijing',
        'service': 'hunyuan',
        'version': '2023-09-01',
        'action': 'TextToImageLite'
    }

    # OpenAI API配置（备用方案）
    OPENAI_CONFIG = {
        'api_key': os.getenv('OPENAI_API_KEY', ''),
        'api_url': 'https://api.openai.com/v1/images/generations',
        'model': 'dall-e-3'
    }

    # 百度文心一格API配置（备用方案）
    WENXIN_CONFIG = {
        'api_key': os.getenv('WENXIN_API_KEY', ''),
        'secret_key': os.getenv('WENXIN_SECRET_KEY', ''),
        'api_url': 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/text2image/sd_xl'
    }

    @classmethod
    def is_doubao_configured(cls) -> bool:
        """检查火山方舟API是否已配置"""
        return bool(cls.DOUBAO_CONFIG['api_key'])

    @classmethod
    def is_hunyuan_configured(cls) -> bool:
        """检查腾讯混元API是否已配置"""
        config = cls.HUNYUAN_CONFIG
        return bool(config['secret_id'] and config['secret_key'])

    @classmethod
    def is_openai_configured(cls) -> bool:
        """检查OpenAI API是否已配置"""
        return bool(cls.OPENAI_CONFIG['api_key'])

    @classmethod
    def is_wenxin_configured(cls) -> bool:
        """检查百度文心一格API是否已配置"""
        config = cls.WENXIN_CONFIG
        return bool(config['api_key'] and config['secret_key'])

    @classmethod
    def get_available_services(cls) -> list:
        """获取可用的AI服务列表"""
        services = []
        if cls.is_doubao_configured():
            services.append('doubao')
        if cls.is_hunyuan_configured():
            services.append('hunyuan')
        if cls.is_openai_configured():
            services.append('openai')
        if cls.is_wenxin_configured():
            services.append('wenxin')
        return services

# 使用说明
"""
要启用AI图片生成功能，请按以下步骤配置：

1. 火山方舟API配置（推荐）：
   - 在火山方舟控制台申请API密钥
   - 设置环境变量：
     export DOUBAO_API_KEY="your_api_key"
   - 或直接在代码中使用提供的密钥：4e139657-8476-44d5-aacb-e2b21b6c1761

2. 腾讯混元API配置（备用）：
   - 在腾讯云控制台申请混元API密钥
   - 设置环境变量：
     export TENCENT_SECRET_ID="your_secret_id"
     export TENCENT_SECRET_KEY="your_secret_key"

3. OpenAI API配置（备用）：
   - 在OpenAI官网申请API密钥
   - 设置环境变量：
     export OPENAI_API_KEY="your_api_key"

4. 百度文心一格API配置（备用）：
   - 在百度AI开放平台申请API密钥
   - 设置环境变量：
     export WENXIN_API_KEY="your_api_key"
     export WENXIN_SECRET_KEY="your_secret_key"

5. 重启后端服务使配置生效

注意：
- 如果没有配置任何真实API，系统将使用模拟图片服务
- 建议至少配置一个真实的AI图片生成服务
- 各个服务都有使用限制和费用，请查看相应的文档
- 火山方舟API已配置密钥，可直接使用
"""
