# 推荐模块 API 文档

本文档详细描述了 Flask 版本后端的推荐模块 API 接口及其在 Postman 中的测试用例。

> **重要更新**：
> 1. 所有推荐模块的路由已经重构为使用基于数据结构和算法的高效实现。原有的 API 接口路径保持不变，以确保向后兼容性。详细的高级推荐模块 API 请参考 [高级推荐模块文档](./高级推荐模块.md)。

## 目录

1. [基于内容的推荐](#基于内容的推荐)
2. [基于协同过滤的推荐](#基于协同过滤的推荐)
3. [基于热度的推荐](#基于热度的推荐)
4. [基于评分的推荐](#基于评分的推荐)
5. [混合推荐](#混合推荐)
6. [获取相似用户](#获取相似用户)

## 基于内容的推荐

### 接口信息

- **URL**: `/api/recommend/content`
- **方法**: `POST`
- **描述**: 基于用户浏览历史和地点内容特征进行推荐
- **请求体**: JSON

> **注意**：此接口已被重定向到 `/api/advanced-recommend/content`，建议直接使用后者以获得更好的性能和更准确的推荐结果。

### 请求参数

| 参数名  | 类型    | 必填 | 描述                        |
| ------- | ------- | ---- | --------------------------- |
| user_id | integer | 是   | 用户 ID                     |
| limit   | integer | 否   | 返回结果数量限制，默认为 10 |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Content-based recommendations retrieved successfully",
    "data": {
        "recommendations": [
            {
                "location_id": 5,
                "name": "天坛",
                "type": 1,
                "keyword": "文化,历史,公园",
                "popularity": 120,
                "evaluation": 4.7,
                "content_score": 0.85
            },
            {
                "location_id": 8,
                "name": "颐和园",
                "type": 2,
                "keyword": "文化,历史,公园,湖泊",
                "popularity": 150,
                "evaluation": 4.8,
                "content_score": 0.78
            }
        ],
        "elapsed_time": "0.03 seconds"
    }
}
```

#### 错误响应

- **用户不存在** (404 Not Found)

```json
{
    "code": 1,
    "message": "User not found",
    "data": null
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `POST`
4. 输入 URL: `http://localhost:5000/api/recommend/content`
5. 选择 Body 选项卡，选择 raw，设置类型为 JSON
6. 输入请求体:

```json
{
    "user_id": 1,
    "limit": 5
}
```

7. 点击 Send 按钮发送请求

## 基于协同过滤的推荐

### 接口信息

- **URL**: `/api/recommend/collaborative`
- **方法**: `POST`
- **描述**: 基于用户相似度和协同过滤算法进行推荐
- **请求体**: JSON

> **注意**：此接口已被重定向到 `/api/advanced-recommend/collaborative`，建议直接使用后者以获得更好的性能和更准确的推荐结果。

### 请求参数

| 参数名  | 类型    | 必填 | 描述                        |
| ------- | ------- | ---- | --------------------------- |
| user_id | integer | 是   | 用户 ID                     |
| limit   | integer | 否   | 返回结果数量限制，默认为 10 |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Collaborative recommendations retrieved successfully",
    "data": {
        "recommendations": [
            {
                "location_id": 12,
                "name": "北海公园",
                "type": 2,
                "keyword": "公园,湖泊,历史",
                "popularity": 110,
                "evaluation": 4.6,
                "collaborative_score": 0.92
            },
            {
                "location_id": 7,
                "name": "国家博物馆",
                "type": 1,
                "keyword": "文化,历史,博物馆",
                "popularity": 100,
                "evaluation": 4.5,
                "collaborative_score": 0.85
            }
        ],
        "elapsed_time": "0.05 seconds"
    }
}
```

#### 错误响应

- **用户不存在** (404 Not Found)

```json
{
    "code": 1,
    "message": "User not found",
    "data": null
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `POST`
4. 输入 URL: `http://localhost:5000/api/recommend/collaborative`
5. 选择 Body 选项卡，选择 raw，设置类型为 JSON
6. 输入请求体:

```json
{
    "user_id": 1,
    "limit": 5
}
```

7. 点击 Send 按钮发送请求

## 基于热度的推荐

### 接口信息

- **URL**: `/api/recommend/popularity`
- **方法**: `GET`
- **描述**: 基于地点热度（浏览量和评分）进行推荐
- **请求参数**: 查询字符串

> **注意**：此接口已被重定向到 `/api/advanced-recommend/popularity`，建议直接使用后者以获得更好的性能和更准确的推荐结果。

### 请求参数

| 参数名 | 类型    | 必填 | 描述                               |
| ------ | ------- | ---- | ---------------------------------- |
| limit  | integer | 否   | 返回结果数量限制，默认为 10        |
| type   | integer | 否   | 地点类型筛选，不提供则返回所有类型 |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Popular locations retrieved successfully",
    "data": {
        "recommendations": [
            {
                "location_id": 3,
                "name": "故宫博物院",
                "type": 1,
                "keyword": "文化,历史,博物馆",
                "popularity": 200,
                "evaluation": 4.9,
                "popularity_score": 0.95
            },
            {
                "location_id": 8,
                "name": "颐和园",
                "type": 2,
                "keyword": "文化,历史,公园,湖泊",
                "popularity": 150,
                "evaluation": 4.8,
                "popularity_score": 0.85
            }
        ],
        "elapsed_time": "0.02 seconds"
    }
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `GET`
4. 输入 URL: `http://localhost:5000/api/recommend/popularity?limit=5&type=1`
5. 点击 Send 按钮发送请求

## 基于评分的推荐

### 接口信息

- **URL**: `/api/recommend/rating`
- **方法**: `GET`
- **描述**: 基于地点评分进行推荐
- **请求参数**: 查询字符串

> **注意**：此接口已被重定向到 `/api/advanced-recommend/rating`，建议直接使用后者以获得更好的性能和更准确的推荐结果。

### 请求参数

| 参数名 | 类型    | 必填 | 描述                               |
| ------ | ------- | ---- | ---------------------------------- |
| limit  | integer | 否   | 返回结果数量限制，默认为 10        |
| type   | integer | 否   | 地点类型筛选，不提供则返回所有类型 |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Top rated locations retrieved successfully",
    "data": {
        "recommendations": [
            {
                "location_id": 3,
                "name": "故宫博物院",
                "type": 1,
                "keyword": "文化,历史,博物馆",
                "popularity": 200,
                "evaluation": 4.9,
                "rating_score": 4.9
            },
            {
                "location_id": 8,
                "name": "颐和园",
                "type": 2,
                "keyword": "文化,历史,公园,湖泊",
                "popularity": 150,
                "evaluation": 4.8,
                "rating_score": 4.8
            }
        ],
        "elapsed_time": "0.02 seconds"
    }
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `GET`
4. 输入 URL: `http://localhost:5000/api/recommend/rating?limit=5&type=1`
5. 点击 Send 按钮发送请求

## 混合推荐

### 接口信息

- **URL**: `/api/recommend/hybrid`
- **方法**: `POST`
- **描述**: 结合内容推荐、协同过滤和热度推荐的混合推荐
- **请求体**: JSON

> **注意**：此接口已被重定向到 `/api/advanced-recommend/hybrid`，建议直接使用后者以获得更好的性能和更准确的推荐结果。

### 请求参数

| 参数名  | 类型    | 必填 | 描述                           |
| ------- | ------- | ---- | ------------------------------ |
| user_id | integer | 是   | 用户 ID                        |
| limit   | integer | 否   | 返回结果数量限制，默认为 10    |
| weights | object  | 否   | 各推荐算法的权重，默认均为 1.0 |

### 权重对象

| 参数名        | 类型  | 必填 | 描述                         |
| ------------- | ----- | ---- | ---------------------------- |
| content       | float | 否   | 内容推荐权重，默认为 1.0     |
| collaborative | float | 否   | 协同过滤推荐权重，默认为 1.0 |
| popularity    | float | 否   | 热度推荐权重，默认为 0.5     |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Hybrid recommendations retrieved successfully",
    "data": {
        "recommendations": [
            {
                "location_id": 3,
                "name": "故宫博物院",
                "type": 1,
                "keyword": "文化,历史,博物馆",
                "popularity": 200,
                "evaluation": 4.9,
                "score": 0.95
            },
            {
                "location_id": 8,
                "name": "颐和园",
                "type": 2,
                "keyword": "文化,历史,公园,湖泊",
                "popularity": 150,
                "evaluation": 4.8,
                "score": 0.92
            }
        ],
        "elapsed_time": "0.08 seconds"
    }
}
```

#### 错误响应

- **用户不存在** (404 Not Found)

```json
{
    "code": 1,
    "message": "User not found",
    "data": null
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `POST`
4. 输入 URL: `http://localhost:5000/api/recommend/hybrid`
5. 选择 Body 选项卡，选择 raw，设置类型为 JSON
6. 输入请求体:

```json
{
    "user_id": 1,
    "limit": 5,
    "weights": {
        "content": 1.2,
        "collaborative": 1.0,
        "popularity": 0.8
    }
}
```

7. 点击 Send 按钮发送请求

## 获取相似用户

### 接口信息

- **URL**: `/api/recommend/similar_users`
- **方法**: `POST`
- **描述**: 获取与指定用户相似的用户列表
- **请求体**: JSON

> **注意**：此接口已被重定向到 `/api/advanced-recommend/similar_users`，建议直接使用后者以获得更好的性能和更准确的结果。

### 请求参数

| 参数名  | 类型    | 必填 | 描述                        |
| ------- | ------- | ---- | --------------------------- |
| user_id | integer | 是   | 用户 ID                     |
| limit   | integer | 否   | 返回结果数量限制，默认为 10 |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Similar users retrieved successfully",
    "data": {
        "similar_users": [
            {
                "user_id": 5,
                "username": "user5",
                "similarity": 0.85
            },
            {
                "user_id": 12,
                "username": "user12",
                "similarity": 0.78
            }
        ],
        "elapsed_time": "0.04 seconds"
    }
}
```

#### 错误响应

- **用户不存在** (404 Not Found)

```json
{
    "code": 1,
    "message": "User not found",
    "data": null
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `POST`
4. 输入 URL: `http://localhost:5000/api/recommend/similar_users`
5. 选择 Body 选项卡，选择 raw，设置类型为 JSON
6. 输入请求体:

```json
{
    "user_id": 1,
    "limit": 5
}
```

7. 点击 Send 按钮发送请求

## 推荐算法说明

> **注意**：推荐模块现在使用高级推荐系统的算法实现，详细的算法说明请参考 [高级推荐模块文档](./高级推荐模块.md#高级推荐算法说明)。

推荐模块使用以下算法：

### 1. 基于内容的推荐

基于内容的推荐算法通过分析用户浏览过的地点的特征（如类型、关键词等），找出与这些地点相似的其他地点进行推荐。算法步骤：

1. 获取用户浏览历史中的地点
2. 提取这些地点的特征（类型、关键词）
3. 计算其他地点与用户浏览过的地点的相似度
4. 使用堆排序获取相似度最高的地点

### 2. 基于协同过滤的推荐

基于协同过滤的推荐算法通过分析用户之间的相似性，找出与当前用户相似的其他用户，然后推荐这些相似用户喜欢但当前用户尚未浏览过的地点。算法步骤：

1. 使用余弦相似度计算用户之间的相似度（考虑浏览次数等强度信息）
2. 找出与当前用户最相似的 N 个用户
3. 获取这些相似用户浏览过但当前用户尚未浏览过的地点
4. 计算这些地点的推荐分数
5. 使用堆排序获取分数最高的地点

### 3. 基于热度的推荐

基于热度的推荐算法根据地点的热度（浏览量和评分）进行推荐。算法步骤：

1. 计算地点的热度分数（结合浏览量和评分）
2. 使用堆排序获取热度最高的地点

### 4. 混合推荐

混合推荐算法结合了上述三种算法的结果，通过加权平均的方式计算最终的推荐分数。算法步骤：

1. 分别获取基于内容、协同过滤和热度的推荐结果
2. 归一化各个推荐方法的分数
3. 根据用户指定的权重计算每个地点的加权平均分数
4. 使用堆排序获取分数最高的地点

## 完整的 Postman 测试流程

以下是一个完整的测试流程，按照顺序执行可以测试推荐模块的所有功能：

### 1. 刷新缓存（推荐先执行）

```
POST http://localhost:5000/api/advanced-recommend/refresh-cache
Content-Type: application/json

{
    "force": true
}
```

### 2. 基于内容的推荐

```
POST http://localhost:5000/api/recommend/content
Content-Type: application/json

{
    "user_id": 1,
    "limit": 5
}
```

### 3. 基于协同过滤的推荐

```
POST http://localhost:5000/api/recommend/collaborative
Content-Type: application/json

{
    "user_id": 1,
    "limit": 5
}
```

### 4. 基于热度的推荐

```
GET http://localhost:5000/api/recommend/popularity?limit=5&type=1
```

### 5. 基于评分的推荐

```
GET http://localhost:5000/api/recommend/rating?limit=5&type=1
```

### 6. 混合推荐

```
POST http://localhost:5000/api/recommend/hybrid
Content-Type: application/json

{
    "user_id": 1,
    "limit": 5,
    "weights": {
        "content": 1.0,
        "collaborative": 1.0,
        "popularity": 0.5
    }
}
```

### 7. 获取相似用户

```
POST http://localhost:5000/api/recommend/similar_users
Content-Type: application/json

{
    "user_id": 1,
    "limit": 5
}
```

### 8. 性能比较

```
POST http://localhost:5000/api/advanced-recommend/performance-comparison
Content-Type: application/json

{
    "user_id": 1
}
```

## 注意事项

1. 推荐算法的效果依赖于系统中的数据量，数据越丰富，推荐效果越好。
2. 对于新用户（冷启动问题），系统会优先使用基于热度的推荐。
3. 混合推荐可以通过调整权重来优化推荐效果，适应不同的场景需求。
4. 推荐结果会排除用户已经浏览过的地点，避免重复推荐。
5. 推荐算法会在内存中缓存用户相似度矩阵和地点相似度矩阵，以提高推荐效率。
6. 在应用启动时或数据发生重大变化时，应调用 `/api/advanced-recommend/refresh-cache` 接口刷新缓存。
7. 所有原始的推荐接口 (`/api/recommend/*`) 现在都重定向到高级推荐接口 (`/api/advanced-recommend/*`)，但为了向后兼容性，两者都可以使用。
8. 高级推荐接口提供了更详细的性能信息，包括响应时间。