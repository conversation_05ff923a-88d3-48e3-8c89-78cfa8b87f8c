<template>
  <div class="article-summary-generator">
    <div class="generator-header">
      <h2>智能文章摘要生成</h2>
      <p>为旅游文章自动生成简洁准确的摘要，提取关键信息</p>
    </div>

    <div class="form-section">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
        <!-- 文章选择 -->
        <el-form-item label="选择文章" prop="articleId" required>
          <el-select
            v-model="form.articleId"
            filterable
            remote
            reserve-keyword
            placeholder="请输入文章标题或地点名称进行搜索"
            :remote-method="searchArticles"
            :loading="searchLoading"
            style="width: 100%"
            @change="handleArticleChange"
          >
            <el-option
              v-for="article in articleOptions"
              :key="article.article_id"
              :label="article.title"
              :value="article.article_id"
            >
              <div style="display: flex; justify-content: space-between;">
                <span>{{ article.title }}</span>
                <span style="color: #8492a6; font-size: 13px">
                  {{ article.location }}
                </span>
              </div>
            </el-option>
          </el-select>
          <div class="form-tip">
            支持输入文章标题或地点名称进行模糊搜索
          </div>
        </el-form-item>

        <!-- 摘要长度 -->
        <el-form-item label="摘要长度" prop="maxLength">
          <el-slider
            v-model="form.maxLength"
            :min="50"
            :max="500"
            :step="50"
            show-stops
            show-input
            style="width: 100%"
          />
          <div class="length-hint">
            <span>简短 (50字)</span>
            <span>适中 (200字)</span>
            <span>详细 (500字)</span>
          </div>
        </el-form-item>

        <!-- 生成按钮 -->
        <el-form-item>
          <el-button
            type="primary"
            @click="generateSummary"
            :loading="loading"
            size="large"
            style="width: 200px"
          >
            <i class="el-icon-document"></i>
            {{ loading ? '生成中...' : '生成摘要' }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 文章预览 -->
    <div v-if="selectedArticle" class="article-preview">
      <h3>文章预览</h3>
      <div class="preview-content">
        <div class="article-info">
          <h4>{{ selectedArticle.title }}</h4>
          <div class="article-meta">
            <span><i class="el-icon-location"></i> {{ selectedArticle.location }}</span>
            <span><i class="el-icon-view"></i> {{ selectedArticle.popularity }} 次浏览</span>
          </div>
        </div>
        <div class="article-content">
          {{ selectedArticle.content ? selectedArticle.content.substring(0, 300) + '...' : '暂无内容预览' }}
        </div>
      </div>
    </div>

    <!-- 生成结果 -->
    <div v-if="generatedSummary" class="result-section">
      <div class="result-header">
        <h3>
          <i class="el-icon-document"></i>
          智能生成摘要
        </h3>
        <div class="result-actions">
          <el-button type="success" @click="copyToClipboard" size="small">
            <i class="el-icon-document-copy"></i>
            复制摘要
          </el-button>
          <el-button type="primary" @click="regenerateSummary" size="small">
            <i class="el-icon-refresh"></i>
            重新生成
          </el-button>
        </div>
      </div>

      <!-- 文章信息卡片 -->
      <div v-if="selectedArticle" class="article-info-card">
        <div class="article-header">
          <div class="article-avatar">
            <i class="el-icon-document"></i>
          </div>
          <div class="article-details">
            <h4>{{ selectedArticle.title }}</h4>
            <div class="article-meta">
              <el-tag size="small" type="info">
                <i class="el-icon-location"></i>
                {{ selectedArticle.location }}
              </el-tag>
              <span class="article-popularity">{{ selectedArticle.popularity }} 次浏览</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 摘要内容 -->
      <div class="summary-main">
        <div class="summary-header">
          <h4>
            <i class="el-icon-edit-outline"></i>
            AI生成摘要
          </h4>
          <div class="summary-badges">
            <el-tag size="small" type="info">{{ form.maxLength }}字以内</el-tag>
            <el-tag size="small" type="success">压缩比 {{ compressionRatio }}%</el-tag>
          </div>
        </div>

        <div class="summary-text">
          <div class="text-content">
            <MarkdownRenderer :content="generatedSummary" />
          </div>
          <div class="text-decoration">
            <div class="quote-mark">"</div>
          </div>
        </div>

        <div class="summary-stats">
          <div class="stat-item">
            <i class="el-icon-document"></i>
            <span>{{ generatedSummary.length }} 字</span>
          </div>
          <div class="stat-item">
            <i class="el-icon-data-analysis"></i>
            <span>压缩比 {{ compressionRatio }}%</span>
          </div>
          <div class="stat-item">
            <i class="el-icon-star-on"></i>
            <span>智能摘要</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="error-section">
      <el-alert
        :title="error"
        type="error"
        :closable="false"
        show-icon
      />
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import aiApi from '@/api/ai'
import articlesApi from '@/api/articles'
import MarkdownRenderer from '@/components/common/MarkdownRenderer.vue'
import searchApi from '@/api/search'

export default {
  name: 'ArticleSummaryGenerator',
  components: {
    MarkdownRenderer
  },
  emits: ['generated'],
  setup(props, { emit }) {
    const formRef = ref(null)
    const loading = ref(false)
    const searchLoading = ref(false)
    const error = ref('')
    const generatedSummary = ref('')
    const articleOptions = ref([])
    const selectedArticle = ref(null)

    const form = reactive({
      articleId: null,
      maxLength: 200
    })

    const rules = {
      articleId: [
        { required: true, message: '请选择一篇文章', trigger: 'change' }
      ]
    }

    // 计算压缩比
    const compressionRatio = computed(() => {
      if (!selectedArticle.value || !generatedSummary.value) return 0
      const originalLength = selectedArticle.value.content?.length || 0
      const summaryLength = generatedSummary.value.length
      if (originalLength === 0) return 0
      return Math.round((summaryLength / originalLength) * 100)
    })

    // 搜索文章
    const searchArticles = async (query) => {
      console.log('搜索文章:', query)

      searchLoading.value = true
      try {
        const response = await searchApi.fuzzySearchArticles(query || '', 10)
        console.log('文章搜索响应:', response)

        if (response.data && response.data.code === 0) {
          articleOptions.value = response.data.data || []
          console.log('找到文章:', articleOptions.value.length, '篇')
        } else {
          console.warn('文章搜索失败:', response.data?.message)
          articleOptions.value = []
        }
      } catch (error) {
        console.error('搜索文章失败:', error)
        articleOptions.value = []
        ElMessage.error('搜索文章失败，请稍后重试')
      } finally {
        searchLoading.value = false
      }
    }

    // 处理文章选择变化
    const handleArticleChange = async (articleId) => {
      if (!articleId) {
        selectedArticle.value = null
        return
      }

      // 从搜索结果中找到选中的文章
      const article = articleOptions.value.find(a => a.article_id === articleId)
      if (article) {
        selectedArticle.value = article
      }

      try {
        const response = await articlesApi.getArticleById(articleId, false)
        if (response.data.code === 200) {
          selectedArticle.value = response.data.data
        }
      } catch (error) {
        console.error('获取文章详情失败:', error)
        ElMessage.error('获取文章详情失败')
      }
    }

    // 生成摘要
    const generateSummary = async () => {
      try {
        await formRef.value.validate()

        loading.value = true
        error.value = ''
        generatedSummary.value = ''

        const requestData = {
          article_id: form.articleId,
          max_length: form.maxLength
        }

        const response = await aiApi.generateArticleSummary(requestData)

        if (response.code === 0) {
          generatedSummary.value = response.data.summary
          emit('generated', generatedSummary.value)
          ElMessage.success('摘要生成成功！')
        } else {
          error.value = response.message || '生成失败'
          ElMessage.error(error.value)
        }
      } catch (error) {
        console.error('生成摘要失败:', error)
        error.value = '生成失败，请稍后重试'
        ElMessage.error('生成失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }

    // 重新生成摘要
    const regenerateSummary = () => {
      generateSummary()
    }

    // 复制到剪贴板
    const copyToClipboard = async () => {
      try {
        await navigator.clipboard.writeText(generatedSummary.value)
        ElMessage.success('摘要已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
        ElMessage.error('复制失败')
      }
    }

    // 初始化时加载热门文章
    onMounted(async () => {
      console.log('初始化加载热门文章')
      try {
        const response = await searchApi.fuzzySearchArticles('', 10)
        console.log('初始化文章响应:', response)

        if (response.data && response.data.code === 0) {
          articleOptions.value = response.data.data || []
          console.log('初始化加载了', articleOptions.value.length, '篇文章')
        } else {
          console.warn('初始化加载文章失败:', response.data?.message)
        }
      } catch (error) {
        console.error('加载热门文章失败:', error)
        ElMessage.error('加载文章列表失败')
      }
    })

    return {
      formRef,
      form,
      rules,
      loading,
      searchLoading,
      error,
      generatedSummary,
      articleOptions,
      selectedArticle,
      compressionRatio,
      searchArticles,
      handleArticleChange,
      generateSummary,
      regenerateSummary,
      copyToClipboard
    }
  }
}
</script>

<style scoped>
.article-summary-generator {
  max-width: 800px;
  margin: 0 auto;
}

.generator-header {
  text-align: center;
  margin-bottom: 40px;
}

.generator-header h2 {
  color: #333;
  margin-bottom: 10px;
}

.generator-header p {
  color: #666;
  font-size: 1rem;
}

.form-section {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 15px;
  margin-bottom: 30px;
}

.form-tip {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
}

.length-hint {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  font-size: 0.9rem;
  color: #666;
}

.article-preview {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 15px;
  margin-bottom: 30px;
}

.article-preview h3 {
  color: #333;
  margin-bottom: 20px;
}

.preview-content {
  background: white;
  padding: 20px;
  border-radius: 10px;
}

.article-info h4 {
  color: #333;
  margin-bottom: 10px;
  font-size: 1.2rem;
}

.article-meta {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
  font-size: 0.9rem;
  color: #666;
}

.article-meta span {
  display: flex;
  align-items: center;
  gap: 5px;
}

.article-content {
  color: #666;
  line-height: 1.6;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.result-section {
  margin-top: 30px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
  border-radius: 15px;
  color: white;
}

.result-header h3 {
  margin: 0;
  font-size: 1.4rem;
  display: flex;
  align-items: center;
  gap: 10px;
}

.result-actions {
  display: flex;
  gap: 10px;
}

/* 文章信息卡片 */
.article-info-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.article-header {
  display: flex;
  align-items: center;
  gap: 15px;
}

.article-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.3rem;
}

.article-details h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1.3rem;
  font-weight: 600;
}

.article-meta {
  display: flex;
  align-items: center;
  gap: 15px;
}

.article-popularity {
  font-size: 0.85rem;
  color: #666;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
}

/* 摘要主体 */
.summary-main {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.summary-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e9ecef;
}

.summary-header h4 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-badges {
  display: flex;
  gap: 8px;
}

.summary-text {
  padding: 30px;
  position: relative;
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
}

.text-content {
  line-height: 1.8;
  color: #333;
  font-size: 1.1rem;
  position: relative;
  z-index: 2;
}

.text-decoration {
  position: absolute;
  top: 10px;
  right: 20px;
  z-index: 1;
}

.quote-mark {
  font-size: 6rem;
  color: rgba(108, 92, 231, 0.1);
  font-family: serif;
  line-height: 1;
}

.summary-stats {
  display: flex;
  justify-content: space-around;
  padding: 20px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 0.9rem;
}

.stat-item i {
  color: #6c5ce7;
}

.error-section {
  margin-top: 20px;
}

@media (max-width: 768px) {
  .result-header {
    flex-direction: column;
    gap: 15px;
  }

  .result-actions {
    width: 100%;
    justify-content: center;
  }

  .article-meta {
    flex-direction: column;
    gap: 10px;
  }

  .summary-stats {
    flex-direction: column;
    gap: 5px;
    text-align: center;
  }
}
</style>
