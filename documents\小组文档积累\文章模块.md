# 文章模块 API 文档

本文档详细描述了 Flask 版本后端的文章模块 API 接口及其在 Postman 中的测试用例。

## 目录

1. [添加文章](#添加文章)
2. [获取文章详情](#获取文章详情)
3. [获取文章列表](#获取文章列表)
4. [更新文章](#更新文章)
5. [删除文章](#删除文章)
6. [搜索文章](#搜索文章)
7. [模糊搜索文章](#模糊搜索文章)
8. [文章压缩技术](#文章压缩技术)

## 添加文章

### 接口信息

- **URL**: `/api/articles`
- **方法**: `POST`
- **描述**: 添加新文章，支持JSON和表单两种提交方式
- **请求体**: JSON 或 form-data

### 请求参数 (JSON)

| 参数名      | 类型    | 必填 | 描述                               |
| ----------- | ------- | ---- | ---------------------------------- |
| user_id     | integer | 是   | 用户 ID                            |
| title       | string  | 是   | 文章标题                           |
| content     | string  | 是   | 文章内容                           |
| location_id | integer | 是   | 地点 ID                            |
| image_url   | string  | 否   | 图片 URL，可以通过上传图片接口获取 |
| video_url   | string  | 否   | 视频 URL，可以通过上传视频接口获取 |

### 请求参数 (form-data)

| 参数名      | 类型   | 必填 | 描述                                          |
| ----------- | ------ | ---- | --------------------------------------------- |
| user_id     | string | 是   | 用户 ID                                       |
| title       | string | 是   | 文章标题                                      |
| content     | string | 是   | 文章内容                                      |
| location_id | string | 是   | 地点 ID                                       |
| image       | file   | 否   | 图片文件，支持 png, jpg, jpeg, gif, webp 格式 |
| video       | file   | 否   | 视频文件，支持 mp4, webm, ogg, mov 格式       |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Article added successfully",
    "data": {
        "article_id": 1,
        "image_url": "/uploads/images/image_1_abc123.jpg",
        "video_url": "/uploads/videos/video_1_abc123.mp4"
    }
}
```

#### 错误响应

- **参数缺失** (400 Bad Request)

```json
{
    "code": 1,
    "message": "Missing required fields",
    "data": null
}
```

- **用户不存在** (404 Not Found)

```json
{
    "code": 1,
    "message": "User not found",
    "data": null
}
```

- **地点不存在** (404 Not Found)

```json
{
    "code": 1,
    "message": "Location not found",
    "data": null
}
```

### 实现细节

- 文章内容使用 Huffman 编码进行压缩存储，减少数据库存储空间
- 图片和视频文件存储在服务器的 uploads 目录下，数据库中只存储文件路径
- 支持同时上传文章内容、图片和视频
- 文章创建时间和更新时间自动记录

### Postman 测试

#### JSON 提交

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `POST`
4. 输入 URL: `http://localhost:5000/api/articles`
5. 选择 Body 选项卡，选择 raw，设置类型为 JSON
6. 输入请求体:

```json
{
    "user_id": 1,
    "title": "北京旅游攻略",
    "content": "这是一篇关于北京旅游的攻略...",
    "location_id": 1,
    "image_url": "/uploads/images/image_abc123.jpg",
    "video_url": "/uploads/videos/video_abc123.mp4"
}
```

7. 点击 Send 按钮发送请求

#### 表单提交（带文件上传）

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `POST`
4. 输入 URL: `http://localhost:5000/api/articles`
5. 选择 Body 选项卡，选择 form-data
6. 添加键值对:
   - Key: `user_id`, Value: `1`, Type: Text
   - Key: `title`, Value: `北京旅游攻略`, Type: Text
   - Key: `content`, Value: `这是一篇关于北京旅游的攻略...`, Type: Text
   - Key: `location_id`, Value: `1`, Type: Text
   - Key: `image`, Value: [选择图片文件], Type: File
   - Key: `video`, Value: [选择视频文件], Type: File
7. 点击 Send 按钮发送请求

## 获取文章详情

### 接口信息

- **URL**: `/api/articles/{article_id}`
- **方法**: `GET`
- **描述**: 获取文章详情
- **URL 参数**: article_id - 文章 ID
- **查询参数**:

| 参数名       | 类型    | 必填 | 描述                              |
| ------------ | ------- | ---- | --------------------------------- |
| content_only | boolean | 否   | 如果设置为 true，则只返回文章内容 |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Article retrieved successfully",
    "data": {
        "article_id": 1,
        "user_id": 1,
        "username": "testuser",
        "title": "北京旅游攻略",
        "content": "这是一篇关于北京旅游的攻略...",
        "location_id": 1,
        "location_name": "北京",
        "popularity": 10,
        "evaluation": 4.5,
        "image_url": "/uploads/images/image_1_abc123.jpg",
        "video_url": "/uploads/videos/video_1_abc123.mp4",
        "created_at": "2023-05-01T12:00:00Z",
        "updated_at": "2023-05-01T12:00:00Z"
    }
}
```

#### 错误响应

- **文章不存在** (404 Not Found)

```json
{
    "code": 1,
    "message": "Article not found",
    "data": null
}
```

### 实现细节

- 获取文章时会自动增加文章的浏览量（popularity）
- 文章内容从数据库中取出后会自动解压缩
- 返回的数据包含用户名和地点名称，方便前端展示

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `GET`
4. 输入 URL: `http://localhost:5000/api/articles/1` (替换 `1` 为实际的文章 ID)
5. 点击 Send 按钮发送请求

## 获取文章列表

### 接口信息

- **URL**: `/api/articles`
- **方法**: `GET`
- **描述**: 获取文章列表，支持分页和排序
- **查询参数**:

| 参数名    | 类型    | 必填 | 描述                                                 |
| --------- | ------- | ---- | ---------------------------------------------------- |
| page      | integer | 否   | 页码，默认为 1                                       |
| size      | integer | 否   | 每页数量，默认为 10                                  |
| sort      | string  | 否   | 排序字段，可选值: created_at, popularity, evaluation |
| direction | string  | 否   | 排序方向，可选值: asc, desc，默认为 desc             |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Articles retrieved successfully",
    "data": {
        "articles": [
            {
                "article_id": 1,
                "user_id": 1,
                "username": "testuser",
                "title": "北京旅游攻略",
                "location_id": 1,
                "location_name": "北京",
                "popularity": 10,
                "evaluation": 4.5,
                "image_url": "/uploads/images/image_1_abc123.jpg",
                "created_at": "2023-05-01T12:00:00Z"
            }
        ],
        "total": 1,
        "page": 1,
        "size": 10,
        "pages": 1
    }
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `GET`
4. 输入 URL: `http://localhost:5000/api/articles?page=1&size=10&sort=popularity&direction=desc`
5. 点击 Send 按钮发送请求

## 搜索文章

### 接口信息

- **URL**: `/api/articles/search`
- **方法**: `GET`
- **描述**: 搜索文章，支持按标题或地点名称搜索
- **查询参数**:

| 参数名   | 类型   | 必填 | 描述           |
| -------- | ------ | ---- | -------------- |
| title    | string | 否   | 按标题搜索     |
| location | string | 否   | 按地点名称搜索 |

> **注意**：此接口整合了原来的 `/api/articles/getArticleByTitle` 和 `/api/articles/getArticleByLocation` 接口的功能。可以同时提供 title 和 location 参数进行组合搜索。

### 响应

#### 成功响应 (200 OK)

```json
{
    "articles": [
        {
            "article_id": 1,
            "user_id": 1,
            "username": "testuser",
            "title": "北京旅游攻略",
            "location_id": 1,
            "location_name": "北京",
            "popularity": 10,
            "evaluation": 4.5,
            "image_url": "/uploads/images/image_1_abc123.jpg",
            "created_at": "2023-05-01T12:00:00Z"
        }
    ]
}
```

#### 错误响应

- **参数缺失** (400 Bad Request)

```json
{
    "error": "Missing search parameters"
}
```

### 实现细节

- 搜索支持模糊匹配，使用SQL的LIKE操作符实现
- 按标题搜索时，会在文章标题中查找包含关键词的文章
- 按地点搜索时，会先查找匹配的地点，然后查找与这些地点关联的文章
- 搜索结果不包含文章内容，需要通过获取文章详情接口获取完整内容

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `GET`
4. 输入 URL: `http://localhost:5000/api/articles/search?title=北京`
5. 点击 Send 按钮发送请求

## 模糊搜索文章

### 接口信息

- **URL**: `/api/articles/fuzzy_search`
- **方法**: `GET`
- **描述**: 模糊搜索文章，主要用于AI生成模块
- **查询参数**:

| 参数名  | 类型   | 必填 | 描述       |
| ------- | ------ | ---- | ---------- |
| keyword | string | 是   | 搜索关键词 |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "Articles found",
    "data": [
        {
            "article_id": 1,
            "title": "北京旅游攻略",
            "content": "这是一篇关于北京旅游的攻略...",
            "location_name": "北京"
        }
    ]
}
```

### Postman 测试

```
GET http://localhost:5000/api/articles/fuzzy_search?keyword=旅游
```

## 文章内容搜索

> **注意**：原 `/api/articles/search_word` 接口已移除。此功能建议在前端实现，可以使用 JavaScript 的字符串搜索功能或第三方库来实现文章内容的关键词高亮和搜索。

## 删除文章

### 接口信息

- **URL**: `/api/articles/{article_id}`
- **方法**: `DELETE`
- **描述**: 删除指定ID的文章
- **URL 参数**: article_id - 文章 ID
- **请求体**: JSON

### 请求参数

| 参数名  | 类型    | 必填 | 描述                              |
| ------- | ------- | ---- | --------------------------------- |
| user_id | integer | 是   | 用户 ID（必须是文章作者才能删除） |

### 响应

#### 成功响应 (200 OK)

```json
{
    "message": "Article deleted successfully"
}
```

#### 错误响应

- **参数缺失** (400 Bad Request)

```json
{
    "error": "No data provided"
}
```

```json
{
    "error": "Missing user_id parameter"
}
```

- **文章不存在** (404 Not Found)

```json
{
    "error": "Article not found"
}
```

- **无权限** (403 Forbidden)

```json
{
    "error": "Unauthorized"
}
```

### 实现细节

- 只有文章的作者才能删除文章
- 删除文章时会同时删除与文章相关的评分和收藏记录（通过外键约束实现）
- 文章相关的图片和视频文件不会自动删除，需要单独清理

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `DELETE`
4. 输入 URL: `http://localhost:5000/api/articles/1` (替换 `1` 为实际的文章 ID)
5. 选择 Body 选项卡，选择 raw，设置类型为 JSON
6. 输入请求体:

```json
{
    "user_id": 1
}
```

7. 点击 Send 按钮发送请求

## 更新文章

### 接口信息

- **URL**: `/api/articles/{article_id}`
- **方法**: `PUT`
- **描述**: 更新指定ID的文章
- **URL 参数**: article_id - 文章 ID
- **请求体**: JSON

### 请求参数

| 参数名      | 类型    | 必填 | 描述                              |
| ----------- | ------- | ---- | --------------------------------- |
| user_id     | integer | 是   | 用户 ID（必须是文章作者才能更新） |
| title       | string  | 是   | 文章标题                          |
| content     | string  | 是   | 文章内容                          |
| location_id | integer | 是   | 地点 ID                           |

### 响应

#### 成功响应 (200 OK)

```json
{
    "message": "Article updated successfully"
}
```

#### 错误响应

- **参数缺失** (400 Bad Request)

```json
{
    "error": "No data provided"
}
```

```json
{
    "error": "Missing required fields"
}
```

- **文章不存在** (404 Not Found)

```json
{
    "error": "Article not found"
}
```

- **无权限** (403 Forbidden)

```json
{
    "error": "Unauthorized"
}
```

### 实现细节

- 只有文章的作者才能更新文章
- 更新文章内容时会重新进行Huffman编码压缩
- 更新文章不会影响文章的评分和收藏状态
- 更新时间会自动更新

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `PUT`
4. 输入 URL: `http://localhost:5000/api/articles/1` (替换 `1` 为实际的文章 ID)
5. 选择 Body 选项卡，选择 raw，设置类型为 JSON
6. 输入请求体:

```json
{
    "user_id": 1,
    "title": "北京旅游攻略（更新版）",
    "content": "这是一篇更新后的北京旅游攻略...",
    "location_id": 1
}
```

## 旧版接口说明

> **注意**：以下旧版接口已被移除，请使用新版的 `/api/articles/search` 接口代替：
>
> - `/api/articles/getArticleByTitle` - 按标题搜索文章
> - `/api/articles/getArticleByLocation` - 按地点名称搜索文章
>
> 新接口提供了更统一的搜索体验，并支持组合搜索功能。

## 文章压缩技术

文章模块使用 Huffman 编码对文章内容进行压缩，以减少数据库存储空间。Huffman 编码是一种变长编码方案，根据字符出现的频率分配编码，出现频率高的字符使用较短的编码，出现频率低的字符使用较长的编码。

### 压缩流程

1. 统计文章内容中每个字符的出现频率
2. 构建 Huffman 树
3. 生成 Huffman 编码表
4. 使用编码表对文章内容进行压缩
5. 将压缩后的内容和编码表存储到数据库

### 解压流程

1. 从数据库中获取压缩后的内容和编码表
2. 使用编码表对压缩内容进行解码
3. 返回解压后的原始文章内容

### 压缩效果

对于中文文本，Huffman 编码通常可以实现 40%-60% 的压缩率，具体取决于文本内容的特性。

## 文章推荐优化算法

文章模块的推荐功能使用了高效的算法和数据结构，特别针对系统中约200篇文章的规模进行了优化。

### 热门文章推荐优化

1. **堆数据结构**：使用堆（Heap）数据结构维护热门文章列表，而非全量排序
   - 时间复杂度从O(n log n)降低到O(n log k)，其中k是预缓存的热门文章数量
   - 对于约200篇文章的系统，响应时间通常在5ms以内

2. **增量更新机制**：当文章热度变化时，使用增量更新机制维护排序列表
   - 避免全量重排序，大幅提高数据更新效率
   - 支持实时反映文章热度变化

### 高评分文章推荐优化

1. **预排序缓存**：预先计算并缓存高评分文章列表
   - 使用堆数据结构高效维护Top-K文章
   - 支持按评分快速筛选文章

2. **增量更新**：当文章评分变化时，高效更新排序列表
   - 只更新变化的文章位置，不影响其他文章
   - 保证推荐结果的实时性和准确性

### 性能提升

1. **查询性能**：优化后的推荐算法比直接数据库查询快3-5倍
2. **内存效率**：对于约200篇文章的系统，内存占用可控
3. **实时性**：支持文章热度和评分的实时变化反映到推荐结果中

## 完整的 Postman 测试流程

以下是一个完整的测试流程，按照顺序执行可以测试文章模块的所有功能：

### 1. 添加文章

```
POST http://localhost:5000/api/articles
Content-Type: application/json

{
    "user_id": 1,
    "title": "北京旅游攻略",
    "content": "这是一篇关于北京旅游的攻略...",
    "location_id": 1
}
```

### 2. 获取文章详情

```
GET http://localhost:5000/api/articles/1
```

### 3. 获取文章内容

```
GET http://localhost:5000/api/articles/1?content_only=true
```

### 4. 获取文章列表

```
GET http://localhost:5000/api/articles?page=1&size=10&sort=created_at&direction=desc
```

### 5. 更新文章

```
PUT http://localhost:5000/api/articles/1
Content-Type: application/json

{
    "user_id": 1,
    "title": "北京旅游攻略（更新版）",
    "content": "这是一篇更新后的北京旅游攻略...",
    "location_id": 1
}
```

### 6. 搜索文章（按标题）

```
GET http://localhost:5000/api/articles/search?title=北京
```

### 7. 搜索文章（按地点）

```
GET http://localhost:5000/api/articles/search?location=北京
```

### 8. 搜索文章（组合搜索）

```
GET http://localhost:5000/api/articles/search?title=旅游&location=北京
```

### 9. 删除文章

```
DELETE http://localhost:5000/api/articles/1
Content-Type: application/json

{
    "user_id": 1
}
```