<!--diary-detail.wxml-->
<view class="page">
  <!-- 顶部导航栏 -->
  <view class="nav-header">
    <view class="nav-left" bindtap="goBack">
      <text class="iconfont icon-arrow-left"></text>
    </view>
    <view class="nav-title">日记详情</view>
    <view class="nav-right" bindtap="shareArticle">
      <text class="iconfont icon-share"></text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 日记内容 -->
  <view class="diary-detail" wx:if="{{!loading && diaryData}}">
    <!-- 头部信息 -->
    <view class="diary-header">
      <view class="diary-title">{{diaryData.title}}</view>
      <view class="diary-meta">
        <view class="diary-location" wx:if="{{diaryData.location}}">
          <text class="location-icon">📍</text>
          <text class="location-text">{{diaryData.location}}</text>
        </view>
        <view class="diary-date">
          <text class="date-icon">📅</text>
          <text class="date-text">{{diaryData.created_at}}</text>
        </view>
      </view>
      <view class="diary-stats">
        <view class="stat-item">
          <text class="stat-icon">👁️</text>
          <text class="stat-text">{{diaryData.popularity || 0}}</text>
        </view>
        <view class="stat-item">
          <text class="stat-icon">❤️</text>
          <text class="stat-text">{{diaryData.like_count || 0}}</text>
        </view>
      </view>
    </view>

    <!-- 图片展示 -->
    <view class="diary-images" wx:if="{{imageList.length > 0}}">
      <view class="images-header">
        <text class="section-icon">🖼️</text>
        <text class="section-title">图片展示 ({{imageList.length}}张)</text>
      </view>
      <view class="image-carousel">
        <swiper class="image-swiper" indicator-dots="{{imageList.length > 1}}" autoplay="{{false}}" circular="{{true}}">
          <swiper-item wx:for="{{imageList}}" wx:key="index">
            <view class="image-item" bindtap="previewImage" data-index="{{index}}">
              <image src="{{item}}" mode="aspectFill" class="diary-image" />
              <view class="image-overlay">
                <text class="image-count">{{index + 1}}/{{imageList.length}}</text>
              </view>
            </view>
          </swiper-item>
        </swiper>
      </view>
    </view>

    <!-- 内容 -->
    <view class="diary-content">
      <view class="content-header">
        <text class="section-icon">📝</text>
        <text class="section-title">日记内容</text>
      </view>
      <view class="content-body">
        <text class="content-text">{{diaryData.content}}</text>
      </view>
    </view>

    <!-- 标签 -->
    <view class="diary-tags" wx:if="{{tagList.length > 0}}">
      <view class="tags-header">
        <text class="section-icon">🏷️</text>
        <text class="section-title">标签</text>
      </view>
      <view class="tags-list">
        <view class="tag-item" wx:for="{{tagList}}" wx:key="index">
          <text class="tag-text">{{item}}</text>
        </view>
      </view>
    </view>

    <!-- 操作栏 -->
    <view class="action-bar">
      <view class="action-item {{isLiked ? 'active' : ''}}" bindtap="toggleLike">
        <view class="action-icon">
          <text class="action-emoji">{{isLiked ? '❤️' : '🤍'}}</text>
        </view>
        <text class="action-text">{{isLiked ? '已点赞' : '点赞'}}</text>
      </view>
      <view class="action-item {{isFavorited ? 'active' : ''}}" bindtap="toggleFavorite">
        <view class="action-icon">
          <text class="action-emoji">{{isFavorited ? '⭐' : '☆'}}</text>
        </view>
        <text class="action-text">{{isFavorited ? '已收藏' : '收藏'}}</text>
      </view>
      <view class="action-item" bindtap="shareArticle">
        <view class="action-icon">
          <text class="action-emoji">📤</text>
        </view>
        <text class="action-text">分享</text>
      </view>
    </view>

    <!-- 底部安全区域 -->
    <view class="safe-area"></view>
  </view>

  <!-- 错误状态 -->
  <view class="error-state" wx:if="{{!loading && !diaryData}}">
    <view class="error-container">
      <view class="error-icon">😕</view>
      <view class="error-title">日记不存在</view>
      <view class="error-desc">该日记可能已被删除或不存在</view>
      <view class="error-action" bindtap="goBack">
        <text class="iconfont icon-arrow-left"></text>
        <text>返回</text>
      </view>
    </view>
  </view>
</view>
