from flask import Blueprint, request, jsonify
from models.location_rating import LocationRating
from models.location import Location
from models.user import User
from utils.database import db
from utils.response import success, error
from sqlalchemy import func

location_rating_bp = Blueprint('location_rating', __name__)

@location_rating_bp.route('/rate', methods=['POST'])
def rate_location():
    """
    Rate a location
    """
    try:
        data = request.get_json()
        
        # Validate required fields
        if not all(key in data for key in ['location_id', 'user_id', 'rating']):
            return error('Missing required fields: location_id, user_id, rating')
        
        location_id = data['location_id']
        user_id = data['user_id']
        rating = data['rating']
        
        # Validate rating value
        if not isinstance(rating, (int, float)) or rating < 1 or rating > 5:
            return error('Rating must be a number between 1 and 5')
        
        # Check if location and user exist
        location = Location.query.get(location_id)
        user = User.query.get(user_id)
        
        if not location:
            return error('Location not found', 404)
        
        if not user:
            return error('User not found', 404)
        
        # Check if user has already rated this location
        existing_rating = LocationRating.query.filter_by(
            user_id=user_id, location_id=location_id).first()
        
        if existing_rating:
            # Update existing rating
            existing_rating.rating = rating
        else:
            # Create new rating
            new_rating = LocationRating(
                user_id=user_id,
                location_id=location_id,
                rating=rating
            )
            db.session.add(new_rating)
        
        # Update location's average rating
        avg_rating = db.session.query(func.avg(LocationRating.rating)).filter(
            LocationRating.location_id == location_id).scalar() or 0
        
        # Round to 1 decimal place
        avg_rating = round(avg_rating, 1)
        
        # Update location's evaluation
        location.evaluation = avg_rating
        
        db.session.commit()
        
        return success({
            'new_rating': avg_rating,
            'user_rating': rating
        }, 'Rating submitted successfully')
    
    except Exception as e:
        db.session.rollback()
        import traceback
        traceback.print_exc()
        return error(f'Error rating location: {str(e)}')

@location_rating_bp.route('/user/<int:user_id>/location/<int:location_id>', methods=['GET'])
def get_user_rating(user_id, location_id):
    """
    Get a user's rating for a specific location
    """
    try:
        rating = LocationRating.query.filter_by(
            user_id=user_id, location_id=location_id).first()
        
        if rating:
            return success({
                'rating': rating.rating
            }, 'User rating retrieved successfully')
        else:
            return success({
                'rating': 0
            }, 'User has not rated this location')
    
    except Exception as e:
        return error(f'Error retrieving user rating: {str(e)}')

@location_rating_bp.route('/location/<int:location_id>', methods=['GET'])
def get_location_ratings(location_id):
    """
    Get all ratings for a location
    """
    try:
        ratings = LocationRating.query.filter_by(location_id=location_id).all()
        
        # Calculate average rating
        avg_rating = db.session.query(func.avg(LocationRating.rating)).filter(
            LocationRating.location_id == location_id).scalar() or 0
        
        # Round to 1 decimal place
        avg_rating = round(avg_rating, 1)
        
        return success({
            'ratings': [rating.to_dict() for rating in ratings],
            'average_rating': avg_rating,
            'count': len(ratings)
        }, 'Location ratings retrieved successfully')
    
    except Exception as e:
        return error(f'Error retrieving location ratings: {str(e)}')
