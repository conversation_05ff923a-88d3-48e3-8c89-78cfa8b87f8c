"""
统一响应格式工具
对应Java版本的Result类
"""
from flask import jsonify

def success(data=None, message="操作成功"):
    """
    成功响应
    
    Args:
        data: 响应数据
        message: 响应消息
        
    Returns:
        JSON响应
    """
    response = {
        "code": 0,
        "message": message
    }
    
    if data is not None:
        response["data"] = data
    
    return jsonify(response), 200

def error(message="操作失败", code=1, status_code=400):
    """
    错误响应
    
    Args:
        message: 错误消息
        code: 错误码
        status_code: HTTP状态码
        
    Returns:
        JSON响应
    """
    response = {
        "code": code,
        "message": message,
        "data": None
    }
    
    return jsonify(response), status_code

def not_found(message="资源不存在"):
    """
    资源不存在响应
    
    Args:
        message: 错误消息
        
    Returns:
        JSON响应
    """
    return error(message, code=404, status_code=404)

def unauthorized(message="未授权"):
    """
    未授权响应
    
    Args:
        message: 错误消息
        
    Returns:
        JSON响应
    """
    return error(message, code=401, status_code=401)

def forbidden(message="禁止访问"):
    """
    禁止访问响应
    
    Args:
        message: 错误消息
        
    Returns:
        JSON响应
    """
    return error(message, code=403, status_code=403)

def server_error(message="服务器错误"):
    """
    服务器错误响应
    
    Args:
        message: 错误消息
        
    Returns:
        JSON响应
    """
    return error(message, code=500, status_code=500)
