<template>
  <div class="smart-location-description-generator">
    <div class="generator-form">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <!-- 选择地点 -->
        <el-form-item label="选择地点" prop="locationName" required>
          <el-select
            v-model="form.locationName"
            filterable
            remote
            reserve-keyword
            allow-create
            default-first-option
            placeholder="请输入地点名称，支持搜索数据库中的景点"
            :remote-method="searchLocations"
            :loading="searchLoading"
            style="width: 100%;"
            @change="handleLocationSelect"
          >
            <el-option
              v-for="location in locationOptions"
              :key="location.location_id"
              :label="location.location_name"
              :value="location.location_name"
            >
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <div style="flex: 1;">
                  <div style="font-weight: 500; color: #333;">{{ location.location_name }}</div>
                  <div style="color: #8492a6; font-size: 12px; margin-top: 2px;">
                    <span>{{ getLocationTypeText(location.type) }}</span>
                    <span v-if="location.address" style="margin-left: 10px;">{{ location.address }}</span>
                  </div>
                </div>
              </div>
            </el-option>
          </el-select>
          <div class="form-tip">
            <div v-if="searchLoading" style="color: #409eff;">
              <i class="el-icon-loading"></i> 正在搜索景点数据库...
            </div>
            <div v-else-if="locationOptions.length > 0" style="color: #67c23a;">
              找到 {{ locationOptions.length }} 个相关景点，您也可以直接输入其他地点名称
            </div>
            <div v-else style="color: #909399;">
              支持从景点数据库搜索或直接输入任意地点名称
            </div>
          </div>
        </el-form-item>

        <!-- 描述风格 -->
        <el-form-item label="描述风格" prop="style">
          <el-radio-group v-model="form.style">
            <el-radio label="详细">详细介绍</el-radio>
            <el-radio label="简洁">简洁概述</el-radio>
            <el-radio label="文艺">文艺描述</el-radio>
            <el-radio label="实用">实用指南</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 重点内容 -->
        <el-form-item label="重点内容" prop="focus">
          <el-checkbox-group v-model="form.focus">
            <el-checkbox label="历史">历史背景</el-checkbox>
            <el-checkbox label="文化">文化特色</el-checkbox>
            <el-checkbox label="建筑">建筑特点</el-checkbox>
            <el-checkbox label="自然">自然风光</el-checkbox>
            <el-checkbox label="美食">美食特色</el-checkbox>
            <el-checkbox label="交通">交通指南</el-checkbox>
            <el-checkbox label="门票">门票信息</el-checkbox>
            <el-checkbox label="最佳时间">最佳游览时间</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <!-- 描述长度 -->
        <el-form-item label="描述长度" prop="length">
          <el-slider
            v-model="form.length"
            :min="100"
            :max="1000"
            :step="100"
            :marks="{ 100: '简短', 500: '适中', 1000: '详细' }"
            show-stops
            style="width: 300px;"
          />
          <span style="margin-left: 20px; color: #666;">约{{ form.length }}字</span>
        </el-form-item>

        <!-- 生成按钮 -->
        <el-form-item>
          <el-button
            type="primary"
            @click="generateDescription"
            :loading="loading"
            size="large"
            style="width: 200px;"
          >
            <i class="el-icon-location-information"></i>
            {{ loading ? '生成中...' : '生成描述' }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 地点信息预览 -->
    <div v-if="selectedLocation" class="location-preview">
      <h3>地点信息</h3>
      <div class="preview-content">
        <div class="location-info">
          <h4>{{ selectedLocation.location_name }}</h4>
          <div class="location-meta">
            <span><i class="el-icon-location"></i> {{ getLocationTypeText(selectedLocation.type) }}</span>
            <span v-if="selectedLocation.address"><i class="el-icon-map-location"></i> {{ selectedLocation.address }}</span>
          </div>
          <div v-if="selectedLocation.description" class="location-desc">
            {{ selectedLocation.description }}
          </div>
        </div>
      </div>
    </div>

    <!-- 生成结果 -->
    <div v-if="generatedDescription" class="result-section">
      <div class="result-header">
        <h3>📍 生成的地点描述</h3>
        <div class="result-actions">
          <el-button @click="copyToClipboard" type="text">
            <i class="el-icon-document-copy"></i>
            复制描述
          </el-button>
          <el-button @click="regenerateDescription" type="text">
            <i class="el-icon-refresh"></i>
            重新生成
          </el-button>
        </div>
      </div>

      <div class="description-content">
        <div class="description-title">
          <h2>{{ form.locationName }}</h2>
          <div class="description-meta">
            <span><i class="el-icon-view"></i> {{ form.style }}风格</span>
            <span><i class="el-icon-star-on"></i> {{ form.focus.length }}个重点</span>
            <span><i class="el-icon-document"></i> {{ generatedDescription.length }}字</span>
          </div>
        </div>

        <div class="description-text">
          <div class="text-content">
            {{ generatedDescription }}
          </div>
        </div>

        <div class="description-stats">
          <div class="stat-item">
            <i class="el-icon-document"></i>
            <span>{{ generatedDescription.length }} 字</span>
          </div>
          <div class="stat-item">
            <i class="el-icon-view"></i>
            <span>{{ form.style }}风格</span>
          </div>
          <div class="stat-item">
            <i class="el-icon-star-on"></i>
            <span>{{ form.focus.length }}个重点</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误信息 -->
    <div v-if="error" class="error-section">
      <el-alert :title="error" type="error" show-icon />
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'

export default {
  name: 'SmartLocationDescriptionGenerator',
  setup() {
    const formRef = ref(null)
    const loading = ref(false)
    const searchLoading = ref(false)
    const error = ref('')
    const generatedDescription = ref('')
    const locationOptions = ref([])
    const selectedLocation = ref(null)

    const form = reactive({
      locationName: '',
      style: '详细',
      focus: ['历史', '文化'],
      length: 500
    })

    const rules = {
      locationName: [
        { required: true, message: '请选择或输入一个地点', trigger: 'blur' }
      ],
      style: [
        { required: true, message: '请选择描述风格', trigger: 'change' }
      ]
    }

    // 获取地点类型文本
    const getLocationTypeText = (type) => {
      const typeMap = {
        0: '教育场所',
        1: '文化景点',
        2: '自然景观',
        3: '娱乐场所'
      }
      return typeMap[type] || '未知类型'
    }

    // 搜索地点
    const searchLocations = async (query) => {
      if (!query || query.length < 2) {
        locationOptions.value = []
        return
      }

      searchLoading.value = true
      try {
        console.log('搜索地点:', query)
        const response = await axios.get(`http://localhost:5000/api/locations/fuzzy_search`, {
          params: { query, limit: 10 }
        })

        console.log('搜索响应:', response.data)

        if (response.data && response.data.code === 0) {
          // 转换数据格式，确保字段名正确
          locationOptions.value = (response.data.data || []).map(location => ({
            location_id: location.location_id,
            location_name: location.name || location.location_name,
            type: location.type,
            address: location.address,
            keyword: location.keyword
          }))
          console.log('处理后的地点选项:', locationOptions.value)
        } else {
          locationOptions.value = []
        }
      } catch (error) {
        console.error('搜索地点失败:', error)
        locationOptions.value = []
      } finally {
        searchLoading.value = false
      }
    }

    // 处理地点选择
    const handleLocationSelect = (locationName) => {
      selectedLocation.value = locationOptions.value.find(
        location => location.location_name === locationName
      )
      // 清空搜索结果，避免下次搜索时显示旧结果
      if (locationName) {
        locationOptions.value = []
      }
    }

    // 生成描述
    const generateDescription = async () => {
      try {
        await formRef.value.validate()

        loading.value = true
        error.value = ''
        generatedDescription.value = ''

        const requestData = {
          location_name: form.locationName,
          style: form.style,
          focus: form.focus.join(', '),
          length: form.length
        }

        const response = await axios.post('http://localhost:5000/api/ai/generate_location_description_by_name', requestData)

        if (response.data.code === 0) {
          generatedDescription.value = response.data.data.description
          ElMessage.success('地点描述生成成功！')
        } else {
          error.value = response.data.message || '生成失败'
          ElMessage.error(error.value)
        }
      } catch (error) {
        console.error('生成描述失败:', error)
        error.value = '生成失败，请稍后重试'
        ElMessage.error('生成失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }

    // 重新生成描述
    const regenerateDescription = () => {
      generateDescription()
    }

    // 复制到剪贴板
    const copyToClipboard = async () => {
      try {
        await navigator.clipboard.writeText(generatedDescription.value)
        ElMessage.success('描述已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
        ElMessage.error('复制失败')
      }
    }

    return {
      formRef,
      form,
      rules,
      loading,
      searchLoading,
      error,
      generatedDescription,
      locationOptions,
      selectedLocation,
      getLocationTypeText,
      searchLocations,
      handleLocationSelect,
      generateDescription,
      regenerateDescription,
      copyToClipboard
    }
  }
}
</script>

<style scoped>
.smart-location-description-generator {
  max-width: 1000px;
  margin: 0 auto;
}

.generator-form {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 15px;
  margin-bottom: 30px;
}



.form-tip {
  margin-top: 8px;
  font-size: 12px;
}

.location-preview {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 15px;
  margin-bottom: 30px;
}

.location-preview h3 {
  color: #333;
  margin-bottom: 20px;
}

.preview-content {
  background: white;
  padding: 20px;
  border-radius: 10px;
}

.location-info h4 {
  color: #333;
  margin-bottom: 10px;
  font-size: 1.2rem;
}

.location-meta {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
  font-size: 0.9rem;
  color: #666;
}

.location-meta span {
  display: flex;
  align-items: center;
  gap: 5px;
}

.location-desc {
  color: #666;
  line-height: 1.6;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.result-section {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.result-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-header h3 {
  margin: 0;
  font-size: 1.4rem;
}

.result-actions {
  display: flex;
  gap: 10px;
}

.description-content {
  padding: 30px;
}

.description-title h2 {
  color: #333;
  margin-bottom: 10px;
}

.description-meta {
  display: flex;
  gap: 20px;
  color: #666;
  margin-bottom: 25px;
}

.description-meta span {
  display: flex;
  align-items: center;
  gap: 5px;
}

.description-text {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 10px;
  padding: 25px;
  margin-bottom: 25px;
}

.text-content {
  line-height: 1.8;
  color: #333;
  font-size: 1.1rem;
}

.description-stats {
  display: flex;
  justify-content: space-around;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 0.9rem;
}

.stat-item i {
  color: #667eea;
}

.error-section {
  margin-top: 20px;
}

@media (max-width: 768px) {
  .location-meta {
    flex-direction: column;
    gap: 10px;
  }

  .description-meta {
    flex-direction: column;
    gap: 10px;
  }

  .description-stats {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .result-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
}
</style>
