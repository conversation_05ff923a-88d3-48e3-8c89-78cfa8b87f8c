<template>
  <div class="navbar">
    <div class="logo">
      <router-link to="/">个性化旅游系统</router-link>
    </div>
    <div class="nav-links">
      <router-link to="/home">首页</router-link>
      <router-link to="/recommend">景点推荐</router-link>
      <router-link to="/diary">旅游日记</router-link>
      <router-link to="/route">路线规划</router-link>
      <router-link to="/ai-generator" class="ai-link">
        <i class="el-icon-magic-stick"></i>
        AI生成
      </router-link>
    </div>
    <div class="auth-links">
      <!-- 根据登录状态显示不同的内容 -->
      <template v-if="isAuthenticated">
        <span class="welcome-text">欢迎，{{ username }}</span>
        <router-link to="/user/profile">个人中心</router-link>
        <a href="#" @click.prevent="handleLogout">退出登录</a>
      </template>
      <template v-else>
        <router-link to="/login">登录</router-link>
        <router-link to="/register">注册</router-link>
      </template>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'

export default {
  name: 'NavBar',
  setup() {
    const store = useStore()
    const router = useRouter()

    // 从store获取登录状态
    const isAuthenticated = computed(() => store.getters.isAuthenticated)
    const username = computed(() => store.getters.username)

    // 处理退出登录
    const handleLogout = () => {
      store.dispatch('logout')
      router.push('/login')
    }

    return {
      isAuthenticated,
      username,
      handleLogout
    }
  }
}
</script>

<style scoped>
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 60px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.logo a {
  font-size: 20px;
  font-weight: bold;
  color: #409EFF;
  text-decoration: none;
}

.nav-links {
  display: flex;
  gap: 20px;
}

.nav-links a {
  color: #606266;
  text-decoration: none;
  transition: color 0.3s;
}

.nav-links a:hover,
.nav-links a.router-link-active {
  color: #409EFF;
}

.ai-link {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white !important;
  padding: 8px 15px;
  border-radius: 20px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 5px;
}

.ai-link:hover {
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
  color: white !important;
}

.ai-link.router-link-active {
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
  color: white !important;
}

.auth-links {
  display: flex;
  gap: 15px;
  align-items: center;
}

.auth-links a {
  color: #606266;
  text-decoration: none;
  transition: color 0.3s;
}

.auth-links a:hover {
  color: #409EFF;
}

.welcome-text {
  color: #67c23a;
  margin-right: 10px;
}
</style>
