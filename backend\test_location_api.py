#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的景点搜索API
"""

import requests
import json

# API基础URL
BASE_URL = "http://localhost:5000/api"

def test_search_locations():
    """测试景点搜索API"""
    print("=== 测试景点搜索API ===")
    
    # 测试搜索故宫
    print("\n1. 搜索'故宫':")
    response = requests.get(f"{BASE_URL}/path/search-locations?name=故宫")
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"找到 {len(data)} 个结果:")
        for item in data:
            print(f"  - {item['name']} (ID: {item['location_id']}, 热度: {item['popularity']}, 评分: {item['evaluation']})")
    else:
        print(f"错误: {response.text}")
    
    # 测试搜索天安门
    print("\n2. 搜索'天安门':")
    response = requests.get(f"{BASE_URL}/path/search-locations?name=天安门")
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"找到 {len(data)} 个结果:")
        for item in data:
            print(f"  - {item['name']} (ID: {item['location_id']}, 热度: {item['popularity']}, 评分: {item['evaluation']})")
    else:
        print(f"错误: {response.text}")
    
    # 测试搜索北京
    print("\n3. 搜索'北京':")
    response = requests.get(f"{BASE_URL}/path/search-locations?name=北京&limit=5")
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"找到 {len(data)} 个结果:")
        for item in data:
            print(f"  - {item['name']} (ID: {item['location_id']}, 热度: {item['popularity']}, 评分: {item['evaluation']})")
    else:
        print(f"错误: {response.text}")

def test_popular_locations():
    """测试热门景点API"""
    print("\n=== 测试热门景点API ===")
    
    # 获取热门景点
    print("\n1. 获取前10个热门景点:")
    response = requests.get(f"{BASE_URL}/path/popular-locations?limit=10")
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"返回 {len(data)} 个热门景点:")
        for i, item in enumerate(data, 1):
            print(f"  {i}. {item['name']} (热度: {item['popularity']}, 评分: {item['evaluation']})")
    else:
        print(f"错误: {response.text}")
    
    # 获取热门景点（仅景点类型）
    print("\n2. 获取前5个热门景点（仅景点类型）:")
    response = requests.get(f"{BASE_URL}/path/popular-locations?limit=5&type=1")
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"返回 {len(data)} 个热门景点:")
        for i, item in enumerate(data, 1):
            print(f"  {i}. {item['name']} (热度: {item['popularity']}, 评分: {item['evaluation']})")
    else:
        print(f"错误: {response.text}")

def test_original_search():
    """测试原有的搜索API（对比）"""
    print("\n=== 测试原有搜索API（对比） ===")
    
    # 测试原有的按名称搜索
    print("\n1. 原有API搜索'故宫':")
    response = requests.get(f"{BASE_URL}/path/search-by-name?name=故宫")
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"找到 {len(data)} 个结果:")
        for item in data:
            print(f"  - {item['name']} (ID: {item['vertex_id']}, 类型: {item['type']})")
    else:
        print(f"错误: {response.text}")

def test_database_content():
    """测试数据库内容"""
    print("\n=== 测试数据库内容 ===")
    
    try:
        # 导入数据库模型
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from app import app
        from models.location import Location
        from models.path_planning import Vertex
        
        with app.app_context():
            # 检查Location表
            print("\n1. Location表统计:")
            total_locations = Location.query.count()
            attractions = Location.query.filter_by(type=1).count()
            schools = Location.query.filter_by(type=0).count()
            print(f"  总数: {total_locations}")
            print(f"  景点: {attractions}")
            print(f"  学校: {schools}")
            
            # 查找包含"故宫"的记录
            gugong_locations = Location.query.filter(Location.name.ilike('%故宫%')).all()
            print(f"\n2. 包含'故宫'的Location记录 ({len(gugong_locations)}个):")
            for loc in gugong_locations:
                print(f"  - {loc.name} (ID: {loc.location_id}, 热度: {loc.popularity})")
            
            # 查找包含"天安门"的记录
            tiananmen_locations = Location.query.filter(Location.name.ilike('%天安门%')).all()
            print(f"\n3. 包含'天安门'的Location记录 ({len(tiananmen_locations)}个):")
            for loc in tiananmen_locations:
                print(f"  - {loc.name} (ID: {loc.location_id}, 热度: {loc.popularity})")
            
            # 检查Vertex表
            print("\n4. Vertex表统计:")
            total_vertices = Vertex.query.count()
            print(f"  总数: {total_vertices}")
            
            # 查找包含"故宫"的顶点
            gugong_vertices = Vertex.query.filter(Vertex.label.ilike('%故宫%')).all()
            print(f"\n5. 包含'故宫'的Vertex记录 ({len(gugong_vertices)}个):")
            for vertex in gugong_vertices:
                print(f"  - {vertex.label} (ID: {vertex.vertex_id}, 类型: {vertex.type})")
                
    except Exception as e:
        print(f"数据库测试失败: {e}")

if __name__ == "__main__":
    print("开始测试景点搜索功能...")
    
    try:
        # 测试数据库内容
        test_database_content()
        
        # 测试API
        test_search_locations()
        test_popular_locations()
        test_original_search()
        
        print("\n=== 测试完成 ===")
        
    except requests.exceptions.ConnectionError:
        print("错误: 无法连接到后端服务，请确保后端服务正在运行 (http://localhost:5000)")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
