Component({
  data: {
    selected: 0,
    list: [
      {
        pagePath: "/pages/index/index",
        text: "首页",
        iconPath: "/images/icon/home.png",
        selectedIconPath: "/images/icon/home-active.png",
        iconText: "🏠"
      },
      {
        pagePath: "/pages/place-search/place-search",
        text: "景点",
        iconPath: "/images/icon/place.png",
        selectedIconPath: "/images/icon/place-active.png",
        iconText: "🏛"
      },
      {
        pagePath: "/pages/route-plan/route-plan",
        text: "路线",
        iconPath: "/images/icon/route.png",
        selectedIconPath: "/images/icon/route-active.png",
        iconText: "🗺"
      },
      {
        pagePath: "/pages/diary/diary",
        text: "日记",
        iconPath: "/images/icon/diary.png",
        selectedIconPath: "/images/icon/diary-active.png",
        iconText: "📖"
      },
      {
        pagePath: "/pages/user-center/user-center",
        text: "我的",
        iconPath: "/images/icon/user.png",
        selectedIconPath: "/images/icon/user-active.png",
        iconText: "👤"
      }
    ]
  },
  methods: {
    switchTab(e) {
      const data = e.currentTarget.dataset;
      const url = data.path;
      wx.switchTab({
        url
      });
      this.setData({
        selected: data.index
      });
    },

    // 图标加载失败时的处理
    onIconError(e) {
      console.log('图标加载失败，使用文字图标作为后备');
      // 可以在这里添加逻辑来显示文字图标
    }
  }
})
