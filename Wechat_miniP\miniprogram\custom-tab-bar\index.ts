Component({
  data: {
    selected: 0,
    color: "#999999",
    selectedColor: "#409EFF",
    list: [
      {
        pagePath: "/pages/index/index",
        text: "首页",
        iconText: "🏠",
        iconPath: "images/icon/home.png",
        selectedIconPath: "images/icon/home-active.png"
      },
      {
        pagePath: "/pages/place-search/place-search",
        text: "景点",
        iconText: "🗺️",
        iconPath: "images/icon/place.png",
        selectedIconPath: "images/icon/place-active.png"
      },
      {
        pagePath: "/pages/route-plan/route-plan",
        text: "路线",
        iconText: "🧭",
        iconPath: "images/icon/route.png",
        selectedIconPath: "images/icon/route-active.png"
      },
      {
        pagePath: "/pages/diary/diary",
        text: "日记",
        iconText: "📖",
        iconPath: "images/icon/diary.png",
        selectedIconPath: "images/icon/diary-active.png"
      },
      {
        pagePath: "/pages/user-center/user-center",
        text: "我的",
        iconText: "👤",
        iconPath: "images/icon/user.png",
        selectedIconPath: "images/icon/user-active.png"
      }
    ]
  },
  lifetimes: {
    attached() {
      // 获取当前页面路径
      const pages = getCurrentPages();
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1];
        const currentPath = '/' + currentPage.route;
        
        // 设置当前选中的tab
        const selected = this.data.list.findIndex((item: any) => item.pagePath === currentPath);
        this.setData({
          selected: selected >= 0 ? selected : 0
        });
      }
    }
  },
  methods: {
    switchTab(e: any) {
      const data = e.currentTarget.dataset;
      const url = data.path;
      wx.switchTab({ url });
      this.setData({
        selected: data.index
      });
    }
  }
});
