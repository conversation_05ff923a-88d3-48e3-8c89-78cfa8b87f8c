// diary.ts
interface DiaryItem {
  article_id: number;
  title: string;
  content_preview: string;
  location?: string;
  image_url?: string;
  popularity: number;
  like_count: number;
  created_at: string;
}

Component({
  data: {
    diaryList: [] as DiaryItem[],
    loading: false,
    showSearch: false,
    searchKeyword: '',
    filterType: 'all', // all, recent, popular
    currentPage: 1,
    pageSize: 10,
    hasMore: true,
    userId: 1 // 临时用户ID，实际应该从全局状态获取
  },

  pageLifetimes: {
    show() {
      if (typeof this.getTabBar === 'function' && this.getTabBar()) {
        this.getTabBar().setData({
          selected: 3
        });
      }
      // 加载日记列表
      this.loadDiaryList(true);
    }
  },

  methods: {
    // 加载日记列表
    async loadDiaryList(reset: boolean = false) {
      if (this.data.loading) return;

      this.setData({ loading: true });

      try {
        const page = reset ? 1 : this.data.currentPage;
        const response = await this.requestDiaryList(page);

        // 修复响应格式判断：后端返回 code: 0 表示成功
        if (response.code === 0) {
          const newList = response.data.articles || [];
          const diaryList = reset ? newList : [...this.data.diaryList, ...newList];

          this.setData({
            diaryList: diaryList.map((item: any) => ({
              ...item,
              content_preview: this.getContentPreview(item.content || ''),
              created_at: this.formatDate(item.created_at)
            })),
            currentPage: page,
            hasMore: newList.length >= this.data.pageSize,
            loading: false
          });
        } else {
          console.error('加载日记失败:', response);
          wx.showToast({
            title: response.message || '加载失败',
            icon: 'none'
          });
          this.setData({ loading: false });
        }
      } catch (error) {
        console.error('加载日记列表失败:', error);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
        this.setData({ loading: false });
      }
    },

    // 请求日记列表
    requestDiaryList(page: number) {
      return new Promise((resolve) => {
        wx.request({
          url: 'http://localhost:5000/api/articles',
          method: 'GET',
          data: {
            page: page,
            size: this.data.pageSize,
            user_id: this.data.userId
          },
          success: (res) => {
            resolve(res.data);
          },
          fail: (error) => {
            resolve({ code: 1, message: '网络错误', error });
          }
        });
      });
    },

    // 获取内容预览
    getContentPreview(content: string): string {
      if (!content) return '';
      // 移除HTML标签并截取前100个字符
      const plainText = content.replace(/<[^>]*>/g, '');
      return plainText.length > 100 ? plainText.substring(0, 100) + '...' : plainText;
    },

    // 格式化日期
    formatDate(dateString: string): string {
      if (!dateString) return '';
      const date = new Date(dateString);
      const now = new Date();
      const diff = now.getTime() - date.getTime();
      const days = Math.floor(diff / (1000 * 60 * 60 * 24));

      if (days === 0) {
        return '今天';
      } else if (days === 1) {
        return '昨天';
      } else if (days < 7) {
        return `${days}天前`;
      } else {
        return `${date.getMonth() + 1}月${date.getDate()}日`;
      }
    },

    // 切换搜索栏显示
    toggleSearch() {
      this.setData({
        showSearch: !this.data.showSearch
      });
    },

    // 搜索输入
    onSearchInput(e: any) {
      this.setData({
        searchKeyword: e.detail.value
      });
    },

    // 执行搜索
    async onSearch() {
      if (!this.data.searchKeyword.trim()) {
        this.loadDiaryList(true);
        return;
      }

      this.setData({ loading: true });

      try {
        const response = await this.searchDiaries(this.data.searchKeyword);
        if (response.success) {
          const searchResults = response.data.articles || [];
          this.setData({
            diaryList: searchResults.map((item: any) => ({
              ...item,
              content_preview: this.getContentPreview(item.content || ''),
              created_at: this.formatDate(item.created_at)
            })),
            loading: false,
            hasMore: false
          });
        }
      } catch (error) {
        console.error('搜索失败:', error);
        wx.showToast({
          title: '搜索失败',
          icon: 'none'
        });
        this.setData({ loading: false });
      }
    },

    // 搜索日记
    searchDiaries(keyword: string) {
      return new Promise((resolve) => {
        wx.request({
          url: 'http://localhost:5000/api/articles/search',
          method: 'GET',
          data: {
            title: keyword
          },
          success: (res) => {
            resolve(res.data);
          },
          fail: (error) => {
            resolve({ success: false, error });
          }
        });
      });
    },

    // 筛选类型改变
    onFilterChange(e: any) {
      const filterType = e.currentTarget.dataset.type;
      this.setData({ filterType });
      this.loadDiaryList(true);
    },

    // 加载更多
    loadMore() {
      if (this.data.hasMore && !this.data.loading) {
        this.setData({
          currentPage: this.data.currentPage + 1
        });
        this.loadDiaryList(false);
      }
    },

    // 跳转到日记详情
    goToDiaryDetail(e: any) {
      const diaryId = e.currentTarget.dataset.id;
      wx.navigateTo({
        url: `/pages/diary-detail/diary-detail?id=${diaryId}`
      });
    },

    // 跳转到创建日记
    goToCreateDiary() {
      wx.navigateTo({
        url: '/pages/diary-create/diary-create'
      });
    }
  }
})
