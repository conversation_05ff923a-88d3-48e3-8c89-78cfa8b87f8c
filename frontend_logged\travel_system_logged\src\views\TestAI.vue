<template>
  <div class="test-ai">
    <h1>AI功能测试页面</h1>
    
    <div class="debug-info">
      <p>当前activeTab: {{ activeTab }}</p>
      <p>点击次数: {{ clickCount }}</p>
    </div>
    
    <div class="test-cards">
      <div 
        class="test-card"
        :class="{ active: activeTab === 'travel-plan' }"
        @click="handleClick('travel-plan')"
      >
        <h3>旅游计划</h3>
        <p>点击测试</p>
      </div>
      
      <div 
        class="test-card"
        :class="{ active: activeTab === 'article-summary' }"
        @click="handleClick('article-summary')"
      >
        <h3>文章摘要</h3>
        <p>点击测试</p>
      </div>
    </div>
    
    <div class="content-area">
      <div v-if="activeTab === 'travel-plan'">
        <h2>旅游计划内容</h2>
        <p>这是旅游计划的内容</p>
      </div>
      
      <div v-if="activeTab === 'article-summary'">
        <h2>文章摘要内容</h2>
        <p>这是文章摘要的内容</p>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

export default {
  name: 'TestAI',
  setup() {
    const activeTab = ref('travel-plan')
    const clickCount = ref(0)
    
    const handleClick = (tab) => {
      console.log('点击了:', tab)
      clickCount.value++
      activeTab.value = tab
      ElMessage.success(`切换到${tab}`)
    }
    
    return {
      activeTab,
      clickCount,
      handleClick
    }
  }
}
</script>

<style scoped>
.test-ai {
  padding: 20px;
}

.debug-info {
  background: #f0f0f0;
  padding: 10px;
  margin-bottom: 20px;
  border-radius: 5px;
}

.test-cards {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.test-card {
  background: white;
  border: 2px solid #ddd;
  border-radius: 10px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.test-card:hover {
  border-color: #409eff;
}

.test-card.active {
  border-color: #409eff;
  background: #e6f7ff;
}

.content-area {
  background: #f9f9f9;
  padding: 20px;
  border-radius: 10px;
  min-height: 200px;
}
</style>
