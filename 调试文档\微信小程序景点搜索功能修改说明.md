# 微信小程序景点搜索功能修改说明

## 修改概述

本次修改将微信小程序的景点搜索界面功能完善，使其与网页端功能保持一致，能够正确调用后端算法进行景点搜索。

## 主要修改内容

### 1. 完善了三种搜索模式

#### 按名称搜索 (name)
- **功能**: 根据景点名称进行模糊搜索
- **API接口**: `/api/path/search-by-name`
- **参数**: `name` - 景点名称关键词
- **特点**: 不需要起始位置，直接搜索所有匹配的景点

#### 按类型搜索 (type)  
- **功能**: 在指定起始位置附近搜索特定类型的景点
- **API接口**: `/api/path/spots-by-criteria`
- **参数**: 
  - `start_vertex_id` - 起始位置ID
  - `distance` - 搜索距离
  - `types` - 景点类型数组
  - `keywords` - 可选的关键词数组
- **特点**: 支持距离过滤和结果排序

#### 附近搜索 (nearby)
- **功能**: 搜索指定起始位置附近的所有景点
- **API接口**: `/api/path/spots-by-start`
- **参数**:
  - `start_vertex_id` - 起始位置ID
  - `distance` - 搜索距离
- **特点**: 返回距离内的所有景点，按距离排序

### 2. 实现了位置建议功能

- **API接口**: `/api/path/location-suggestions`
- **功能**: 根据用户输入提供位置建议
- **特点**: 
  - 实时搜索建议
  - 显示位置名称和类型
  - 支持点击选择

### 3. 优化了用户界面

- **类型选择器**: 修复了显示问题，正确显示当前选择的类型
- **距离选择器**: 修复了显示问题，正确显示当前选择的距离
- **搜索结果**: 优化了距离显示，支持未计算距离的情况
- **错误处理**: 增加了完善的错误提示

### 4. 修复了API调用问题

- **统一API地址**: 使用配置文件中的API地址，避免硬编码
- **正确的接口**: 使用与网页端一致的后端接口
- **参数格式**: 确保请求参数格式与后端API匹配

## 技术实现细节

### 类型映射
```javascript
const typeMap = {
  1: '起点',
  2: '终点', 
  3: '景点',
  4: '餐厅',
  5: '酒店',
  6: '购物',
  7: '交通',
  8: '娱乐',
  9: '医疗',
  10: '教育'
};
```

### 搜索流程
1. 用户选择搜索模式
2. 根据模式要求输入相应参数
3. 验证输入参数的完整性
4. 调用对应的后端API
5. 处理返回结果并显示
6. 支持点击查看详情

### 错误处理
- 网络错误处理
- 参数验证
- 结果为空的提示
- API响应格式验证

## 使用说明

### 按名称搜索
1. 选择"按名称搜索"模式
2. 在搜索框中输入景点名称
3. 点击"开始搜索"

### 按类型搜索
1. 选择"按类型搜索"模式
2. 输入起始位置并从建议中选择
3. 选择场所类型
4. 选择搜索范围
5. 可选输入关键词进一步筛选
6. 点击"开始搜索"

### 附近搜索
1. 选择"附近搜索"模式
2. 输入起始位置并从建议中选择
3. 选择搜索范围
4. 点击"开始搜索"

## 注意事项

1. **网络配置**: 确保微信小程序能够访问后端API地址
2. **域名校验**: 开发环境需要关闭域名校验或配置合法域名
3. **API地址**: 在 `config/api.ts` 中配置正确的后端API地址
4. **后端服务**: 确保后端服务正常运行并支持相应的API接口

## 测试建议

1. 测试三种搜索模式的基本功能
2. 测试位置建议的实时搜索
3. 测试错误情况的处理
4. 测试搜索结果的显示和交互
5. 测试不同距离范围的搜索效果

## 后续优化建议

1. 添加搜索历史记录
2. 支持收藏功能
3. 添加地图显示
4. 优化搜索性能
5. 添加更多筛选条件
