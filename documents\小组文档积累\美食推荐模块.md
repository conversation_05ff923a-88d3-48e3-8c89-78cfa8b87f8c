# 美食推荐模块 API 文档

本文档详细描述了 Flask 版本后端的美食推荐模块 API 接口及其在 Postman 中的测试用例。美食推荐系统采用了多种高效算法来实现不同特征的排序和查找功能，以提供个性化和多样化的美食推荐体验。

## 目录

1. [获取推荐餐厅](#获取推荐餐厅)
2. [获取热门美食](#获取热门美食)
3. [获取特色美食](#获取特色美食)
4. [搜索餐厅](#搜索餐厅)
5. [获取附近美食](#获取附近美食)
6. [餐厅收藏功能](#餐厅收藏功能)
7. [餐厅评论功能](#餐厅评论功能)
8. [餐厅评分功能](#餐厅评分功能)
9. [算法实现总结](#算法实现总结)

## 获取推荐餐厅

### 接口信息

- **URL**: `/api/food/recommendations`
- **方法**: `GET`
- **描述**: 获取个性化餐厅推荐
- **请求参数**: 查询参数

### 请求参数

| 参数名       | 类型    | 必填 | 描述                                                             |
| ------------ | ------- | ---- | ---------------------------------------------------------------- |
| limit        | integer | 否   | 返回结果数量限制，默认为10                                       |
| cuisine_type | string  | 否   | 菜系类型                                                         |
| sort_by      | string  | 否   | 排序方式，支持 'popularity'(默认), 'rating', 'distance', 'price' |
| order        | string  | 否   | 排序顺序，支持 'desc'(默认), 'asc'                               |
| location_x   | number  | 否   | 位置x坐标，用于按距离排序                                        |
| location_y   | number  | 否   | 位置y坐标，用于按距离排序                                        |
| user_id      | integer | 否   | 用户ID，用于个性化推荐                                           |

### 响应

#### 成功响应 (200 OK)

```json
{
    "code": 0,
    "message": "推荐餐厅获取成功",
    "data": [
        {
            "restaurant_id": 1,
            "name": "北京烤鸭店",
            "cuisine_type": "北京菜",
            "evaluation": 4.5,
            "average_price_perperson": 120,
            "popularity": 95,
            "location_x": 116.3,
            "location_y": 39.9,
            "description": "正宗北京烤鸭，传统工艺制作"
        }
    ]
}
```

### Postman 测试

1. 打开 Postman
2. 创建新请求
3. 设置请求方法为 `GET`
4. 输入 URL: `http://localhost:5000/api/food/recommendations?limit=10&sort_by=rating&order=desc`
5. 点击 Send 按钮发送请求

## 获取热门美食

### 接口信息

- **URL**: `/api/food/popular`
- **方法**: `GET`
- **描述**: 获取热门餐厅
- **请求参数**: 查询参数

### 请求参数

| 参数名       | 类型    | 必填 | 描述             |
| ------------ | ------- | ---- | ---------------- |
| limit        | integer | 否   | 返回结果数量限制 |
| cuisine_type | string  | 否   | 菜系类型         |

### Postman 测试

```
GET http://localhost:5000/api/food/popular?limit=10&cuisine_type=川菜
```

## 搜索餐厅

### 接口信息

- **URL**: `/api/food/search`
- **方法**: `GET`
- **描述**: 按关键词搜索餐厅
- **请求参数**: 查询参数

### 请求参数

| 参数名  | 类型    | 必填 | 描述         |
| ------- | ------- | ---- | ------------ |
| keyword | string  | 是   | 搜索关键词   |
| limit   | integer | 否   | 返回结果数量 |

### Postman 测试

```
GET http://localhost:5000/api/food/search?keyword=火锅&limit=10
```

## 餐厅收藏功能

### 收藏餐厅

- **URL**: `/api/restaurant/favorite`
- **方法**: `POST`
- **描述**: 收藏餐厅

#### 请求参数

```json
{
    "user_id": 1,
    "restaurant_id": 5
}
```

### 取消收藏

- **URL**: `/api/restaurant/unfavorite`
- **方法**: `DELETE`
- **描述**: 取消收藏餐厅

### 获取收藏列表

- **URL**: `/api/restaurant/favorites/{user_id}`
- **方法**: `GET`
- **描述**: 获取用户收藏的餐厅

## 餐厅评论功能

### 评论餐厅

- **URL**: `/api/restaurant/comment`
- **方法**: `POST`
- **描述**: 评论餐厅

#### 请求参数

```json
{
    "user_id": 1,
    "restaurant_id": 5,
    "content": "菜品很好吃，服务也不错",
    "rating": 4
}
```

### 获取餐厅评论

- **URL**: `/api/restaurant/{restaurant_id}/comments`
- **方法**: `GET`
- **描述**: 获取餐厅评论列表

## 餐厅评分功能

### 对餐厅评分

- **URL**: `/api/restaurant/rating`
- **方法**: `POST`
- **描述**: 对餐厅进行评分

#### 请求参数

```json
{
    "user_id": 1,
    "restaurant_id": 5,
    "rating": 4
}
```

### 获取餐厅评分

- **URL**: `/api/restaurant/rating/{restaurant_id}`
- **方法**: `GET`
- **描述**: 获取餐厅评分信息

## 算法实现总结

## 一、排序算法

### 1. 快速排序 (Quick Sort)

**实现位置**：`backend/utils/restaurant_sorter.py` 中的 `quick_sort` 方法

**算法特点**：
- 时间复杂度：平均 O(n log n)，最坏 O(n²)
- 空间复杂度：O(log n)
- 使用随机选择枢轴元素的优化，减少最坏情况的发生概率
- 可根据不同属性（评分、价格、人气等）进行排序
- 支持升序和降序排序

**应用场景**：
- 完整排序餐厅列表
- 按距离排序时使用 (`sort_by_distance` 方法)

### 2. 堆排序 (Heap Sort)

**实现位置**：`backend/utils/restaurant_sorter.py` 中的 `heap_sort` 方法

**算法特点**：
- 时间复杂度：O(n log n)
- 空间复杂度：O(n)
- 利用 Python 的 `heapq` 模块实现
- 稳定的 O(n log n) 性能，不受输入数据分布影响

**应用场景**：
- 作为备选排序算法
- 在 `top_k_sort` 中用于找出前 K 个元素

### 3. Top-K 排序

**实现位置**：`backend/utils/restaurant_sorter.py` 中的 `top_k_sort` 方法

**算法特点**：
- 时间复杂度：O(n log k)，优于完全排序的 O(n log n)
- 空间复杂度：O(k)
- 当只需要前 k 个元素时，比完全排序更高效
- 使用堆数据结构实现
- 降序排序时使用最小堆，升序排序时使用最大堆

**应用场景**：
- 获取评分最高的前 N 个餐厅
- 获取价格最低的前 N 个餐厅
- 获取人气最高的前 N 个餐厅
- 在 API 接口中广泛使用，如 `get_popular_food`、`get_food_recommendations` 等

### 4. 多关键字排序

**实现位置**：`backend/utils/restaurant_sorter.py` 中的 `multi_key_sort` 方法

**算法特点**：
- 时间复杂度：O(n log n * k)，其中 k 是关键字数量
- 空间复杂度：O(n)
- 支持按多个属性依次排序
- 从最低优先级到最高优先级依次排序

**应用场景**：
- 复杂排序需求，如先按评分再按价格排序

## 二、查找和搜索算法

### 1. Boyer-Moore 字符串搜索算法

**实现位置**：`backend/utils/restaurant_finder.py` 中的 `boyer_moore_search` 方法

**算法特点**：
- 时间复杂度：平均 O(n/m)，最坏 O(n*m)，其中 n 是文本长度，m 是模式长度
- 空间复杂度：O(1)
- 在实际应用中比朴素的字符串匹配算法更高效
- 通过坏字符规则跳过不必要的比较

**应用场景**：
- 在餐厅名称、菜系类型和菜品名称中搜索关键词
- 用于 `content_based_search` 方法中的文本匹配

### 2. 基于内容的搜索 (Content-Based Search)

**实现位置**：`backend/utils/restaurant_finder.py` 中的 `content_based_search` 方法

**算法特点**：
- 将搜索关键词分解为多个单词
- 对餐厅的不同属性（名称、菜系、菜品）进行加权匹配
- 计算相关度得分并归一化
- 返回相关度大于0的结果，按相关度降序排序

**应用场景**：
- 关键词搜索餐厅
- 当用户输入搜索词时提供相关餐厅推荐

### 3. 基于距离的查找

**实现位置**：`backend/utils/restaurant_sorter.py` 中的 `top_k_by_distance` 方法

**算法特点**：
- 使用欧几里得距离计算餐厅与指定位置的距离
- 结合 Top-K 排序算法找出最近的 K 个餐厅
- 时间复杂度：O(n log k)

**应用场景**：
- 查找用户附近的餐厅
- 基于位置的餐厅推荐

## 三、推荐算法

### 1. 基于协同过滤的推荐

**实现位置**：`backend/utils/recommendation_algorithms.py` 和 `backend/utils/algorithm_recommendation.py`

**算法特点**：
- 基于用户相似度的协同过滤
- 计算用户之间的相似度（使用余弦相似度或皮尔逊相关系数）
- 根据相似用户的偏好推荐餐厅
- 考虑用户的浏览历史和评分行为

**应用场景**：
- 为登录用户提供个性化推荐
- "为您推荐"功能

### 2. 基于内容的推荐

**实现位置**：`backend/utils/recommendation_algorithms.py` 中的 `content_based_filtering` 方法

**算法特点**：
- 分析用户历史浏览的餐厅特征（菜系、价格区间等）
- 找出与用户偏好匹配的其他餐厅
- 考虑多种特征的权重

**应用场景**：
- 推荐与用户历史偏好相似的餐厅
- 作为协同过滤的补充

### 3. 混合推荐

**实现位置**：`backend/utils/algorithm_recommendation.py` 中的 `get_hybrid_recommendations` 方法

**算法特点**：
- 结合协同过滤、基于内容的推荐和热门推荐
- 对不同来源的推荐结果进行加权融合
- 归一化各种推荐的分数
- 提供更全面和多样化的推荐结果

**应用场景**：
- 智能推荐功能
- 解决冷启动问题和数据稀疏问题

## 四、特征排序应用

### 1. 按评分排序

**实现**：使用 `top_k_sort` 方法，关键字函数为 `lambda x: x.evaluation`

**应用**：获取评分最高的餐厅，适用于寻找高质量餐厅

### 2. 按价格排序

**实现**：使用 `top_k_sort` 方法，关键字函数为 `lambda x: x.average_price_perperson`

**应用**：按价格升序或降序排列餐厅，适用于不同预算的用户

### 3. 按人气排序

**实现**：使用 `top_k_sort` 方法，关键字函数为 `lambda x: x.popularity` 或 `lambda x: x.number_of_view`

**应用**：获取最受欢迎的餐厅，适用于推荐热门餐厅

### 4. 按距离排序

**实现**：使用 `top_k_by_distance` 方法，计算欧几里得距离

**应用**：查找用户附近的餐厅，基于位置的推荐

### 5. 按相关度排序

**实现**：使用 `content_based_search` 方法返回的相关度得分

**应用**：关键词搜索结果排序，确保最相关的餐厅排在前面

## 总结

美食推荐系统采用了多种高效算法来实现不同特征的排序和查找功能：

1. **排序算法**：快速排序、堆排序和Top-K排序，根据不同场景选择最适合的算法
2. **搜索算法**：Boyer-Moore字符串搜索算法，提高文本匹配效率
3. **推荐算法**：协同过滤、基于内容的推荐和混合推荐，提供个性化和多样化的推荐
4. **特征排序**：支持按评分、价格、人气、距离和相关度等多种特征排序

这些算法的组合使用，确保了美食推荐系统能够高效地处理大量餐厅数据，并为用户提供个性化、多样化的美食推荐体验。
