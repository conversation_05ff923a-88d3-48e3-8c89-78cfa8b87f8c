/* place-search.wxss */
.page {
  background-color: #f8f9fa;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 搜索区域 */
.search-section {
  background-color: #ffffff;
  padding: 20rpx 30rpx 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.search-box {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.search-input-wrapper {
  flex: 1;
  position: relative;
  background-color: #f5f6fa;
  border-radius: 25rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.search-input-wrapper:focus-within {
  border-color: #409EFF;
  background-color: #ffffff;
  box-shadow: 0 0 0 4rpx rgba(64, 158, 255, 0.1);
}

.search-input {
  width: 100%;
  height: 80rpx;
  padding: 0 50rpx 0 30rpx;
  font-size: 28rpx;
  background-color: transparent;
  border: none;
}

.search-placeholder {
  color: #adb5bd;
}

.search-icon {
  position: absolute;
  right: 25rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32rpx;
  color: #adb5bd;
}

.clear-btn {
  background-color: #6c757d;
  color: white;
  font-size: 24rpx;
  padding: 0 20rpx;
  height: 60rpx;
  line-height: 60rpx;
  border-radius: 30rpx;
  border: none;
  transition: all 0.3s ease;
}

.clear-btn:active {
  background-color: #5a6268;
  transform: scale(0.95);
}

/* 内容区域 */
.content {
  flex: 1;
  padding: 30rpx;
}

/* 搜索状态 */
.search-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 20rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.loading-icon {
  font-size: 48rpx;
  margin-bottom: 20rpx;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.status-text {
  font-size: 28rpx;
  color: #495057;
  font-weight: 500;
}

/* 内容区块 */
.section {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.section-header {
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f1f3f4;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #212529;
  margin-bottom: 8rpx;
}

.section-subtitle {
  font-size: 24rpx;
  color: #6c757d;
}

/* 列表样式 */
.location-list {
  padding: 0;
}

.location-item {
  display: flex;
  align-items: center;
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid #f1f3f4;
  transition: background-color 0.2s ease;
}

.location-item:last-child {
  border-bottom: none;
}

.location-item:active {
  background-color: #f8f9fa;
}

.location-info {
  flex: 1;
  min-width: 0;
}

.location-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #212529;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.location-arrow {
  font-size: 32rpx;
  color: #adb5bd;
  margin-left: 20rpx;
  font-weight: bold;
}

.location-meta {
  display: flex;
  align-items: center;
  gap: 15rpx;
  margin-bottom: 12rpx;
  flex-wrap: wrap;
}

.location-type {
  font-size: 20rpx;
  color: #409EFF;
  background-color: #e3f2fd;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.location-popularity {
  font-size: 22rpx;
  color: #ff6b35;
  font-weight: 500;
}

.location-evaluation {
  font-size: 22rpx;
  color: #28a745;
  font-weight: 500;
}

.location-description {
  font-size: 26rpx;
  color: #6c757d;
  line-height: 1.5;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.location-address {
  font-size: 24rpx;
  color: #adb5bd;
  margin-top: 8rpx;
}

/* 特殊样式 */
.popular-item {
  position: relative;
}

.popular-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6rpx;
  background: linear-gradient(to bottom, #ff6b35, #f7931e);
  border-radius: 0 3rpx 3rpx 0;
}

.result-item {
  position: relative;
}

.result-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6rpx;
  background: linear-gradient(to bottom, #409EFF, #36cfc9);
  border-radius: 0 3rpx 3rpx 0;
}

/* 空状态和默认状态 */
.empty-state, .default-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.empty-icon, .default-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #495057;
  margin-bottom: 15rpx;
}

.empty-text, .default-text {
  font-size: 28rpx;
  color: #6c757d;
  margin-bottom: 30rpx;
  line-height: 1.5;
}

.empty-tips {
  margin-bottom: 40rpx;
}

.empty-tips text {
  display: block;
  font-size: 24rpx;
  color: #adb5bd;
  margin-bottom: 8rpx;
  text-align: left;
}

.retry-btn {
  background: linear-gradient(135deg, #409EFF 0%, #36cfc9 100%);
  color: white;
  font-size: 28rpx;
  font-weight: 500;
  padding: 15rpx 40rpx;
  border-radius: 25rpx;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
}

.retry-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(64, 158, 255, 0.3);
}
