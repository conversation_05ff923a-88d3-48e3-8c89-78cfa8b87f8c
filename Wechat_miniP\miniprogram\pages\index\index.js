// index.js
// 获取应用实例
const app = getApp()

Component({
  data: {
    // 轮播图数据
    carouselList: [
      {
        title: '热门景点推荐',
        description: '发现全球热门旅游景点',
        image: '/images/assets/forbidden_city.jpg'
      },
      {
        title: '特色路线精选',
        description: '专家推荐的最佳旅行路线',
        image: '/images/assets/waitan.jpg'
      },
      {
        title: '最优路径规划',
        description: '智能导航为您规划最佳出行路线',
        image: '/images/assets/Disney_Shanghai.jpg'
      }
    ],

    // 功能卡片数据
    features: [
      {
        id: 1,
        icon: '🌟',
        title: '智能推荐',
        description: '根据您的偏好智能推荐最佳景点',
        route: '/pages/place-search/place-search'
      },
      {
        id: 2,
        icon: '🗺️',
        title: '路线规划',
        description: '智能算法规划最优旅行路线',
        route: '/pages/route-plan/route-plan'
      },
      {
        id: 3,
        icon: '🔍',
        title: '场所查询',
        description: '实时查询景区信息与服务',
        route: '/pages/place-search/place-search'
      },
      {
        id: 4,
        icon: '📖',
        title: '游记社区',
        description: '分享精彩旅行故事',
        route: '/pages/diary/diary'
      },
      {
        id: 5,
        icon: '📞',
        title: '联系我们',
        description: '获取帮助与客户支持',
        route: '/pages/contact/contact'
      }
    ],

    // 热门景点数据
    popularPlaces: [
      {
        id: 1,
        name: '故宫博物院',
        address: '北京市东城区景山前街4号',
        category: 'culture',
        region: '北京',
        rating: 4.8,
        reviews: 1250,
        tags: ['历史', '宫殿', '古迹'],
        image: '/images/assets/forbidden_city.jpg'
      },
      {
        id: 2,
        name: '外滩观光隧道',
        address: '上海市黄浦区中山东一路',
        category: 'sightseeing',
        region: '上海',
        rating: 4.7,
        reviews: 980,
        tags: ['夜景', '网红打卡', '江景'],
        image: '/images/assets/waitan.jpg'
      },
      {
        id: 3,
        name: '上海迪士尼乐园',
        address: '上海市浦东新区川沙新镇黄赵路310号',
        category: 'entertainment',
        region: '上海',
        rating: 4.9,
        reviews: 1560,
        tags: ['主题乐园', '亲子', '娱乐'],
        image: '/images/assets/Disney_Shanghai.jpg'
      }
    ]
  },

  pageLifetimes: {
    show() {
      if (typeof this.getTabBar === 'function' && this.getTabBar()) {
        this.getTabBar().setData({
          selected: 0
        });
      }
    }
  },

  methods: {
    // 导航到对应页面
    navigateTo(e) {
      console.log('navigateTo 被调用');
      const url = e.currentTarget.dataset.url;
      console.log('跳转URL:', url);

      if (!url) {
        wx.showToast({
          title: '页面路径错误',
          icon: 'none'
        });
        return;
      }

      // 定义tabbar页面列表
      const tabbarPages = [
        '/pages/index/index',
        '/pages/place-search/place-search',
        '/pages/route-plan/route-plan',
        '/pages/diary/diary',
        '/pages/user-center/user-center'
      ];

      // 检查是否为tabbar页面
      const isTabbarPage = tabbarPages.includes(url);

      if (isTabbarPage) {
        // 使用switchTab跳转tabbar页面
        wx.switchTab({
          url: url,
          success: () => {
            console.log('switchTab跳转成功:', url);
          },
          fail: (err) => {
            console.error('switchTab跳转失败:', err);
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      } else {
        // 使用navigateTo跳转普通页面
        wx.navigateTo({
          url: url,
          success: () => {
            console.log('navigateTo跳转成功:', url);
          },
          fail: (err) => {
            console.error('navigateTo跳转失败:', err);
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      }
    },

    // 跳转到景点详情
    goToPlaceDetail(e) {
      const id = e.currentTarget.dataset.id;
      wx.navigateTo({
        url: `/pages/place-detail/place-detail?id=${id}`
      });
    },

    // 用户点击轮播图
    onCarouselTap(e) {
      const index = e.currentTarget.dataset.index;
      const item = this.data.carouselList[index];

      // 根据轮播图内容跳转到不同页面
      if (index === 0) {
        wx.switchTab({
          url: '/pages/place-search/place-search'
        });
      } else if (index === 1) {
        wx.navigateTo({
          url: '/pages/recommend/recommend'
        });
      } else if (index === 2) {
        wx.switchTab({
          url: '/pages/route-plan/route-plan'
        });
      }
    }
  }
})
