"""
游记排序工具类

该模块实现了各种游记排序算法，包括：
1. 快速排序
2. 堆排序
3. Top-K排序
"""

from typing import List, Callable, Any
import random
from models.article import Article

class DiarySorter:
    """游记排序工具类"""
    
    @staticmethod
    def quick_sort(diaries: List[Article], key_func: Callable[[Article], Any], reverse: bool = False) -> List[Article]:
        """
        使用快速排序算法对游记列表进行排序
        
        Args:
            diaries: 游记列表
            key_func: 排序关键字函数，例如 lambda x: x.evaluation
            reverse: 是否降序排序，默认为False（升序）
            
        Returns:
            排序后的游记列表
        """
        if not diaries:
            return []
            
        # 创建副本，避免修改原始列表
        diaries_copy = diaries.copy()
        
        def _quick_sort(arr, low, high):
            if low < high:
                # 分区操作，返回分区点
                pivot_index = _partition(arr, low, high)
                # 对分区点左侧子数组进行递归排序
                _quick_sort(arr, low, pivot_index - 1)
                # 对分区点右侧子数组进行递归排序
                _quick_sort(arr, pivot_index + 1, high)
                
        def _partition(arr, low, high):
            # 选择随机元素作为pivot，减少最坏情况的发生
            pivot_index = random.randint(low, high)
            arr[pivot_index], arr[high] = arr[high], arr[pivot_index]
            
            pivot = key_func(arr[high])
            i = low - 1
            
            for j in range(low, high):
                # 根据reverse参数决定比较方式
                if (not reverse and key_func(arr[j]) <= pivot) or (reverse and key_func(arr[j]) >= pivot):
                    i += 1
                    arr[i], arr[j] = arr[j], arr[i]
                    
            arr[i + 1], arr[high] = arr[high], arr[i + 1]
            return i + 1
            
        _quick_sort(diaries_copy, 0, len(diaries_copy) - 1)
        return diaries_copy
    
    @staticmethod
    def top_k_sort(diaries: List[Article], k: int, key_func: Callable[[Article], Any], reverse: bool = True) -> List[Article]:
        """
        只排序前K个元素的高效算法
        
        Args:
            diaries: 游记列表
            k: 需要排序的前K个元素
            key_func: 排序关键字函数，例如 lambda x: x.evaluation
            reverse: 是否降序排序，默认为True（降序）
            
        Returns:
            排序后的前K个游记
        """
        if not diaries:
            return []
            
        if k >= len(diaries):
            # 如果k大于等于列表长度，直接使用完全排序
            return DiarySorter.quick_sort(diaries, key_func, reverse)
            
        # 使用自实现的堆排序找出前K个元素
        return DiarySorter._custom_heap_top_k(diaries, k, key_func, reverse)

    @staticmethod
    def _custom_heap_top_k(diaries: List[Article], k: int, key_func, reverse: bool = False) -> List[Article]:
        """
        自实现的堆排序Top-K算法，不使用heapq模块
        """
        if not diaries or k <= 0:
            return []

        if k >= len(diaries):
            return DiarySorter.quick_sort(diaries, key_func, reverse)

        # 构建最小堆（用于降序）或最大堆（用于升序）
        heap = []

        for diary in diaries:
            key_value = key_func(diary)

            if len(heap) < k:
                # 堆未满，直接添加
                heap.append((key_value, diary))
                DiarySorter._heapify_up(heap, len(heap) - 1, reverse)
            else:
                # 堆已满，比较并可能替换堆顶
                if reverse:  # 降序，使用最小堆
                    if key_value > heap[0][0]:
                        heap[0] = (key_value, diary)
                        DiarySorter._heapify_down(heap, 0, reverse)
                else:  # 升序，使用最大堆
                    if key_value < heap[0][0]:
                        heap[0] = (key_value, diary)
                        DiarySorter._heapify_down(heap, 0, reverse)

        # 提取结果并排序
        result = [diary for _, diary in heap]
        return DiarySorter.quick_sort(result, key_func, reverse)

    @staticmethod
    def _heapify_up(heap: List, index: int, reverse: bool):
        """堆的上浮操作"""
        while index > 0:
            parent = (index - 1) // 2
            if reverse:  # 最小堆
                if heap[index][0] >= heap[parent][0]:
                    break
            else:  # 最大堆
                if heap[index][0] <= heap[parent][0]:
                    break

            heap[index], heap[parent] = heap[parent], heap[index]
            index = parent

    @staticmethod
    def _heapify_down(heap: List, index: int, reverse: bool):
        """堆的下沉操作"""
        size = len(heap)
        while True:
            target = index
            left = 2 * index + 1
            right = 2 * index + 2

            if reverse:  # 最小堆
                if left < size and heap[left][0] < heap[target][0]:
                    target = left
                if right < size and heap[right][0] < heap[target][0]:
                    target = right
            else:  # 最大堆
                if left < size and heap[left][0] > heap[target][0]:
                    target = left
                if right < size and heap[right][0] > heap[target][0]:
                    target = right

            if target == index:
                break

            heap[index], heap[target] = heap[target], heap[index]
            index = target
            
        return result
