import request from '@/utils/request';

/**
 * 获取热门景点推荐
 * @param {Object} params - 请求参数
 * @param {number} [params.limit=10] - 返回结果数量限制
 * @param {number} [params.type] - 地点类型筛选，不提供则返回所有类型
 * @returns {Promise} - 返回推荐结果的Promise
 */
export function getPopularRecommendations(params) {
  return request({
    url: '/api/recommend/popularity',
    method: 'get',
    params
  });
}

/**
 * 获取评分最高的地点推荐
 * @param {Object} params - 请求参数
 * @param {number} [params.limit=10] - 返回结果数量限制
 * @param {number} [params.type] - 地点类型筛选，不提供则返回所有类型
 * @returns {Promise} - 返回推荐结果的Promise
 */
export function getTopRatedRecommendations(params) {
  return request({
    url: '/api/recommend/rating',
    method: 'get',
    params
  });
}

/**
 * 获取基于内容的推荐
 * @param {Object} data - 请求数据
 * @param {number} data.user_id - 用户ID
 * @param {number} [data.limit=10] - 返回结果数量限制
 * @returns {Promise} - 返回推荐结果的Promise
 */
export function getContentRecommendations(data) {
  return request({
    url: '/api/recommend/content',
    method: 'post',
    data
  });
}

/**
 * 获取基于协同过滤的推荐
 * @param {Object} data - 请求数据
 * @param {number} data.user_id - 用户ID
 * @param {number} [data.limit=10] - 返回结果数量限制
 * @returns {Promise} - 返回推荐结果的Promise
 */
export function getCollaborativeRecommendations(data) {
  return request({
    url: '/api/recommend/collaborative',
    method: 'post',
    data
  });
}

/**
 * 获取混合推荐
 * @param {Object} data - 请求数据
 * @param {number} data.user_id - 用户ID
 * @param {number} [data.limit=10] - 返回结果数量限制
 * @param {Object} [data.weights] - 各推荐算法的权重
 * @returns {Promise} - 返回推荐结果的Promise
 */
export function getHybridRecommendations(data) {
  return request({
    url: '/api/recommend/hybrid',
    method: 'post',
    data
  });
}

/**
 * 获取高级协同过滤推荐
 * @param {Object} data - 请求数据
 * @param {number} data.user_id - 用户ID
 * @param {number} [data.limit=10] - 返回结果数量限制
 * @returns {Promise} - 返回推荐结果的Promise
 */
export function getAdvancedCollaborativeRecommendations(data) {
  return request({
    url: '/api/advanced-recommend/collaborative',
    method: 'post',
    data
  });
}

/**
 * 获取高级内容推荐
 * @param {Object} data - 请求数据
 * @param {number} data.user_id - 用户ID
 * @param {number} [data.limit=10] - 返回结果数量限制
 * @returns {Promise} - 返回推荐结果的Promise
 */
export function getAdvancedContentRecommendations(data) {
  return request({
    url: '/api/advanced-recommend/content',
    method: 'post',
    data
  });
}

/**
 * 获取高级混合推荐
 * @param {Object} data - 请求数据
 * @param {number} data.user_id - 用户ID
 * @param {number} [data.limit=10] - 返回结果数量限制
 * @param {Object} [data.weights] - 各推荐算法的权重
 * @returns {Promise} - 返回推荐结果的Promise
 */
export function getAdvancedHybridRecommendations(data) {
  return request({
    url: '/api/advanced-recommend/hybrid',
    method: 'post',
    data
  });
}

/**
 * 获取"为您推荐"的景点
 * @param {Object} data - 请求数据
 * @param {boolean} data.is_guest - 是否是游客模式
 * @param {number} [data.user_id] - 用户ID（登录模式下必须）
 * @returns {Promise} - 返回推荐结果的Promise
 */
export function getForYouRecommendations(data) {
  return request({
    url: '/api/recommend',
    method: 'post',
    data
  });
}

/**
 * 获取所有景点（按热度排序）
 * @param {Object} params - 请求参数
 * @param {number} [params.limit] - 返回结果数量限制
 * @param {number} [params.type] - 地点类型筛选，不提供则返回所有类型
 * @returns {Promise} - 返回所有景点的Promise
 */
export function getAllLocationsByPopularity(params) {
  return request({
    url: '/api/recommend/all-locations',
    method: 'get',
    params
  });
}

/**
 * 刷新推荐系统缓存
 * @param {Object} data - 请求数据
 * @param {boolean} [data.force=false] - 是否强制刷新
 * @returns {Promise} - 返回刷新结果的Promise
 */
export function refreshRecommendationCache(data) {
  return request({
    url: '/api/advanced-recommend/refresh-cache',
    method: 'post',
    data
  });
}