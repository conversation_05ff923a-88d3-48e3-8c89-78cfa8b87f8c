#!/usr/bin/env python3
"""
协同过滤推荐系统测试脚本

该脚本用于测试景点推荐、游记社区、美食推荐三个模块的协同过滤功能
"""

import requests
import json
import time

# 配置
BASE_URL = "http://localhost:5000"
TEST_USER_ID = 1

def test_location_collaborative_filtering():
    """测试景点推荐的协同过滤功能"""
    print("=" * 50)
    print("测试景点推荐协同过滤功能")
    print("=" * 50)
    
    url = f"{BASE_URL}/api/recommend/collaborative"
    data = {
        "user_id": TEST_USER_ID,
        "limit": 10
    }
    
    try:
        response = requests.post(url, json=data, timeout=30)
        result = response.json()
        
        print(f"请求URL: {url}")
        print(f"请求数据: {data}")
        print(f"响应状态码: {response.status_code}")
        print(f"响应数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        if result.get('code') == 0:
            recommendations = result.get('data', {}).get('recommendations', [])
            elapsed_time = result.get('data', {}).get('elapsed_time', 'N/A')
            print(f"✅ 景点协同过滤推荐成功")
            print(f"   推荐数量: {len(recommendations)}")
            print(f"   执行时间: {elapsed_time}")
            if recommendations:
                print(f"   第一个推荐: {recommendations[0].get('name', 'N/A')}")
            return True
        else:
            print(f"❌ 景点协同过滤推荐失败: {result.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ 景点协同过滤推荐异常: {str(e)}")
        return False

def test_article_collaborative_filtering():
    """测试游记社区的协同过滤功能"""
    print("\n" + "=" * 50)
    print("测试游记社区协同过滤功能")
    print("=" * 50)
    
    url = f"{BASE_URL}/api/articles/collaborative-recommendations"
    data = {
        "user_id": TEST_USER_ID,
        "limit": 10
    }
    
    try:
        response = requests.post(url, json=data, timeout=30)
        result = response.json()
        
        print(f"请求URL: {url}")
        print(f"请求数据: {data}")
        print(f"响应状态码: {response.status_code}")
        print(f"响应数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        if result.get('code') == 0:
            articles = result.get('data', {}).get('articles', [])
            elapsed_time = result.get('data', {}).get('elapsed_time', 'N/A')
            print(f"✅ 游记协同过滤推荐成功")
            print(f"   推荐数量: {len(articles)}")
            print(f"   执行时间: {elapsed_time}")
            if articles:
                print(f"   第一篇文章: {articles[0].get('title', 'N/A')}")
            return True
        else:
            print(f"❌ 游记协同过滤推荐失败: {result.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ 游记协同过滤推荐异常: {str(e)}")
        return False

def test_food_collaborative_filtering():
    """测试美食推荐的协同过滤功能"""
    print("\n" + "=" * 50)
    print("测试美食推荐协同过滤功能")
    print("=" * 50)
    
    url = f"{BASE_URL}/api/food/collaborative-recommendations"
    params = {
        "user_id": TEST_USER_ID,
        "limit": 10
    }
    
    try:
        response = requests.get(url, params=params, timeout=30)
        result = response.json()
        
        print(f"请求URL: {url}")
        print(f"请求参数: {params}")
        print(f"响应状态码: {response.status_code}")
        print(f"响应数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        if result.get('code') == 0:
            restaurants = result.get('data', [])
            print(f"✅ 美食协同过滤推荐成功")
            print(f"   推荐数量: {len(restaurants)}")
            if restaurants:
                print(f"   第一家餐馆: {restaurants[0].get('name', 'N/A')}")
                print(f"   推荐类型: {restaurants[0].get('recommendation_type', 'N/A')}")
            return True
        else:
            print(f"❌ 美食协同过滤推荐失败: {result.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ 美食协同过滤推荐异常: {str(e)}")
        return False

def test_api_connectivity():
    """测试API连通性"""
    print("=" * 50)
    print("测试API连通性")
    print("=" * 50)
    
    try:
        response = requests.get(f"{BASE_URL}/api/health", timeout=10)
        if response.status_code == 200:
            print("✅ 后端API连通正常")
            return True
        else:
            print(f"❌ 后端API连通异常，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 后端API连通失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("协同过滤推荐系统功能测试")
    print("测试时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    print("测试用户ID:", TEST_USER_ID)
    
    # 测试结果统计
    test_results = []
    
    # 1. 测试API连通性
    connectivity_result = test_api_connectivity()
    test_results.append(("API连通性", connectivity_result))
    
    if not connectivity_result:
        print("\n❌ API连通性测试失败，跳过后续测试")
        return
    
    # 2. 测试景点推荐协同过滤
    location_result = test_location_collaborative_filtering()
    test_results.append(("景点推荐协同过滤", location_result))
    
    # 3. 测试游记社区协同过滤
    article_result = test_article_collaborative_filtering()
    test_results.append(("游记社区协同过滤", article_result))
    
    # 4. 测试美食推荐协同过滤
    food_result = test_food_collaborative_filtering()
    test_results.append(("美食推荐协同过滤", food_result))
    
    # 输出测试总结
    print("\n" + "=" * 50)
    print("测试结果总结")
    print("=" * 50)
    
    passed_count = 0
    total_count = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed_count += 1
    
    print(f"\n总计: {passed_count}/{total_count} 项测试通过")
    
    if passed_count == total_count:
        print("🎉 所有协同过滤功能测试通过！")
    else:
        print("⚠️  部分协同过滤功能测试失败，请检查相关实现")

if __name__ == "__main__":
    main()
