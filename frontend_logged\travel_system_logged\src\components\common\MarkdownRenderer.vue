<template>
  <div class="markdown-renderer">
    <div v-html="renderedContent"></div>
  </div>
</template>
 
<script>
import { computed } from 'vue'
<<<<<<< HEAD
import { marked } from 'marked'
import hljs from 'highlight.js'
hljs.configure({
  languages: ['javascript', 'json', 'python', 'bash']
})
marked.setOptions({
  highlight: function(code) {
    return hljs.highlightAuto(code).value
  }
})
=======

>>>>>>> backend
export default {
  name: 'MarkdownRenderer',
  props: {
    content: {
      type: String,
      default: ''
    },
    options: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    // 简化的Markdown渲染器，不依赖外部库
    const renderedContent = computed(() => {
      if (!props.content) return ''

      try {
        let html = props.content

        // 基本的Markdown转换
        // 标题
        html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>')
        html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>')
        html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>')

        // 粗体
        html = html.replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
        html = html.replace(/__(.*?)__/gim, '<strong>$1</strong>')

        // 斜体
        html = html.replace(/\*(.*)\*/gim, '<em>$1</em>')
        html = html.replace(/_(.*?)_/gim, '<em>$1</em>')

        // 换行
        html = html.replace(/\n/gim, '<br>')

        // 段落
        html = html.replace(/(<br>\s*){2,}/gim, '</p><p>')
        html = '<p>' + html + '</p>'

        return html
      } catch (error) {
        console.error('Markdown rendering error:', error)
        // 如果渲染失败，返回原始内容
        return `<div style="white-space: pre-wrap;">${props.content}</div>`
      }
    })

    return {
      renderedContent
    }
  }
}
</script>

<style scoped>
.markdown-renderer {
  line-height: 1.6;
  color: #333;
}

/* 标题样式 */
.markdown-renderer :deep(h1) {
  font-size: 2rem;
  font-weight: 700;
  margin: 1.5rem 0 1rem 0;
  color: #2c3e50;
  border-bottom: 2px solid #3498db;
  padding-bottom: 0.5rem;
}

.markdown-renderer :deep(h2) {
  font-size: 1.6rem;
  font-weight: 600;
  margin: 1.3rem 0 0.8rem 0;
  color: #34495e;
  border-bottom: 1px solid #bdc3c7;
  padding-bottom: 0.3rem;
}

.markdown-renderer :deep(h3) {
  font-size: 1.3rem;
  font-weight: 600;
  margin: 1.1rem 0 0.6rem 0;
  color: #34495e;
}

.markdown-renderer :deep(h4) {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 1rem 0 0.5rem 0;
  color: #34495e;
}

.markdown-renderer :deep(h5),
.markdown-renderer :deep(h6) {
  font-size: 1rem;
  font-weight: 600;
  margin: 0.8rem 0 0.4rem 0;
  color: #34495e;
}

/* 段落样式 */
.markdown-renderer :deep(p) {
  margin: 0.8rem 0;
  line-height: 1.7;
}

/* 列表样式 */
.markdown-renderer :deep(ul),
.markdown-renderer :deep(ol) {
  margin: 0.8rem 0;
  padding-left: 2rem;
}

.markdown-renderer :deep(li) {
  margin: 0.3rem 0;
  line-height: 1.6;
}

.markdown-renderer :deep(ul li) {
  list-style-type: disc;
}

.markdown-renderer :deep(ol li) {
  list-style-type: decimal;
}

/* 引用样式 */
.markdown-renderer :deep(blockquote) {
  margin: 1rem 0;
  padding: 0.8rem 1.2rem;
  background: #f8f9fa;
  border-left: 4px solid #3498db;
  border-radius: 0 4px 4px 0;
  font-style: italic;
  color: #555;
}

.markdown-renderer :deep(blockquote p) {
  margin: 0;
}

/* 代码样式 */
.markdown-renderer :deep(code) {
  background: #f1f2f6;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.9rem;
  color: #e74c3c;
}

.markdown-renderer :deep(pre) {
  background: #2d3748;
  color: #e2e8f0;
  padding: 1rem;
  border-radius: 6px;
  overflow-x: auto;
  margin: 1rem 0;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.4;
}

.markdown-renderer :deep(pre code) {
  background: none;
  padding: 0;
  color: inherit;
  border-radius: 0;
}

/* 表格样式 */
.markdown-renderer :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
  background: white;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.markdown-renderer :deep(th),
.markdown-renderer :deep(td) {
  padding: 0.8rem 1rem;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.markdown-renderer :deep(th) {
  background: #f8f9fa;
  font-weight: 600;
  color: #495057;
}

.markdown-renderer :deep(tr:hover) {
  background: #f8f9fa;
}

/* 链接样式 */
.markdown-renderer :deep(a) {
  color: #3498db;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.2s ease;
}

.markdown-renderer :deep(a:hover) {
  color: #2980b9;
  border-bottom-color: #2980b9;
}

/* 分割线样式 */
.markdown-renderer :deep(hr) {
  border: none;
  height: 2px;
  background: linear-gradient(to right, transparent, #bdc3c7, transparent);
  margin: 2rem 0;
}

/* 图片样式 */
.markdown-renderer :deep(img) {
  max-width: 100%;
  height: auto;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 1rem 0;
}

/* 强调样式 */
.markdown-renderer :deep(strong) {
  font-weight: 700;
  color: #2c3e50;
}

.markdown-renderer :deep(em) {
  font-style: italic;
  color: #7f8c8d;
}

/* 删除线样式 */
.markdown-renderer :deep(del) {
  text-decoration: line-through;
  color: #95a5a6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .markdown-renderer :deep(h1) {
    font-size: 1.6rem;
  }

  .markdown-renderer :deep(h2) {
    font-size: 1.4rem;
  }

  .markdown-renderer :deep(h3) {
    font-size: 1.2rem;
  }

  .markdown-renderer :deep(pre) {
    font-size: 0.8rem;
    padding: 0.8rem;
  }

  .markdown-renderer :deep(table) {
    font-size: 0.9rem;
  }

  .markdown-renderer :deep(th),
  .markdown-renderer :deep(td) {
    padding: 0.6rem 0.8rem;
  }
}
</style>
