from utils.database import db
import datetime

def get_utc_now():
    """Get current UTC time in a timezone-aware format"""
    return datetime.datetime.now(datetime.timezone.utc)

class ArticleLike(db.Model):
    """
    Article like model - represents a user's like on an article
    """
    __tablename__ = 'article_likes'
    __table_args__ = {'extend_existing': True}

    user_id = db.Column(db.Integer, db.ForeignKey('users.user_id'), primary_key=True)
    article_id = db.Column(db.Integer, db.<PERSON>ey('articles.article_id'), primary_key=True)
    created_at = db.Column(db.DateTime, default=get_utc_now)

    # Define relationships
    user = db.relationship('User', backref='article_likes')
    article = db.relationship('Article', backref='likes')

    def to_dict(self):
        """Convert article like to dictionary"""
        return {
            'user_id': self.user_id,
            'article_id': self.article_id,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
