"""
购物车相关API
1. 添加菜品到购物车
2. 获取用户购物车
3. 更新购物车中菜品数量
4. 从购物车中移除菜品
5. 清空购物车
6. 提交订单
"""

from flask import Blueprint, request, current_app
from models.cart import UserCart
from models.restaurant import Restaurant
from utils.database import db
from utils.response import success, error

cart_bp = Blueprint('cart', __name__)

@cart_bp.route('/add', methods=['POST'])
def add_to_cart():
    """
    添加菜品到购物车
    """
    try:
        data = request.get_json()
        
        # 验证必要参数
        required_fields = ['user_id', 'dish_name', 'dish_price', 'restaurant_id']
        for field in required_fields:
            if field not in data:
                return error(f"缺少必要参数: {field}")
        
        # 获取餐馆信息
        restaurant = Restaurant.query.get(data['restaurant_id'])
        if not restaurant:
            return error("餐馆不存在")
        
        # 检查购物车中是否已存在该菜品
        existing_item = UserCart.query.filter_by(
            user_id=data['user_id'],
            dish_name=data['dish_name'],
            restaurant_id=data['restaurant_id']
        ).first()
        
        if existing_item:
            # 如果已存在，增加数量
            existing_item.quantity += 1
            db.session.commit()
            return success(existing_item.to_dict())
        
        # 创建新的购物车项
        cart_item = UserCart(
            user_id=data['user_id'],
            dish_name=data['dish_name'],
            dish_price=data['dish_price'],
            dish_image=data.get('dish_image'),
            restaurant_id=data['restaurant_id'],
            restaurant_name=restaurant.name,
            quantity=1
        )
        
        db.session.add(cart_item)
        db.session.commit()
        
        return success(cart_item.to_dict())
    except Exception as e:
        current_app.logger.error(f"添加菜品到购物车出错: {str(e)}")
        return error(str(e))

@cart_bp.route('/user/<int:user_id>', methods=['GET'])
def get_user_cart(user_id):
    """
    获取用户购物车
    """
    try:
        cart_items = UserCart.query.filter_by(user_id=user_id).all()
        
        # 转换为字典列表
        result = [item.to_dict() for item in cart_items]
        
        # 计算总价
        total_price = sum(item.dish_price * item.quantity for item in cart_items)
        
        return success({
            'items': result,
            'total_price': round(total_price, 2),
            'item_count': len(result)
        })
    except Exception as e:
        current_app.logger.error(f"获取用户购物车出错: {str(e)}")
        return error(str(e))

@cart_bp.route('/update/<int:cart_id>', methods=['PUT'])
def update_cart_item(cart_id):
    """
    更新购物车中菜品数量
    """
    try:
        data = request.get_json()
        
        # 验证必要参数
        if 'quantity' not in data:
            return error("缺少必要参数: quantity")
        
        # 获取购物车项
        cart_item = UserCart.query.get(cart_id)
        if not cart_item:
            return error("购物车项不存在")
        
        # 更新数量
        cart_item.quantity = max(1, data['quantity'])  # 确保数量至少为1
        db.session.commit()
        
        return success(cart_item.to_dict())
    except Exception as e:
        current_app.logger.error(f"更新购物车项出错: {str(e)}")
        return error(str(e))

@cart_bp.route('/remove/<int:cart_id>', methods=['DELETE'])
def remove_from_cart(cart_id):
    """
    从购物车中移除菜品
    """
    try:
        # 获取购物车项
        cart_item = UserCart.query.get(cart_id)
        if not cart_item:
            return error("购物车项不存在")
        
        # 删除购物车项
        db.session.delete(cart_item)
        db.session.commit()
        
        return success({"message": "已从购物车中移除"})
    except Exception as e:
        current_app.logger.error(f"从购物车中移除菜品出错: {str(e)}")
        return error(str(e))

@cart_bp.route('/clear/<int:user_id>', methods=['DELETE'])
def clear_cart(user_id):
    """
    清空购物车
    """
    try:
        # 删除用户的所有购物车项
        UserCart.query.filter_by(user_id=user_id).delete()
        db.session.commit()
        
        return success({"message": "购物车已清空"})
    except Exception as e:
        current_app.logger.error(f"清空购物车出错: {str(e)}")
        return error(str(e))

@cart_bp.route('/checkout', methods=['POST'])
def checkout():
    """
    提交订单
    """
    try:
        data = request.get_json()
        
        # 验证必要参数
        if 'user_id' not in data:
            return error("缺少必要参数: user_id")
        
        user_id = data['user_id']
        
        # 获取用户购物车
        cart_items = UserCart.query.filter_by(user_id=user_id).all()
        if not cart_items:
            return error("购物车为空")
        
        # 这里可以添加订单创建逻辑
        # ...
        
        # 清空购物车
        UserCart.query.filter_by(user_id=user_id).delete()
        db.session.commit()
        
        return success({"message": "订单提交成功"})
    except Exception as e:
        current_app.logger.error(f"提交订单出错: {str(e)}")
        return error(str(e))
